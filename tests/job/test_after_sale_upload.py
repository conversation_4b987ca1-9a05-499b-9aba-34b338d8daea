import json

import pytest
from dramatiq import Retry
from result import Ok

from robot_processor.enums import JobStatus, ErpType
from robot_processor.job.after_sale_upload import AfterSaleUploadExecutor
from rpa.erp.wdt import WarehouseQueryResp, TradeQueryResp
from rpa.erp.wdtulti import WdtultiOrderResp

mock_wdt_trade = '''
{
  "response": {
    "errorcode": 0,
    "message": "ok",
    "total_count": 1,
    "trades": [
      {
        "bad_reason": 0,
        "buyer_message": "无痕发货",
        "buyer_message_count": "1",
        "buyer_nick": "",
        "cancel_reason": "0",
        "check_step": "0",
        "checker_id": 211,
        "checker_name": "高怡",
        "checkouter_id": 0,
        "checkouter_name": "",
        "cod_amount": "0.0000",
        "commission": "3.7400",
        "consign_status": 12,
        "created": "2023-03-20 00:56:15",
        "cs_remark": "",
        "cs_remark_change_count": "0",
        "cs_remark_count": "0",
        "currency": "",
        "customer_id": "4553846",
        "customer_name": "",
        "customer_no": "KH202303200265",
        "customer_type": "0",
        "dap_amount": "68.0000",
        "delay_to_time": "0",
        "delivery_term": 1,
        "discount": "108.0000",
        "discount_change": "0.0000",
        "ext_cod_fee": "0.0000",
        "fchecker_id": 0,
        "fchecker_name": "系统",
        "fenxiao_nick": "",
        "fenxiao_tid": "",
        "fenxiao_type": 0,
        "flag_id": "0",
        "flag_name": "",
        "freeze_reason": 0,
        "freeze_reason_info": "",
        "fullname": "系统",
        "gift_mask": "0",
        "goods_amount": "176.0000",
        "goods_cost": "51.0000",
        "goods_count": "1.0000",
        "goods_list": [
          {
            "actual_num": "1.0000",
            "adjust": "0.0000",
            "api_goods_name": "全身镜穿衣落地镜女生家用卧室试衣镜ins风旋转可移动立体大镜子",
            "api_spec_name": "标准款-椭圆简约白;其他;是",
            "barcode": "",
            "base_unit_id": 0,
            "bind_oid": "",
            "cid": 200,
            "class_name": "无",
            "commission": "3.7400",
            "created": "2023-03-20 00:56:15",
            "delivery_term": 1,
            "discount": "108.0000",
            "flag": 0,
            "from_mask": 1,
            "gift_type": 0,
            "goods_id": 177,
            "goods_name": "全身镜落地镜家用女北欧简约网红ins风女生卧室少女试衣穿衣镜子",
            "goods_no": "JZ-01",
            "goods_type": 1,
            "guarantee_mode": "1",
            "invoice_content": "",
            "invoice_type": 0,
            "is_consigned": "1",
            "is_master": "1",
            "is_print_suite": "0",
            "is_received": "1",
            "is_zero_cost": "1",
            "large_type": 0,
            "modified": "2023-03-30 12:10:16",
            "num": "1.0000",
            "order_price": "68.0000",
            "paid": "68.0000",
            "pay_id": "2023032022001157021420779763",
            "pay_status": "2",
            "pay_time": "2023-03-20 00:56:07",
            "platform_goods_id": "655629585687",
            "platform_id": 1,
            "platform_spec_id": "4908218331987",
            "price": "176.0000",
            "prop2": "",
            "rec_id": 11179103,
            "refund_num": "0.0000",
            "refund_status": 0,
            "remark": "",
            "share_amount": "68.0000",
            "share_amount2": "0.0000",
            "share_post": "0.0000",
            "share_price": "68.0000",
            "spec_code": "",
            "spec_id": 5541,
            "spec_name": "简约白,标准款-椭圆-不可旋转",
            "spec_no": "JZ-01-椭圆-白",
            "src_oid": "3268940115984170159",
            "src_tid": "3268940115984170159",
            "stock_reserved": "0",
            "suite_amount": "0.0000",
            "suite_discount": "0.0000",
            "suite_id": 0,
            "suite_name": "",
            "suite_no": "",
            "suite_num": "0.0000",
            "tax_rate": "0.0000",
            "tc_order_id": "",
            "trade_id": 9706139,
            "unit_name": "",
            "weight": "9.5000"
          }
        ],
        "goods_type_count": 1,
        "id_card": "",
        "id_card_type": 0,
        "invoice_content": "",
        "invoice_id": 0,
        "invoice_title": "",
        "invoice_type": 0,
        "is_prev_notify": "0",
        "is_sealed": "0",
        "is_unpayment_sms": "0",
        "large_type": "0",
        "logistics_code": "646",
        "logistics_id": 734,
        "logistics_name": "淘系安能9.1(瑞格)偏远新疆西藏海南内蒙甘肃宁夏青海（黄山淳安建德绩溪县）",
        "logistics_no": "************",
        "logistics_template_id": "0",
        "logistics_type": 95,
        "modified": "2023-03-30 12:10:16",
        "note_count": "0",
        "other_amount": "0.0000",
        "other_cost": "0.0000",
        "package_id": "0",
        "paid": "68.0000",
        "pay_account": "",
        "pay_time": "2023-03-20 00:56:07",
        "pi_amount": "0.0000",
        "platform_id": 1,
        "post_amount": "0.0000",
        "post_cost": "15.7300",
        "pre_charge_time": "",
        "print_remark": "",
        "profit": "-2.4700",
        "raw_goods_count": "1.0000",
        "raw_goods_type_count": 1,
        "receivable": "68.0000",
        "receiver_address": "",
        "receiver_area": "江苏省 南通市 通州区",
        "receiver_city": 320600,
        "receiver_country": "0",
        "receiver_district": 320612,
        "receiver_dtb": " ",
        "receiver_mobile": "",
        "receiver_name": "",
        "receiver_province": "320000",
        "receiver_ring": "叶**",
        "receiver_telno": "",
        "receiver_zip": "000000",
        "refund_status": 0,
        "remark_flag": 0,
        "reserve": "",
        "revert_reason": "0",
        "sales_score": "0",
        "salesman_id": 0,
        "sendbill_template_id": "0",
        "shop_id": "8",
        "shop_name": "誉斯嘉旗舰店",
        "shop_no": "008",
        "shop_platform_id": "1",
        "shop_remark": "",
        "single_spec_no": "JZ-01-椭圆-白",
        "split_from_trade_id": "0",
        "split_package_num": "0",
        "src_tids": "3268940115984170159",
        "stockout_no": "CK202303208223",
        "tax": "0.0000",
        "tax_rate": "0.0000",
        "to_deliver_time": "",
        "trade_from": 1,
        "trade_id": 9706139,
        "trade_mask": "131200",
        "trade_no": "JY202303200266",
        "trade_prepay": "0.0000",
        "trade_status": 110,
        "trade_time": "2023-03-20 00:56:01",
        "trade_type": 1,
        "unmerge_mask": "0",
        "version_id": 5,
        "volume": "42385.0000",
        "warehouse_id": "82",
        "warehouse_no": "77",
        "warehouse_type": 1,
        "weight": "9.5000"
      }
    ]
  }
}
'''

mock_wdtulti_trade = '''
{
    "response":{
        "data":{
            "order":[
                {
                    "bad_reason":0,
                    "buyer_message":"",
                    "cancel_reason":"无",
                    "check_time":"",
                    "checker_name":"系统",
                    "checkouter_name":"系统",
                    "cod_amount":"0.0000",
                    "commission":"0.0000",
                    "created":"1691462147000",
                    "cs_remark":"8月15日发货【晚香玉 08-08 10:37】",
                    "currency":"",
                    "customer_id":1741670,
                    "customer_no":"KH202206110808",
                    "customer_type":0,
                    "delay_to_time":"",
                    "delivery_term":1,
                    "detail_list":[
                        {
                            "actual_num":"1.0000",
                            "adjust":"0.0000",
                            "api_goods_id":"3410323272490217683",
                            "api_goods_name":"【会员周】云洁面慕斯组合氨基酸洗面奶温和清洁套组男女/官方",
                            "api_spec_id":"1705550344495163",
                            "api_spec_name":"150ml袋装+150ml瓶装",
                            "barcode":"6970647713233",
                            "bind_oid":"",
                            "cid":0,
                            "commission":"0.0000",
                            "created":"1691462146000",
                            "created_date":"2023-08-08 10:35:46",
                            "delivery_term":1,
                            "discount":"0.0000",
                            "flag":0,
                            "from_mask":0,
                            "gift_type":0,
                            "goods_id":1997,
                            "goods_name":"醒澈橙净云洁面慕斯",
                            "goods_no":"DSJMzz0001",
                            "goods_type":1,
                            "guarantee_mode":1,
                            "img_url":"",
                            "invoice_content":"",
                            "is_consigned":false,
                            "is_received":0,
                            "modified":"1691462146000",
                            "modified_date":"2023-08-08 10:35:46",
                            "num":"1.0000",
                            "order_price":"51.1292",
                            "paid":"51.1292",
                            "pay_status":2,
                            "platform_id":69,
                            "platform_status":30,
                            "price":"51.1292",
                            "print_suite_mode":0,
                            "prop1":"",
                            "prop2":"",
                            "rec_id":18576797,
                            "refund_num":"0.0000",
                            "refund_status":0,
                            "remark":"aa",
                            "share_amount":"51.1292",
                            "share_post_price":"0",
                            "share_price":"51.1292",
                            "spec_code":"",
                            "spec_id":1943,
                            "spec_name":"醒澈橙净云洁面慕斯150ml",
                            "spec_no":"DSJMzz0001",
                            "src_oid":"6920738534165189799",
                            "src_tid":"6920738534165189799",
                            "stock_state":3,
                            "suite_amount":"146.0000",
                            "suite_discount":"0.0000",
                            "suite_id":680,
                            "suite_name":"洁面正装+补充装*2",
                            "suite_no":"DYZS002",
                            "suite_num":"1.0000",
                            "tax_rate":"0.0000",
                            "trade_id":3991697,
                            "weight":"0.0000"
                        },
                        {
                            "actual_num":"2.0000",
                            "adjust":"0.0000",
                            "api_goods_id":"3410323272490217683",
                            "api_goods_name":"【会员周】云洁面慕斯组合氨基酸洗面奶温和清洁套组男女/官方",
                            "api_spec_id":"1705550344495163",
                            "api_spec_name":"150ml袋装+150ml瓶装",
                            "barcode":"6970647713240",
                            "bind_oid":"",
                            "cid":0,
                            "commission":"0.0000",
                            "created":"1691462146000",
                            "created_date":"2023-08-08 10:35:46",
                            "delivery_term":1,
                            "discount":"0.0000",
                            "flag":0,
                            "from_mask":0,
                            "gift_type":0,
                            "goods_id":1998,
                            "goods_name":"醒澈橙净云洁面慕斯",
                            "goods_no":"DSJMzz0002",
                            "goods_type":1,
                            "guarantee_mode":1,
                            "img_url":"",
                            "invoice_content":"",
                            "is_consigned":false,
                            "is_received":0,
                            "modified":"1691462146000",
                            "modified_date":"2023-08-08 10:35:46",
                            "num":"2.0000",
                            "order_price":"47.4354",
                            "paid":"94.8708",
                            "pay_status":2,
                            "platform_id":69,
                            "platform_status":30,
                            "price":"47.4354",
                            "print_suite_mode":0,
                            "prop1":"",
                            "prop2":"",
                            "rec_id":18576798,
                            "refund_num":"0.0000",
                            "refund_status":0,
                            "remark":"ddd",
                            "share_amount":"94.8708",
                            "share_post_price":"0",
                            "share_price":"47.4354",
                            "spec_code":"",
                            "spec_id":1944,
                            "spec_name":"醒澈橙净云洁面慕斯补充装150ml",
                            "spec_no":"DSJMzz0002",
                            "src_oid":"6920738534165189799",
                            "src_tid":"6920738534165189799",
                            "stock_state":3,
                            "suite_amount":"146.0000",
                            "suite_discount":"0.0000",
                            "suite_id":680,
                            "suite_name":"洁面正装+补充装*2",
                            "suite_no":"DYZS002",
                            "suite_num":"1.0000",
                            "tax_rate":"0.0000",
                            "trade_id":3991697,
                            "weight":"0.0000"
                        },
                        {
                            "actual_num":"1.0000",
                            "adjust":"0.0000",
                            "api_goods_id":"3596762355473765332",
                            "api_goods_name":"经典一号面膜1片",
                            "api_spec_id":"1756231616656430",
                            "api_spec_name":"默认",
                            "barcode":"6970647712816",
                            "bind_oid":"",
                            "cid":0,
                            "commission":"0.0000",
                            "created":"1691462146000",
                            "created_date":"2023-08-08 10:35:46",
                            "delivery_term":1,
                            "discount":"0.0000",
                            "flag":0,
                            "from_mask":0,
                            "gift_type":0,
                            "goods_id":1979,
                            "goods_name":"经典一号焕颜修护面膜",
                            "goods_no":"DSMM0001",
                            "goods_type":1,
                            "guarantee_mode":1,
                            "img_url":"",
                            "invoice_content":"",
                            "is_consigned":false,
                            "is_received":0,
                            "modified":"1691462146000",
                            "modified_date":"2023-08-08 10:35:46",
                            "num":"1.0000",
                            "order_price":"0.0000",
                            "paid":"0.0000",
                            "pay_status":2,
                            "platform_id":69,
                            "platform_status":30,
                            "price":"0.0000",
                            "print_suite_mode":0,
                            "prop1":"",
                            "prop2":"",
                            "rec_id":18576799,
                            "refund_num":"0.0000",
                            "refund_status":0,
                            "remark":"销售类型:自营；",
                            "share_amount":"0.0000",
                            "share_post_price":"0",
                            "share_price":"0.0000",
                            "spec_code":"",
                            "spec_id":1926,
                            "spec_name":"经典一号焕颜修护面膜(V2)23ml",
                            "spec_no":"DSMM0001",
                            "src_oid":"6920738534165255335",
                            "src_tid":"6920738534165189799",
                            "stock_state":3,
                            "suite_amount":"0.0000",
                            "suite_discount":"0.0000",
                            "suite_id":0,
                            "suite_name":"",
                            "suite_no":"",
                            "suite_num":"0.0000",
                            "tax_rate":"0.0000",
                            "trade_id":3991697,
                            "weight":"0.0000"
                        },
                        {
                            "actual_num":"1.0000",
                            "adjust":"0.0000",
                            "api_goods_id":"",
                            "api_goods_name":"抖音卡2#",
                            "api_spec_id":"",
                            "api_spec_name":"DY气味相投的朋友问候卡",
                            "barcode":"6970647713080",
                            "bind_oid":"",
                            "cid":0,
                            "commission":"0.0000",
                            "created":"1691462147000",
                            "created_date":"2023-08-08 10:35:47",
                            "delivery_term":1,
                            "discount":"0.0000",
                            "flag":0,
                            "from_mask":0,
                            "gift_type":1,
                            "goods_id":1952,
                            "goods_name":"抖音卡2#",
                            "goods_no":"DSZB0002",
                            "goods_type":1,
                            "guarantee_mode":2,
                            "img_url":"",
                            "is_consigned":false,
                            "is_received":0,
                            "modified":"1691462147000",
                            "modified_date":"2023-08-08 10:35:47",
                            "num":"1.0000",
                            "order_price":"0.0000",
                            "paid":"0.0000",
                            "platform_id":0,
                            "platform_status":0,
                            "price":"0.0000",
                            "print_suite_mode":0,
                            "prop1":"",
                            "prop2":"",
                            "rec_id":18576800,
                            "refund_num":"0.0000",
                            "refund_status":0,
                            "remark":"赠品策略编号:ZP22041108,规则名称:抖音卡片，原始单号：6920738534165189799",
                            "share_amount":"0.0000",
                            "share_post_price":"0",
                            "share_price":"0.0000",
                            "spec_code":"",
                            "spec_id":1900,
                            "spec_name":"DY气味相投的朋友问候卡",
                            "spec_no":"DSZB0002",
                            "src_oid":"AD202308080639",
                            "src_tid":"6920738534165189799",
                            "stock_state":3,
                            "suite_amount":"0.0000",
                            "suite_discount":"0.0000",
                            "suite_id":0,
                            "suite_name":"",
                            "suite_no":"",
                            "suite_num":"0.0000",
                            "tax_rate":"0.0000",
                            "trade_id":3991697,
                            "weight":"0.0000"
                        }
                    ],
                    "discount":"0.0000",
                    "estimate_consign_time":"2023-08-15 23:59:59",
                    "ext_cod_fee":"0.0000",
                    "fchecker_name":"系统",
                    "fenxiao_nick":"【id:65718439477；DAISY SKY雏菊的天空官方旗舰店】",
                    "fenxiao_type":0,
                    "flag_name":"无",
                    "freeze_reason":"等通知发货",
                    "gift_mask":1,
                    "goods_amount":"146.0000",
                    "goods_cost":"33.5197",
                    "goods_count":"5.0000",
                    "goods_type_count":"4",
                    "id_card_type":0,
                    "invoice_content":"",
                    "invoice_id":0,
                    "invoice_title":"",
                    "invoice_type":0,
                    "is_sealed":false,
                    "large_type":0,
                    "logistics_code":"0007",
                    "logistics_id":43,
                    "logistics_name":"顺丰wms",
                    "logistics_no":"",
                    "logistics_type_name":"顺丰速运",
                    "modified":"2023-08-08 10:37:13",
                    "other_amount":"0.0000",
                    "other_cost":"0.0000",
                    "package_id":0,
                    "package_name":"未知",
                    "paid":"146.0000",
                    "pay_time":"2023-08-08 10:35:35",
                    "platform_id":69,
                    "post_amount":"0.0000",
                    "post_cost":"0.0000",
                    "print_remark":"",
                    "profit":"112.4803",
                    "raw_goods_count":"2.0000",
                    "raw_goods_type_count":2,
                    "receivable":"146.0000",
                    "receiver_area":"湖南省 长沙市 岳麓区",
                    "receiver_city":430100,
                    "receiver_district":430104,
                    "receiver_dtb":"长市 岳麓区",
                    "receiver_province":430000,
                    "receiver_ring":"",
                    "receiver_zip":"",
                    "refund_status":0,
                    "remark_flag":1,
                    "revert_reason":"无",
                    "salesman_name":"系统",
                    "shop_id":17,
                    "shop_name":"雏菊天空-抖音",
                    "shop_no":"0017",
                    "shop_platform_id":69,
                    "shop_remark":"",
                    "single_spec_no":"",
                    "src_tids":"6920738534165189799",
                    "stockout_no":"",
                    "sub_platform_id":0,
                    "tax":"0.0000",
                    "tax_rate":"0.0000",
                    "to_deliver_time":"",
                    "trade_from":1,
                    "trade_id":3991697,
                    "trade_label":"",
                    "trade_mask":0,
                    "trade_no":"JY202308080835",
                    "trade_status":"30",
                    "trade_time":"1691462132000",
                    "trade_type":1,
                    "version_id":1,
                    "warehouse_id":45,
                    "warehouse_no":"CK0042",
                    "warehouse_type":3,
                    "weight":"0.0000"
                }
            ],
            "total_count":1
        },
        "message":"OK",
        "status":0
    }
}
'''

@pytest.fixture
def mock_warehouse_query(mocker):
    p = mocker.patch("rpa.erp.wdt.WdtClient.warehouse_query")
    data = {
        "response": {
            "errorcode": 0,
            "message": "ok",
            "total_count": 1,
            "warehouses": [
                {
                    "address": "",
                    "api_key": "",
                    "api_object_id": "",
                    "city": "无",
                    "cod_logistics_id": "0",
                    "contact": "",
                    "coordinates_x": "0.000000",
                    "coordinates_y": "0.000000",
                    "created": "2018-09-12 16:40:23",
                    "district": "无",
                    "division_id": "",
                    "ext_warehouse_no": "",
                    "flag": "0",
                    "is_defect": 0,
                    "is_disabled": 0,
                    "is_outer_goods": "0",
                    "is_outer_stock": "0",
                    "match_warehouse_id": "0",
                    "mobile": "",
                    "modified": "2023-03-07 09:34:19",
                    "name": "顺果",
                    "picker_num": "0",
                    "priority": "57",
                    "prop1": "",
                    "prop2": "",
                    "province": "",
                    "remark": "",
                    "shop_id": "0",
                    "sub_type": "0",
                    "tag": "1536741623",
                    "telno": "",
                    "type": 1,
                    "warehouse_id": "43",
                    "warehouse_no": "0-31",
                    "warehouse_type": "1",
                    "zip": ""
                }
            ]
        }
    }
    p.return_value = WarehouseQueryResp(**data)


def test_wdt_process(mocker, client, mock_full_job, mock_job_args,
                     mock_erp_type, mock_job_states,
                     ignore_token_bucket_rate_limit, mock_warehouse_query):
    mock_erp_type(ErpType.WDT)
    data = {
        "tid": [{"tid": "1q23e", "oid": "1"}],
        "sku_id": [{"sku": "4644774856896", "spu": "640249484811",
                    "quantity": "1", "outer_sku": "YH-BX38A119-Y",
                    "price": 1.0}],
        "amount": 1.0,
        "question_type": ["其他"],
        "qty": 1,
        "address": {
            "name": "test",
            "mobile": "13520198123",
            "address": "asf",
            "state": "北京市",
            "city": "北京市市辖区",
            "zone": "东城区",
            "district": "东城区",
            "town": "西镇"
        },
        "remark": "remark",
        "logistics_type": "aaa",
        "warehouse_name": "顺果",
    }
    mock_job_args(**data)
    mocker.patch("robot_processor.client.buyer_client.get_buyer_nick_by_tid",
                 return_value="buyer")
    mocker.patch("rpa.erp.wdt.WdtClient.trade_push")
    with pytest.raises(Retry):
        AfterSaleUploadExecutor(mock_full_job).process()
    mocker.patch("robot_processor.job.after_sale_upload.WdtClient.trade_query",
                 return_value=TradeQueryResp(
                     **json.loads(mock_wdt_trade)))
    status, _ = AfterSaleUploadExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
    assert mock_job_states.write_job_output.call_args.args[0][
        'wdt_tid'].startswith("1q23e")


def test_wdtulti_process(mocker, client, mock_full_job, mock_job_args,
                         mock_erp_type, mock_job_states,
                         ignore_token_bucket_rate_limit):
    mock_erp_type(ErpType.WDTULTI)
    data = {
        "tid": [{"tid": "2063354177360590000", "oid": "88888888"}],
        "wdt_tid_uuid": "2063354177360590000BFabc1",
        "sku_id": [{"spu_name": "百星计划-品宣折页	", "sku": "11000137",
                    "spu": "11000137", "quantity": "1",
                    "outer_sku": "11000137", "price": 1}
                   ],
        "amount": 1,
        "question_type": ["其他"],
        "qty": 1,
        "address": {
            "name": "buyer",
            "mobile": "18888888888",
            "address": "华春一寸",
            "state": "北京市",
            "city": "北京市市辖区",
            "zone": "东城区",
            "town": "西镇"
        },
        "remark": "测试勿发",
        "logistics_type": "aaa"
    }
    mock_job_args(**data)
    mocker.patch("robot_processor.client.buyer_client.get_buyer_nick_by_tid",
                 return_value="buyer")
    mocker.patch("rpa.erp.wdtulti.WdtUltiClient.trade_push")
    mocker.patch("rpa.erp.wdtulti.WdtUltiClient.trade_query",
                 return_value={"response": {"status": 0,
                                            "data": {"total_count": 1,
                                                     "order": [{
                                                         "trade_no": "123"}]}}})
    mocker.patch("robot_processor.job.after_sale_upload.WdtUltiQM.get_orders",
                 return_value=WdtultiOrderResp(
                     **json.loads(mock_wdtulti_trade)["response"]["data"]))
    with pytest.raises(Retry):
        AfterSaleUploadExecutor(mock_full_job).process()
    status, msg = AfterSaleUploadExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
    assert mock_job_states.write_job_output.call_args.args[0][
        'wdt_tid'].startswith("2063354177360590000")
    assert mock_job_states.write_job_output.call_args.args[0][
        'trade_no'].startswith("123")

    data.update({
        "sku_id": [{"spu_name": "百星计划-品宣折页	", "SKU": "11000137",
                    "SPU": "11000137", "REISSUE_QUANTITY": "1",
                    "SKU_OUTER": "11000137"}
                   ]
    })
    mock_job_states.write_job_states({}, True)
    mock_job_args(**data)
    mocker.patch("robot_processor.client.buyer_client.get_buyer_nick_by_tid",
                 return_value="buyer")
    mocker.patch("rpa.erp.wdtulti.WdtUltiClient.trade_push")
    mocker.patch("rpa.erp.wdtulti.WdtUltiClient.trade_query",
                 return_value={"response": {"status": 0,
                                            "data": {"total_count": 1,
                                                     "order": [{
                                                         "trade_no": "123"}]}}})
    mocker.patch("robot_processor.job.after_sale_upload.WdtUltiQM.get_orders",
                 return_value=WdtultiOrderResp(
                     **json.loads(mock_wdtulti_trade)["response"]["data"]))
    with pytest.raises(Retry):
        AfterSaleUploadExecutor(mock_full_job).process()
    status, msg = AfterSaleUploadExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
    assert mock_job_states.write_job_output.call_args.args[0][
        'wdt_tid'].startswith("2063354177360590000")
    assert mock_job_states.write_job_output.call_args.args[0][
        'trade_no'].startswith("123")


def test_kuaimai_process(mocker, client, mock_full_job, mock_job_args,
                         mock_erp_type, mock_job_states):
    mock_erp_type(ErpType.KUAIMAI)
    data = {
        "tid": [{"tid": "66666", "oid": "88888"}],
        "warehouse": "ck",
        "sku_id": [
            {
                "sku": "4644774856896",
                "spu": "XHZ-9012D",
                "quantity": "1",
                "outer_sku": "YH-BX38A119-Y",
            },
            {
                "sku": "4644774856897",
                "spu": "XHZ-9012D",
                "quantity": "3",
                "outer_sku": "YH-BX38A119-Z",
            }
        ],
    }
    mock_job_args(**data)
    mocker.patch("rpa.erp.kuaimai.KuaimaiClient.after_sale_upload",
                 return_value=Ok('3561898630753227'))
    status, msg = AfterSaleUploadExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED


def test_baisheng_process(mocker, client, mock_full_job, mock_job_args,
                          mock_erp_type, mock_job_states):
    mock_erp_type(ErpType.BAISHENG)
    data = {
        "tid": [{"tid": "66666666", "oid": "8888"}],
        "sku_id": [{"spu_name": "04", "sku": "4644774856896",
                    "spu": "XHZ-9012D", "quantity": "1",
                    "outer_sku": "YH-BX38A119-Y", "price": 1},
                   {"spu_name": "04", "sku": "4644774856897",
                    "spu": "XHZ-9012D", "quantity": "3",
                    "outer_sku": "YH-BX38A119-Z", "price": 1}
                   ],
        "address": {
            "name": "test",
            "mobile": "13520198123",
            "address": "asf",
            "state": "北京市",
            "city": "北京市市辖区",
            "zone": "东城区"
        },
        "amount": 1,
        "qty": 1
    }
    mock_job_args(**data)
    mocker.patch(
        "rpa.erp.baisheng.BaiShengClient.after_sale_upload",
        return_value=Ok(None)
    )
    status, msg = AfterSaleUploadExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED


def test_wanliniu_process(mocker, client, mock_full_job, mock_job_args,
                          mock_erp_type, mock_job_states):
    mock_erp_type(ErpType.WANLINIU)
    data = {
        "tid": [{"tid": "666"}],
        "buyer": "test_buyer",
        "store": "[淘宝]橙子家居",
        "sku_id": [
            {
                "sku": "4644774856896",
                "spu": "XHZ-9012D",
                "quantity": "1",
                "outer_sku": "YH-BX38A119-Y",
            },
            {
                "sku": "4644774856897",
                "spu": "XHZ-9012D",
                "quantity": "3",
                "outer_sku": "YH-BX38A119-Z",
            }
        ],
        "address": {
            "state": "上海市",
            "city": "上海市",
            "zone": "长宁区",
            "address": "长宁路1号",
            "name": "小熊",
            "mobile": "123",
        }
    }
    mock_job_args(**data)
    mocker.patch("rpa.erp.wanliniu.WanliniuClient.after_sale_upload",
                 return_value=Ok("1"))
    status, _ = AfterSaleUploadExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED


def test_duohong_process(mocker, client, mock_full_job, mock_job_args,
                         mock_erp_type, mock_job_states):
    mock_erp_type(ErpType.DUOHONG)
    data = {
        "tid": [{"tid": "66666666"}],
        "buyer": "test_buyer",
        "sku_id": [
            {'spu': '',
             'sku': '510175-直边撞色玻璃壳-苹果11-奶奶灰[0]-灰棕紫撞色CB',
             'outer_sku': '510175-直边撞色玻璃壳-苹果11-奶奶灰[0]-灰棕紫撞色CB',
             'quantity': '2',
             'price': 0},
            {'spu': '', 'sku': 'ZYTSNHPG11+WOM女孩黑字CB',
             'outer_sku': 'ZYTSNHPG11+WOM女孩黑字CB', 'quantity': '3',
             'price': 0}
        ],
        "address": {
            "state": "上海市",
            "city": "上海市",
            "zone": "长宁区",
            "address": "长宁路1号",
            "name": "小熊",
            "mobile": "123",
        }
    }
    mock_job_args(**data)
    mocker.patch(
        "rpa.erp.duohong.DuohongClient.after_sale_upload",
        return_value=Ok("1516575145066781075_469936")
    )
    status, msg = AfterSaleUploadExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED


def test_duohong_process_replicate(mocker, client, mock_full_job, mock_job_args,
                                   mock_erp_type, mock_job_states):
    mock_erp_type(ErpType.DUOHONG)
    data = {
        "tid": [{"tid": "66666666"}],
        "buyer": "test_buyer",
        "sku_id": {
            'after_sales_type': 'original',
            'sku_list': [
                {'spu': '',
                 'sku': '510175-直边撞色玻璃壳-苹果11-奶奶灰[0]-灰棕紫撞色CB',
                 'outer_sku': '510175-直边撞色玻璃壳-苹果11-奶奶灰[0]-灰棕紫撞色CB',
                 'quantity': '2', 'price': 0},
                {'spu': '',
                 'sku': 'ZYTSNHPG11+WOM女孩黑字CB',
                 'outer_sku': 'ZYTSNHPG11+WOM女孩黑字CB',
                 'quantity': '3',
                 'price': 0}
            ],
        },
        "address": {
            "state": "上海市",
            "city": "上海市",
            "zone": "长宁区",
            "address": "长宁路1号",
            "name": "小熊",
            "mobile": "123",
        }
    }
    mock_job_args(**data)
    mocker.patch(
        "rpa.erp.duohong.DuohongClient.after_sale_upload_replicate",
        return_value=Ok("1516575145066781075_469936"))
    status, msg = AfterSaleUploadExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED


def test_gyy_process(mocker, client, mock_full_job, mock_job_args,
                     mock_erp_type, mock_job_states):
    mock_erp_type(ErpType.GUANYIYUN)
    data = {
        "tid": [{"tid": "1589358686359781075", "oid": "1589358686359781075"}],
        "sku_id": [{"spu_name": "04", "sku": "T100252",
                    "spu": "T100252", "quantity": "1",
                    "outer_sku": "YH-BX38A119-Y", "price": 1}
                   ],
        "receiverInfo": {
            "name": "test",
            "mobile": "13520198123",
            "address": "asf",
            "state": "北京市",
            "city": "北京市市辖区",
            "zone": "东城区"
        },
        "extendMemo": [{"label": "是", "value": "是"}],
        "qty": 1,
        "is_confirm": 1,
        "is_benefit": [{"label": "是", "value": "是"}],
        "warehouseName": [],
        "orderTypeName": [],
        "logisticsName": [{"label": "天天快递-疫情", "value": "天天快递-疫情"}],
    }
    mock_job_args(**data)
    mocker.patch(
        "rpa.erp.guanyiyun.GuanyiyunClient.after_sale_upload",
        return_value=Ok("123")
    )
    mocker.patch(
        "rpa.erp.guanyiyun.GuanyiyunClient.after_sale_approve",
        return_value=Ok("123")
    )
    status, msg = AfterSaleUploadExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
