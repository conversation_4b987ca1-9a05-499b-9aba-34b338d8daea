from _decimal import Decimal

import arrow

from robot_processor.enums import JobStatus
from robot_processor.job.doudian_trade_info import (DoudianTradeInfoExecutor,
                                                    DoudianTradeInfoOutput,
                                                    DoudianLogistics,
                                                    DoudianSubOrder)


def test_doudian_trade_info(mocker, mock_random_full_job, mock_job_args, mock_job_states):
    mock_job_args(
        tid=[{"tid": "123123"}]
    )
    mock_random_full_job.shop.platform = "DOUDIAN"
    mock_order = {
        "order_id": "123",
        "order_status": 3,
        "create_time": arrow.Arrow(2023, 11, 11, 11, 11, 11, tzinfo="Asia/Shanghai").int_timestamp,
        "pay_time": arrow.Arrow(2023, 11, 11, 11, 11, 11, tzinfo="Asia/Shanghai").int_timestamp,
        "pay_amount": 1234,
        "post_addr": {
            "city": {
                "id": "1",
                "name": "city"
            },
            "province": {
                "id": "2",
                "name": "province"
            },
            "street": {
                "id": "3",
                "name": "street"
            },
            "town": {
                "id": "4",
                "name": "town"
            },
            "encrypt_detail": "encrypted_detail"
        },
        "encrypt_post_tel": "encrypt_post_tel",
        "encrypt_post_receiver": "encrypt_post_receiver",
        "seller_words": "xx",
        "seller_remark_stars": 0,
        "sku_order_list": [{
            "product_id": 1,
            "sku_id": 2,
            "goods_price": 1234,
            "product_name": "product_name",
            "out_product_id": "out_product_id",
            "out_sku_id": "out_sku_id",
            "code": "code",
            "spec": [{"name": "a", "value": "a"}, {"name": "b", "value": "b"}],
            "item_num": 1,
        }],
        "logistics_info": [{
            "tracking_no": "tracking_no",
            "company": "company",
            "ship_time": arrow.Arrow(2023, 11, 11, 11, 11, 11, tzinfo="Asia/Shanghai").int_timestamp,
            "delivery_id": 1,
            "company_name": "company_name"
        }]
    }
    mocker.patch("robot_processor.client.doudian_cloud._request",
                 return_value={
                     "decrypted_data": mock_order
                 })
    expected_output = DoudianTradeInfoOutput(
        logistics_info=[
            DoudianLogistics(
                company_name="company_name",
                tracking_no="tracking_no"
            )
        ],
        pay_time="2023-11-11 11:11:11",
        create_time="2023-11-11 11:11:11",
        sub_orders=[DoudianSubOrder(
            product_name="product_name",
            product_id=1,
            out_product_id="out_product_id",
            out_sku_id="out_sku_id",
            goods_price=Decimal("12.34").quantize(Decimal(".01")),
            sku_id=2,
            code="code",
            spec_str="a;b",
            num=1
        )],
        pay_amount=Decimal("12.34").quantize(Decimal(".01")),
        order_status_str="已发货",
        seller_words="xx",
        first_company_name="company_name",
        first_tracking_no="tracking_no",
        first_product_id=1,
        first_product_name="product_name",
        first_code="code",
        first_sku_id=2,
        first_spec_str="a;b",
        seller_remark_stars_str="灰",
        desensitization_address="province city town street"
    )
    status, error = DoudianTradeInfoExecutor(mock_random_full_job).process()
    assert status == JobStatus.SUCCEED, error
    mock_job_states.write_job_widget_collection.assert_called_with(expected_output.dict())
