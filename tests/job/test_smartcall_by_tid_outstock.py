from typing import NamedTuple
from pytest import fixture, mark

from robot_processor.form.models import Step
from robot_processor.business_order.models import BusinessOrder, Job
from robot_processor.enums import DataType, JobStatus
from robot_processor.job.smartcall_by_tid import SmartCallByTidExecutor


@fixture
def register_rpa(client, db):
    from robot_processor.rpa_service.models import Rpa

    rpa = Rpa()
    db.session.add(rpa)
    # 与 tests/job/resources/smartcall.job-arguments.json /steps/1/rpa 保持一致
    rpa.name = "智能外呼-通过订单号-JobArguments"
    rpa.argument = {
        "input": [
            {
                "desc": "通过订单号来外呼",
                "name": "tid",
                "label": "订单号(输入)",
                "limits": [
                    {
                        "mode": "child",
                        "widget_type": "order"
                    },
                    {
                        "mode": "order",
                        "widget_type": "order"
                    }
                ],
                "required": True,
                "data_type": DataType.SELECT.value,
                "default_value": "",
            },
            {
                "desc": "需要外呼的场景唯一识别(自动获取无需修改)",
                "name": "graph_main_id",
                "label": "外呼场景（输入）",
                "limits": None,
                "required": True,
                "data_type": DataType.AUTO.value,
                "default_value": "前世有约-无尺寸",
            },
            {
                "name": "店铺名称",
                "label": "店铺名称",
                "limits": [
                    {"mode": "sku", "widget_type": "product"},
                    {"mode": "", "widget_type": "textarea"},
                    {"mode": "", "widget_type": "string"},
                    {"mode": "single", "widget_type": "select"},
                    {"mode": "", "widget_type": "number"},
                ],
                "required": True,
                "data_type": DataType.SELECT.value,
                "default_value": None,
            }
        ],
        "output": [
            {
                "desc": "外呼结果",
                "name": "user_intention",
                "label": "外呼结果(输出)",
                "limits": [
                    {"mode": "", "widget_type": "textarea"},
                    {"mode": "", "widget_type": "string"},
                ],
                "required": True,
                "data_type": DataType.SELECT.value,
                "default_value": "",
            }
        ],
    }


@fixture
def setup_shop_smartcall_info(client, db):
    from robot_processor.shop.models import SmartCallInfo

    config = SmartCallInfo()
    db.session.add(config)
    config.meta = {
        "company_id": "1234",
        "app_key": "app_key",
        "app_secret": "app_secret",
    }
    config.shop = client.shop
    yield


@fixture
def testbed(register_rpa, client):
    from tests.testbed import BusinessOrderBuilder

    testbed = BusinessOrderBuilder(
        {
            "form_template": "smartcall_by_tid.job-arguments",
            "name": "测试 JobArguments",
            "creator_user_id": 321,
            "creator_type": "LEYAN",
            "aid": "ut",
            "current_job": "外呼",
            "jobs": [
                {"name": "填写表单", "assignee": "ut"},
                {"name": "外呼", "status": JobStatus.INIT},
            ],
            "data": {
                "tid": "3347115014283095003",
                "1a725b2d-eaf3-4c88-aeea-5db68886ddb6": "唐狮官方旗舰店",
                "27c33af4-606c-42f0-a7cb-a2a00543c84c": "无明显意向",
                "34b0d25e-3c03-425a-83ee-8b3d739699fd": "是",
                "71c40def-cf95-4f80-81fa-4ceaddf4dc20": "拍下非预售未发货的商品",
                "98ad62c8-694f-4f06-a583-d7caa7e0be2a": "627220093106A90105",
                "b033f130-460a-476e-938f-fff978e501d3": "没货了",
                "b806315e-a2e3-42f5-93e1-ffb9403ee789": [
                    {"tid": "3347115014283095003"}
                ],
                "c2377f57-a43f-4a8b-bfb3-b44c5ebc1f95": None,
                "cb5d41ac-fe23-4118-842c-a116346066e6": "闹闹爱米粒儿",
            },
        },
        client.shop,
        "tests.job.resources",
    )
    testbed.build()
    step = testbed.form.steps.filter_by(name="外呼").first()
    job = testbed.business_order.jobs.filter_by(step=step).first()

    yield NamedTuple("Testbed", bo=BusinessOrder, job=Job, step=Step)(
        testbed.business_order, job, step
    )


@mark.usefixtures("setup_shop_smartcall_info")
def test_params_extracting(testbed, requests_mock):
    patcher = requests_mock.post(
        "/smartcall-backend/openapi/put_flow_call",
        json={
            "tradeId": "3347115014283095003",
            "platform": "TAOBAO",
            "storeId": "1234",
            "graph_main_id": "前世有约-无尺寸",
            "param_map": {
                "business_order_id": testbed.bo.id,
                "step_id": testbed.step.id,
                "user_intention": "无明显意向",
                "商品名称": "拍下非预售未发货的商品",
                "缺货原因": "没货了",
                "店铺名称": "唐狮官方旗舰店",
                "是否分机号": "是",
                "reason": "没货了",
                "product": "拍下非预售未发货的商品",
                "物流状态": None,
            },
        },
    )
    executor = SmartCallByTidExecutor(testbed.job)
    executor.process()
    assert len(patcher.request_history) == 1
    payload = patcher.last_request.json()
    assert payload["tradeId"] == "3347115014283095003"
    assert payload["companyId"] == "1234"
    assert payload["appKey"] == "app_key"
    assert payload["paramMap"] == {
        "business_order_id": testbed.bo.id,
        "step_id": testbed.step.id,
        "user_intention": "无明显意向",
        "商品名称": "拍下非预售未发货的商品",
        "缺货原因": "没货了",
        "店铺名称": "唐狮官方旗舰店",
        "是否分机号": "是",
        "reason": "没货了",
        "product": "拍下非预售未发货的商品",
        "物流状态": None,
    }
