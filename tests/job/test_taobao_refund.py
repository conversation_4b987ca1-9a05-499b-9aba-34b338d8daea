from result import Err
from result import Ok

from robot_processor.enums import JobStatus
from robot_processor.job.taobao_refund import DepositRefundRPAExecutor
from robot_processor.job.taobao_refund import RefundRPAExecutor


class TestRefundRPAExecutor:
    def test_success(self, mocker, client, mock_full_job, mock_job_args):
        mock_job_args(**{"tid": [{"tid": "1001"}]})
        executor = RefundRPAExecutor(mock_full_job)
        mocker.patch(
            "rpa.mola.MolaClient.call_namespace_method",
            return_value=Ok(
                {
                    "success": True,
                    "message": "调用服务成功",
                    "code": 200,
                    "request": {},
                    "context": {},
                    "version": "0.10.4-test231052",
                    "result": {},
                }
            ),
        )
        status, _ = executor.process()
        assert status == JobStatus.SUCCEED

    def test_not_found_tid(self, mocker, client, mock_full_job, mock_job_args):
        executor = RefundRPAExecutor(mock_full_job)
        status, e = executor.process()
        assert status == JobStatus.FAILED
        assert e == "未获取到订单号"

    def test_mola_timeout(self, mocker, client, mock_full_job, mock_job_args):
        mock_job_args(**{"tid": [{"tid": "1001"}]})
        executor = RefundRPAExecutor(mock_full_job)
        mocker.patch("rpa.mola.MolaClient.call_namespace_method", return_value=Err("request mola timeout"))
        status, e = executor.process()
        assert status == JobStatus.FAILED
        assert e == "request mola timeout"


class TestDepositRefundRPAExecutor:
    def test_success(self, mocker, client, mock_full_job, mock_job_args, grant_record_factory):
        grant_record_factory.create(
            access_token="114514",
            shop_id=mock_full_job.shop.id,
        )
        mock_job_args(**{"tid": [{"tid": "1001"}]})
        executor = DepositRefundRPAExecutor(mock_full_job)
        mocker.patch(
            "robot_processor.client.taobao_client.trade_support_refund_open",
            return_value=Ok(
                {"tid": "1001", "oid": "1001", "operation_time": "2025-04-01 12:00:00", "remark": "订单已经打开过退定金入口"}
            ),
        )
        status, _ = executor.process()
        assert status == JobStatus.SUCCEED

    def test_not_found_grant_record(self, mocker, client, mock_full_job, mock_job_args):
        executor = DepositRefundRPAExecutor(mock_full_job)
        status, e = executor.process()
        assert status == JobStatus.FAILED
        assert e == "未查询到店铺授权信息"

    def test_timeout(self, mocker, client, mock_full_job, mock_job_args, grant_record_factory):
        grant_record_factory.create(
            access_token="114514",
            shop_id=mock_full_job.shop.id,
        )
        mock_job_args(**{"tid": [{"tid": "1001"}]})
        executor = DepositRefundRPAExecutor(mock_full_job)
        mocker.patch(
            "robot_processor.client.taobao_client.trade_support_refund_open", return_value=Err("request timeout")
        )
        status, e = executor.process()
        assert status == JobStatus.FAILED
        assert e == "request timeout"
