from robot_processor.business_order.models import JobSystemAttributes
from robot_processor.utils import get_nonce


def test_should_replace_temp():
    step_uuid = get_nonce()
    assert (
        JobSystemAttributes.replace_temp_symbol_name("system_job_temp_success", step_uuid)
        == f"system_job_{step_uuid}_success"
    )
    assert (
        JobSystemAttributes.replace_temp_symbol_name("system_job_temp_reason", step_uuid)
        == f"system_job_{step_uuid}_reason"
    )
    assert (
        JobSystemAttributes.replace_temp_symbol_name("system_job_temp_suggestion", step_uuid)
        == f"system_job_{step_uuid}_suggestion"
    )
    assert (
        JobSystemAttributes.replace_temp_symbol_name("system_job_temp_exception_info", step_uuid)
        == f"system_job_{step_uuid}_exception_info"
    )


def test_should_do_nothing():
    step_uuid = get_nonce()
    key = get_nonce()
    assert JobSystemAttributes.replace_temp_symbol_name(key, step_uuid) == key
