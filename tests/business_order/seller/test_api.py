from unittest.mock import PropertyMock

import pytest
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.assistant.schema import AssistantV2
from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.business_order.exception_rule import ExceptionBusinessOrder
from robot_processor.business_order.fsm import constants
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import JobPool
from robot_processor.business_order.seller.enums import MyBusinessOrdersTab
from robot_processor.currents import g
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import Creator
from robot_processor.enums import JobP<PERSON>cessMark
from robot_processor.enums import JobStatus
from robot_processor.enums import PaymentMethod
from robot_processor.enums import SelectType
from robot_processor.enums import StepType
from robot_processor.ext import db
from robot_processor.form.models import Widget
from robot_processor.form.models import WidgetInfo
from robot_processor.users.models import LeyanUser


class TestSellerApi:
    @pytest.fixture
    def mock_data(self, client, mock_form, business_order_factory, mock_step, job_factory):
        mock_form.subscribe(client.shop, True)
        mock_form.update_user = "nick"
        mock_step.form_id = mock_form.id
        mock_step.data = {}
        bo_1, bo_2 = business_order_factory.create_batch(2)
        assert isinstance(bo_1, BusinessOrder)
        bo_1.aid = "nick"
        bo_1.set_creator_info(client.assistant)
        bo_1.data = {"oid": "1234", "tid": "5678"}
        bo_1.uid = "abcdefghijklmn"
        job_1_1 = job_factory.create2(mock_step, bo_1, status=JobStatus.PENDING)
        job_1_1.set_assignee_assistant(client.assistant)
        job_1_2 = job_factory.create2(mock_step, bo_1)
        assert isinstance(job_1_1, Job)
        bo_1.job_history = [job_1_1.id, job_1_2.id]

        bo_1.sid = client.sid
        bo_1.form_id = mock_form.id
        bo_1.updated_at = 2
        job_2_1 = job_factory.create2(mock_step, bo_2, status=JobStatus.SUCCEED)
        job_2_1.set_assignee_assistant(client.assistant)
        job_2_2 = job_factory.create2(mock_step, bo_2, status=JobStatus.PENDING)
        job_2_2.set_assignee_assistant(client.assistant)
        bo_2.sid = client.sid
        bo_2.form_id = mock_form.id
        bo_2.updated_at = 1
        bo_2.uid = "buyer_nick"
        bo_2.job_history = [job_2_1.id, job_2_2.id]
        bo_2.current_job_id = job_2_2.id
        bo_1.current_job_id = job_1_1.id
        bo_2.shop = client.shop
        db.session.add(bo_2)
        db.session.commit()
        return bo_1, bo_2

    def test_get_order_forms(self, mocker, client, mock_form, mock_step, mock_job, mock_business_order):
        mocker.patch("robot_processor.business_order.models.Job.job_name")
        mocker.patch("robot_processor.business_order.models.Job.task_type")

        mock_business_order.form_id = mock_form.id
        mock_business_order.sid = client.shop.sid
        mock_business_order.aid = "123"
        mock_business_order.status = BusinessOrderStatus.RUNNING
        mock_business_order.creator_user_id = 123
        mock_step.form_id = mock_form.id
        mock_step.step_type = mock_step.step_type.human
        mock_step.assistants_v2["select_type"] = SelectType.part
        flag_modified(mock_step, "assistants_v2")
        db.session.commit()
        mock_step.update_raw_step()
        mock_job.step_id = mock_step.id
        mock_job.step_uuid = mock_step.step_uuid
        mock_job.assignee_user_id = client.assistant.user_id
        mock_job.assignee_type = client.assistant.user_type
        mock_job.status = JobStatus.PENDING
        mock_business_order.jobs.append(mock_job)
        mock_form.subscribe(client.shop, True)
        db.session.commit()
        resp = client.post("/v1/seller/accessors/forms")
        assert resp.status_code == 200
        assert len(resp.json["forms"]) == 0

    def test_get_list(self, client, mock_data):
        bo_1, bo_2 = mock_data

        # 非法的tab
        resp = client.post(
            "/v1/seller/business_orders", json={"tab": "xxxx", "buyer_nick": "buyer_nick", "sort": "desc"}
        )
        assert resp.status_code == 400

        # 没有buyer nick
        resp = client.post("/v1/seller/business_orders", json={"tab": "unprocessed", "sort": "desc"})
        assert resp.status_code == 200
        assert len(resp.json["data"]) == 2
        assert resp.json["total"] == 1000  # api degrade
        assert resp.json["data"][0]["id"] == bo_1.id
        assert resp.json["data"][1]["id"] == bo_2.id

        # 过滤关闭的工单
        bo_2.status = BusinessOrderStatus.CLOSE
        resp = client.post(
            "/v1/seller/business_orders",
            json={"tab": "ALL", "buyer_nick": "buyer_nick", "sort": "DESC", "filter_close": True},
        )
        assert resp.status_code == 200
        assert len(resp.json["data"]) == 1

        resp = client.post(
            "/v1/seller/business_orders",
            json={"tab": "UNPROCESSED", "buyer_nick": "buyer_nick", "sort": "TIMEOUT", "filter_close": True},
        )
        assert resp.status_code == 200
        assert len(resp.json["data"]) == 1
        assert resp.json["timeout"]["unset_count"] == 1

    def test_get_detail(
        self,
        mocker,
        client,
        mock_form,
        mock_business_order,
        mock_step,
        step_factory,
    ):
        mock_business_order.sid = client.shop.sid
        step1, step2 = step_factory.create_batch(
            2,
            form_id=mock_form.id,
            step_type=StepType.human,
            can_retry=True,
            assistants_v2={
                "select_type": 2,
                "leyan_accounts": [],
                "channle_accounts": [
                    {
                        "user_id": client.assistant.user_id,
                        "user_nick": client.assistant.user_nick,
                    }
                ],
            },
        )
        step3 = step_factory.create(
            form_id=mock_form.id,
            step_type=StepType.jump,
        )
        step1.next_step_ids = [step2.step_uuid]
        step2.prev_step_ids = [step1.step_uuid]
        step2.next_step_ids = [step3.step_uuid]
        step3.prev_step_ids = [step2.step_uuid]
        version = mock_form.snapshot()
        mock_business_order.form_version_id = version.id
        # 注意：这里依赖了 init_bo_jobs 会继续创建 human step 的下个 step 的 job 这一实现细节
        mock_business_order.init_bo_jobs()
        assert mock_business_order.jobs.count() == 3
        job_1, job_2, job_3 = mock_business_order.jobs.order_by(Job.id).all()
        job_1.assignee = client.assistant.user_nick
        job_1.assignee_user_id = client.assistant.user_id
        job_1.assignee_type = Creator(client.assistant.user_type)
        job_1.set_status(JobStatus.PENDING)

        bo: BusinessOrder = mock_business_order
        bo.aid = client.assistant.user_nick
        bo.creator_type = client.assistant.user_type
        bo.creator_user_id = client.assistant.user_id
        bo.sid = client.shop.sid
        bo.job_history = [job_1.id, job_2.id, job_3.id, job_2.id]
        bo.form_id = mock_form.id
        bo.current_job_id = job_1.id
        resp = client.post(f"/v1/seller/business_orders/{bo.id}")
        assert resp.status_code == 200
        assert resp.json["data"]["id"] == bo.id
        assert resp.json["data"]["current_job"]["id"] == job_1.id
        assert resp.json["data"]["next_job"]["id"] == job_2.id
        assert resp.json["data"]["job_history"][2]["jump"] == {}

        step3.jump = {"target": step1.step_uuid}
        db.session.commit()

        resp = client.post(f"/v1/seller/business_orders/{bo.id}")
        assert resp.status_code == 200
        assert resp.json["data"]["job_history"][2]["jump"] == {"target": step1.step_uuid}

        job_1.set_status(JobStatus.SUCCEED)
        job_2.set_status(JobStatus.PENDING)
        bo.current_job_id = job_2.id
        resp = client.post(f"/v1/seller/business_orders/{bo.id}")
        assert resp.status_code == 200
        assert resp.json["data"]["current_job"]["id"] == job_2.id

        #  完成的工单只能撤回
        job_1.set_status(JobStatus.SUCCEED)
        job_2.set_status(JobStatus.SUCCEED)
        bo.status = BusinessOrderStatus.SUCCEED
        resp = client.post(f"/v1/seller/business_orders/{bo.id}")
        assert resp.status_code == 200
        assert resp.json["data"]["current_job"]["id"] == job_2.id

        # 待领取的工单只能进行领取和退回
        job_1.set_status(JobStatus.SUCCEED)
        job_2.set_status(JobStatus.SUCCEED)
        bo.status = BusinessOrderStatus.TO_BE_COLLECTED
        resp = client.post(f"/v1/seller/business_orders/{bo.id}")
        assert resp.status_code == 200

        # 异常中工单
        bo.status = BusinessOrderStatus.IN_EXCEPTION
        bo.current_job_id = job_2.id
        job_2.set_status(JobStatus.FAILED)
        in_processing_patch = mocker.patch(
            "robot_processor.business_order.exception_rule.ExceptionBusinessOrder.Queries.in_processing_by_job",
            autospec=ExceptionBusinessOrder.Queries.in_processing_by_job,
        )
        in_processing_patch.return_value = True
        resp = client.post(f"/v1/seller/business_orders/{bo.id}")
        assert resp.status_code == 200

    def test_get_buyer_orders_list(self, client, mock_data):
        bo_1, bo_2 = mock_data
        buyer_nick = "mini_app-test"
        bo_1.uid = buyer_nick
        bo_2.uid = buyer_nick
        bo_1.buyer_open_uid = "buyer-open-uid"
        bo_2.uid = buyer_nick
        bo_2.buyer_open_uid = "buyer-open-uid"

        bo_2.status = BusinessOrderStatus.SUCCEED
        db.session.commit()

        resp = client.post(
            "/v1/seller/buyer_business_orders",
            json={
                "status": [2, 4],
                "buyer_nick": buyer_nick,
                "form_id": bo_1.form_id,
                "buyer_open_uid": bo_1.buyer_open_uid,
            },
        )

        data = resp.json["data"]
        assert resp.status_code == 200
        assert len(data) == 2
        assert data[0]["status"] == BusinessOrderStatus.RUNNING.value
        assert data[1]["status"] == BusinessOrderStatus.SUCCEED.value


class TestMyBusinessOrderStat:
    @staticmethod
    def mock_data(form, business_order_factory, client, job_factory, step_factory):
        shop, assistant = client.shop, client.assistant
        form.subscribe(shop, True)
        bo_1, bo_2 = business_order_factory.create_batch(2)
        bo_1.aid = "nick"
        bo_1.set_creator_info(assistant)
        bo_1.data = {"oid": "1234"}
        bo_1.uid = "abcdefghijklmn"
        job_1_1, job_1_2 = job_factory.create_batch(2)
        job_1_1.set_assignee_assistant(assistant)
        job_1_1.status = JobStatus.PENDING
        bo_1.jobs.append(job_1_1)
        bo_1.jobs.append(job_1_2)
        bo_1.sid = shop.sid
        bo_1.form_id = form.id
        bo_1.current_job_id = job_1_1.id
        job_2_1, job_2_2 = job_factory.create_batch(2)
        job_2_1.set_assignee_assistant(assistant)
        job_2_1.status = JobStatus.SUCCEED
        job_2_2.set_assignee_assistant(assistant)
        job_2_2.status = JobStatus.PENDING
        bo_2.jobs.append(job_2_1)
        bo_2.jobs.append(job_2_2)
        bo_2.sid = shop.sid
        bo_2.form_id = form.id
        bo_2.current_job_id = job_2_2.id
        step_1_1, step_1_2 = step_factory.create_batch(2, step_type=StepType.human, form_id=form.id)
        job_1_1.step_id = step_1_1.id
        job_1_2.step_id = step_1_2.id
        step_2_1, step_2_2 = step_factory.create_batch(2, step_type=StepType.human, form_id=form.id)
        job_2_1.step_id = step_2_1.id
        job_2_2.step_id = step_2_2.id
        db.session.commit()
        return bo_1, bo_2

    def test_business_orders_stats(
        self,
        mocker,
        client,
        mock_form,
        business_order_factory,
        job_factory,
        step_factory,
        job_approver_factory,
    ):
        from flask import current_app

        current_app.config.update({"NEW_RELATE_RULE_SHOP_SIDS": [client.shop.sid]})

        bo_1, bo_2 = self.mock_data(mock_form, business_order_factory, client, job_factory, step_factory)

        resp = client.post("/v1/seller/business_orders/stats")
        assert resp.status_code == 200
        assert resp.json == {"ALL": 2, "CREATED": 1, "PROCESSED": 1, "UNPROCESSED": 2, "RELATE": 0, "TO_BE_APPROVE": 0}

        # 无结果的keyword
        BusinessManager.merge_tid_oid_uid_from_data(bo_1)
        resp = client.post("/v1/seller/business_orders/stats", json={"keyword": "fffffffffffffff"})
        assert resp.status_code == 200
        assert resp.json == {"ALL": 0, "CREATED": 0, "PROCESSED": 0, "UNPROCESSED": 0, "RELATE": 0, "TO_BE_APPROVE": 0}

        # 有订单keyword
        resp = client.post("/v1/seller/business_orders/stats", json={"keyword": "1234"})
        assert resp.status_code == 200
        assert resp.json == {"ALL": 1, "CREATED": 1, "PROCESSED": 0, "UNPROCESSED": 1, "RELATE": 0, "TO_BE_APPROVE": 0}

        # 有买家昵称keyword
        resp = client.post("/v1/seller/business_orders/stats", json={"keyword": "bcdefghijklmn"})
        assert resp.status_code == 200
        assert resp.json == {"ALL": 1, "CREATED": 1, "PROCESSED": 0, "UNPROCESSED": 1, "RELATE": 0, "TO_BE_APPROVE": 0}

        # 审批节点的情况
        bo_3 = business_order_factory.create(sid=client.shop.sid)
        bo_3.status = BusinessOrderStatus.PENDING
        bo_3.form_id = mock_form.id
        job_3 = job_factory.create(business_order_id=bo_3.id)
        job_3.status = JobStatus.PENDING
        bo_3.current_job_id = job_3.id
        step_3 = step_factory.create(step_type=StepType.approve, form_id=mock_form.id)
        job_3.step_id = step_3.id
        job_approver_factory.create(
            job_id=job_3.id,
            step_uuid=step_3.step_uuid,
            user_id=client.leyan_assistant.user_id,
        )
        db.session.commit()

        resp = client.post("/v1/seller/business_orders/stats")
        assert resp.status_code == 200
        assert resp.json == {"ALL": 2, "CREATED": 1, "PROCESSED": 1, "UNPROCESSED": 2, "RELATE": 0, "TO_BE_APPROVE": 1}

        # 候选人的情况
        bo_4 = business_order_factory.create(sid=client.shop.sid)
        bo_4.status = BusinessOrderStatus.PENDING
        bo_4.form_id = mock_form.id
        job_4 = job_factory.create(business_order_id=bo_4.id)
        job_4.status = JobStatus.PENDING
        bo_4.current_job_id = job_4.id
        step_4 = step_factory.create(step_type=StepType.human, form_id=mock_form.id)
        job_4.step_id = step_4.id
        job_4.assignee_user_id = client.another_leyan_assistant.user_id
        job_4.assignee_type = client.another_leyan_assistant.user_type
        db.session.commit()

        mocker.patch(
            "robot_processor.business_order.seller.query_by_accessor.AccessorQuery.accessible_form_ids",
            new_callable=PropertyMock(return_value=[mock_form.id]),
        )

        mocker.patch(
            "robot_processor.form.models.Step.get_assistants_v2", return_value=AssistantV2(select_type=SelectType.all)
        )

        resp = client.post("/v1/seller/business_orders/stats")
        assert resp.status_code == 200
        assert resp.json == {"ALL": 4, "CREATED": 1, "PROCESSED": 1, "UNPROCESSED": 2, "RELATE": 1, "TO_BE_APPROVE": 1}

        mocker.patch(
            "robot_processor.form.models.Step.get_assistants_v2", return_value=AssistantV2(select_type=SelectType.part)
        )
        mocker.patch(
            "robot_processor.users.services.AssistantService.filter_leyan_users_by_users_and_groups",
            return_value=[
                LeyanUser(
                    id=client.leyan_assistant.user_id,
                    nickname=client.leyan_assistant.user_nick,
                    phone=client.leyan_assistant.phone,
                    status=1,
                    locked=0,
                )
            ],
        )

        resp = client.post("/v1/seller/business_orders/stats")
        assert resp.status_code == 200
        assert resp.json == {"ALL": 4, "CREATED": 1, "PROCESSED": 1, "UNPROCESSED": 2, "RELATE": 1, "TO_BE_APPROVE": 1}

        mocker.patch(
            "robot_processor.users.services.AssistantService.filter_leyan_users_by_users_and_groups",
            return_value=[
                LeyanUser(
                    id=client.another_leyan_assistant.user_id,
                    nickname=client.another_leyan_assistant.user_nick,
                    phone=client.another_leyan_assistant.phone,
                    status=1,
                    locked=0,
                )
            ],
        )

        resp = client.post("/v1/seller/business_orders/stats")
        assert resp.status_code == 200
        assert resp.json == {"ALL": 4, "CREATED": 1, "PROCESSED": 1, "UNPROCESSED": 2, "RELATE": 0, "TO_BE_APPROVE": 1}

        current_app.config.update({"NEW_RELATE_RULE_SHOP_SIDS": []})
        mocker.patch(
            "robot_processor.form.models.Step.get_assistants_v2", return_value=AssistantV2(select_type=SelectType.all)
        )
        mocker.patch(
            "robot_processor.users.services.AssistantService.filter_leyan_users_by_users_and_groups",
            return_value=[
                LeyanUser(
                    id=client.leyan_assistant.user_id,
                    nickname=client.leyan_assistant.user_nick,
                    phone=client.leyan_assistant.phone,
                    status=1,
                    locked=0,
                )
            ],
        )

        resp = client.post("/v1/seller/business_orders/stats")
        assert resp.status_code == 200
        assert resp.json == {"ALL": 4, "CREATED": 1, "PROCESSED": 1, "UNPROCESSED": 2, "RELATE": 1, "TO_BE_APPROVE": 1}

    def test_task_center_bo_list(
        self, mocker, client, mock_form, business_order_factory, job_factory, step_factory, job_approver_factory
    ):
        bo_1, bo_2 = self.mock_data(mock_form, business_order_factory, client, job_factory, step_factory)
        # 审批节点的情况
        bo_3 = business_order_factory.create(sid=client.shop.sid)
        bo_3.status = BusinessOrderStatus.PENDING
        bo_3.form_id = mock_form.id
        job_3 = job_factory.create(business_order_id=bo_3.id)
        job_3.status = JobStatus.PENDING
        bo_3.current_job_id = job_3.id
        step_3 = step_factory.create(step_type=StepType.approve, form_id=mock_form.id)
        job_3.step_id = step_3.id
        job_approver_factory.create(
            job_id=job_3.id,
            step_uuid=step_3.step_uuid,
            user_id=client.leyan_assistant.user_id,
        )
        job_1 = bo_1.current_job
        step_1 = job_1.step
        step_1.step_type = StepType.approve
        job_1.set_assignee_assistant(None)
        db.session.commit()

        accessors = [
            {
                "sid": client.sid,
                "accessor_user_type": client.assistant.user_type,
                "accessor_user_id": client.assistant.user_id,
            }
        ]
        resp = client.post(
            "/v1/task-center/business-orders/list", json={"page_tab": "TO_BE_APPROVE", "accessors": accessors}
        )
        assert resp.status_code == 200
        assert resp.json.get("total") == 1


class TestFreePickApi:
    @pytest.fixture
    def mock_data(self, client, step_factory, mock_form, job_factory, business_order_factory):
        def create(picked: bool, shop=None, assistant=None):
            step = step_factory.create(
                form_id=mock_form.id, step_type=1, name="test", data={"rpa_id": 1}, is_dirty=False, assignee_rule=5
            )
            shop = shop or client.shop
            bo_status = BusinessOrderStatus.PENDING if picked else BusinessOrderStatus.TO_BE_COLLECTED
            bo = business_order_factory(uid="xxxxx ", form_id=mock_form.id, sid=shop.sid, aid="123", status=bo_status)
            job = job_factory(
                step_id=step.id, step_uuid=step.step_uuid, business_order_id=bo.id, status=JobStatus.PENDING
            )
            mock_form.subscribe(shop, True)
            bo.job_history = [job.id for job in bo.jobs]
            bo.job_road = [bo.job_history]
            bo.current_job_id = job.id
            assistant = assistant or client.leyan_assistant
            if picked:
                job.set_assignee_assistant(assistant)
            db.session.commit()
            JobPool.add_job(shop.sid, bo.id, job.id, [assistant])
            return bo, job

        return create

    @pytest.mark.parametrize("picked", [True, False])
    def test_free_picked_get_forms(self, mocker, client, picked, mock_data):
        # test picked
        mock_data(picked)
        body = {
            "tab": MyBusinessOrdersTab.FREE_PICK.name,
            "picked": picked,
        }

        resp = client.post("/v1/seller/accessors/forms", json=body)

        assert resp.status_code == 200
        assert len(resp.json["forms"]) == 1
        assert resp.json["forms"][0]["total"] == 1
        body["picked"] = not picked
        resp = client.post("/v1/seller/accessors/forms", json=body)
        assert resp.status_code == 200
        assert len(resp.json["forms"]) == 0

    @pytest.mark.parametrize("picked", [True, False])
    def test_get_free_pick_list(self, client, picked, mock_data):
        body = {
            "tab": MyBusinessOrdersTab.FREE_PICK.name,
            "picked": picked,
            "buyer_nick": "buyer_nick",
            "sort": "TIMEOUT",
            "keyword": None,
            "filter_close": True,
        }

        mock_data(picked)
        resp = client.post("/v1/seller/business_orders", json=body)
        assert resp.status_code == 200
        assert len(resp.json["data"]) == 1
        assert resp.json["total"] == 1000

    def test_free_pick_stats_picked(self, client, mock_data):
        mock_data(picked=True, assistant=client.leyan_assistant)
        resp = client.post("/v1/seller/business_orders/free_pick_stats")
        assert resp.status_code == 200
        assert resp.json == {"UNPICKED_COUNT": 0, "PICKED_COUNT": 1}

    def test_free_pick_stats_unpicked(self, client, mock_data):
        bo, job = mock_data(picked=False, assistant=client.leyan_assistant)
        resp = client.post("/v1/seller/business_orders/free_pick_stats")
        assert resp.status_code == 200
        assert resp.json == {"UNPICKED_COUNT": 1, "PICKED_COUNT": 0}

        # 删除工单。
        bo.deleted = True
        db.session.commit()
        resp = client.post("/v1/seller/business_orders/free_pick_stats")
        assert resp.status_code == 200
        assert resp.json == {"UNPICKED_COUNT": 0, "PICKED_COUNT": 0}

    def test_pick(self, client, mock_business_order, mock_data):
        mock_business_order, mock_job = mock_data(picked=False)
        body = {
            "action": JobProcessMark.PICK.name.lower(),
        }

        resp = client.patch(f"/v1/business_orders/{mock_business_order.id}/jobs/{mock_job.id}", json=body)
        assert resp.status_code == 200

        bo = BusinessOrder.query.get(mock_business_order.id)
        assert bo.status == BusinessOrderStatus.PENDING

    def test_deliver(self, mocker, client, mock_business_order, mock_data):
        shop2 = client.other_shop(name="org1_shop3")
        assistant2 = client.other_assistant(sid=shop2.sid)
        mock_business_order, mock_job = mock_data(picked=True, shop=shop2, assistant=assistant2)
        body = {
            "action": JobProcessMark.DELIVER.name.lower(),
            "job_assistant": {
                "assistant_user_id": assistant2.user_id,
                "assistant_type": assistant2.user_type,
            },
        }
        resp = client.patch(f"/v1/business_orders/{mock_business_order.id}/jobs/{mock_job.id}", json=body)
        assert resp.status_code == 200

        assistant3 = client.other_assistant(sid=client.shop.sid, has_bound_leyan_user=False)
        g.login_user_detail = assistant3
        resp = client.patch(f"/v1/business_orders/{mock_business_order.id}/jobs/{mock_job.id}", json=body)
        assert resp.status_code == 200
        assert resp.json["reason"] == constants.INVALID_ASSIGNEE


class TestDataInspect:
    widgets = ["买家昵称", "支付宝账号", "订单/子订单", "收款信息"]

    @staticmethod
    def mock_data(payment_method, widget_label):
        return {
            "买家昵称": "usernick",
            "支付宝账号": "***********",
            "订单/子订单": [{"tid": "tid", "oid": "oid"}],
            "收款信息": {
                "payment_method": payment_method,
                "receive_usernick": "receive_usernick",
                "tid": "tid",
                "receive_name": "receive_name",
                "receive_account": "receive_account",
            },
        }[widget_label]

    @pytest.fixture
    def create_business_order(self, client, mocker, mock_form, mock_step, business_order_factory, job_factory):
        def create(widget_label):
            widget = Widget.query.filter(Widget.label == widget_label).first()
            mock_step.step_type = StepType.human
            mock_step.form_id = mock_form.id
            mock_form.subscribe(client.shop, True)
            bo = business_order_factory.create(form_id=mock_form.id, sid=client.sid)
            bo.jobs = [job_factory.create(business_order_id=bo.id, step_id=mock_step.id, step_uuid=mock_step.step_uuid)]
            widgets = [
                WidgetInfo(
                    option_value={"valueUnique": True, "checkType": 1, "type": widget.schema["type"]},
                    key=widget_label,
                    widget_id=widget.id,
                )
            ]
            db.session.add_all(widgets)
            widget_info = {widget.key: widget.brief() for widget in widgets}
            mocker.patch("robot_processor.form.getter.get_non_ref_widgets_by_form_id", return_value=widget_info)
            db.session.commit()
            return bo

        return create

    @pytest.mark.parametrize("payment_method", map(lambda v: v.value, PaymentMethod.__members__.values()))
    @pytest.mark.parametrize("widget_label", widgets)
    def test_form_data_inspect(self, client, payment_method, widget_label, create_business_order):
        mock_data = self.mock_data(payment_method, widget_label)
        bo = create_business_order(widget_label)
        body = {
            "widget_key": widget_label,
            "value": mock_data,
            "form_id": bo.form_id,
        }
        resp = client.post("/v1/seller/business_orders/data-inspect", json=body)
        assert resp.status_code == 200

    @pytest.mark.parametrize("payment_method", map(lambda v: v.value, PaymentMethod.__members__.values()))
    @pytest.mark.parametrize("widget_label", widgets)
    def test_bo_data_inspect(self, client, payment_method, widget_label, create_business_order):
        mock_data = self.mock_data(payment_method, widget_label)
        bo = create_business_order(widget_label)
        body = {
            "widget_key": widget_label,
            "value": mock_data,
            "form_id": bo.form_id,
        }
        resp = client.post(f"/v1/seller/business_orders/{bo.id}/data-inspect", json=body)
        assert resp.status_code == 200
