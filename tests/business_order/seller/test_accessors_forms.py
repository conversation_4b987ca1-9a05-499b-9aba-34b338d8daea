import random

from pytest import fixture

from robot_processor.business_order.seller.query_by_accessor import AccessorQuery
from robot_processor.enums import AssigneeRule
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import Creator
from robot_processor.enums import StepType


@fixture(autouse=True)
def _create_free_pick_bo_and_job(
    db,
    mock_form,
    job_factory,
    job_pool_factory,
    business_order_factory,
    mock_step,
    form_shop_factory,
    shop_factory,
):
    mock_step.form_id = mock_form.id
    mock_step.assignee_rule = AssigneeRule.FREE_PICK
    mock_step.step_type = StepType.human
    mock_step.is_dirty = False
    mock_step.set_assistants_v2({})
    for sid in ["1", "2", "3"]:
        shop_factory.create(
            sid=sid,
            channel_id=int(sid),
        )
        form_shop_factory.create(form_id=mock_form.id, org_id="1", channel_id=int(sid), sid=sid)
    db.session.commit()
    """获取一个符合自由领取条件的工单"""

    def _do_create(bo_status, assignee_user_id, deleted=False):
        bo = business_order_factory(
            sid=random.choice(["1", "2", "3"]), deleted=deleted, form_id=mock_form.id, status=bo_status
        )
        job = job_factory(business_order_id=bo.id, assignee_user_id=assignee_user_id, step_id=mock_step.id)
        job_pool_factory(
            job_id=job.id, business_order_id=bo.id, sid=bo.sid, assignee_user_id=1, assignee_user_type=Creator.LEYAN
        )

    # 创建5个已领取的工单 和 3个待领取工单
    for i in range(5):
        _do_create(BusinessOrderStatus.PENDING, 1)
    for i in range(3):
        _do_create(BusinessOrderStatus.TO_BE_COLLECTED, None)
    _do_create(BusinessOrderStatus.PENDING, 1, True)


def test_picked():
    """测试已领取的场景"""
    finder = AccessorQuery(sid_list=["1", "2", "3"], user_id=1, org_id=1)
    res = finder.accessors_forms("FREE_PICK", picked=True)
    count = 0
    for item in res:
        count += item["total"]
    assert count == 5


def test_unpicked():
    """测试未领取的场景"""
    finder = AccessorQuery(sid_list=["1", "2", "3"], user_id=1, org_id=1)
    res = finder.accessors_forms("FREE_PICK", picked=False)
    count = 0
    for item in res:
        count += item["total"]
    assert count == 3
