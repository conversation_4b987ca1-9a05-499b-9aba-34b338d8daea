from robot_types.core import Symbol
from robot_types.core import TypeSpec

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.enums import FromType


def testcase(human_only_form_testbed, shop_factory):
    shop = shop_factory.create()
    step = human_only_form_testbed(
        shop=shop,
        symbols=[
            Symbol(name="refund-id", type_spec=TypeSpec("number"), render_config=dict(label="售后单号")),
            Symbol(name="tid", type_spec=TypeSpec("string"), render_config=dict(label="订单号")),
            Symbol(
                name="logistics-info",
                type_spec=TypeSpec(
                    "array",
                    items=TypeSpec(
                        "collection", properties={"company": TypeSpec("string"), "tracking-num": TypeSpec("string")}
                    ),
                ),
                render_config=dict(label="物流信息"),
                children=[
                    Symbol(
                        name="logistics-info_ITEM_",
                        type_spec=TypeSpec(
                            "collection", properties={"company": TypeSpec("string"), "tracking-num": TypeSpec("string")}
                        ),
                        children=[
                            Symbol(name="company", type_spec=TypeSpec("string"), render_config=dict(label="物流公司")),
                            Symbol(
                                name="tracking-num", type_spec=TypeSpec("string"), render_config=dict(label="物流单号")
                            ),
                        ],
                    )
                ],
            ),
        ],
    )
    business_manager = BusinessManager(
        form=step.form, shop=shop, creator=AccountDetailV2.system_processor(), from_type=FromType.LEYAN
    )
    bo_data = {
        # 提示错误，预期 number 类型，得到 string
        "refund-id": "1234",
        # 提示错误，预期 string 类型，得到 number
        "tid": 123,
        "logistics-info": [
            # 检查通过
            {"company": "顺丰", "tracking-num": "SF1234"},
            # logistics-no 检查不通过
            {"company": "韵达", "tracking-num": 1234},
        ],
    }
    check_result = business_manager.type_check_by_step(step, bo_data)
    assert check_result.is_err()
    errors = {tuple(error.loc): error for error in check_result.unwrap_err().errors}
    assert ("refund-id",) in errors
    # 如果需要定位具体是哪个组件，可以用 reveal
    refund_id_error = errors[("refund-id",)]
    refund_id_symbol = business_manager.reveal_data_type_error_symbol(step, refund_id_error)
    assert refund_id_symbol.label == "售后单号", refund_id_symbol

    assert ("tid",) in errors
    tid_error = errors[("tid",)]
    tid_symbol = business_manager.reveal_data_type_error_symbol(step, tid_error)
    assert tid_symbol.label == "订单号", tid_symbol

    assert ("logistics-info", 1, "tracking-num") in errors
    tracking_num_error = errors[("logistics-info", 1, "tracking-num")]
    tracking_num_symbol = business_manager.reveal_data_type_error_symbol(step, tracking_num_error)
    assert tracking_num_symbol.label == "物流信息", tracking_num_symbol
    assert tracking_num_symbol.children[0].label == "物流单号", tracking_num_symbol.children[0]
