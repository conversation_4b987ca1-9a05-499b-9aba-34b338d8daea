from sqlalchemy.orm.attributes import flag_modified

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.business_order.exception_rule.models import ExceptionBusinessOrder
from robot_processor.business_order.fsm import ActionInfo
from robot_processor.business_order.fsm import BatchBusinessOrderStatusController
from robot_processor.business_order.fsm import BusinessOrderStatusController
from robot_processor.business_order.fsm import PermissionFunctionCode
from robot_processor.business_order.fsm import constants
from robot_processor.enums import Action
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.enums import StepType
from robot_processor.ext import db
from robot_processor.task_center.getter import StepInfo


class TestBusinessOrderStatusController:
    def test_second_assistant_on_sixth_step_can_accept(
        self, mocker, mock_business_order_and_jobs, mock_operators, mock_form_and_steps
    ) -> None:
        business_order, (
            first_job,
            second_job,
            third_job,
            fourth_job,
            fifth_job,
            sixth_job,
            seventh_job,
            eighth_job,
            ninth_job,
            tenth_job,
        ) = mock_business_order_and_jobs
        _, (
            first_step,
            second_step,
            third_step,
            fourth_step,
            fifth_step,
            sixth_step,
            seventh_step,
            eighth_step,
            ninth_step,
            tenth_step,
        ) = mock_form_and_steps
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=second_assistant,
        )
        business_order.job_history = [
            first_job.id,
            second_job.id,
            third_job.id,
            fourth_job.id,
            fifth_job.id,
            sixth_job.id,
        ]
        mocker.patch(
            "robot_processor.assistant.schema.AssistantV2.get_latest_assignee_account_details",
            return_value=[
                first_assistant,
                second_assistant,
            ],
        )
        can_do, err = bsc.can_accept()
        assert can_do

        actions = bsc.get_actions()
        assert actions.get(Action.accept) == ActionInfo(
            label=Action.accept.label,
            disabled=False,
            job_id=sixth_job.id,
            job_step_id=sixth_job.step_id,
            job_step_uuid=sixth_job.step_uuid,
            job_assignee=AccountDetailV2(
                user_nick=sixth_job.assignee,
                user_type=sixth_job.assignee_type.value,
                user_id=sixth_job.assignee_user_id,
            ),
            coordinators=[
                second_assistant,
            ],
            next_human_job_step_uuid=seventh_step.step_uuid,
            next_human_job_need_assistant=True,
        )

    def test_admin_on_second_step_can_not_upgrade(
        self,
        mocker,
        mock_business_order_and_jobs,
        mock_operators,
    ):
        business_order, (
            first_job,
            second_job,
            third_job,
            fourth_job,
            fifth_job,
            sixth_job,
            seventh_job,
            eighth_job,
            ninth_job,
            tenth_job,
        ) = mock_business_order_and_jobs
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_ldap_account,
        )

        business_order.current_job_id = second_job.id
        business_order.job_history = [first_job.id, second_job.id]
        mocker.patch(
            "robot_processor.assistant.schema.AssistantV2.get_latest_assignee_account_details",
            return_value=[
                first_assistant,
                second_assistant,
            ],
        )
        can_do, err = bsc.can_upgrade()
        assert not can_do
        assert err == constants.NO_UPGRADE_STEPS

    def test_first_assistant_on_sixth_step_with_form_not_co_edit(
        self, mocker, mock_business_order_and_jobs, mock_operators, mock_form_and_steps
    ) -> None:
        business_order, _ = mock_business_order_and_jobs
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )
        mocker.patch(
            "robot_processor.assistant.schema.AssistantV2.get_latest_assignee_account_details",
            return_value=[
                first_assistant,
                second_assistant,
            ],
        )
        can_do, err = bsc.can_accept()
        assert can_do

        form, _ = mock_form_and_steps
        form.co_edit = False
        db.session.commit()

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )

        can_do, err = bsc.can_accept()
        assert not can_do

    def test_first_assistant_on_sixth_step_with_form_owners(
        self, mocker, mock_business_order_and_jobs, mock_operators, mock_form_and_steps
    ) -> None:
        business_order, _ = mock_business_order_and_jobs
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )
        form, _ = mock_form_and_steps
        form.co_edit = False
        db.session.commit()

        # 1 - 禁用协同编辑，且没有设置工单管理员。
        # want：无法“提交”
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )

        can_do, err = bsc.can_accept()
        assert not can_do

        # 2 - 禁用协同编辑，但是将 first_assistant 设置为工单管理员。
        # want：可以“提交”
        begin_step = form.get_begin_step()
        begin_step.set_owners(
            {
                "details": [
                    {
                        "user_type": first_assistant.user_type,
                        "user_id": first_assistant.user_id,
                        "user_nick": first_assistant.user_nick,
                    }
                ],
                "online_only": False,
                "select_type": 2,
                "leyan_accounts": [],
                "assign_strategy": 1,
                "assignee_groups": [],
                "channel_accounts": [
                    {
                        "user_type": first_assistant.user_type,
                        "user_id": first_assistant.user_id,
                        "user_nick": first_assistant.user_nick,
                    }
                ],
            }
        )
        db.session.commit()

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )

        can_do, err = bsc.can_accept()
        assert can_do

    def test_second_assistant_on_fifth_step_get_actions_on_paused(
        self, mocker, mock_business_order_and_jobs, client
    ) -> None:
        business_order, _ = mock_business_order_and_jobs
        business_order.status = BusinessOrderStatus.PAUSED
        db.session.commit()
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.assistant,
        )
        bsc._permissions = set()
        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.unpause,
            Action.close,
            Action.complete,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.assistant,
        )

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.unpause,
            Action.delete,
            Action.complete,
            Action.close,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

    def test_second_assistant_on_fifth_step_get_actions_can_assign(
        self, mocker, mock_business_order_and_jobs, client
    ) -> None:
        business_order, _ = mock_business_order_and_jobs
        business_order.status = BusinessOrderStatus.IN_EXCEPTION
        db.session.commit()
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.another_assistant,
        )
        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.complete,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.another_assistant,
        )
        bsc._permissions = {PermissionFunctionCode.BUSINESS_ORDER_ASSIGN}
        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.complete,
            Action.assign,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        bsc = BusinessOrderStatusController(
            business_order=business_order, operator=client.another_assistant, is_admin=True
        )
        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.complete,
            Action.assign,
            Action.delete,
            Action.close,
            Action.upgrade,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

    def test_second_assistant_on_sixth_step_get_actions(
        self, mocker, mock_business_order_and_jobs, mock_form_and_steps, client, step_retry_factory
    ) -> None:
        business_order, (
            first_job,
            second_job,
            third_job,
            fourth_job,
            fifth_job,
            sixth_job,
            seventh_job,
            eighth_job,
            ninth_job,
            tenth_job,
        ) = mock_business_order_and_jobs

        _, steps = mock_form_and_steps
        (
            first_step,
            second_step,
            third_step,
            fourth_step,
            fifth_step,
            sixth_step,
            seventh_step,
            eighth_step,
            ninth_step,
            tenth_step,
        ) = steps

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.another_assistant,
        )
        bsc._permissions = {PermissionFunctionCode.BUSINESS_ORDER_DELETE}
        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.accept,
            Action.reject,
            Action.close,
            Action.save,
            Action.pause,
            Action.deliver,
            Action.delete,
            Action.upgrade,
            Action.complete,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        assert actions.get(Action.reject) == ActionInfo(
            label=Action.reject.label,
            disabled=False,
            job_id=sixth_job.id,
            job_step_id=sixth_job.step_id,
            job_step_uuid=sixth_job.step_uuid,
            job_assignee=AccountDetailV2(
                user_nick=sixth_job.assignee,
                user_type=sixth_job.assignee_type.value,
                user_id=sixth_job.assignee_user_id,
            ),
            coordinators=[
                client.another_leyan_assistant,
            ],
            can_operate_steps=[
                StepInfo(
                    step_id=step.id,
                    step_uuid=step.step_uuid,
                    step_name=step.name,
                    step_type=step.step_type.value,
                    index=i,
                    job_id=job.id,
                )
                for i, (step, job) in enumerate(
                    zip(
                        [
                            fifth_step,
                        ],
                        [
                            fifth_job,
                        ],
                    )
                )
            ],
        )

        # 第四步 RPA 应用设置为支持重试。
        step_retry_factory.create(step_uuid=fourth_step.step_uuid, can_retry=True)
        db.session.commit()

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.another_assistant,
        )
        bsc._permissions = {PermissionFunctionCode.BUSINESS_ORDER_DELETE}
        actions = bsc.get_actions()
        assert actions.get(Action.reject) == ActionInfo(
            label=Action.reject.label,
            disabled=False,
            job_id=sixth_job.id,
            job_step_id=sixth_job.step_id,
            job_step_uuid=sixth_job.step_uuid,
            job_assignee=AccountDetailV2(
                user_nick=sixth_job.assignee,
                user_type=sixth_job.assignee_type.value,
                user_id=sixth_job.assignee_user_id,
            ),
            coordinators=[client.another_leyan_assistant],
            can_operate_steps=[
                StepInfo(
                    step_id=step.id,
                    step_uuid=step.step_uuid,
                    step_name=step.name,
                    step_type=step.step_type.value,
                    index=i,
                    job_id=job.id,
                )
                for i, (step, job) in enumerate(
                    zip(
                        [
                            second_step,
                            fourth_step,
                            fifth_step,
                        ],
                        [
                            second_job,
                            fourth_job,
                            fifth_job,
                        ],
                    )
                )
            ],
        )

        # 第一个人工步骤设置为不支持重试。
        step_retry_factory.create(step_uuid=second_step.step_uuid, can_retry=False)
        db.session.commit()

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.another_assistant,
        )
        bsc._permissions = {PermissionFunctionCode.BUSINESS_ORDER_DELETE}
        actions = bsc.get_actions()
        assert actions.get(Action.reject) == ActionInfo(
            label=Action.reject.label,
            disabled=False,
            job_id=sixth_job.id,
            job_step_id=sixth_job.step_id,
            job_step_uuid=sixth_job.step_uuid,
            job_assignee=AccountDetailV2(
                user_nick=sixth_job.assignee,
                user_type=sixth_job.assignee_type.value,
                user_id=sixth_job.assignee_user_id,
            ),
            coordinators=[client.another_leyan_assistant],
            can_operate_steps=[
                StepInfo(
                    step_id=step.id,
                    step_uuid=step.step_uuid,
                    step_name=step.name,
                    step_type=step.step_type.value,
                    index=i,
                    job_id=job.id,
                )
                for i, (step, job) in enumerate(
                    zip(
                        [
                            fourth_step,
                            fifth_step,
                        ],
                        [
                            fourth_job,
                            fifth_job,
                        ],
                    )
                )
            ],
        )

    def test_second_assistant_on_sixth_step_get_actions_with_skip(
        self, mocker, mock_business_order_and_jobs, client, mock_form_and_steps, step_skip_factory
    ):
        business_order, _ = mock_business_order_and_jobs

        _, steps = mock_form_and_steps

        sixth_step = steps[5]
        step_skip_factory.create(step_uuid=sixth_step.step_uuid, can_skip=True)
        db.session.commit()

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.another_assistant,
        )
        bsc._permissions = {PermissionFunctionCode.BUSINESS_ORDER_DELETE}
        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.accept,
            Action.reject,
            Action.close,
            Action.save,
            Action.pause,
            Action.deliver,
            Action.delete,
            Action.upgrade,
            Action.complete,
            Action.skip,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        bsc._permissions = {PermissionFunctionCode.BUSINESS_ORDER_DELETE, PermissionFunctionCode.REMIND_WITHOUT_ADMIN}
        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.accept,
            Action.reject,
            Action.close,
            Action.save,
            Action.pause,
            Action.deliver,
            Action.delete,
            Action.upgrade,
            Action.complete,
            Action.skip,
            Action.copy,
            Action.remind,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

    def test_first_assistant_on_sixth_step_get_actions_with_recall(
        self,
        mocker,
        mock_business_order_and_jobs,
        client,
        mock_form_and_steps,
        step_skip_factory,
    ) -> None:
        business_order, jobs = mock_business_order_and_jobs

        _, steps = mock_form_and_steps

        sixth_step = steps[5]
        step_skip_factory.create(step_uuid=sixth_step.step_uuid, can_skip=True)
        db.session.commit()

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.assistant,
        )
        bsc._permissions = {PermissionFunctionCode.BUSINESS_ORDER_DELETE}
        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.accept,
            Action.reject,
            Action.close,
            Action.save,
            Action.pause,
            Action.deliver,
            Action.delete,
            Action.upgrade,
            Action.complete,
            Action.skip,
            Action.recall,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names
        assert len(actions.get(Action.recall).coordinators) != 0
        assert len(actions.get(Action.recall).can_operate_steps) != 0

    def test_first_assistant_on_sixth_step_can_upgrade(
        self, mocker, mock_business_order_and_jobs, client, mock_form_and_steps
    ):
        business_order, jobs = mock_business_order_and_jobs

        _, steps = mock_form_and_steps

        fifth_step = steps[4]
        fifth_job = jobs[4]
        sixth_job = jobs[5]

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.assistant,
        )
        bsc._permissions = {PermissionFunctionCode.BUSINESS_ORDER_DELETE}
        mocker.patch(
            "robot_processor.business_order.fsm.fsm.BusinessOrderStatusController.get_current_valid_candidate_assistants",  # noqa
            return_value={
                client.another_assistant,
            },
        )
        mocker.patch(
            "robot_processor.business_order.fsm.fsm.BusinessOrderStatusController.get_history_valid_candidate_assistants",  # noqa
            return_value={fifth_job.id: {client.assistant}},
        )
        actions = bsc.get_actions()

        assert actions.get(Action.upgrade) == ActionInfo(
            label=Action.upgrade.label,
            disabled=False,
            job_id=sixth_job.id,
            job_step_id=sixth_job.step_id,
            job_step_uuid=sixth_job.step_uuid,
            job_assignee=AccountDetailV2(
                user_nick=sixth_job.assignee,
                user_type=sixth_job.assignee_type.value,
                user_id=sixth_job.assignee_user_id,
            ),
            coordinators=[
                client.assistant,
            ],
            can_operate_steps=[
                StepInfo(
                    step_id=fifth_step.id,
                    step_uuid=fifth_step.step_uuid,
                    step_name=fifth_step.name,
                    step_type=fifth_step.step_type.value,
                    index=4,
                    job_id=fifth_job.id,
                )
            ],
        )

    def test_first_assistant_on_ninth_step_get_actions(
        self,
        mocker,
        mock_business_order_and_jobs,
        client,
        mock_form_and_steps,
    ) -> None:
        business_order, (
            first_job,
            second_job,
            third_job,
            fourth_job,
            fifth_job,
            sixth_job,
            seventh_job,
            eighth_job,
            ninth_job,
            tenth_job,
        ) = mock_business_order_and_jobs

        _, steps = mock_form_and_steps
        (
            first_step,
            second_step,
            third_step,
            fourth_step,
            fifth_step,
            sixth_step,
            seventh_step,
            eighth_step,
            ninth_step,
            tenth_step,
        ) = steps

        business_order.current_job_id = ninth_job.id
        business_order.job_history = [
            first_job.id,
            second_job.id,
            third_job.id,
            fourth_job.id,
            fifth_job.id,
            sixth_job.id,
            seventh_job.id,
            eighth_job.id,
            ninth_job.id,
        ]
        business_order.status = BusinessOrderStatus.TO_BE_COLLECTED

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.assistant,
        )
        bsc._permissions = set()

        mocker.patch(
            "robot_processor.business_order.fsm.BusinessOrderStatusController.get_current_valid_candidate_assistants",
            return_value={
                client.leyan_assistant,
                client.another_leyan_assistant,
            },
        )

        actions = bsc.get_actions()

        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.recall,
            Action.copy,
            Action.upgrade,
            Action.pick,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        assert actions.get(Action.pick) == ActionInfo(
            label=Action.pick.label,
            disabled=False,
            job_id=ninth_job.id,
            job_step_id=ninth_job.step_id,
            job_step_uuid=ninth_job.step_uuid,
            job_assignee=AccountDetailV2(
                user_nick=ninth_job.assignee,
                user_type=ninth_job.assignee_type.value,
                user_id=ninth_job.assignee_user_id,
            ),
            coordinators=[
                client.leyan_assistant,
            ],
        )

        assert actions.get(Action.recall) == ActionInfo(
            label=Action.recall.label,
            disabled=False,
            job_id=ninth_job.id,
            job_step_id=ninth_job.step_id,
            job_step_uuid=ninth_job.step_uuid,
            job_assignee=AccountDetailV2(
                user_nick=ninth_job.assignee,
                user_type=ninth_job.assignee_type.value,
                user_id=ninth_job.assignee_user_id,
            ),
            coordinators=[
                client.leyan_assistant,
            ],
            can_operate_steps=[
                StepInfo(
                    step_id=step.id,
                    step_uuid=step.step_uuid,
                    step_name=step.name,
                    step_type=step.step_type.value,
                    index=0,
                    job_id=job.id,
                )
                for (step, job) in zip([eighth_step], [eighth_job])
            ],
        )

        assert actions.get(Action.upgrade) == ActionInfo(
            label=Action.upgrade.label,
            disabled=False,
            job_id=ninth_job.id,
            job_step_id=ninth_job.step_id,
            job_step_uuid=ninth_job.step_uuid,
            job_assignee=AccountDetailV2(
                user_nick=ninth_job.assignee,
                user_type=ninth_job.assignee_type.value,
                user_id=ninth_job.assignee_user_id,
            ),
            coordinators=[
                client.leyan_assistant,
            ],
            can_operate_steps=[
                StepInfo(
                    step_id=step.id,
                    step_uuid=step.step_uuid,
                    step_name=step.name,
                    step_type=step.step_type.value,
                    index=steps.index(step),
                    job_id=job.id,
                )
                for (step, job) in zip(
                    [second_step, fifth_step, sixth_step, seventh_step, eighth_step],
                    [second_job, fifth_job, sixth_job, seventh_job, eighth_job],
                )
            ][::-1],
        )

    def test_first_assistant_on_ninth_step_get_actions_with_can_retry_auto_jobs(
        self,
        mocker,
        mock_business_order_and_jobs,
        client,
        mock_form_and_steps,
        step_retry_factory,
    ) -> None:
        business_order, (
            first_job,
            second_job,
            third_job,
            fourth_job,
            fifth_job,
            sixth_job,
            seventh_job,
            eighth_job,
            ninth_job,
            tenth_job,
        ) = mock_business_order_and_jobs

        _, steps = mock_form_and_steps
        (
            first_step,
            second_step,
            third_step,
            fourth_step,
            fifth_step,
            sixth_step,
            seventh_step,
            eighth_step,
            ninth_step,
            tenth_step,
        ) = steps

        business_order.current_job_id = ninth_job.id
        business_order.status = BusinessOrderStatus.TO_BE_COLLECTED
        step_retry_factory.create(step_uuid=fourth_step.step_uuid, can_retry=True)
        db.session.commit()

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.assistant,
        )
        bsc._permissions = set()
        actions = bsc.get_actions()

        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.recall,
            Action.copy,
            Action.upgrade,
            Action.pick,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

    def test_first_ldap_account_on_ninth_step_get_actions_with_admin_mode(
        self,
        mocker,
        mock_business_order_and_jobs,
        mock_operators,
        mock_form_and_steps,
    ) -> None:
        business_order, (
            first_job,
            second_job,
            third_job,
            fourth_job,
            fifth_job,
            sixth_job,
            seventh_job,
            eighth_job,
            ninth_job,
            tenth_job,
        ) = mock_business_order_and_jobs

        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators

        business_order.current_job_id = ninth_job.id
        business_order.status = BusinessOrderStatus.TO_BE_COLLECTED
        db.session.commit()

        bsc = BusinessOrderStatusController(business_order=business_order, operator=first_ldap_account, is_admin=True)

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.accept,
            Action.reject,
            Action.save,
            Action.pause,
            Action.remind,
            Action.deliver,
            Action.assign,
            Action.delete,
            Action.upgrade,
            Action.complete,
            Action.close,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

    def test_first_ldap_account_on_ninth_step_and_bo_succeed_get_actions(
        self,
        mocker,
        mock_business_order_and_jobs,
        mock_operators,
        mock_form_and_steps,
    ) -> None:
        business_order, (
            first_job,
            second_job,
            third_job,
            fourth_job,
            fifth_job,
            sixth_job,
            seventh_job,
            eighth_job,
            ninth_job,
            tenth_job,
        ) = mock_business_order_and_jobs

        _, steps = mock_form_and_steps
        (
            first_step,
            second_step,
            third_step,
            fourth_step,
            fifth_step,
            sixth_step,
            seventh_step,
            eighth_step,
            ninth_step,
            tenth_step,
        ) = steps

        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators

        business_order.current_job_id = ninth_job.id
        business_order.job_history = [
            first_job.id,
            second_job.id,
            third_job.id,
            fourth_job.id,
            fifth_job.id,
            sixth_job.id,
            seventh_job.id,
            eighth_job.id,
            ninth_job.id,
        ]
        business_order.status = BusinessOrderStatus.SUCCEED
        db.session.commit()

        bsc = BusinessOrderStatusController(business_order=business_order, operator=first_ldap_account, is_admin=True)

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [Action.reject, Action.upgrade, Action.delete]
        expected_action_names.sort()
        assert action_names == expected_action_names

        assert actions.get(Action.upgrade) == ActionInfo(
            label=Action.upgrade.label,
            disabled=False,
            job_id=ninth_job.id,
            job_step_id=ninth_job.step_id,
            job_step_uuid=ninth_job.step_uuid,
            job_assignee=AccountDetailV2(
                user_nick=ninth_job.assignee,
                user_type=ninth_job.assignee_type.value,
                user_id=ninth_job.assignee_user_id,
            ),
            coordinators=[
                first_ldap_account,
            ],
            can_operate_steps=[
                StepInfo(
                    step_id=step.id,
                    step_uuid=step.step_uuid,
                    step_name=step.name,
                    step_type=step.step_type.value,
                    index=steps.index(step),
                    job_id=job.id,
                )
                for (step, job) in zip(
                    [second_step, fifth_step, sixth_step, seventh_step, eighth_step, ninth_step],
                    [second_job, fifth_job, sixth_job, seventh_job, eighth_job, ninth_job],
                )
            ][::-1],
        )

        business_order.status = BusinessOrderStatus.PENDING
        db.session.commit()

        bsc = BusinessOrderStatusController(business_order=business_order, operator=first_ldap_account, is_admin=True)

        actions = bsc.get_actions()

        assert actions.get(Action.upgrade) == ActionInfo(
            label=Action.upgrade.label,
            disabled=False,
            job_id=ninth_job.id,
            job_step_id=ninth_job.step_id,
            job_step_uuid=ninth_job.step_uuid,
            job_assignee=AccountDetailV2(
                user_nick=ninth_job.assignee,
                user_type=ninth_job.assignee_type.value,
                user_id=ninth_job.assignee_user_id,
            ),
            coordinators=[
                first_ldap_account,
            ],
            can_operate_steps=[
                StepInfo(
                    step_id=step.id,
                    step_uuid=step.step_uuid,
                    step_name=step.name,
                    step_type=step.step_type.value,
                    index=steps.index(step),
                    job_id=job.id,
                )
                for (step, job) in zip(
                    [second_step, fifth_step, sixth_step, seventh_step, eighth_step],
                    [second_job, fifth_job, sixth_job, seventh_job, eighth_job],
                )
            ][::-1],
        )

    def test_get_actions_with_in_exception(
        self,
        mocker,
        mock_business_order_and_jobs,
        client,
        mock_form_and_steps,
    ) -> None:
        business_order, (
            first_job,
            second_job,
            third_job,
            fourth_job,
            fifth_job,
            sixth_job,
            seventh_job,
            eighth_job,
            ninth_job,
            tenth_job,
        ) = mock_business_order_and_jobs

        business_order.current_job_id = ninth_job.id
        business_order.status = BusinessOrderStatus.IN_EXCEPTION
        db.session.commit()

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.leyan_assistant,
        )
        bsc._permissions = {
            PermissionFunctionCode.BUSINESS_ORDER_DELETE,
            PermissionFunctionCode.BUSINESS_ORDER_CLOSE,
            PermissionFunctionCode.BUSINESS_ORDER_RETRY,
            PermissionFunctionCode.BUSINESS_ORDER_ASSIGN,
        }

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.assign,
            Action.complete,
            Action.close,
            Action.delete,
            Action.upgrade,
            Action.recall,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        business_order.current_job_id = tenth_job.id
        business_order.status = BusinessOrderStatus.IN_EXCEPTION
        db.session.commit()

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.another_assistant,
        )
        bsc._permissions = {
            PermissionFunctionCode.BUSINESS_ORDER_DELETE,
            PermissionFunctionCode.BUSINESS_ORDER_CLOSE,
            PermissionFunctionCode.BUSINESS_ORDER_RETRY,
            PermissionFunctionCode.BUSINESS_ORDER_ASSIGN,
        }

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.retry,
            Action.complete,
            Action.close,
            Action.delete,
            Action.upgrade,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        business_order.current_job_id = fourth_job.id
        business_order.status = BusinessOrderStatus.IN_EXCEPTION
        db.session.commit()

        mocker.patch(
            "robot_processor.business_order.fsm.BusinessOrderStatusController.get_history_valid_candidate_assistants",
            return_value={},
        )

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.assistant,
        )
        bsc._permissions = {
            PermissionFunctionCode.BUSINESS_ORDER_DELETE,
            PermissionFunctionCode.BUSINESS_ORDER_CLOSE,
            PermissionFunctionCode.BUSINESS_ORDER_RETRY,
            PermissionFunctionCode.BUSINESS_ORDER_ASSIGN,
        }

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.complete,
            Action.close,
            Action.delete,
            Action.upgrade,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        in_processing_patch = mocker.patch(
            "robot_processor.business_order.exception_rule.ExceptionBusinessOrder.Queries.in_processing_by_job",
            autospec=ExceptionBusinessOrder.Queries.in_processing_by_job,
        )
        in_processing_patch.return_value = True

        business_order.current_job_id = fourth_job.id
        business_order.status = BusinessOrderStatus.IN_EXCEPTION
        db.session.commit()

        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=client.leyan_assistant,
        )
        bsc._permissions = {
            PermissionFunctionCode.BUSINESS_ORDER_DELETE,
            PermissionFunctionCode.BUSINESS_ORDER_CLOSE,
            PermissionFunctionCode.BUSINESS_ORDER_RETRY,
            PermissionFunctionCode.BUSINESS_ORDER_ASSIGN,
        }

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [Action.copy]
        expected_action_names.sort()
        assert action_names == expected_action_names

    def test_running_cron_business_order_can_operate(
        self,
        mocker,
        mock_business_order_and_jobs,
        mock_operators,
        mock_form_and_steps,
        rpa_factory,
        job_execute_cron_factory,
        step_skip_factory,
    ):
        business_order, (
            first_job,
            second_job,
            third_job,
            fourth_job,
            fifth_job,
            sixth_job,
            seventh_job,
            eighth_job,
            ninth_job,
            tenth_job,
        ) = mock_business_order_and_jobs

        _, steps = mock_form_and_steps
        (
            first_step,
            second_step,
            third_step,
            fourth_step,
            fifth_step,
            sixth_step,
            seventh_step,
            eighth_step,
            ninth_step,
            tenth_step,
        ) = steps

        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators

        business_order.current_job_id = fourth_job.id
        business_order.job_history = [first_job.id, second_job.id, third_job.id, fourth_job.id]
        business_order.status = BusinessOrderStatus.RUNNING
        rpa_id = 1001
        fourth_step.data = {"rpa_id": rpa_id}
        rpa_factory.create(id=rpa_id, task=JobType.JOB_EXECUTE_CRON)

        db.session.commit()

        # 1 - 不可以操作的情况。
        bsc = BusinessOrderStatusController(business_order=business_order, operator=first_assistant)

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [Action.copy]
        expected_action_names.sort()
        assert action_names == expected_action_names

        # 2 - 仅能完成和关闭的情况。
        job_execute_cron_factory.create(
            business_order_id=business_order.id,
            job_id=fourth_job.id,
        )
        db.session.commit()

        bsc = BusinessOrderStatusController(business_order=business_order, operator=first_assistant)

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.complete,
            Action.close,
            Action.copy,
            Action.delete,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        # 3 - 也支持跳过的情况。
        step_skip_factory.create(step_uuid=fourth_step.step_uuid, can_skip=True)

        db.session.commit()

        bsc = BusinessOrderStatusController(business_order=business_order, operator=second_assistant)

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [Action.complete, Action.skip, Action.close, Action.copy]
        expected_action_names.sort()
        assert action_names == expected_action_names

        # 4 - 第一步人工步骤的执行客服，可以撤回
        second_job.set_assignee_assistant(second_assistant)
        db.session.commit()

        bsc = BusinessOrderStatusController(business_order=business_order, operator=second_assistant)

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [Action.complete, Action.skip, Action.close, Action.recall, Action.copy]
        expected_action_names.sort()
        assert action_names == expected_action_names

    def test_running_transfer_business_order_can_operate(
        self,
        mocker,
        mock_business_order_and_jobs,
        client,
        mock_form_and_steps,
        rpa_factory,
        step_skip_factory,
    ):
        business_order, (
            first_job,
            second_job,
            third_job,
            fourth_job,
            fifth_job,
            sixth_job,
            seventh_job,
            eighth_job,
            ninth_job,
            tenth_job,
        ) = mock_business_order_and_jobs

        _, steps = mock_form_and_steps
        (
            first_step,
            second_step,
            third_step,
            fourth_step,
            fifth_step,
            sixth_step,
            seventh_step,
            eighth_step,
            ninth_step,
            tenth_step,
        ) = steps

        business_order.current_job_id = fourth_job.id
        business_order.job_history = [first_job.id, second_job.id, third_job.id, fourth_job.id]
        business_order.status = BusinessOrderStatus.RUNNING
        rpa_id = 1001
        fourth_step.data = {"rpa_id": rpa_id}
        rpa_factory.create(id=rpa_id, task=JobType.CONFIRM_ALIPAY, tag="支付交易")

        db.session.commit()

        # 1 - 请求发现没有打款单，允许操作。
        mocker.patch("robot_processor.client.robot_transfer.find_transfer", return_value={})
        bsc = BusinessOrderStatusController(business_order=business_order, operator=client.assistant)

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [Action.close, Action.complete, Action.copy, Action.delete]
        expected_action_names.sort()
        assert action_names == expected_action_names

        # 2 - 请求成功，并且打款单状态为 INIT，允许操作。
        step_skip_factory.create(step_uuid=fourth_step.step_uuid, can_skip=True)
        second_job.set_assignee_assistant(client.assistant)
        db.session.commit()

        mocker.patch("robot_processor.client.robot_transfer.find_transfer", return_value={"transfer": {"status": 1}})
        bsc = BusinessOrderStatusController(business_order=business_order, operator=client.assistant)

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [Action.skip, Action.complete, Action.close, Action.recall, Action.copy, Action.delete]
        expected_action_names.sort()
        assert action_names == expected_action_names

        # 3 - 请求成功，但是已经支付完成，不允许操作。
        mocker.patch("robot_processor.client.robot_transfer.find_transfer", return_value={"transfer": {"status": 4}})
        bsc = BusinessOrderStatusController(business_order=business_order, operator=client.assistant)

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [Action.copy]
        expected_action_names.sort()
        assert action_names == expected_action_names


class TestBatchBusinessOrderStatusController:
    def test_second_assistant_on_sixth_step_get_actions(
        self, mocker, mock_business_order_and_jobs, mock_operators
    ) -> None:
        business_order, _ = mock_business_order_and_jobs
        business_order.status = BusinessOrderStatus.PENDING
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators
        bbsc = BatchBusinessOrderStatusController(
            business_orders=[business_order],
            operator=second_assistant,
        )
        mocker.patch(
            "robot_processor.assistant.schema.AssistantV2.get_latest_assignee_account_details",
            return_value=[
                first_assistant,
                second_assistant,
            ],
        )
        business_order_action_names = {}
        business_order_actions = bbsc.get_all_business_order_actions()
        for bo_id, actions in business_order_actions.items():
            action_names = [action for action, action_info in actions.items() if not action_info.disabled]
            action_names.sort()
            business_order_action_names[bo_id] = action_names
        expected_action_names = [
            Action.accept,
            Action.reject,
            Action.close,
            Action.save,
            Action.pause,
            Action.deliver,
            Action.upgrade,
            Action.complete,
            Action.copy,
        ]
        expected_action_names.sort()
        assert business_order_action_names == {business_order.id: expected_action_names}

    def test_second_assistant_on_sixth_step_can_do_accept(
        self, mocker, mock_business_order_and_jobs, mock_operators
    ) -> None:
        business_order, _ = mock_business_order_and_jobs
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators
        bbsc = BatchBusinessOrderStatusController(
            business_orders=[business_order],
            operator=second_assistant,
        )
        mocker.patch(
            "robot_processor.assistant.schema.AssistantV2.get_latest_assignee_account_details",
            return_value=[
                first_assistant,
                second_assistant,
            ],
        )
        results = bbsc.get_all_business_orders_can_do_by_action(Action.accept)
        assert results == {business_order.id: (True, "")}

    def test_second_assistant_on_sixth_step_can_do_delete(
        self, mocker, mock_business_order_and_jobs, mock_operators
    ) -> None:
        business_order, _ = mock_business_order_and_jobs
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators
        bbsc = BatchBusinessOrderStatusController(
            business_orders=[business_order],
            operator=second_assistant,
        )
        mocker.patch(
            "robot_processor.assistant.schema.AssistantV2.get_latest_assignee_account_details",
            return_value=[
                first_assistant,
                second_assistant,
            ],
        )
        results = bbsc.get_all_business_orders_can_do_by_action(Action.delete)
        assert results == {business_order.id: (False, constants.OPERATOR_DO_NOT_HAVE_PERMISSION)}

    def test_missing_current_step(
        self, mocker, mock_business_order_and_jobs, mock_operators, mock_form_and_steps
    ) -> None:
        business_order, _ = mock_business_order_and_jobs
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators
        bbsc = BatchBusinessOrderStatusController(
            business_orders=[business_order],
            operator=second_assistant,
        )
        _, (
            first_step,
            second_step,
            third_step,
            fourth_step,
            fifth_step,
            sixth_step,
            seventh_step,
            eighth_step,
            ninth_step,
            tenth_step,
        ) = mock_form_and_steps

        sixth_step.id = 114514
        db.session.commit()

        mocker.patch(
            "robot_processor.assistant.schema.AssistantV2.get_latest_assignee_account_details",
            return_value=[
                first_assistant,
                second_assistant,
            ],
        )
        results = bbsc.get_all_business_orders_can_do_by_action(Action.delete)
        assert results == {business_order.id: (False, f"工单 {business_order.id} 未找到当前任务对应的步骤")}

        results = bbsc.get_all_business_order_actions()
        assert results == {business_order.id: {}}


class TestComplexBusinessOrderActions:
    def test_first_assistant_can_recall(
        self,
        mocker,
        mock_complex_form_and_steps,
        mock_complex_business_order_and_jobs,
        mock_operators,
        step_retry_factory,
    ):
        form, (
            begin_step,
            step_1,
            step_2,
            step_3,
            step_4,
            step_5,
            step_6,
            step_7,
            step_8,
            step_9,
            step_10,
            step_11,
            step_12,
            step_13,
            step_14,
            step_15,
            step_16,
            step_17,
            step_18,
            step_19,
            step_20,
            step_21,
            step_22,
            step_23,
            step_24,
            end_step,
        ) = mock_complex_form_and_steps

        business_order, (
            begin_job,
            job_1,
            job_2,
            job_3,
            job_4,
            job_5,
            job_6,
            job_7,
            job_8,
            job_9,
            job_10,
            job_11,
            job_12,
            job_13,
            job_14,
            job_15,
            job_16,
            job_17,
            job_18,
            job_19,
            job_20,
            job_21,
            job_22,
            job_23,
            job_24,
            end_job,
        ) = mock_complex_business_order_and_jobs
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators

        # 最后一步 RPA 应用设置为支持重试。
        step_retry_factory.create(step_uuid=end_step.step_uuid, can_retry=True)
        step_retry_factory.create(step_uuid=step_21.step_uuid, can_retry=True)
        db.session.commit()

        # 1 -
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )

        ok, reason = bsc.can_recall()
        assert ok

        can_recall_steps = bsc.get_can_recall_steps()
        assert can_recall_steps == [
            StepInfo(
                step_id=step_1.id,
                step_uuid=step_1.step_uuid,
                step_name=step_1.name,
                step_type=step_1.step_type.value,
                index=0,
                job_id=job_1.id,
            )
        ]

        # 2 -
        business_order.current_job_id = job_21.id
        business_order.status = BusinessOrderStatus.PENDING
        db.session.commit()
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )

        can_recall_steps = bsc.get_can_recall_steps()
        assert can_recall_steps == [
            StepInfo(
                step_id=step_15.id,
                step_uuid=step_15.step_uuid,
                step_name=step_15.name,
                step_type=step_15.step_type.value,
                index=0,
                job_id=job_15.id,
            )
        ]

        # 3 -
        business_order.current_job_id = job_15.id
        business_order.status = BusinessOrderStatus.PENDING
        job_13.status = JobStatus.RUNNING
        db.session.commit()
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )

        can_recall_steps = bsc.get_can_recall_steps()
        assert can_recall_steps == []

        can_revert_jobs = bsc.get_can_revert_jobs()
        assert job_1 in can_revert_jobs

    def test_first_assistant_can_upgrade_steps(
        self,
        mocker,
        mock_complex_form_and_steps,
        mock_complex_business_order_and_jobs,
        mock_operators,
    ):
        form, (
            begin_step,
            step_1,
            step_2,
            step_3,
            step_4,
            step_5,
            step_6,
            step_7,
            step_8,
            step_9,
            step_10,
            step_11,
            step_12,
            step_13,
            step_14,
            step_15,
            step_16,
            step_17,
            step_18,
            step_19,
            step_20,
            step_21,
            step_22,
            step_23,
            step_24,
            end_step,
        ) = mock_complex_form_and_steps

        business_order, (
            begin_job,
            job_1,
            job_2,
            job_3,
            job_4,
            job_5,
            job_6,
            job_7,
            job_8,
            job_9,
            job_10,
            job_11,
            job_12,
            job_13,
            job_14,
            job_15,
            job_16,
            job_17,
            job_18,
            job_19,
            job_20,
            job_21,
            job_22,
            job_23,
            job_24,
            end_job,
        ) = mock_complex_business_order_and_jobs
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators

        # 1 -
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )

        ok, reason = bsc.can_upgrade()
        assert ok

        can_upgrade_steps = bsc.get_can_upgrade_steps()
        can_upgrade_step_ids = [step_info.step_id for step_info in can_upgrade_steps]
        assert step_1.id in can_upgrade_step_ids

        # 2 -
        business_order.job_history.append(job_1.id)
        flag_modified(business_order, "job_history")
        job_1.status = JobStatus.PENDING
        db.session.commit()
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )

        can_upgrade_steps = bsc.get_can_upgrade_steps()
        can_upgrade_step_ids = [step_info.step_id for step_info in can_upgrade_steps]
        assert step_1.id not in can_upgrade_step_ids

    def test_first_assistant_can_approve(
        self,
        mocker,
        mock_complex_form_and_steps,
        mock_complex_business_order_and_jobs,
        mock_operators,
    ):
        form, (
            begin_step,
            step_1,
            step_2,
            step_3,
            step_4,
            step_5,
            step_6,
            step_7,
            step_8,
            step_9,
            step_10,
            step_11,
            step_12,
            step_13,
            step_14,
            step_15,
            step_16,
            step_17,
            step_18,
            step_19,
            step_20,
            step_21,
            step_22,
            step_23,
            step_24,
            end_step,
        ) = mock_complex_form_and_steps

        business_order, (
            begin_job,
            job_1,
            job_2,
            job_3,
            job_4,
            job_5,
            job_6,
            job_7,
            job_8,
            job_9,
            job_10,
            job_11,
            job_12,
            job_13,
            job_14,
            job_15,
            job_16,
            job_17,
            job_18,
            job_19,
            job_20,
            job_21,
            job_22,
            job_23,
            job_24,
            end_job,
        ) = mock_complex_business_order_and_jobs
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators
        business_order.current_job_id = job_21.id
        business_order.set_status(BusinessOrderStatus.PENDING)

        # 1 -
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.approve,
            Action.overrule,
            Action.save,
            Action.pause,
            Action.complete,
            Action.close,
            Action.delete,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        # 2 -
        bbsc = BatchBusinessOrderStatusController(
            business_orders=[business_order], operator=first_assistant, is_admin=True
        )

        actions = bbsc.get_all_business_order_actions().get(business_order.id)
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.approve,
            Action.overrule,
            Action.save,
            Action.pause,
            Action.complete,
            Action.close,
            Action.delete,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        # 3 -
        business_order.set_status(BusinessOrderStatus.TO_BO_SUBMITTED)
        db.session.commit()
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )
        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.approve,
            Action.overrule,
            Action.save,
            Action.pause,
            Action.complete,
            Action.close,
            Action.delete,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

    def test_first_assistant_can_overrule(
        self,
        mocker,
        mock_complex_form_and_steps,
        mock_complex_business_order_and_jobs,
        mock_operators,
        step_retry_factory,
    ):
        form, (
            begin_step,
            step_1,
            step_2,
            step_3,
            step_4,
            step_5,
            step_6,
            step_7,
            step_8,
            step_9,
            step_10,
            step_11,
            step_12,
            step_13,
            step_14,
            step_15,
            step_16,
            step_17,
            step_18,
            step_19,
            step_20,
            step_21,
            step_22,
            step_23,
            step_24,
            end_step,
        ) = mock_complex_form_and_steps

        business_order, (
            begin_job,
            job_1,
            job_2,
            job_3,
            job_4,
            job_5,
            job_6,
            job_7,
            job_8,
            job_9,
            job_10,
            job_11,
            job_12,
            job_13,
            job_14,
            job_15,
            job_16,
            job_17,
            job_18,
            job_19,
            job_20,
            job_21,
            job_22,
            job_23,
            job_24,
            end_job,
        ) = mock_complex_business_order_and_jobs
        (buyer, first_assistant, second_assistant, first_leyan_account, first_ldap_account, rpa) = mock_operators
        business_order.current_job_id = job_21.id
        step_15.step_type = StepType.approve
        step_15.update_raw_step()
        business_order.set_status(BusinessOrderStatus.PENDING)

        # 1 -
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )

        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.approve,
            Action.save,
            Action.pause,
            Action.complete,
            Action.close,
            Action.delete,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names

        # 2 -
        step_retry_factory.create(step_uuid=step_15.step_uuid, can_retry=True)
        db.session.commit()
        bsc = BusinessOrderStatusController(
            business_order=business_order,
            operator=first_assistant,
        )
        actions = bsc.get_actions()
        action_names = [action for action, action_info in actions.items() if not action_info.disabled]
        action_names.sort()
        expected_action_names = [
            Action.approve,
            Action.overrule,
            Action.save,
            Action.pause,
            Action.complete,
            Action.close,
            Action.delete,
            Action.copy,
        ]
        expected_action_names.sort()
        assert action_names == expected_action_names
