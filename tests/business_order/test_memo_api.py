from faker import Faker
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import LogisticsInfo
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import PddTradeInfo
from leyan_proto.digismart.trade.dgt_trade_pb2 import GetTradesByTidListAndChannelResp
from pydantic_factories import ModelFactory
from pytest import fixture
from pytest import mark
from result import Ok

from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import BusinessOrderMemo
from robot_processor.ext import db

fake = Faker(locale="zh_CN")


@fixture
def client(gen_fresh_client):
    yield gen_fresh_client()


def test_business_order_not_found(client):
    """测试场景：工单不存在时，会提示错误"""
    bo_id = 999
    assert BusinessOrder.query.get(bo_id) is None, "随便找一个不存在的工单"
    response = client.get(f"/v1/business_orders/{bo_id}/memo")
    assert response.json["succeed"], "不过滤 GET 请求"
    response = client.post(f"/v1/business_orders/{bo_id}/memo", json={})
    assert not response.json["succeed"]
    assert response.json["error_display"] == "未找到工单"


@mark.order(1)
def test_save_and_sync_bo_memo_taobao(client, mocker, mock_business_order, mock_form):
    note = fake.text()
    client.get(f"/v1/shop/grant?user_nick={client.shop.nick}&access_token=234&open_id={client.shop.nick}")

    mock_business_order.sid = client.shop.sid
    mock_business_order.form_id = mock_form.id
    response = client.post(
        f"/v1/business_orders/{mock_business_order.id}/memo",
        json={
            "note": note,
            "sync_trade_flag": True,
        },
    )
    # 模板中没有订单组件
    assert not response.json["succeed"]
    assert response.json["error_display"] == "备注发表成功，同步至订单备注失败:当前工单模版无订单组件，无法同步订单备注"

    mocker.patch("robot_processor.function.trade.seller_memo.UpdateTradeSellerMemo.call", return_value=Ok(None))
    mock_tid_list = [{"tid": "123"}]
    mocker.patch(
        "robot_processor.business_order.api.memo_api.form_widget_wrapper",
        return_value=[{"key": "mock_key", "type": "order"}],
    )
    mock_business_order.data = {"mock_key": mock_tid_list}
    response = client.post(
        f"/v1/business_orders/{mock_business_order.id}/memo",
        json={
            "note": note,
            "sync_trade_flag": True,
        },
    )
    assert response.json["succeed"]
    assert mock_business_order.memo is not None


def test_save_and_sync_bo_memo_pdd(client, mocker, shop_factory, mock_business_order, mock_form):
    note = fake.text()
    shop = shop_factory(platform="PDD")
    mock_business_order.sid = shop.sid
    mock_business_order.form_id = mock_form.id
    mocker.patch("robot_processor.plugin.trade_api.pdd_order_address_use_api", return_value=True)
    mock_trade = GetTradesByTidListAndChannelResp()
    pdd_trade_info = PddTradeInfo(
        trade_id="220808-032096972432216",
        trade_type=2,
        create_time="2022-08-08 00:39:54",
        pay_time="2022-08-08 00:39:56",
        pay_amount=5.6,
        logistics_info=LogisticsInfo(
            receiver_name="name",
            receiver_phone="phone",
            province="province",
            city="city",
            town="town",
            address="address",
            logistics_id=1,
            logistics_name="logistics",
            tracking_number="",
            country="country",
        ),
    )
    trade_info = GetTradesByTidListAndChannelResp.TradeInfo(pdd_trade_info=pdd_trade_info)
    mock_trade.trade_info_list.extend([trade_info])
    mocker.patch("robot_processor.client.trade_client.get_trade_by_tid_and_channel", return_value=mock_trade)
    mocker.patch("robot_processor.function.trade.seller_memo.UpdateTradeSellerMemo.call", return_value=Ok(None))

    mock_tid_list = [{"tid": "123"}]
    mocker.patch(
        "robot_processor.business_order.api.memo_api.form_widget_wrapper",
        return_value=[{"key": "mock_key", "type": "order"}],
    )
    mock_business_order.data = {"mock_key": mock_tid_list}

    response = client.post(
        f"/v1/business_orders/{mock_business_order.id}/memo",
        json={
            "note": note,
            "sync_trade_flag": True,
        },
    )
    # 使用api
    assert response.json["succeed"]


def test_memo_create_fail_non_empty_content(client, mock_business_order):
    response = client.post(
        f"/v1/business_orders/{mock_business_order.id}/memo",
        json={},
    )
    assert not response.json["succeed"]
    assert response.json["error_display"] == "备注内容不能为空"


def test_memo_crud(client, mock_business_order):
    # create
    attachment = AttachmentFactory.build()
    note = fake.text()

    response = client.post(
        f"/v1/business_orders/{mock_business_order.id}/memo",
        json={
            "note": note,
            "attachments": [attachment.dict()],
        },
    )
    assert response.json["succeed"]
    assert mock_business_order.memo is not None

    # get
    response = client.get(f"/v1/business_orders/{mock_business_order.id}/memo")
    assert response.status_code == 200
    assert response.json["data"]["id"] == mock_business_order.id
    notes = response.json["data"]["notes"]
    assert len(notes) == 1
    assert notes[0]["note"] == note
    assert notes[0]["user"] == client.assistant.dict()

    # update
    new_attachment = AttachmentFactory.build()
    response = client.patch(
        f"/v1/business_orders/{mock_business_order.id}/memo",
        json={
            # 筛选条件
            "user": {"user_id": client.assistant.user_id, "user_type": client.assistant.user_type},
            "created_at": notes[0]["created_at"],
            # 修改内容
            "note": "随便改一下",
            "attachments": [new_attachment.dict()],
        },
    )
    assert response.json["succeed"]

    response = client.get(f"/v1/business_orders/{mock_business_order.id}/memo")
    assert response.status_code == 200
    notes = response.json["data"]["notes"]
    assert len(notes) == 1
    assert notes[0]["note"] == "随便改一下"

    # delete
    response = client.delete(
        f"/v1/business_orders/{mock_business_order.id}/memo",
        json={
            "user": {"user_id": client.assistant.user_id, "user_type": client.assistant.user_type},
            "created_at": notes[0]["created_at"],
        },
    )
    assert response.json["succeed"]

    response = client.get(f"/v1/business_orders/{mock_business_order.id}/memo")
    assert response.status_code == 200
    notes = response.json["data"]["notes"]
    assert len(notes) == 0


def test_update_failed_(client, mock_business_order):
    attachment = AttachmentFactory.build()
    note = fake.text()
    response = client.post(
        f"/v1/business_orders/{mock_business_order.id}/memo",
        json={
            "note": note,
            "attachments": [attachment.dict()],
        },
    )
    assert len(response.json["data"]["notes"]) == 1
    note = response.json["data"]["notes"][0]

    # 场景0: 无权限修改他人备注
    note_user = "某个用户"
    response = client.patch(
        f"/v1/business_orders/{mock_business_order.id}/memo",
        json={
            "user": {
                "user_id": client.assistant.user_id + 1,
                "user_type": client.assistant.user_type,
                "user_nick": note_user,
            },
            "created_at": note["created_at"],
        },
    )
    assert not response.json["succeed"]
    assert response.json["error_display"] == "当前用户{}无权限修改{}的备注".format(
        client.assistant.user_nick, note_user
    )

    memo = mock_business_order.memo
    request_body = {
        "user": {"user_id": client.assistant.user_id, "user_type": client.assistant.user_type},
        "created_at": note["created_at"],
    }

    # 场景1: user_id 错误
    note_user_id_error = note.copy()
    note_user_id_error["user"]["user_id"] = 0
    memo.notes = [note_user_id_error]
    db.session.commit()
    response = client.patch(f"/v1/business_orders/{mock_business_order.id}/memo", json=request_body)
    assert not response.json["succeed"]
    assert response.json["error_display"] == "未找到需要修改的备注信息"

    # 场景2: user_type 错误
    note_user_type_error = note.copy()
    note_user_type_error["user"]["user_type"] = 0
    memo.notes = [note_user_type_error]
    db.session.commit()
    response = client.patch(f"/v1/business_orders/{mock_business_order.id}/memo", json=request_body)
    assert not response.json["succeed"]
    assert response.json["error_display"] == "未找到需要修改的备注信息"

    # 场景3: created_at 错误
    note_created_at_error = note.copy()
    note_created_at_error["created_at"] = "一个错误的创建时间"
    memo.notes = [note_created_at_error]
    db.session.commit()
    response = client.patch(
        f"/v1/business_orders/{mock_business_order.id}/memo",
        json=request_body,
    )
    assert not response.json["succeed"]
    assert response.json["error_display"] == "未找到需要修改的备注信息"

    # 场景4: note 的状态为已删除时，不允许修改
    note_status_error = note.copy()
    note_status_error["status"] = "已删除"
    memo.notes = [note_status_error]
    db.session.commit()
    response = client.patch(
        f"/v1/business_orders/{mock_business_order.id}/memo",
        json={
            "user": {"user_id": client.assistant.user_id, "user_type": client.assistant.user_type},
            "created_at": note["created_at"],
        },
    )
    assert not response.json["succeed"]
    assert response.json["error_display"] == "未找到需要修改的备注信息"


class NoteFactory(ModelFactory):
    __model__ = BusinessOrderMemo.Schema.Note
    __faker__ = fake

    # created_at 是日期时间类型 YYYY-MM-DD HH:mm:ss
    created_at = lambda: str(fake.date_time())  # noqa: E731


class AttachmentFactory(ModelFactory):
    __model__ = BusinessOrderMemo.Schema.Attachment
    __faker__ = fake
