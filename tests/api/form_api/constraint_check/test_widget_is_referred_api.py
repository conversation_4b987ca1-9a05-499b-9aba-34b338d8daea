def test_get(client, mock_form, mock_step, mock_widget_collection, widget_info_factory, widget):
    mock_form.shop = client.shop
    mock_form.steps.append(mock_step)
    mock_step.widget_collection_id = mock_widget_collection.id
    mock_step.step_type = mock_step.step_type.human

    product_widget = widget('商品')
    string_widget = widget('单行输入')

    widget_info_factory.create(
        widget_id=product_widget.id,
        key="product_widget_uuid",
        widget_collection_id=mock_widget_collection.id,
        option_value={'label': '商品'}
    )

    widget_info_factory.create(
        widget_id=string_widget.id,
        key="text_widget_uuid",
        ref_config=[
            {
                "ref_key": "product_widget_uuid",
                "provider_label": "label",
                "provider_title": "title"
            }
        ],
        before=True,
        option_value={'label': '留言'},
        widget_collection_id=mock_widget_collection.id
    )

    response = client.get('/v1/forms/check-constraint/widget-refer-by-other',
                          query_string={
                              'form_id': mock_form.id,
                              'step_uuid': mock_step.step_uuid,
                              'widget_key': 'text_widget_uuid',
                          })
    assert response.json["succeed"]
