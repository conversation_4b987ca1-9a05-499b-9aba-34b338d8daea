import json
from io import BytesIO
from unittest.mock import MagicMock

import arrow
import requests
from flask import current_app
from google.protobuf.json_format import MessageToDict
from leyan_proto.digismart.buyer_server.buyer_server_rpc_pb2 import GetReceiverInfoResponse
from leyan_proto.digismart.dgt_common_pb2 import ChannelType
from leyan_proto.digismart.dgt_common_pb2 import DgtResponseCode
from leyan_proto.digismart.item.dgt_erp_item_pb2 import DgtGetErpSkuListResponse
from leyan_proto.digismart.item.dgt_item_pb2 import DgtGetItemListResponse
from leyan_proto.digismart.item.dgt_item_pb2 import DgtGetSpuBySpuIdResponse
from leyan_proto.digismart.item.dgt_item_pb2 import DgtProviderSkuResponse
from leyan_proto.digismart.item.dgt_item_pb2 import ItemInfo
from leyan_proto.digismart.item.dgt_item_pb2 import SkuInfo
from leyan_proto.digismart.trade.dgt_ks_trade_pb2 import CommonTradeInfo
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import LogisticsInfo
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import PddTradeInfo
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import TradeChannelInfo
from leyan_proto.digismart.trade.dgt_trade_pb2 import GetTradesByTidListAndChannelResp
from leyan_proto.digismart.trade.dgt_trade_pb2 import TradeInfo
from leyan_proto.digismart.trade.dgt_trade_pb2 import TradeList
from leyan_proto.digismart.trade.dgt_trade_pb2 import TradeRateResponse
from oss2.http import Response
from oss2.models import GetObjectResult
from oss2.models import PutObjectResult
from pytest import fixture
from pytest import mark
from result import Ok

from robot_processor.client.conf import oss_config
from robot_processor.client.errors import DoudianCloudServiceError
from robot_processor.client.errors import PddServiceError
from robot_processor.client.schema import DoudianReceiverInfoResp
from robot_processor.client.schema import PddBatchDecryptResponse
from robot_processor.client.schema import PddDecryptResponse
from robot_processor.enums import ErpType
from robot_processor.enums import StepType
from robot_processor.enums import UserType
from robot_processor.ext import db
from robot_processor.plugin.tasks import async_compress_image
from robot_processor.user_customize_config.models import UserCustomizeConfig
from rpa.doudian.schemas import Location
from rpa.doudian.schemas import PostAddr
from rpa.doudian.schemas import ReceiveDetail
from rpa.doudian.schemas import ReceiveInfo
from rpa.erp.jackyun.schemas import GoodsListResp as JackyunGoodsListResp
from rpa.erp.jackyun.schemas import OrderListResp as JackyunOrderListResp
from rpa.erp.jst import OrderAction
from rpa.erp.jst import OrderActionQueryResp
from rpa.erp.jst.schemas import InventoryQueryResp as JSTInventoryQueryResp
from rpa.erp.jst.schemas import JstBaseOrder
from rpa.erp.jst.schemas import QueryCombineSkuResp as JstQueryCombineSkuResp
from rpa.erp.jst.schemas import QuerySkuResp as JSTQuerySkuResp
from rpa.erp.jst.schemas import WmsPartnerQueryResp as JSTWmsPartnerQueryResp
from rpa.erp.wdt.schemas import QuerySkuResp as WdtQuerySkuResp
from rpa.erp.wdt.schemas import StockQueryResp as WdtStockQueryResp
from rpa.erp.wdt.schemas import TradeQueryResp
from rpa.erp.wdtulti.schemas import QuerySkuResp as WdtUltiQuerySkuResp
from rpa.erp.wdtulti.schemas import QueryStockResp as WdtUltiStockQueryResp


@fixture(autouse=True)
def mock_get_item_by_spu_id(mocker):
    yield mocker.patch(
        "robot_processor.client.item_client.client.GetItemBySpuId",
        return_value=ItemInfo(),
    )


JACKYUN_QUERY_TRADE_RESPONSE = """{
  "response": {
    "jackyunCode": "200",
    "jackyunData": {
      "trades": [
        {
          "flagIds": "",
          "flagNames": "售后,已成本核算",
          "goodsDetail": [
            {
              "barcode": "6976407860513",
              "customerPrice": "7.32",
              "divideSellTotal": "21.96",
              "goodsName": "金标松茸一品鲜（松茸调味汁）40g",
              "goodsNo": "03030040",
              "goodsPlatDiscountFee": "0",
              "isFit": "0",
              "isGift": "0",
              "platCode": "TMALL",
              "platGoodsId": "************",
              "platSkuId": "",
              "sellCount": "3",
              "sellPrice": "7.3217",
              "sellTotal": "21.97",
              "shareOrderDiscountFee": "0",
              "shareOrderPlatDiscountFee": "0",
              "sourceSubtradeNo": "",
              "specId": "1809634774062662784",
              "specName": "默认规格",
              "subTradeId": "2062386462611408512",
              "taxFee": "0"
            },
            {
              "barcode": "6976407860926",
              "customerPrice": "17.94",
              "divideSellTotal": "17.94",
              "goodsName": "松茸调味料35g（香松）",
              "goodsNo": "********",
              "goodsPlatDiscountFee": "0",
              "isFit": "0",
              "isGift": "0",
              "platCode": "TMALL",
              "platGoodsId": "************",
              "platSkuId": "",
              "sellCount": "1",
              "sellPrice": "17.9351",
              "sellTotal": "17.94",
              "shareOrderDiscountFee": "0",
              "shareOrderPlatDiscountFee": "0",
              "sourceSubtradeNo": "",
              "specId": "1956745701322916736",
              "specName": "35克/袋",
              "subTradeId": "2062386462611408513",
              "taxFee": "0"
            }
          ],
          "isDelete": "0",
          "logisticName": "郎溪仓邮政快递",
          "mainPostid": "9876700080731",
          "onlineTradeNo": "4076103709860479734",
          "packageWeight": "244",
          "realFee": "0",
          "sellerMemo": "补发",
          "shopCode": "02",
          "shopId": 1819610582755115500,
          "shopName": "ds-天猫-松鲜鲜调味品旗舰店",
          "shopTypeCode": "TMALL",
          "sourceAfterNo": "SH202410160149",
          "tradeId": "2062386462435247744",
          "tradeNo": "SXX20241016010686",
          "tradeOrderAssemblyGoodsDtoList": [],
          "tradeType": "7",
          "warehouseCode": "1002",
          "warehouseName": "安徽郎溪成品-公司仓-自营"
        },
        {
          "flagIds": "",
          "flagNames": "同名未合并",
          "goodsDetail": [
            {
              "barcode": "6976407860513",
              "customerPrice": "0",
              "divideSellTotal": "0.01",
              "goodsName": "金标松茸一品鲜（松茸调味汁）40g",
              "goodsNo": "03030040",
              "goodsPlatDiscountFee": "0",
              "isFit": "0",
              "isGift": "0",
              "platCode": "TMALL",
              "platGoodsId": "************",
              "platSkuId": "",
              "sellCount": "3",
              "sellPrice": "7.3217",
              "sellTotal": "0.01",
              "shareOrderDiscountFee": "0",
              "shareOrderPlatDiscountFee": "0",
              "sourceSubtradeNo": "4076103709860479734",
              "specId": "1809634774062662784",
              "specName": "默认规格",
              "subTradeId": "2059398756149953409",
              "taxFee": "0",
              "tradeGoodsNo": "tmrhlb"
            },
            {
              "barcode": "6976407860926",
              "customerPrice": "0",
              "divideSellTotal": "0",
              "goodsName": "松茸调味料35g（香松）",
              "goodsNo": "********",
              "goodsPlatDiscountFee": "0",
              "isFit": "0",
              "isGift": "0",
              "platCode": "TMALL",
              "platGoodsId": "************",
              "platSkuId": "",
              "sellCount": "1",
              "sellPrice": "17.9351",
              "sellTotal": "0",
              "shareOrderDiscountFee": "0",
              "shareOrderPlatDiscountFee": "0",
              "sourceSubtradeNo": "4076103709860479734",
              "specId": "1956745701322916736",
              "specName": "35克/袋",
              "subTradeId": "2059398756149953408",
              "taxFee": "0",
              "tradeGoodsNo": "tmrhlb"
            }
          ],
          "isDelete": "0",
          "logisticName": "南京仓库韵达快递",
          "mainPostid": "",
          "onlineTradeNo": "4076103709860479734",
          "packageWeight": "0",
          "realFee": "0",
          "sellerMemo": "退6.9元【可可｜10-12 10:17:20】拦截已反馈工单—小雨10-16 13:12已补发小雨10-16 13:13|追加： 补发; 2024-10-16 15:44:15|退6.9元【可可｜10-12 10:17:20】拦截已反馈工单—小雨10-16 13:12已补发小雨10-16 13:13 2024-10-16 13:13:52|退6.9元【可可｜10-12 10:17:20】拦截已反馈工单—小雨10-16 13:12 2024-10-16 13:12:36|退6.9元【可可｜10-12 10:17:20】",
          "shopCode": "02",
          "shopId": 1819610582755115500,
          "shopName": "ds-天猫-松鲜鲜调味品旗舰店",
          "shopTypeCode": "TMALL",
          "tradeId": "2059398729397732864",
          "tradeNo": "SXX20241012004824",
          "tradeOrderAssemblyGoodsDtoList": [],
          "tradeType": "1",
          "warehouseCode": "1001",
          "warehouseName": "南京仓库成品-公司仓-自营"
        },
        {
          "flagIds": "",
          "flagNames": "改货品,有赠品,修改,合并,物流上传成功,已成本核算,部分退款成功",
          "goodsDetail": [
            {
              "barcode": "6976407860551",
              "customerPrice": "0",
              "divideSellTotal": "35.9",
              "goodsName": "金标松茸鲜酱油490mL",
              "goodsNo": "03030041",
              "goodsPlatDiscountFee": "0",
              "isFit": "0",
              "isGift": "0",
              "platCode": "TMALL",
              "platGoodsId": "************",
              "platSkuId": "5437268376493",
              "sellCount": "2",
              "sellPrice": "34.95",
              "sellTotal": "35.9",
              "shareOrderDiscountFee": "0",
              "shareOrderPlatDiscountFee": "0",
              "sourceSubtradeNo": "4076300450566479734",
              "specId": "1811096732527297792",
              "specName": "默认规格",
              "subTradeId": "2059398796121670528",
              "taxFee": "0",
              "tradeGoodsNo": "jbsrxjy-2"
            },
            {
              "barcode": "6976407860513",
              "customerPrice": "2",
              "goodsName": "金标松茸一品鲜（松茸调味汁）40g",
              "goodsNo": "03030040",
              "goodsPlatDiscountFee": "0",
              "isFit": "0",
              "isGift": "1",
              "platCode": "TMALL",
              "platGoodsId": "",
              "platSkuId": "",
              "sellCount": "2",
              "sellPrice": "2",
              "sellTotal": "0",
              "shareOrderDiscountFee": "0",
              "shareOrderPlatDiscountFee": "0",
              "specId": "1809634774062662784",
              "specName": "默认规格",
              "subTradeId": "2059398796121670529",
              "taxFee": "0"
            },
            {
              "barcode": "6976407860513",
              "customerPrice": "0",
              "divideSellTotal": "0.01",
              "goodsName": "金标松茸一品鲜（松茸调味汁）40g",
              "goodsNo": "03030040",
              "goodsPlatDiscountFee": "0",
              "isFit": "0",
              "isGift": "0",
              "platCode": "TMALL",
              "platGoodsId": "************",
              "platSkuId": "",
              "sellCount": "3",
              "sellPrice": "7.3217",
              "sellTotal": "0.01",
              "shareOrderDiscountFee": "0",
              "shareOrderPlatDiscountFee": "0",
              "sourceSubtradeNo": "4076103709860479734",
              "specId": "1809634774062662784",
              "specName": "默认规格",
              "subTradeId": "2059398796121670530",
              "taxFee": "0",
              "tradeGoodsNo": "tmrhlb"
            },
            {
              "barcode": "6976407860926",
              "customerPrice": "0",
              "goodsName": "松茸调味料35g（香松）",
              "goodsNo": "********",
              "goodsPlatDiscountFee": "0",
              "isFit": "0",
              "isGift": "0",
              "platCode": "TMALL",
              "platGoodsId": "************",
              "platSkuId": "",
              "sellCount": "1",
              "sellPrice": "17.9351",
              "sellTotal": "0",
              "shareOrderDiscountFee": "0",
              "shareOrderPlatDiscountFee": "0",
              "sourceSubtradeNo": "4076103709860479734",
              "specId": "1956745701322916736",
              "specName": "35克/袋",
              "subTradeId": "2059398796121670531",
              "taxFee": "0",
              "tradeGoodsNo": "tmrhlb"
            }
          ],
          "isDelete": "0",
          "logisticName": "南京仓圆通快递",
          "mainPostid": "YT7498182717456",
          "onlineTradeNo": "4076300450566479734,4076103709860479734",
          "packageWeight": "1690",
          "realFee": "0",
          "sellerMemo": "退6.9元【可可｜10-12 10:17:20】拦截已反馈工单—小雨10-16 13:12已补发小雨10-16 13:13|追加： 补发; 2024-10-16 15:44:15|退6.9元【可可｜10-12 10:17:20】拦截已反馈工单—小雨10-16 13:12已补发小雨10-16 13:13 2024-10-16 13:13:52|退6.9元【可可｜10-12 10:17:20】拦截已反馈工单—小雨10-16 13:12 2024-10-16 13:12:36|内网已拦截10-13 22:09拦截成功10-14 07:10内网已拦截10-14 07:14拦截成功10-14 07:14 2024-10-14 07:14:52|内网已拦截10-13 22:09拦截成功10-14 07:10内网已拦截10-14 07:14 2024-10-14 07:14:46|内网已拦截10-13 22:09拦截成功10-14 07:10 2024-10-14 07:10:44|内网已拦截10-13 22:09 2024-10-13 22:09:59|退6.9元【可可｜10-12 10:17:20】",
          "shopCode": "02",
          "shopId": 1819610582755115500,
          "shopName": "ds-天猫-松鲜鲜调味品旗舰店",
          "shopTypeCode": "TMALL",
          "tradeId": "2059398795945509762",
          "tradeNo": "SXX20241012004826",
          "tradeOrderAssemblyGoodsDtoList": [],
          "tradeType": "1",
          "warehouseCode": "1001",
          "warehouseName": "南京仓库成品-公司仓-自营"
        }
      ]
    },
    "jackyunFlag": "success",
    "jackyunMessage": "success"
  }
}"""  # noqa

JACKYUN_QUERY_SINGLE_FIRST_RESPONSE = """{
  "code": 200,
  "developerInfo": "",
  "msg": "操作成功",
  "result": {
    "contextId": "2063172831130061570",
    "data": {
      "goods": [
        {
          "relDataId": null,
          "goodsField1": "安徽香松自然调味品有限公司",
          "goodsField2": "********",
          "goodsField3": null,
          "goodsField4": null,
          "goodsField5": null,
          "goodsField6": null,
          "goodsField7": null,
          "goodsField8": null,
          "goodsField9": null,
          "goodsField10": null,
          "goodsField11": null,
          "goodsField12": null,
          "goodsField13": null,
          "goodsField14": null,
          "goodsField15": null,
          "goodsField16": null,
          "goodsField17": null,
          "goodsField18": null,
          "goodsField19": null,
          "goodsField20": null,
          "goodsField21": null,
          "goodsField22": null,
          "goodsField23": null,
          "goodsField24": null,
          "goodsField25": null,
          "goodsField26": null,
          "goodsField27": null,
          "goodsField28": null,
          "goodsField29": null,
          "goodsField30": null,
          "goodsMemo": null,
          "memo": null,
          "goodsId": "1956745569650771456",
          "goodsNo": "********",
          "goodsName": "松茸调味料35g（香松）",
          "goodsNameEn": null,
          "skuName": "35克/袋",
          "skuId": "1956745701322916736",
          "skuBarcode": "6976407860926",
          "unitName": "袋",
          "skuCode": "********",
          "isPackageGood": 0,
          "isDelete": 0,
          "goodsAttr": 1,
          "retailPrice": 0.887,
          "cateId": 10,
          "cateCode": "301",
          "cateName": "调味料",
          "brandId": 1,
          "brandName": "松鲜鲜",
          "goodsDesc": null,
          "goodsAlias": null,
          "imgUrlList": null,
          "skuImgUrl": null,
          "shelfLife": "18.0000",
          "shelfLiftUnit": "month",
          "isBlockup": 0,
          "skuIsBlockup": 0,
          "abcCate": null,
          "skuLength": 10,
          "skuWidth": 14,
          "skuHeight": 2,
          "skuWeight": 46.9,
          "volume": 280,
          "colorCode": null,
          "colorName": null,
          "sizeCode": null,
          "sizeName": null,
          "isBatchMgmt": 1,
          "isPickupCard": 0,
          "isPaidService": 0,
          "warehouseId": "1718754725470601856",
          "warehouseName": "安徽郎溪成品-公司仓-自营",
          "defaultVendId": null,
          "defaultVendName": null,
          "gmtCreate": 1716462264000,
          "goodsGmtModified": 1725678000000,
          "skuGmtCreate": 1716462264000,
          "skuGmtModified": 1729149366000,
          "isSerialManagement": 0,
          "cateFullName": "成品/调味料",
          "skuField1": "150袋/箱，毛重：6.84kg，尺寸：41*34*23cm³",
          "skuField2": null,
          "skuField3": null,
          "skuField4": null,
          "skuField5": null,
          "skuField6": null,
          "skuField7": null,
          "skuField8": null,
          "skuField9": null,
          "skuField10": null,
          "skuField11": null,
          "skuField12": null,
          "skuField13": null,
          "skuField14": null,
          "skuField15": null,
          "skuField16": null,
          "skuField17": null,
          "skuField18": null,
          "skuField19": null,
          "skuField20": null,
          "skuField21": null,
          "skuField22": null,
          "skuField23": null,
          "skuField24": null,
          "skuField25": null,
          "skuField26": null,
          "skuField27": null,
          "skuField28": null,
          "skuField29": null,
          "skuField30": null,
          "flagData": null,
          "skuNo": null,
          "ownerType": 0,
          "ownerName": "杭州松鲜鲜自然调味品有限公司",
          "mainBarcode": "6976407860926",
          "goodsUnit": [
            {
              "id": 13780,
              "unitName": "袋",
              "countRate": 1,
              "goodsId": "1956745569650771456",
              "pBaseUnit": 1,
              "unitId": null,
              "baseCountRate": 1,
              "assistCountRate": 1,
              "type": null,
              "unitMainBarcode": null,
              "unitWeight": null,
              "unitWeightUnit": null,
              "baseWeight": null,
              "unitLength": null,
              "unitWidth": null,
              "unitHeight": null,
              "unitVolume": null,
              "unitVolumeUnit": null,
              "baseUnitName": null,
              "gmtModified": null,
              "goodsUnitBusinessList": [],
              "skuId": null,
              "isBaseUnit": 1
            }
          ],
          "lastPurchPrice": 0.84,
          "goodsInfoDescript": null,
          "materialCode": null,
          "materialName": null,
          "goodsFileList": [
            {
              "fileId": "1956745701272585089",
              "relDataId": "1956745569650771456",
              "fileName": "调味料35g-中封.jpg",
              "fileKey": "longterm/1285398371469492608/system/erp/1431916585947103488/1546292349769319296.jpg",
              "fileUrl": "https://jkyun.oss-cn-hangzhou.aliyuncs.com/longterm/1285398371469492608/system/erp/1431916585947103488/1546292349769319296.jpg?Expires=4821132413&OSSAccessKeyId=LTAI5tPkb173kAgKZCTXcZWt&Signature=5DOE%2FyO5JxXMpbEGDavjpoVUiKw%3D"
            },
            {
              "fileId": "1956745701272585090",
              "relDataId": "1956745569650771456",
              "fileName": "调味料35g.jpg",
              "fileKey": "longterm/1285398371469492608/system/erp/1431916585947103488/1529718929245012480.jpg",
              "fileUrl": "https://jkyun.oss-cn-hangzhou.aliyuncs.com/longterm/1285398371469492608/system/erp/1431916585947103488/1529718929245012480.jpg?Expires=4819156707&OSSAccessKeyId=LTAI5tPkb173kAgKZCTXcZWt&Signature=hsnh%2FDXW6WUtiDXc4WIypaJsEeA%3D"
            }
          ],
          "shelfLiftUnitName": "月"
        },
        {
          "relDataId": null,
          "goodsField1": "",
          "goodsField2": "",
          "goodsField3": null,
          "goodsField4": null,
          "goodsField5": null,
          "goodsField6": null,
          "goodsField7": null,
          "goodsField8": null,
          "goodsField9": null,
          "goodsField10": null,
          "goodsField11": null,
          "goodsField12": null,
          "goodsField13": null,
          "goodsField14": null,
          "goodsField15": null,
          "goodsField16": null,
          "goodsField17": null,
          "goodsField18": null,
          "goodsField19": null,
          "goodsField20": null,
          "goodsField21": null,
          "goodsField22": null,
          "goodsField23": null,
          "goodsField24": null,
          "goodsField25": null,
          "goodsField26": null,
          "goodsField27": null,
          "goodsField28": null,
          "goodsField29": null,
          "goodsField30": null,
          "goodsMemo": null,
          "memo": null,
          "goodsId": "1971729772549637632",
          "goodsNo": "*********2",
          "goodsName": "松茸调味料35g（香松）*2袋",
          "goodsNameEn": null,
          "skuName": "默认规格",
          "skuId": "1971729772566414848",
          "skuBarcode": null,
          "unitName": "盒",
          "skuCode": null,
          "isPackageGood": 1,
          "isDelete": 0,
          "goodsAttr": 1,
          "retailPrice": null,
          "cateId": 7,
          "cateCode": "3",
          "cateName": "成品",
          "brandId": null,
          "brandName": null,
          "goodsDesc": null,
          "goodsAlias": null,
          "imgUrlList": null,
          "skuImgUrl": null,
          "shelfLife": null,
          "shelfLiftUnit": null,
          "isBlockup": 0,
          "skuIsBlockup": 0,
          "abcCate": null,
          "skuLength": null,
          "skuWidth": null,
          "skuHeight": null,
          "skuWeight": 93.8,
          "volume": null,
          "colorCode": null,
          "colorName": null,
          "sizeCode": null,
          "sizeName": null,
          "isBatchMgmt": 0,
          "isPickupCard": null,
          "isPaidService": null,
          "warehouseId": null,
          "warehouseName": null,
          "defaultVendId": null,
          "defaultVendName": null,
          "gmtCreate": 1718248505000,
          "goodsGmtModified": 1724398110000,
          "skuGmtCreate": 1718248505000,
          "skuGmtModified": 1718248505000,
          "isSerialManagement": 0,
          "cateFullName": "成品",
          "skuField1": null,
          "skuField2": null,
          "skuField3": null,
          "skuField4": null,
          "skuField5": null,
          "skuField6": null,
          "skuField7": null,
          "skuField8": null,
          "skuField9": null,
          "skuField10": null,
          "skuField11": null,
          "skuField12": null,
          "skuField13": null,
          "skuField14": null,
          "skuField15": null,
          "skuField16": null,
          "skuField17": null,
          "skuField18": null,
          "skuField19": null,
          "skuField20": null,
          "skuField21": null,
          "skuField22": null,
          "skuField23": null,
          "skuField24": null,
          "skuField25": null,
          "skuField26": null,
          "skuField27": null,
          "skuField28": null,
          "skuField29": null,
          "skuField30": null,
          "flagData": null,
          "skuNo": null,
          "ownerType": 0,
          "ownerName": "杭州挚先食品科技有限公司",
          "mainBarcode": null,
          "goodsUnit": [
            {
              "id": 14663,
              "unitName": "盒",
              "countRate": 1,
              "goodsId": "1971729772549637632",
              "pBaseUnit": 1,
              "unitId": null,
              "baseCountRate": 1,
              "assistCountRate": 1,
              "type": null,
              "unitMainBarcode": null,
              "unitWeight": null,
              "unitWeightUnit": null,
              "baseWeight": null,
              "unitLength": null,
              "unitWidth": null,
              "unitHeight": null,
              "unitVolume": null,
              "unitVolumeUnit": null,
              "baseUnitName": null,
              "gmtModified": null,
              "goodsUnitBusinessList": [],
              "skuId": null,
              "isBaseUnit": 1
            }
          ],
          "lastPurchPrice": null,
          "goodsInfoDescript": null,
          "materialCode": null,
          "materialName": null,
          "goodsFileList": null,
          "shelfLiftUnitName": null
        }
      ]
    },
    "desensitizationItem": [],
    "handlerInfo": null,
    "noPrivilegeItem": null,
    "pageInfo": {
      "expand": null,
      "offset": 0,
      "pageIndex": 0,
      "pageSize": 100,
      "sortField": "",
      "sortOrder": "ASC",
      "total": 2
    }
  },
  "subCode": "0030000004"
}"""  # noqa


JACKYUN_QUERY_SINGLE_SECOND_RESPONSE = """{
  "code": 200,
  "developerInfo": "",
  "msg": "操作成功",
  "result": {
    "contextId": "2063172831601002498",
    "data": {
      "goods": [
        {
          "relDataId": null,
          "goodsField1": null,
          "goodsField2": "03030040",
          "goodsField3": null,
          "goodsField4": null,
          "goodsField5": null,
          "goodsField6": null,
          "goodsField7": null,
          "goodsField8": null,
          "goodsField9": null,
          "goodsField10": null,
          "goodsField11": null,
          "goodsField12": null,
          "goodsField13": null,
          "goodsField14": null,
          "goodsField15": null,
          "goodsField16": null,
          "goodsField17": null,
          "goodsField18": null,
          "goodsField19": null,
          "goodsField20": null,
          "goodsField21": null,
          "goodsField22": null,
          "goodsField23": null,
          "goodsField24": null,
          "goodsField25": null,
          "goodsField26": null,
          "goodsField27": null,
          "goodsField28": null,
          "goodsField29": null,
          "goodsField30": null,
          "goodsMemo": null,
          "memo": null,
          "goodsId": "1809634773987165312",
          "goodsNo": "03030040",
          "goodsName": "金标松茸一品鲜（松茸调味汁）40g",
          "goodsNameEn": null,
          "skuName": "默认规格",
          "skuId": "1809634774062662784",
          "skuBarcode": "6976407860513",
          "unitName": "包",
          "skuCode": "03030040",
          "isPackageGood": 0,
          "isDelete": 0,
          "goodsAttr": 1,
          "retailPrice": 0.4565,
          "cateId": 84,
          "cateCode": "309",
          "cateName": "酱油",
          "brandId": 1,
          "brandName": "松鲜鲜",
          "goodsDesc": null,
          "goodsAlias": null,
          "imgUrlList": null,
          "skuImgUrl": null,
          "shelfLife": "12.0000",
          "shelfLiftUnit": "month",
          "isBlockup": 0,
          "skuIsBlockup": 0,
          "abcCate": null,
          "skuLength": 10,
          "skuWidth": 14,
          "skuHeight": 1,
          "skuWeight": 45,
          "volume": 140,
          "colorCode": null,
          "colorName": null,
          "sizeCode": null,
          "sizeName": null,
          "isBatchMgmt": 1,
          "isPickupCard": 0,
          "isPaidService": 0,
          "warehouseId": null,
          "warehouseName": null,
          "defaultVendId": null,
          "defaultVendName": null,
          "gmtCreate": 1698925275000,
          "goodsGmtModified": 1725696935000,
          "skuGmtCreate": 1698925275000,
          "skuGmtModified": 1729149358000,
          "isSerialManagement": 0,
          "cateFullName": "成品/酱油",
          "skuField1": "300包/箱，总重：15.5kg，尺寸：42.9*37.8*19cm³",
          "skuField2": null,
          "skuField3": null,
          "skuField4": null,
          "skuField5": null,
          "skuField6": null,
          "skuField7": null,
          "skuField8": null,
          "skuField9": null,
          "skuField10": null,
          "skuField11": null,
          "skuField12": null,
          "skuField13": null,
          "skuField14": null,
          "skuField15": null,
          "skuField16": null,
          "skuField17": null,
          "skuField18": null,
          "skuField19": null,
          "skuField20": null,
          "skuField21": null,
          "skuField22": null,
          "skuField23": null,
          "skuField24": null,
          "skuField25": null,
          "skuField26": null,
          "skuField27": null,
          "skuField28": null,
          "skuField29": null,
          "skuField30": null,
          "flagData": null,
          "skuNo": null,
          "ownerType": 0,
          "ownerName": "杭州松鲜鲜自然调味品有限公司",
          "mainBarcode": null,
          "goodsUnit": [
            {
              "id": 17089,
              "unitName": "包",
              "countRate": 1,
              "goodsId": "1809634773987165312",
              "pBaseUnit": 1,
              "unitId": null,
              "baseCountRate": 1,
              "assistCountRate": 1,
              "type": null,
              "unitMainBarcode": null,
              "unitWeight": null,
              "unitWeightUnit": null,
              "baseWeight": null,
              "unitLength": null,
              "unitWidth": null,
              "unitHeight": null,
              "unitVolume": null,
              "unitVolumeUnit": null,
              "baseUnitName": null,
              "gmtModified": null,
              "goodsUnitBusinessList": [],
              "skuId": null,
              "isBaseUnit": 1
            }
          ],
          "lastPurchPrice": 0.18,
          "goodsInfoDescript": null,
          "materialCode": null,
          "materialName": null,
          "goodsFileList": null,
          "shelfLiftUnitName": "月"
        }
      ]
    },
    "desensitizationItem": [],
    "handlerInfo": null,
    "noPrivilegeItem": null,
    "pageInfo": {
      "expand": null,
      "offset": 0,
      "pageIndex": 0,
      "pageSize": 100,
      "sortField": "",
      "sortOrder": "ASC",
      "total": 1
    }
  },
  "subCode": "0030000004"
}"""  # noqa

WDT_QUERY_TRADE_RESPONSE = """{
  "response": {
    "errorcode": 0,
    "message": "ok",
    "total_count": 2,
    "trades": [
      {
        "bad_reason": 0,
        "buyer_message": "",
        "buyer_message_count": "0",
        "buyer_nick": "",
        "cancel_reason": "0",
        "check_step": "0",
        "checker_id": 167,
        "checker_name": "陈纯子",
        "checkouter_id": 0,
        "checkouter_name": "",
        "cod_amount": "0.0000",
        "commission": "0.0000",
        "consign_status": 12,
        "created": "2024-10-13 11:57:53",
        "cs_remark": "10-15 19:35已补发10-16 12:01",
        "cs_remark_change_count": "1",
        "cs_remark_count": "1",
        "currency": "",
        "customer_id": "6400322",
        "customer_name": "",
        "customer_no": "KH202410132411",
        "customer_type": "0",
        "dap_amount": "34.8000",
        "delay_to_time": "0",
        "delivery_term": 1,
        "discount": "56.8000",
        "discount_change": "0.0000",
        "ext_cod_fee": "0.0000",
        "fchecker_id": 0,
        "fchecker_name": "系统",
        "fenxiao_nick": "",
        "fenxiao_tid": "",
        "fenxiao_type": 0,
        "flag_id": "0",
        "flag_name": "",
        "freeze_reason": 0,
        "freeze_reason_info": "",
        "fullname": "系统",
        "gift_mask": "0",
        "goods_amount": "91.6000",
        "goods_cost": "18.9000",
        "goods_count": "1.0000",
        "goods_list": [
          {
            "actual_num": "1.0000",
            "adjust": "0.0000",
            "api_goods_name": "【上新价】婴儿玩具0一1岁宝宝早教益智2抬头训练神器跳舞4新生幼儿6个月龄3",
            "api_spec_name": "龙年限定款（48首歌）礼盒装【充电版】",
            "barcode": "735665D",
            "base_unit_id": 0,
            "bind_oid": "",
            "cid": 0,
            "class_name": "电商件",
            "commission": "0.0000",
            "created": "2024-10-13 11:57:53",
            "delivery_term": 1,
            "discount": "56.8000",
            "flag": 0,
            "from_mask": 1,
            "gift_type": 0,
            "goods_id": 4471,
            "goods_name": "跳舞48首小龙人（彩盒+邮购）",
            "goods_no": "735665D",
            "goods_type": 1,
            "guarantee_mode": "1",
            "invoice_content": "",
            "invoice_type": 0,
            "is_consigned": "1",
            "is_master": "1",
            "is_print_suite": "0",
            "is_received": "0",
            "is_zero_cost": "1",
            "large_type": 0,
            "modified": "2024-10-13 16:19:00",
            "num": "1.0000",
            "order_price": "34.8000",
            "paid": "34.8000",
            "pay_id": "2024101322001168361443937307",
            "pay_status": "2",
            "pay_time": "2024-10-13 11:57:42",
            "platform_goods_id": "714843791375",
            "platform_id": 1,
            "platform_spec_id": "5242231757447",
            "price": "91.6000",
            "prop2": "2023-12-8",
            "rec_id": 30281440,
            "refund_num": "0.0000",
            "refund_status": 0,
            "remark": "",
            "share_amount": "34.8000",
            "share_amount2": "0.0000",
            "share_post": "0.0000",
            "share_price": "34.8000",
            "spec_code": "",
            "spec_id": 4404,
            "spec_name": "跳舞48首小龙人（彩盒+邮购）",
            "spec_no": "735-665D升级充电版",
            "src_oid": "*******************",
            "src_tid": "*******************",
            "stock_reserved": "0",
            "suite_amount": "0.0000",
            "suite_discount": "0.0000",
            "suite_id": 0,
            "suite_name": "",
            "suite_no": "",
            "suite_num": "0.0000",
            "tax_rate": "0.0000",
            "tc_order_id": "",
            "trade_id": 9461991,
            "unit_name": "",
            "weight": "0.3760"
          }
        ],
        "goods_type_count": 1,
        "id_card": "",
        "id_card_type": 0,
        "invoice_content": "",
        "invoice_id": 0,
        "invoice_title": "",
        "invoice_type": 0,
        "is_prev_notify": "0",
        "is_sealed": "0",
        "is_unpayment_sms": "0",
        "large_type": "0",
        "logistics_code": "04",
        "logistics_id": 90,
        "logistics_name": "澄海云仓-圆通",
        "logistics_no": "***************",
        "logistics_template_id": "0",
        "logistics_type": 4,
        "modified": "2024-10-16 12:02:16",
        "note_count": "0",
        "other_amount": "0.0000",
        "other_cost": "0.0000",
        "package_id": "0",
        "paid": "34.8000",
        "pay_account": "",
        "pay_time": "2024-10-13 11:57:42",
        "pi_amount": "0.0000",
        "platform_id": 1,
        "post_amount": "0.0000",
        "post_cost": "2.1000",
        "pre_charge_time": "",
        "print_remark": "",
        "profit": "13.8000",
        "raw_goods_count": "1.0000",
        "raw_goods_type_count": 1,
        "receivable": "34.8000",
        "receiver_address": "",
        "receiver_area": "浙江省 杭州市 萧山区",
        "receiver_city": 330100,
        "receiver_country": "0",
        "receiver_district": 330109,
        "receiver_dtb": "杭州市 萧山区",
        "receiver_mobile": "",
        "receiver_name": "",
        "receiver_province": "330000",
        "receiver_ring": "大**",
        "receiver_telno": "",
        "receiver_zip": "000000",
        "refund_status": 0,
        "remark_flag": 0,
        "reserve": "",
        "revert_reason": "0",
        "sales_score": "0",
        "salesman_id": 0,
        "sendbill_template_id": "0",
        "shop_id": "11",
        "shop_name": "遥蓝旗舰店",
        "shop_no": "010",
        "shop_platform_id": "1",
        "shop_remark": "",
        "single_spec_no": "735-665D升级充电版",
        "split_from_trade_id": "0",
        "split_package_num": "0",
        "src_tids": "*******************",
        "stockout_no": "CK202410134300",
        "tags": "",
        "tax": "0.0000",
        "tax_rate": "0.0000",
        "to_deliver_time": "",
        "trade_from": 1,
        "trade_id": 9461991,
        "trade_mask": "128",
        "trade_no": "JY202410132727",
        "trade_prepay": "0.0000",
        "trade_status": 95,
        "trade_time": "2024-10-13 11:57:36",
        "trade_type": 1,
        "unmerge_mask": "0",
        "version_id": 6,
        "volume": "0.0000",
        "warehouse_id": "10",
        "warehouse_no": "006",
        "warehouse_type": 20,
        "weight": "0.3760"
      },
      {
        "bad_reason": 0,
        "buyer_message": "",
        "buyer_message_count": "0",
        "buyer_nick": "",
        "cancel_reason": "0",
        "check_step": "0",
        "checker_id": 167,
        "checker_name": "陈纯子",
        "checkouter_id": 0,
        "checkouter_name": "",
        "cod_amount": "0.0000",
        "commission": "0.0000",
        "consign_status": 7,
        "created": "2024-10-15 19:35:16",
        "cs_remark": "",
        "cs_remark_change_count": "0",
        "cs_remark_count": "0",
        "currency": "",
        "customer_id": "6400322",
        "customer_name": "",
        "customer_no": "KH202410132411",
        "customer_type": "0",
        "dap_amount": "0.0000",
        "delay_to_time": "0",
        "delivery_term": 1,
        "discount": "0.0000",
        "discount_change": "0.0000",
        "ext_cod_fee": "0.0000",
        "fchecker_id": 0,
        "fchecker_name": "系统",
        "fenxiao_nick": "",
        "fenxiao_tid": "",
        "fenxiao_type": 0,
        "flag_id": "0",
        "flag_name": "",
        "freeze_reason": 0,
        "freeze_reason_info": "",
        "fullname": "外部接口",
        "gift_mask": "0",
        "goods_amount": "0.0000",
        "goods_cost": "2.0943",
        "goods_count": "2.0000",
        "goods_list": [
          {
            "actual_num": "1.0000",
            "adjust": "0.0000",
            "api_goods_name": "充电器-5号/7号",
            "api_spec_name": "",
            "barcode": "Z0019",
            "base_unit_id": 1,
            "bind_oid": "",
            "cid": 0,
            "class_name": "无",
            "commission": "0.0000",
            "created": "2024-10-15 19:35:16",
            "delivery_term": 1,
            "discount": "0.0000",
            "flag": 0,
            "from_mask": 131072,
            "gift_type": 0,
            "goods_id": 1458,
            "goods_name": "充电器-5号/7号",
            "goods_no": "-57",
            "goods_type": 1,
            "guarantee_mode": "2",
            "invoice_content": "",
            "invoice_type": 0,
            "is_consigned": "0",
            "is_master": "1",
            "is_print_suite": "0",
            "is_received": "0",
            "is_zero_cost": "1",
            "large_type": 0,
            "modified": "2024-10-16 15:07:14",
            "num": "1.0000",
            "order_price": "0.0000",
            "paid": "0.0000",
            "pay_id": "",
            "pay_status": "2",
            "pay_time": "2024-10-13 11:57:42",
            "platform_goods_id": "1458",
            "platform_id": 0,
            "platform_spec_id": "1458",
            "price": "0.0000",
            "prop2": "",
            "rec_id": 30313304,
            "refund_num": "0.0000",
            "refund_status": 0,
            "remark": "",
            "share_amount": "0.0000",
            "share_amount2": "0.0000",
            "share_post": "0.0000",
            "share_price": "0.0000",
            "spec_code": "",
            "spec_id": 1458,
            "spec_name": "",
            "spec_no": "充电器-5号/7号",
            "src_oid": "AD202410150384",
            "src_tid": "*******************",
            "stock_reserved": "0",
            "suite_amount": "0.0000",
            "suite_discount": "0.0000",
            "suite_id": 0,
            "suite_name": "",
            "suite_no": "",
            "suite_num": "0.0000",
            "tax_rate": "0.0000",
            "tc_order_id": "",
            "trade_id": 9482058,
            "unit_name": "件",
            "weight": "0.0200"
          },
          {
            "actual_num": "1.0000",
            "adjust": "0.0000",
            "api_goods_name": "充电电池",
            "api_spec_name": "充电电池",
            "barcode": "0251",
            "base_unit_id": 1,
            "bind_oid": "",
            "cid": 0,
            "class_name": "配件",
            "commission": "0.0000",
            "created": "2024-10-15 19:35:16",
            "delivery_term": 1,
            "discount": "0.0000",
            "flag": 0,
            "from_mask": 131072,
            "gift_type": 0,
            "goods_id": 2671,
            "goods_name": "充电电池",
            "goods_no": "0251",
            "goods_type": 1,
            "guarantee_mode": "2",
            "invoice_content": "",
            "invoice_type": 0,
            "is_consigned": "0",
            "is_master": "0",
            "is_print_suite": "0",
            "is_received": "0",
            "is_zero_cost": "1",
            "large_type": 0,
            "modified": "2024-10-16 15:07:14",
            "num": "1.0000",
            "order_price": "0.0000",
            "paid": "0.0000",
            "pay_id": "",
            "pay_status": "2",
            "pay_time": "2024-10-13 11:57:42",
            "platform_goods_id": "2671",
            "platform_id": 0,
            "platform_spec_id": "2668",
            "price": "0.0000",
            "prop2": "2022-6-6",
            "rec_id": 30313305,
            "refund_num": "0.0000",
            "refund_status": 0,
            "remark": "",
            "share_amount": "0.0000",
            "share_amount2": "0.0000",
            "share_post": "0.0000",
            "share_price": "0.0000",
            "spec_code": "",
            "spec_id": 2668,
            "spec_name": "充电电池",
            "spec_no": "3颗充电7号",
            "src_oid": "AD202410150385",
            "src_tid": "*******************",
            "stock_reserved": "0",
            "suite_amount": "0.0000",
            "suite_discount": "0.0000",
            "suite_id": 0,
            "suite_name": "",
            "suite_no": "",
            "suite_num": "0.0000",
            "tax_rate": "0.0000",
            "tc_order_id": "",
            "trade_id": 9482058,
            "unit_name": "件",
            "weight": "0.0500"
          }
        ],
        "goods_type_count": 2,
        "id_card": "",
        "id_card_type": 0,
        "invoice_content": "",
        "invoice_id": 0,
        "invoice_title": "",
        "invoice_type": 0,
        "is_prev_notify": "0",
        "is_sealed": "0",
        "is_unpayment_sms": "0",
        "large_type": "0",
        "logistics_code": "047",
        "logistics_id": 96,
        "logistics_name": "圆通2号",
        "logistics_no": "***************",
        "logistics_template_id": "0",
        "logistics_type": 4,
        "modified": "2024-10-16 15:07:14",
        "note_count": "0",
        "other_amount": "0.0000",
        "other_cost": "0.0000",
        "package_id": "0",
        "paid": "0.0000",
        "pay_account": "",
        "pay_time": "2024-10-13 11:57:42",
        "pi_amount": "0.0000",
        "platform_id": 0,
        "post_amount": "0.0000",
        "post_cost": "1.8000",
        "pre_charge_time": "",
        "print_remark": "",
        "profit": "-3.8943",
        "raw_goods_count": "2.0000",
        "raw_goods_type_count": 2,
        "receivable": "0.0000",
        "receiver_address": "",
        "receiver_area": "浙江省 杭州市 萧山区",
        "receiver_city": 330100,
        "receiver_country": "0",
        "receiver_district": 330109,
        "receiver_dtb": "杭州市 萧山区",
        "receiver_mobile": "",
        "receiver_name": "",
        "receiver_province": "330000",
        "receiver_ring": "",
        "receiver_telno": "",
        "receiver_zip": "000000",
        "refund_status": 0,
        "remark_flag": 0,
        "reserve": "",
        "revert_reason": "0",
        "sales_score": "0",
        "salesman_id": -1,
        "sendbill_template_id": "0",
        "shop_id": "11",
        "shop_name": "遥蓝旗舰店",
        "shop_no": "010",
        "shop_platform_id": "1",
        "shop_remark": "",
        "single_spec_no": "",
        "split_from_trade_id": "0",
        "split_package_num": "0",
        "src_tids": "*******************",
        "stockout_no": "CK202410163235",
        "tags": "",
        "tax": "0.0000",
        "tax_rate": "0.0000",
        "to_deliver_time": "",
        "trade_from": 2,
        "trade_id": 9482058,
        "trade_mask": "0",
        "trade_no": "JY202410155890",
        "trade_prepay": "0.0000",
        "trade_status": 110,
        "trade_time": "0000-00-00 00:00:00",
        "trade_type": 7,
        "unmerge_mask": "0",
        "version_id": 4,
        "volume": "0.0000",
        "warehouse_id": "1",
        "warehouse_no": "001",
        "warehouse_type": 1,
        "weight": "0.0700"
      }
    ]
  }
}"""  # noqa

WDT_QUERY_SINGLE_ITEMS_RESPONSE = """{
  "code": 0,
  "goods_list": [
    {
      "alias": "",
      "aux_unit": "0",
      "aux_unit_name": "",
      "brand_id": "887",
      "brand_name": "小马蜂玩具（雅信）",
      "brand_no": "080",
      "class_id": "7",
      "class_name": "电商件",
      "created": "2023-12-08 16:48:57",
      "deleted": "0",
      "flag_id": "0",
      "flag_name": "",
      "goods_created": "2023-12-08 16:48:57",
      "goods_id": "4471",
      "goods_modified": "2024-10-01 15:45:14",
      "goods_name": "跳舞48首小龙人（彩盒+邮购）",
      "goods_no": "735665D",
      "goods_type": "1",
      "modified": "2024-10-01 15:45:14",
      "origin": "",
      "pinyin": "",
      "prop1": "",
      "prop2": "",
      "prop3": "",
      "prop4": "",
      "prop5": "",
      "prop6": "",
      "properties": "0,0,0,0,0,0",
      "remark": "",
      "short_name": "",
      "spec_count": "1",
      "spec_list": [
        {
          "aux_unit": "0",
          "barcode": "735665D",
          "barcode_count": "1",
          "barcode_list": [
            {
              "barcode": "735665D",
              "goods_id": "4471",
              "is_master": "1",
              "modified": "2023-12-08 16:48:56",
              "out_target_num": "1",
              "spec_id": "4404",
              "target_num": "1",
              "type": "1"
            }
          ],
          "created": "2023-12-08 16:48:57",
          "custom_price1": "0.0000",
          "custom_price2": "0.0000",
          "deleted": "0",
          "extra_3": "",
          "flag_id": "0",
          "goods_id": "4471",
          "height": "0.0000",
          "img_key": "",
          "img_url": "https://img.alicdn.com/bao/uploaded/i2/62950023/O1CN01I0SWBP1C2a1IDO1HX_!!62950023.jpg",
          "is_allow_neg_stock": "1",
          "is_lower_cost": "0",
          "is_not_need_examine": "0",
          "is_not_use_air": "0",
          "is_popular": "0",
          "is_single_batch": "0",
          "is_sn_enable": "0",
          "is_zero_cost": "1",
          "large_type": "0",
          "last_date": "",
          "length": "0.0000",
          "lowest_price": "0.0000",
          "market_price": "0.0000",
          "member_price": "0.0000",
          "modified": "2024-10-01 15:45:13",
          "pack_score": "1",
          "pick_score": "8",
          "plat_spec_count": "0",
          "postfix_val": "0",
          "prop1": "电商件",
          "prop2": "2023-12-8",
          "prop3": "",
          "prop4": "",
          "prop5": "",
          "prop6": "",
          "receive_days": "0",
          "remark": "",
          "replace_no": "",
          "replace_proportion": "1",
          "replenish_type": "2",
          "retail_price": "0.0000",
          "sale_score": "0",
          "sales_days": "0",
          "spec_aux_unit_name": "",
          "spec_code": "",
          "spec_created": "2023-12-08 16:48:57",
          "spec_id": "4404",
          "spec_mask": "0",
          "spec_modified": "2024-10-01 15:45:13",
          "spec_name": "跳舞48首小龙人（彩盒+邮购）",
          "spec_no": "735-665D升级充电版",
          "spec_unit_name": "",
          "tax_code": "",
          "tax_code_id": "0",
          "tax_rate": "0.0000",
          "unit": "0",
          "validity_days": "0",
          "washing_label": "",
          "weight": "0.3760",
          "wholesale_price": "0.0000",
          "width": "0.0000"
        }
      ],
      "unit": "1",
      "unit_name": "件",
      "version_id": "0"
    }
  ],
  "total_count": 1,
  "message": "ok"
}"""  # noqa


JST_QUERY_TRADE_RESPONSE = """{
  "response": {
    "data_count": 3,
    "has_next": false,
    "orders": [
      {
        "buyer_paid_amount": "465.5214",
        "co_id": 10125588,
        "created": "2024-09-09 13:42:49",
        "end_time": "2024-09-15 12:16:59",
        "f_freight": "0",
        "f_weight": "0",
        "free_amount": "215.64",
        "freight": "0.0",
        "is_cod": false,
        "is_split": true,
        "items": [
          {
            "amount": "681.16",
            "buyer_paid_amount": "465.5214",
            "i_id": "L折叠椅-胡桃色架-米白-B12",
            "is_gift": "false",
            "is_presale": false,
            "item_pay_amount": "465.52",
            "item_status": "SentCancelled",
            "name": "L折叠椅-胡桃色架-米白-B12",
            "oi_id": "44344842",
            "outer_oi_id": "**************92609",
            "price": "681.16",
            "qty": 1,
            "raw_so_id": "**************92609",
            "refund_status": "success",
            "seller_income_amount": "465.5214",
            "shop_i_id": "824551913572",
            "shop_sku_id": "5711108679463",
            "sku_id": "L折叠椅-胡桃色架-米白-B12",
            "sku_type": "normal",
            "src_combine_sku_id": "G折叠桌-胡桃色-80CM-B12+L折叠椅-胡桃色架-米白-B12"
          }
        ],
        "l_id": "YT7491235623933",
        "labels": "G折叠床永康,N吧凳永鑫,发货后取消,撤销发货",
        "lc_id": "YTO.3",
        "link_o_id": "41924746",
        "logistics_company": "北京大成路圆通",
        "modified": "2024-09-17 15:28:15",
        "o_id": ********,
        "oaid": "1LibEyqicAsSed3XLpiaeAO5z5r1Cr8yZsUoEfh8tQ1YwtgaKZ9kyn0RVbeyZ9Yxss5vrYpqKK",
        "open_id": "AAG-8-dTABsHV6KE1Lz7fZ6y",
        "order_date": "2024-09-09 00:51:52",
        "order_from": "WAP,SPLIT",
        "paid_amount": "465.52",
        "pay_amount": "465.52",
        "pay_date": "2024-09-09 00:52:36",
        "pay_free_amount": "0.0",
        "pays": [
          {
            "amount": "465.52",
            "buyer_account": "****",
            "is_order_pay": true,
            "outer_pay_id": "2024090922001104531408396173",
            "pay_date": "2024-09-09 00:52:36",
            "pay_id": "********",
            "payment": "支付宝",
            "status": "Confirmed"
          }
        ],
        "platform_free_amount": "0.0",
        "receiver_city": "佛山市",
        "receiver_district": "三水区",
        "receiver_state": "广东省",
        "receiver_zip": "000000",
        "referrer_free_amount": "0",
        "referrer_id": "",
        "referrer_name": "",
        "seller_flag": "5",
        "seller_income_amount": "465.5214",
        "send_date": "2024-09-09 14:46:31",
        "shop_buyer_id": "a**",
        "shop_free_amount": "0",
        "shop_id": ********,
        "shop_name": "【天猫】豪美达旗舰店",
        "shop_site": "淘宝天猫",
        "shop_status": "TRADE_CLOSED",
        "skus": "1.L折叠椅-胡桃色架-米白-B12",
        "so_id": "**************92609",
        "status": "Sent",
        "ts": 12945582684,
        "type": "普通订单",
        "weight": "0",
        "wms_co_id": 12228688
      },
      {
        "buyer_paid_amount": "3.4786",
        "co_id": 10125588,
        "created": "2024-09-09 13:42:49",
        "end_time": "2024-09-15 12:16:59",
        "f_freight": "0",
        "f_weight": "0",
        "free_amount": "1.61",
        "freight": "0.0",
        "is_cod": false,
        "is_split": true,
        "items": [
          {
            "amount": "5.09",
            "base_price": "747.5",
            "buyer_paid_amount": "3.4786",
            "i_id": "823979806034",
            "is_gift": "false",
            "is_presale": false,
            "item_pay_amount": "3.48",
            "item_status": "SentCancelled",
            "name": "实木折叠书桌学生家用电脑桌卧室小户型床边学习小桌子简易办公桌",
            "oi_id": "44344841",
            "outer_oi_id": "**************92609",
            "pic": "https://img.alicdn.com/bao/uploaded/i3/2088817296/O1CN01X1BZTl23lcfyBIybe_!!2088817296.jpg_30x30.jpg",
            "price": "5.0917",
            "properties_value": "折叠新升级-胡桃色-80CM-免安装",
            "qty": 1,
            "raw_so_id": "**************92609",
            "refund_status": "success",
            "seller_income_amount": "3.4786",
            "shop_i_id": "824551913572",
            "shop_sku_id": "5711108679463",
            "sku_id": "G折叠桌-胡桃色-80CM-B12",
            "sku_type": "normal",
            "src_combine_sku_id": "G折叠桌-胡桃色-80CM-B12+L折叠椅-胡桃色架-米白-B12"
          }
        ],
        "l_id": "YT7491235623922",
        "labels": "G折叠床永康,N吧凳永鑫,发货后取消,发货后售后,撤销发货",
        "lc_id": "YTO.3",
        "link_o_id": "41924746",
        "logistics_company": "北京大成路圆通",
        "modified": "2024-09-17 15:28:15",
        "o_id": ********,
        "oaid": "1LibEyqicAsSed3XLpiaeAO5z5r1Cr8yZsUoEfh8tQ1YwtgaKZ9kyn0RVbeyZ9Yxss5vrYpqKK",
        "open_id": "AAG-8-dTABsHV6KE1Lz7fZ6y",
        "order_date": "2024-09-09 00:51:52",
        "order_from": "WAP,SPLIT",
        "paid_amount": "3.48",
        "pay_amount": "3.48",
        "pay_date": "2024-09-09 00:52:36",
        "pay_free_amount": "0.0",
        "pays": [
          {
            "amount": "3.48",
            "buyer_account": "****",
            "is_order_pay": true,
            "outer_pay_id": "2024090922001104531408396173",
            "pay_date": "2024-09-09 00:52:36",
            "pay_id": "********",
            "payment": "支付宝",
            "status": "Confirmed"
          },
          {
            "amount": "469.0",
            "buyer_account": "****",
            "is_order_pay": false,
            "outer_pay_id": "293113621344590926",
            "pay_date": "2024-09-15 12:17:04",
            "pay_id": "********",
            "payment": "支付宝",
            "status": "Finished"
          }
        ],
        "platform_free_amount": "0.0",
        "receiver_city": "佛山市",
        "receiver_district": "三水区",
        "receiver_state": "广东省",
        "receiver_zip": "000000",
        "referrer_free_amount": "0",
        "referrer_id": "",
        "referrer_name": "",
        "seller_flag": "5",
        "seller_income_amount": "3.4786",
        "send_date": "2024-09-09 14:46:10",
        "shop_buyer_id": "a**",
        "shop_free_amount": "0",
        "shop_id": ********,
        "shop_name": "【天猫】豪美达旗舰店",
        "shop_site": "淘宝天猫",
        "shop_status": "TRADE_CLOSED",
        "skus": "1.G折叠桌-胡桃色-80CM-B12",
        "so_id": "**************92609",
        "status": "Sent",
        "ts": 12945582683,
        "type": "普通订单",
        "weight": "0",
        "wms_co_id": 12228688
      },
      {
        "buyer_paid_amount": "469.0",
        "co_id": 10125588,
        "created": "2024-09-09 00:51:55",
        "end_time": "2024-09-15 12:16:59",
        "f_freight": "0",
        "f_weight": "0",
        "free_amount": "217.25",
        "freight": "0.0",
        "is_cod": false,
        "items": [
          {
            "amount": "0.0",
            "base_price": "1372.5",
            "buyer_paid_amount": "469.0",
            "i_id": "823979806034",
            "is_gift": "false",
            "is_presale": false,
            "item_pay_amount": "469",
            "item_status": "Sent",
            "name": "【活动价】实木折叠书桌学生家用电脑桌卧室小户型床边学习小桌子简易办公桌",
            "oi_id": "44340160",
            "outer_oi_id": "**************92609",
            "pic": "http://img.alicdn.com/bao/uploaded/i4/4115570546/O1CN01I4ThFO1Fu7EU4pwJh_!!4115570546.jpg_30x30.jpg",
            "price": "686.25",
            "properties_value": "专注成长丨便捷收纳丨桌椅套餐⭐胡桃色-80CM",
            "qty": 0,
            "raw_so_id": "**************92609",
            "refund_status": "none",
            "shop_i_id": "824551913572",
            "shop_sku_id": "5711108679463",
            "sku_id": "G折叠桌-胡桃色-80CM-B12+L折叠椅-胡桃色架-米白-B12",
            "sku_type": "combine"
          },
          {
            "amount": "5.09",
            "base_price": "747.5",
            "buyer_paid_amount": "3.48",
            "i_id": "823979806034",
            "is_gift": "false",
            "is_presale": false,
            "item_pay_amount": "3.48",
            "item_status": "Cancelled",
            "name": "实木折叠书桌学生家用电脑桌卧室小户型床边学习小桌子简易办公桌",
            "oi_id": "44344830",
            "outer_oi_id": "**************92609",
            "pic": "https://img.alicdn.com/bao/uploaded/i3/2088817296/O1CN01X1BZTl23lcfyBIybe_!!2088817296.jpg_30x30.jpg",
            "price": "5.0917",
            "properties_value": "折叠新升级-胡桃色-80CM-免安装",
            "qty": 1,
            "raw_so_id": "**************92609",
            "refund_status": "success",
            "shop_i_id": "824551913572",
            "shop_sku_id": "5711108679463",
            "sku_id": "G折叠桌-胡桃色-80CM-B12",
            "sku_type": "normal",
            "src_combine_sku_id": "G折叠桌-胡桃色-80CM-B12+L折叠椅-胡桃色架-米白-B12"
          },
          {
            "amount": "681.16",
            "buyer_paid_amount": "465.52",
            "i_id": "L折叠椅-胡桃色架-米白-B12",
            "is_gift": "false",
            "is_presale": false,
            "item_pay_amount": "465.52",
            "item_status": "Cancelled",
            "name": "L折叠椅-胡桃色架-米白-B12",
            "oi_id": "44344831",
            "outer_oi_id": "**************92609",
            "price": "681.16",
            "qty": 1,
            "raw_so_id": "**************92609",
            "refund_status": "success",
            "shop_i_id": "824551913572",
            "shop_sku_id": "5711108679463",
            "sku_id": "L折叠椅-胡桃色架-米白-B12",
            "sku_type": "normal",
            "src_combine_sku_id": "G折叠桌-胡桃色-80CM-B12+L折叠椅-胡桃色架-米白-B12"
          }
        ],
        "l_id": "",
        "labels": "G折叠床永康,撤销发货",
        "lc_id": "YTO.1046",
        "logistics_company": "永康折叠床-",
        "modified": "2024-09-17 15:28:15",
        "o_id": 41924746,
        "oaid": "1LibEyqicAsSed3XLpiaeAO5z5r1Cr8yZsUoEfh8tQ1YwtgaKZ9kyn0RVbeyZ9Yxss5vrYpqKK",
        "open_id": "AAG-8-dTABsHV6KE1Lz7fZ6y",
        "order_date": "2024-09-09 00:51:52",
        "order_from": "WAP,WAP",
        "paid_amount": "469.0",
        "pay_amount": "469.0",
        "pay_date": "2024-09-09 00:52:36",
        "pays": [],
        "receiver_city": "佛山市",
        "receiver_district": "三水区",
        "receiver_state": "广东省",
        "receiver_zip": "000000",
        "referrer_free_amount": "",
        "referrer_id": "",
        "referrer_name": "",
        "seller_flag": "5",
        "send_date": "2024-09-09 13:33:01",
        "shop_buyer_id": "a**",
        "shop_free_amount": "",
        "shop_id": ********,
        "shop_name": "【天猫】豪美达旗舰店",
        "shop_site": "淘宝天猫",
        "shop_status": "TRADE_CLOSED",
        "skus": "0.",
        "so_id": "**************92609",
        "status": "Split",
        "ts": 12945582682,
        "type": "普通订单",
        "weight": "0",
        "wms_co_id": 13963277
      }
    ],
    "page_count": 1,
    "page_index": 1,
    "page_size": 3
  }
}"""  # noqa

JST_QUERY_COMBINE_ITEMS_RESPONSE = """{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "datas": [
      {
        "i_id": "823979806034",
        "vc_name": null,
        "created": "2024-08-12 14:56:38",
        "weight": null,
        "sku_id": "G折叠桌-胡桃色-80CM-B12+L折叠椅-胡桃色架-米白-B12",
        "pic": "https://img.alicdn.com/bao/uploaded/i3/2088817296/O1CN01fZsclB23lcg0xs0Lt_!!2088817296.jpg_30x30.jpg",
        "sale_price": 1372.5,
        "enabled": 1,
        "enty_sku_id": null,
        "labels": null,
        "properties_value": "折叠新升级-胡桃色 成长椅(灰)80CM-免安装",
        "sku_qty": 2,
        "name": "实木折叠书桌学生家用电脑桌卧室小户型床边学习小桌子简易办公桌",
        "modified": "2024-08-12 15:11:17",
        "short_name": null,
        "items": [
          {
            "qty": 1,
            "combine_sku_id": "G折叠桌-胡桃色-80CM-B12+L折叠椅-胡桃色架-米白-B12",
            "modified": "2024-08-12 15:11:17",
            "src_sku_id": "G折叠桌-胡桃色-80CM-B12",
            "sale_price": 747.5
          },
          {
            "qty": 1,
            "combine_sku_id": "G折叠桌-胡桃色-80CM-B12+L折叠椅-胡桃色架-米白-B12",
            "modified": "2024-08-12 15:11:17",
            "src_sku_id": "L折叠椅-胡桃色架-米白-B12",
            "sale_price": 99999
          }
        ],
        "brand": null,
        "cost_price": null
      }
    ],
    "requestId": null,
    "page_index": 1,
    "has_next": false,
    "data_count": 1,
    "page_count": 1,
    "page_size": 1
  }
}"""  # noqa

JST_QUERY_SINGLE_ITEMS_RESPONSE = """{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "datas": [
      {
        "i_id": "823979806034",
        "supplier_i_id": null,
        "item_type": "成品",
        "pic": "https://img.alicdn.com/bao/uploaded/i3/2088817296/O1CN01X1BZTl23lcfyBIybe_!!2088817296.jpg_30x30.jpg",
        "modified": "2024-08-12 14:56:38",
        "brand": null,
        "vc_name": null,
        "productionbatch_format": null,
        "created": "2024-08-12 14:56:38",
        "autoid": 978045,
        "weight": null,
        "sku_id": "G折叠桌-胡桃色-80CM-B12",
        "labels": null,
        "unit": null,
        "properties_value": "折叠新升级-胡桃色-80CM-免安装",
        "stock_disabled": 0,
        "is_series_number": false,
        "name": "实木折叠书桌学生家用电脑桌卧室小户型床边学习小桌子简易办公桌",
        "market_price": null,
        "short_name": null,
        "supplier_id": null,
        "sku_code": null,
        "color": "折叠新升级-胡桃色-80CM-免安装",
        "remark": null,
        "production_licence": null,
        "pic_big": "https://img.alicdn.com/bao/uploaded/i3/2088817296/O1CN01X1BZTl23lcfyBIybe_!!2088817296.jpg",
        "enabled": 1,
        "shelf_life": null,
        "c_id": null,
        "supplier_name": null,
        "cost_price": null,
        "creator": 15191365,
        "other_price_5": null,
        "sku_type": "normal",
        "h": null,
        "other_5": null,
        "other_4": null,
        "other_3": null,
        "l": null,
        "other_2": null,
        "sale_price": 747.5,
        "other_1": null,
        "stock_type": "",
        "supplier_sku_id": null,
        "w": null,
        "category": null,
        "other_price_3": null,
        "other_price_4": null,
        "other_price_1": null,
        "other_price_2": null
      },
      {
        "i_id": "L折叠椅-胡桃色架-米白-B12",
        "supplier_i_id": null,
        "item_type": "成品",
        "pic": null,
        "modified": "2024-08-12 15:09:05",
        "brand": null,
        "vc_name": null,
        "productionbatch_format": null,
        "created": "2024-08-12 15:09:05",
        "autoid": 978057,
        "weight": null,
        "sku_id": "L折叠椅-胡桃色架-米白-B12",
        "labels": null,
        "unit": null,
        "properties_value": null,
        "stock_disabled": 0,
        "is_series_number": false,
        "name": "L折叠椅-胡桃色架-米白-B12",
        "market_price": null,
        "short_name": null,
        "supplier_id": null,
        "sku_code": null,
        "color": null,
        "remark": null,
        "production_licence": null,
        "pic_big": null,
        "enabled": 1,
        "shelf_life": null,
        "c_id": null,
        "supplier_name": null,
        "cost_price": null,
        "creator": 15191365,
        "other_price_5": null,
        "sku_type": "normal",
        "h": null,
        "other_5": null,
        "other_4": null,
        "other_3": null,
        "l": null,
        "other_2": null,
        "sale_price": null,
        "other_1": null,
        "stock_type": "",
        "supplier_sku_id": null,
        "w": null,
        "category": null,
        "other_price_3": null,
        "other_price_4": null,
        "other_price_1": null,
        "other_price_2": null
      }
    ],
    "requestId": null,
    "page_index": 1,
    "has_next": false,
    "data_count": 2,
    "page_count": 1,
    "page_size": 50
  }
}"""  # noqa

JST_WAREHOUSE_RESPONSE = """{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "datas": [
      {
        "wms_co_id": 13508273,
        "is_main": true,
        "name": "北京小魔怪科技有限公司",
        "co_id": 13508273,
        "remark1": "",
        "status": "生效",
        "remark2": ""
      },
      {
        "wms_co_id": 13517071,
        "is_main": false,
        "name": "1｜集合仓-浙江",
        "co_id": 13508273,
        "remark1": null,
        "status": "生效",
        "remark2": "开通分仓"
      },
      {
        "wms_co_id": 13517072,
        "is_main": false,
        "name": "2｜发网-龙岗",
        "co_id": 13508273,
        "remark1": null,
        "status": "生效",
        "remark2": "开通分仓"
      },
      {
        "wms_co_id": 13517073,
        "is_main": false,
        "name": "1.1｜集合仓-拆大货",
        "co_id": 13508273,
        "remark1": null,
        "status": "生效",
        "remark2": "开通分仓"
      },
      {
        "wms_co_id": 13517074,
        "is_main": false,
        "name": "4｜深圳-办公室",
        "co_id": 13508273,
        "remark1": null,
        "status": "生效",
        "remark2": "开通分仓"
      },
      {
        "wms_co_id": 13517075,
        "is_main": false,
        "name": "5｜森雅-德州",
        "co_id": 13508273,
        "remark1": null,
        "status": "生效",
        "remark2": "开通分仓"
      },
      {
        "wms_co_id": 13517129,
        "is_main": false,
        "name": "6｜木艺-浙江",
        "co_id": 13508273,
        "remark1": null,
        "status": "生效",
        "remark2": "开通分仓"
      },
      {
        "wms_co_id": 13518555,
        "is_main": false,
        "name": "7｜仓库",
        "co_id": 13508273,
        "remark1": null,
        "status": "生效",
        "remark2": "开通分仓"
      }
    ],
    "requestId": null,
    "page_index": 1,
    "has_next": false,
    "data_count": 8,
    "page_count": 1,
    "page_size": 20
  }
}"""


JST_SKU_RESPONSE = """{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "datas": [
      {
        "i_id": "EE001",
        "supplier_i_id": null,
        "item_type": "成品",
        "pic": "https://img.alicdn.com/bao/uploaded/i2/3642336162/O1CN010TH6Vh1vOFNeRD96k_!!3642336162.png",
        "modified": "2024-07-01 17:47:20",
        "brand": null,
        "vc_name": "小笨钟",
        "productionbatch_format": null,
        "created": "2023-12-11 18:49:20",
        "autoid": 2820,
        "weight": 1.107,
        "sku_id": "6971503540031",
        "labels": "1｜集合仓-浙江,4｜深圳-办公室",
        "unit": null,
        "properties_value": "小笨钟",
        "stock_disabled": 0,
        "is_series_number": false,
        "name": "小笨钟",
        "market_price": null,
        "short_name": "小笨钟",
        "supplier_id": 12079923,
        "sku_code": null,
        "color": null,
        "remark": null,
        "production_licence": null,
        "pic_big": null,
        "enabled": 1,
        "shelf_life": null,
        "c_id": 1100502748,
        "supplier_name": "中山飞旋科技有限公司",
        "cost_price": 70.03,
        "creator": 17998136,
        "other_price_5": null,
        "sku_type": "normal",
        "h": 9.5,
        "other_5": null,
        "other_4": null,
        "other_3": null,
        "l": 28.8,
        "other_2": null,
        "sale_price": 319,
        "other_1": "刘中元",
        "stock_type": "部分禁止",
        "supplier_sku_id": null,
        "w": 30,
        "category": "电子时钟",
        "other_price_3": null,
        "other_price_4": null,
        "other_price_1": null,
        "other_price_2": null
      }
    ],
    "requestId": null,
    "page_index": 1,
    "has_next": false,
    "data_count": 1,
    "page_count": 1,
    "page_size": 10
  }
}"""


JST_STOCK_RESPONSE = """{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "inventorys": [
      {
        "i_id": "EE001",
        "purchase_qty": 0,
        "min_qty": null,
        "allocate_qty": 0,
        "sku_id": "6971503540031",
        "virtual_qty": 0,
        "order_lock": 2,
        "max_qty": null,
        "customize_qty_1": 0,
        "customize_qty_2": 0,
        "pick_lock": 0,
        "wms_co_id": 13508273,
        "qty": 1,
        "name": "小笨钟",
        "modified": "2024-09-27 15:22:04",
        "in_qty": 0,
        "defective_qty": 14,
        "return_qty": 0,
        "ts": 6901398359
      }
    ],
    "requestId": null,
    "page_index": 1,
    "has_next": false,
    "data_count": 1,
    "page_count": 1,
    "page_size": 100
  }
}"""

WDT_SKU_RESPONSE = """{
  "code": 0,
  "goods_list": [
    {
      "alias": "",
      "aux_unit": "0",
      "aux_unit_name": "",
      "brand_id": "3",
      "brand_name": "OEM",
      "brand_no": "BD202208100003",
      "class_id": "49",
      "class_name": "鱼线",
      "created": "2024-06-04 13:15:12",
      "deleted": "0",
      "flag_id": "0",
      "flag_name": "",
      "goods_created": "2024-06-04 13:15:12",
      "goods_id": "9518",
      "goods_modified": "2024-06-04 13:15:11",
      "goods_name": "0391路亚八编F级绿白",
      "goods_no": "0391LYBFLB",
      "goods_type": "1",
      "modified": "2024-06-04 13:15:11",
      "origin": "",
      "pinyin": "",
      "prop1": "",
      "prop2": "",
      "prop3": "",
      "prop4": "",
      "prop5": "",
      "prop6": "",
      "properties": "0,0,0,0,0,0",
      "remark": "",
      "short_name": "",
      "spec_count": "6",
      "spec_list": [
        {
          "aux_unit": "0",
          "barcode": "0391LYBFLB01",
          "barcode_count": "0",
          "barcode_list": [
            {
              "barcode": "0391LYBFLB01",
              "goods_id": "9518",
              "is_master": "1",
              "modified": "2024-06-04 13:15:11",
              "out_target_num": "1",
              "spec_id": "106392",
              "target_num": "1",
              "type": "1"
            }
          ],
          "created": "2024-06-04 13:15:12",
          "custom_price1": "0.0000",
          "custom_price2": "0.0000",
          "deleted": "0",
          "extra_3": "",
          "flag_id": "0",
          "goods_id": "9518",
          "height": "0.0000",
          "img_key": "",
          "img_url": "",
          "is_allow_neg_stock": "1",
          "is_lower_cost": "0",
          "is_not_need_examine": "0",
          "is_not_use_air": "0",
          "is_popular": "0",
          "is_single_batch": "0",
          "is_sn_enable": "0",
          "is_zero_cost": "1",
          "large_type": "0",
          "last_date": "",
          "length": "0.0000",
          "lowest_price": "0.0000",
          "market_price": "0.0000",
          "member_price": "0.0000",
          "modified": "2024-06-04 13:15:11",
          "pack_score": "0",
          "pick_score": "0",
          "plat_spec_count": "0",
          "postfix_val": "0",
          "prop1": "",
          "prop2": "",
          "prop3": "",
          "prop4": "",
          "prop5": "",
          "prop6": "",
          "receive_days": "0",
          "remark": "",
          "replace_no": "",
          "replace_proportion": "1",
          "replenish_type": "2",
          "retail_price": "22.0000",
          "sale_score": "0",
          "sales_days": "0",
          "spec_aux_unit_name": "",
          "spec_code": "",
          "spec_created": "2024-06-04 13:15:12",
          "spec_id": "106392",
          "spec_mask": "0",
          "spec_modified": "2024-06-04 13:15:11",
          "spec_name": "0391路亚 八编F级绿白100米/0.6裸盘",
          "spec_no": "0391LYBFLB01",
          "spec_unit_name": "",
          "tax_code": "",
          "tax_code_id": "0",
          "tax_rate": "0.0000",
          "unit": "0",
          "validity_days": "0",
          "washing_label": "",
          "weight": "0.0000",
          "wholesale_price": "22.0000",
          "width": "0.0000"
        },
        {
          "aux_unit": "0",
          "barcode": "0391LYBFLB02",
          "barcode_count": "0",
          "barcode_list": [
            {
              "barcode": "0391LYBFLB02",
              "goods_id": "9518",
              "is_master": "1",
              "modified": "2024-06-04 13:15:11",
              "out_target_num": "1",
              "spec_id": "106393",
              "target_num": "1",
              "type": "1"
            }
          ],
          "created": "2024-06-04 13:15:12",
          "custom_price1": "0.0000",
          "custom_price2": "0.0000",
          "deleted": "0",
          "extra_3": "",
          "flag_id": "0",
          "goods_id": "9518",
          "height": "0.0000",
          "img_key": "",
          "img_url": "",
          "is_allow_neg_stock": "1",
          "is_lower_cost": "0",
          "is_not_need_examine": "0",
          "is_not_use_air": "0",
          "is_popular": "0",
          "is_single_batch": "0",
          "is_sn_enable": "0",
          "is_zero_cost": "1",
          "large_type": "0",
          "last_date": "",
          "length": "0.0000",
          "lowest_price": "0.0000",
          "market_price": "0.0000",
          "member_price": "0.0000",
          "modified": "2024-06-04 13:15:11",
          "pack_score": "0",
          "pick_score": "0",
          "plat_spec_count": "0",
          "postfix_val": "0",
          "prop1": "",
          "prop2": "",
          "prop3": "",
          "prop4": "",
          "prop5": "",
          "prop6": "",
          "receive_days": "0",
          "remark": "",
          "replace_no": "",
          "replace_proportion": "1",
          "replenish_type": "2",
          "retail_price": "22.0000",
          "sale_score": "0",
          "sales_days": "0",
          "spec_aux_unit_name": "",
          "spec_code": "",
          "spec_created": "2024-06-04 13:15:12",
          "spec_id": "106393",
          "spec_mask": "0",
          "spec_modified": "2024-06-04 13:15:11",
          "spec_name": "0391路亚 八编F级绿白100米/0.8裸盘",
          "spec_no": "0391LYBFLB02",
          "spec_unit_name": "",
          "tax_code": "",
          "tax_code_id": "0",
          "tax_rate": "0.0000",
          "unit": "0",
          "validity_days": "0",
          "washing_label": "",
          "weight": "0.0000",
          "wholesale_price": "22.0000",
          "width": "0.0000"
        },
        {
          "aux_unit": "0",
          "barcode": "0391LYBFLB03",
          "barcode_count": "0",
          "barcode_list": [
            {
              "barcode": "0391LYBFLB03",
              "goods_id": "9518",
              "is_master": "1",
              "modified": "2024-06-04 13:15:11",
              "out_target_num": "1",
              "spec_id": "106394",
              "target_num": "1",
              "type": "1"
            }
          ],
          "created": "2024-06-04 13:15:12",
          "custom_price1": "0.0000",
          "custom_price2": "0.0000",
          "deleted": "0",
          "extra_3": "",
          "flag_id": "0",
          "goods_id": "9518",
          "height": "0.0000",
          "img_key": "",
          "img_url": "",
          "is_allow_neg_stock": "1",
          "is_lower_cost": "0",
          "is_not_need_examine": "0",
          "is_not_use_air": "0",
          "is_popular": "0",
          "is_single_batch": "0",
          "is_sn_enable": "0",
          "is_zero_cost": "1",
          "large_type": "0",
          "last_date": "",
          "length": "0.0000",
          "lowest_price": "0.0000",
          "market_price": "0.0000",
          "member_price": "0.0000",
          "modified": "2024-06-04 13:15:11",
          "pack_score": "0",
          "pick_score": "0",
          "plat_spec_count": "0",
          "postfix_val": "0",
          "prop1": "",
          "prop2": "",
          "prop3": "",
          "prop4": "",
          "prop5": "",
          "prop6": "",
          "receive_days": "0",
          "remark": "",
          "replace_no": "",
          "replace_proportion": "1",
          "replenish_type": "2",
          "retail_price": "22.0000",
          "sale_score": "0",
          "sales_days": "0",
          "spec_aux_unit_name": "",
          "spec_code": "",
          "spec_created": "2024-06-04 13:15:12",
          "spec_id": "106394",
          "spec_mask": "0",
          "spec_modified": "2024-06-04 13:15:11",
          "spec_name": "0391路亚 八编F级绿白100米/1.0裸盘",
          "spec_no": "0391LYBFLB03",
          "spec_unit_name": "",
          "tax_code": "",
          "tax_code_id": "0",
          "tax_rate": "0.0000",
          "unit": "0",
          "validity_days": "0",
          "washing_label": "",
          "weight": "0.0000",
          "wholesale_price": "22.0000",
          "width": "0.0000"
        },
        {
          "aux_unit": "0",
          "barcode": "0391LYBFLB04",
          "barcode_count": "0",
          "barcode_list": [
            {
              "barcode": "0391LYBFLB04",
              "goods_id": "9518",
              "is_master": "1",
              "modified": "2024-06-04 13:15:11",
              "out_target_num": "1",
              "spec_id": "106395",
              "target_num": "1",
              "type": "1"
            }
          ],
          "created": "2024-06-04 13:15:12",
          "custom_price1": "0.0000",
          "custom_price2": "0.0000",
          "deleted": "0",
          "extra_3": "",
          "flag_id": "0",
          "goods_id": "9518",
          "height": "0.0000",
          "img_key": "",
          "img_url": "",
          "is_allow_neg_stock": "1",
          "is_lower_cost": "0",
          "is_not_need_examine": "0",
          "is_not_use_air": "0",
          "is_popular": "0",
          "is_single_batch": "0",
          "is_sn_enable": "0",
          "is_zero_cost": "1",
          "large_type": "0",
          "last_date": "",
          "length": "0.0000",
          "lowest_price": "0.0000",
          "market_price": "0.0000",
          "member_price": "0.0000",
          "modified": "2024-06-04 13:15:11",
          "pack_score": "0",
          "pick_score": "0",
          "plat_spec_count": "0",
          "postfix_val": "0",
          "prop1": "",
          "prop2": "",
          "prop3": "",
          "prop4": "",
          "prop5": "",
          "prop6": "",
          "receive_days": "0",
          "remark": "",
          "replace_no": "",
          "replace_proportion": "1",
          "replenish_type": "2",
          "retail_price": "19.0000",
          "sale_score": "0",
          "sales_days": "0",
          "spec_aux_unit_name": "",
          "spec_code": "",
          "spec_created": "2024-06-04 13:15:12",
          "spec_id": "106395",
          "spec_mask": "0",
          "spec_modified": "2024-06-04 13:15:11",
          "spec_name": "0391路亚 八编F级绿白100米/1.2裸盘",
          "spec_no": "0391LYBFLB04",
          "spec_unit_name": "",
          "tax_code": "",
          "tax_code_id": "0",
          "tax_rate": "0.0000",
          "unit": "0",
          "validity_days": "0",
          "washing_label": "",
          "weight": "0.0000",
          "wholesale_price": "19.0000",
          "width": "0.0000"
        },
        {
          "aux_unit": "0",
          "barcode": "0391LYBFLB05",
          "barcode_count": "0",
          "barcode_list": [
            {
              "barcode": "0391LYBFLB05",
              "goods_id": "9518",
              "is_master": "1",
              "modified": "2024-06-04 13:15:11",
              "out_target_num": "1",
              "spec_id": "106396",
              "target_num": "1",
              "type": "1"
            }
          ],
          "created": "2024-06-04 13:15:12",
          "custom_price1": "0.0000",
          "custom_price2": "0.0000",
          "deleted": "0",
          "extra_3": "",
          "flag_id": "0",
          "goods_id": "9518",
          "height": "0.0000",
          "img_key": "",
          "img_url": "",
          "is_allow_neg_stock": "1",
          "is_lower_cost": "0",
          "is_not_need_examine": "0",
          "is_not_use_air": "0",
          "is_popular": "0",
          "is_single_batch": "0",
          "is_sn_enable": "0",
          "is_zero_cost": "1",
          "large_type": "0",
          "last_date": "",
          "length": "0.0000",
          "lowest_price": "0.0000",
          "market_price": "0.0000",
          "member_price": "0.0000",
          "modified": "2024-06-04 13:15:11",
          "pack_score": "0",
          "pick_score": "0",
          "plat_spec_count": "0",
          "postfix_val": "0",
          "prop1": "",
          "prop2": "",
          "prop3": "",
          "prop4": "",
          "prop5": "",
          "prop6": "",
          "receive_days": "0",
          "remark": "",
          "replace_no": "",
          "replace_proportion": "1",
          "replenish_type": "2",
          "retail_price": "19.0000",
          "sale_score": "0",
          "sales_days": "0",
          "spec_aux_unit_name": "",
          "spec_code": "",
          "spec_created": "2024-06-04 13:15:12",
          "spec_id": "106396",
          "spec_mask": "0",
          "spec_modified": "2024-06-04 13:15:11",
          "spec_name": "0391路亚 八编F级绿白100米/1.5裸盘",
          "spec_no": "0391LYBFLB05",
          "spec_unit_name": "",
          "tax_code": "",
          "tax_code_id": "0",
          "tax_rate": "0.0000",
          "unit": "0",
          "validity_days": "0",
          "washing_label": "",
          "weight": "0.0000",
          "wholesale_price": "19.0000",
          "width": "0.0000"
        },
        {
          "aux_unit": "0",
          "barcode": "0391LYBFLB06",
          "barcode_count": "0",
          "barcode_list": [
            {
              "barcode": "0391LYBFLB06",
              "goods_id": "9518",
              "is_master": "1",
              "modified": "2024-06-04 13:15:11",
              "out_target_num": "1",
              "spec_id": "106397",
              "target_num": "1",
              "type": "1"
            }
          ],
          "created": "2024-06-04 13:15:12",
          "custom_price1": "0.0000",
          "custom_price2": "0.0000",
          "deleted": "0",
          "extra_3": "",
          "flag_id": "0",
          "goods_id": "9518",
          "height": "0.0000",
          "img_key": "",
          "img_url": "",
          "is_allow_neg_stock": "1",
          "is_lower_cost": "0",
          "is_not_need_examine": "0",
          "is_not_use_air": "0",
          "is_popular": "0",
          "is_single_batch": "0",
          "is_sn_enable": "0",
          "is_zero_cost": "1",
          "large_type": "0",
          "last_date": "",
          "length": "0.0000",
          "lowest_price": "0.0000",
          "market_price": "0.0000",
          "member_price": "0.0000",
          "modified": "2024-06-04 13:15:11",
          "pack_score": "0",
          "pick_score": "0",
          "plat_spec_count": "0",
          "postfix_val": "0",
          "prop1": "",
          "prop2": "",
          "prop3": "",
          "prop4": "",
          "prop5": "",
          "prop6": "",
          "receive_days": "0",
          "remark": "",
          "replace_no": "",
          "replace_proportion": "1",
          "replenish_type": "2",
          "retail_price": "19.0000",
          "sale_score": "0",
          "sales_days": "0",
          "spec_aux_unit_name": "",
          "spec_code": "",
          "spec_created": "2024-06-04 13:15:12",
          "spec_id": "106397",
          "spec_mask": "0",
          "spec_modified": "2024-06-04 13:15:11",
          "spec_name": "0391路亚 八编F级绿白100米/2.0裸盘",
          "spec_no": "0391LYBFLB06",
          "spec_unit_name": "",
          "tax_code": "",
          "tax_code_id": "0",
          "tax_rate": "0.0000",
          "unit": "0",
          "validity_days": "0",
          "washing_label": "",
          "weight": "0.0000",
          "wholesale_price": "19.0000",
          "width": "0.0000"
        }
      ],
      "unit": "4",
      "unit_name": "盘",
      "version_id": "0"
    }
  ],
  "total_count": 1,
  "message": "ok"
}"""


WDT_STOCK_RESPONSE = """{
  "response": {
    "errorcode": 0,
    "message": "ok",
    "stocks": [
      {
        "alarm_days": "1",
        "alarm_days1": "5",
        "alarm_type": "0",
        "avaliable_num": "0.0000",
        "barcode": "0391LYBFLB01",
        "brand_name": "OEM",
        "brand_no": "BD202208100003",
        "cost_price": "0.0000",
        "created": "2024-06-04 13:15:19",
        "default_position_id": "-1",
        "flag_id": "0",
        "goods_name": "0391路亚八编F级绿白",
        "goods_no": "0391LYBFLB",
        "img_url": "",
        "last_inout_time": "2024-06-12 14:10:36",
        "last_pd_time": "0000-00-00 00:00:00",
        "last_position_id": "-1",
        "last_sales_time": "2024-06-08 00:00:00",
        "lock_num": "0.0000",
        "modified": "2024-07-09 04:58:34",
        "neg_stockout_num": "0.0000",
        "order_num": "0.0000",
        "part_paid_num": "0.0000",
        "process_onway_num": "0.0000",
        "purchase_arrive_num": "0.0000",
        "purchase_num": "0.0000",
        "rec_id": "2026620",
        "refund_num": "0.0000",
        "refund_onway_num": "0.0000",
        "return_exch_num": "0.0000",
        "return_num": "0.0000",
        "return_onway_num": "0.0000",
        "safe_stock": "0.0000",
        "safe_stock2": "0.0000",
        "safe_stock3": "0.0000",
        "sales_rate": "1.0000",
        "sales_rate_cycle": "30.0000",
        "sales_rate_type": "0",
        "sending_num": "0.0000",
        "spec_code": "",
        "spec_id": 106392,
        "spec_name": "0391路亚 八编F级绿白100米/0.6裸盘",
        "spec_no": "0391LYBFLB01",
        "spec_wh_no": "",
        "spec_wh_no2": "",
        "status": "1",
        "stock_diff": "0.0000",
        "stock_num": "0.0000",
        "stock_today_num": "30.0000",
        "subscribe_num": "0.0000",
        "to_purchase_num": "0.0000",
        "to_transfer_num": "0.0000",
        "today_num": "0.0000",
        "transfer_num": "0.0000",
        "unit": "0",
        "unpay_num": "0.0000",
        "warehouse_id": 1,
        "warehouse_name": "主仓库",
        "warehouse_no": "1",
        "warehouse_type": 1,
        "weight": "0.0000",
        "wms_preempty_stock": "0.0000",
        "wms_reserve_num": "0.0000",
        "wms_stock_diff": "0.0000",
        "wms_sync_stock": "0.0000",
        "wms_sync_time": "0000-00-00 00:00:00"
      }
    ],
    "total_count": 1
  }
}"""

WDTULTI_SKU_RESPONSE = """{
  "status": 0,
  "data": {
    "goods_list": [
      {
        "goods_no": "BF01-005",
        "origin": "",
        "class_id": 0,
        "goods_modified": 1708395053000,
        "remark": "",
        "aux_unit_name": "无",
        "goods_created": "2024-02-20 10:10:52",
        "flag_name": "无",
        "spec_list": [
          {
            "spec_id": 149164,
            "goods_id": 32943,
            "spec_no": "6941963040426",
            "spec_code": "",
            "barcode": "6941963040426",
            "spec_name": "未来可期",
            "lowest_price": 0,
            "retail_price": 3.62,
            "wholesale_price": 0,
            "member_price": 0,
            "market_price": 0,
            "spec_created": "2024-02-20 10:10:52",
            "validity_days": 0,
            "sales_days": 0,
            "receive_days": 0,
            "weight": 0.293,
            "length": 130,
            "width": 180,
            "height": 17,
            "sn_type": 0,
            "is_lower_cost": false,
            "tax_rate": 0,
            "wms_process_mask": 0,
            "deleted": 0,
            "large_type": 0,
            "remark": "",
            "spec_modified": 1708396961000,
            "prop1": "",
            "prop2": "",
            "prop3": "1*96",
            "prop4": "QB/T 1438",
            "prop5": "",
            "prop6": "",
            "img_url": "http://weed.3028.com/4,b68f61777476?collection=workflow3028",
            "is_not_use_air": 0,
            "custom_price1": 0,
            "custom_price2": 0,
            "spec_unit_name": "无",
            "spec_aux_unit_name": "无",
            "goods_label": "",
            "tax_code": "",
            "barcode_list": [
              {
                "barcode": "6941963040426",
                "type": 1,
                "is_master": 1,
                "modified": "2024-02-20 10:10:52"
              }
            ]
          },
          {
            "spec_id": 149165,
            "goods_id": 32943,
            "spec_no": "6941963040433",
            "spec_code": "",
            "barcode": "6941963040433",
            "spec_name": "皆如所愿",
            "lowest_price": 0,
            "retail_price": 3.62,
            "wholesale_price": 0,
            "member_price": 0,
            "market_price": 0,
            "spec_created": "2024-02-20 10:10:52",
            "validity_days": 0,
            "sales_days": 0,
            "receive_days": 0,
            "weight": 0.293,
            "length": 130,
            "width": 180,
            "height": 17,
            "sn_type": 0,
            "is_lower_cost": false,
            "tax_rate": 0,
            "wms_process_mask": 0,
            "deleted": 0,
            "large_type": 0,
            "remark": "",
            "spec_modified": 1708396961000,
            "prop1": "",
            "prop2": "",
            "prop3": "1*96",
            "prop4": "QB/T 1438",
            "prop5": "",
            "prop6": "",
            "img_url": "http://weed.3028.com/5,b69029cbb80e?collection=workflow3028",
            "is_not_use_air": 0,
            "custom_price1": 0,
            "custom_price2": 0,
            "spec_unit_name": "无",
            "spec_aux_unit_name": "无",
            "goods_label": "",
            "tax_code": "",
            "barcode_list": [
              {
                "barcode": "6941963040433",
                "type": 1,
                "is_master": 1,
                "modified": "2024-02-20 10:10:52"
              }
            ]
          },
          {
            "spec_id": 149166,
            "goods_id": 32943,
            "spec_no": "6941963040440",
            "spec_code": "",
            "barcode": "6941963040440",
            "spec_name": "前程似锦",
            "lowest_price": 0,
            "retail_price": 3.62,
            "wholesale_price": 0,
            "member_price": 0,
            "market_price": 0,
            "spec_created": "2024-02-20 10:10:52",
            "validity_days": 0,
            "sales_days": 0,
            "receive_days": 0,
            "weight": 0.293,
            "length": 130,
            "width": 180,
            "height": 17,
            "sn_type": 0,
            "is_lower_cost": false,
            "tax_rate": 0,
            "wms_process_mask": 0,
            "deleted": 0,
            "large_type": 0,
            "remark": "",
            "spec_modified": 1708396961000,
            "prop1": "",
            "prop2": "",
            "prop3": "1*96",
            "prop4": "QB/T 1438",
            "prop5": "",
            "prop6": "",
            "img_url": "http://weed.3028.com/3,b6929ff42f04?collection=workflow3028",
            "is_not_use_air": 0,
            "custom_price1": 0,
            "custom_price2": 0,
            "spec_unit_name": "无",
            "spec_aux_unit_name": "无",
            "goods_label": "",
            "tax_code": "",
            "barcode_list": [
              {
                "barcode": "6941963040440",
                "type": 1,
                "is_master": 1,
                "modified": "2024-02-20 10:10:52"
              }
            ]
          },
          {
            "spec_id": 149167,
            "goods_id": 32943,
            "spec_no": "6941963040457",
            "spec_code": "",
            "barcode": "6941963040457",
            "spec_name": "好运将至",
            "lowest_price": 0,
            "retail_price": 3.62,
            "wholesale_price": 0,
            "member_price": 0,
            "market_price": 0,
            "spec_created": "2024-02-20 10:10:52",
            "validity_days": 0,
            "sales_days": 0,
            "receive_days": 0,
            "weight": 0.293,
            "length": 130,
            "width": 180,
            "height": 17,
            "sn_type": 0,
            "is_lower_cost": false,
            "tax_rate": 0,
            "wms_process_mask": 0,
            "deleted": 0,
            "large_type": 0,
            "remark": "",
            "spec_modified": 1708396961000,
            "prop1": "",
            "prop2": "",
            "prop3": "1*96",
            "prop4": "QB/T 1438",
            "prop5": "",
            "prop6": "",
            "img_url": "http://weed.3028.com/5,b691bd283c47?collection=workflow3028",
            "is_not_use_air": 0,
            "custom_price1": 0,
            "custom_price2": 0,
            "spec_unit_name": "无",
            "spec_aux_unit_name": "无",
            "goods_label": "",
            "tax_code": "",
            "barcode_list": [
              {
                "barcode": "6941963040457",
                "type": 1,
                "is_master": 1,
                "modified": "2024-02-20 10:10:53"
              }
            ]
          }
        ],
        "alias": "",
        "modified": "2024-02-20 10:10:53",
        "spec_count": 4,
        "class_name": "无",
        "goods_name": "国风文字-精装本",
        "goods_id": 32943,
        "brand_name": "三年二班",
        "prop6": "",
        "prop5": "",
        "prop4": "",
        "brand_id": 939,
        "prop3": "",
        "prop2": "30",
        "unit_name": "无",
        "prop1": "1000",
        "pinyin": "",
        "deleted": 0,
        "short_name": "",
        "goods_type": 1
      }
    ],
    "total_count": 0
  }
}"""

WDTULTI_STOCK_RESPONSE = """{
  "status": 0,
  "data": {
    "total_count": 0,
    "detail_list": [
      {
        "spec_code": "",
        "to_transfer_num": 0,
        "warehouse_type": 1,
        "brand_no": "07",
        "wms_stock_diff": 0,
        "refund_exch_num": 0,
        "spec_no": "6941963040433",
        "defect": false,
        "modified": 1715577166000,
        "barcode": "6941963040433",
        "part_paid_num": 0,
        "goods_name": "国风文字-精装本",
        "lock_num": 0,
        "created": 1713943030000,
        "available_send_stock": 0,
        "subscribe_num": 0,
        "weight": 0.293,
        "brand_name": "三年二班",
        "unpay_num": 0,
        "sending_num": 0,
        "to_process_in_num": 0,
        "warehouse_name": "天猫仓",
        "img_url": "http://weed.3028.com/5,b69029cbb80e?collection=workflow3028",
        "refund_num": 0,
        "to_process_out_num": 0,
        "order_num": 0,
        "status": 2,
        "flag_id": 0,
        "wms_sync_stock": 0,
        "to_purchase_num": 0,
        "wms_preempty_stock": 0,
        "goods_no": "BF01-005",
        "stock_num": 0,
        "purchase_arrive_num": 0,
        "wms_preempty_diff": 0,
        "remark": "",
        "flag_name": "无",
        "return_num": 0,
        "purchase_num": 0,
        "warehouse_no": "088",
        "spec_id": 149165,
        "return_exch_num": 0,
        "rec_id": 24543786,
        "to_other_in_num": 0,
        "to_other_out_num": 0,
        "refund_onway_num": 0,
        "transfer_num": 0,
        "spec_name": "皆如所愿",
        "return_onway_num": 0,
        "warehouse_id": 30
      }
    ]
  }
}"""


@fixture
def _mock_client_by_platform(request, mocker, client_unauthorized, mock_erp_info, grant_record_factory):
    client, _, shop = client_unauthorized.login()
    param = request.param
    shop.platform = param
    grant_record_factory.create(shop_id=shop.id)
    mock_erp_info.shop_id = shop.id

    if param in ["TAOBAO", "TMALL"]:
        mock_trade = TradeInfo()
        mock_trade.trade_id = "123"
        mocker.patch(
            "robot_processor.client.trade_client.client.GetTradeByTid",
            return_value=mock_trade,
        )
    elif param == "DOUDIAN":
        mocker.patch(
            "rpa.doudian.doudian_client.get_trade_by_tid",
            return_value=Ok({"trades": []}),
        )
    elif param == "PDD":
        mock_trade = GetTradesByTidListAndChannelResp()
        pdd_trade_info = PddTradeInfo(
            trade_id="220808-***************",
            trade_type=2,
            create_time="2022-08-08 00:39:54",
            pay_time="2022-08-08 00:39:56",
            pay_amount=5.6,
            logistics_info=LogisticsInfo(
                receiver_name="name",
                receiver_phone="phone",
                province="province",
                city="city",
                town="town",
                address="address",
                logistics_id=1,
                logistics_name="logistics",
                tracking_number="",
                country="country",
            ),
        )
        trade_info = GetTradesByTidListAndChannelResp.TradeInfo(pdd_trade_info=pdd_trade_info)
        mock_trade.trade_info_list.extend([trade_info])
        mock_erp_info.shop_id = shop.id
        mocker.patch(
            "robot_processor.client.trade_client.get_trade_by_tid_and_channel",
            return_value=mock_trade,
        )
        mocker.patch(
            "robot_processor.shop.auth_manager.if_shop_pdd_auth_valid",
            return_value=True,
        )
        # 解密接口mock
        mocker.patch(
            "robot_processor.client.pdd_bridge_client.batch_decrypt",
            return_value=PddBatchDecryptResponse(
                data_decrypt_list=[
                    PddDecryptResponse(
                        data_tag="123",
                        encrypted_data="phone",
                        error_code=0,
                        decrypted_data="phone",
                    ),
                    PddDecryptResponse(
                        data_tag="123",
                        encrypted_data="name",
                        error_code=0,
                        decrypted_data="name",
                    ),
                    PddDecryptResponse(
                        data_tag="123",
                        encrypted_data="address",
                        error_code=0,
                        decrypted_data="address",
                    ),
                ]
            ),
        )
    elif param == "JST":
        mock_erp_info.erp_type = ErpType.JST
        mocker.patch("rpa.erp.jst.sdk.JstSDK.get_order_of_nonetb", return_value={"trades": []})
    db.session.commit()

    return client


@fixture(autouse=True)
def auth_detail(mocker):
    return mocker.patch("robot_processor.client.kiosk_client.auth_detail", return_value={})


@mark.order(1)
def test_get_trades_missing_uid(client):
    resp = client.get(f"/v1/trades?sid={client.shop.sid}")
    assert resp.json == {"status_code": 400, "reason": "缺失买家信息或订单ID"}


def test_get_trades_bad_sid(client):
    resp = client.get("/v1/trades?uid=123&sid=0")
    assert resp.json == {"status_code": 400, "reason": "非法的店铺id"}


def test_get_trades_non_taobao(client_unauthorized):
    client, _, shop = client_unauthorized.login()
    shop.platform = "DOUDIAN"
    db.session.commit()
    resp = client.get(f"/v1/trades?uid=123&sid={shop.sid}")
    assert resp.status_code == 200
    assert resp.json == {"status_code": 200}


def test_get_trades_no_shop_grant(client_unauthorized):
    client, _, shop = client_unauthorized.login()
    resp = client.get(f"/v1/trades?uid=123&sid={shop.sid}")
    assert resp.json == {"status_code": 400, "reason": "缺失授权记录", "trades": []}


@mark.order(1)
def test_get_trades_succeed_by_uid(mocker, client, mock_grant_record):
    client.shop.records.append(mock_grant_record)
    client.shop.platform = "TAOBAO"

    mocker.patch(
        "robot_processor.client.trade_client.client.GetRecentTrades",
        return_value=TradeList(),
    )

    resp = client.get(f"/v1/trades?uid=123&sid={client.shop.sid}")
    assert resp.status_code == 200
    assert resp.json == {"status_code": 200, "trades": []}


@mark.order(1)
def test_get_trades_succeed_by_tid(mocker, client, mock_grant_record):
    client.shop.records.append(mock_grant_record)
    client.shop.platform = "TAOBAO"
    db.session.commit()
    mocker.patch(
        "robot_processor.client.trade_client.client.GetTradeFromDbOrApiByTid",
        return_value=TradeInfo(),
    )

    resp = client.get(f"/v1/trades?tid=123&sid={client.shop.sid}")
    assert resp.json["status_code"] == 200
    assert len(resp.json["trades"]) == 1


def test_get_duohong_trade(mocker, client_unauthorized, mock_erp_info, requests_mock):
    client, _, shop = client_unauthorized.login()
    shop.platform = "JD"
    mock_erp_info.shop_id = shop.id
    mock_erp_info.erp_type = ErpType.DUOHONG
    db.session.commit()
    requests_mock.post(
        "/namespaces/duohong-erp/methods/do-get-trade-list",
        json={
            "success": True,
            "message": "调用服务成功",
            "code": 200,
            "result": {
                "pageCount": 1,
                "rowCount": 2,
                "list": [
                    {
                        "receiverCity": "上海市",
                        "tid": "1516575145066781075",
                        "receiverPhone": "",
                        "buyerNick": "aaa",
                        "payment": "12.60",
                        "payTime": "022-03-15 16:10:03",
                        "statusEn": "WAIT_SELLER_SEND_GOODS",
                        "created": "2022-03-15 10:36:06",
                        "receiverAddress": "万*街**村路****弄万里晶品**号楼****",
                        "companyId": "551782",
                        "customerCompanyId": "551782",
                        "receiverState": "上海",
                        "orders": [
                            {
                                "buyerNick": "tb031723874",
                                "payTime": "2022-03-15 10:36:10",
                                "numIid": "************",
                                "num": "1",
                                "tid": "1516575145066781075",
                                "outerSkuId": "510173-魔方猫眼-华为MATE30-透明[0]-黄眼框多个芝士CB华为MATE30",
                                "price": "68.00",
                                "payment": "68.00",
                                "skuId": "4988147315426",
                                "adjustFee": "0.00",
                                "statusEn": "WAIT_SELLER_SEND_GOODS",
                                "picPath": "https://img.alicdn.com/bao/uploaded/i1/3208331609/O1CN01IFKngO1Nky0T"
                                "MIafL_!!3208331609.png",
                                "itemTotal": "3",
                                "oid": "1516575145066781075",
                                "title": "【活动价】华为mate30手机壳mate40pro20女款2022芝士支架新款魅特20pro卡通可爱网红潮牌时尚全",
                                "skuPropertiesName": "华为 mate30 芝士+送镭射卡",
                            }
                        ],
                        "receiverDistrict": "普陀区",
                        "receiverName": "周**",
                        "receiverMobile": "*******6081",
                        "receiverTown": "134",
                        "receiverZip": "456",
                        "orderTotal": 3,
                        "totalFee": "68.00",
                    }
                ],
            },
        },
    )

    mocker.patch(
        "rpa.erp.duohong.DuohongClient.query_address",
        return_value=Ok(
            {
                "name": "周",
                "country": "中国",
                "state": "上海",
                "city": "上海市",
                "district": "普陀区",
                "town": "",
                "address": "万里街道新村路",
                "zip": "000000",
                "mobile": "15670186000",
                "phone": "6987",
            }
        ),
    )

    resp = client.get(f"/v1/trades/1516575145066781075?sid={shop.sid}")
    assert resp.status_code == 200
    assert resp.json["trade_id"] == "1516575145066781075"


def test_get_address_from_duohong(
    mocker,
    client_unauthorized,
    mock_erp_info,
    mock_grant_record,
    mock_buyer_server_get_buyer_nick_by_tid,
    auth_detail,
):
    client, _, shop = client_unauthorized.login()
    shop.platform = "TAOBAO"
    shop.records.append(mock_grant_record)
    mock_erp_info.shop_id = shop.id
    mock_erp_info.erp_type = ErpType.DUOHONG
    db.session.add(shop)
    db.session.add(mock_erp_info)
    db.session.commit()

    mock_trade = TradeInfo()
    mock_trade.trade_id = "123"
    mock_trade.receiver.address = "adr"
    mocker.patch("robot_processor.client.trade_client.get_trade_by_tid", return_value=mock_trade)
    mocker.patch(
        "robot_processor.client.trade_client.get_trade_from_multi_plat_by_tid",
        return_value=MessageToDict(
            mock_trade,
            preserving_proto_field_name=True,
            including_default_value_fields=True,
        ),
    )
    current_app.config["DUOHONG_ADDRESS_BLACK_SHOPS"] = shop.sid
    resp = client.get(f"/v1/trades?sid={shop.sid}&tid=123&uid=223")
    assert resp.status_code == 200

    resp = client.get(f"/v1/trades/123?sid={shop.sid}")
    assert resp.status_code == 200

    shop.platform = "KUAISHOU"
    mock_erp_info.erp_type = ErpType.BAISHOUTAO
    auth_detail.return_value = {
        "orgId": 1,
        "authAccount": "**********",
        "accessToken": "test_token",
    }
    mocker.patch(
        "robot_processor.client.trade_client.get_trade_by_tid_and_channel",
        return_value=GetTradesByTidListAndChannelResp(),
    )
    resp = client.get(f"/v1/trades/123?sid={shop.sid}")
    assert resp.status_code == 200

    auth_detail.return_value = {}
    mocker.patch(
        "robot_processor.client.trade_client.get_trade_by_tid_and_channel",
        return_value=GetTradesByTidListAndChannelResp(),
    )
    resp = client.get(f"/v1/trades/123?sid={shop.sid}")
    assert resp.json["status_code"] == 400


def test_tb_logistics_by_tid(
    mocker,
    client_unauthorized,
    mock_grant_record,
    mock_buyer_server_get_buyer_nick_by_tid,
):
    client, _, shop = client_unauthorized.login()
    shop.platform = "TAOBAO"
    shop.records.append(mock_grant_record)
    db.session.commit()

    mock_trade = TradeInfo()
    mock_trade.trade_id = "123"
    mock_trade.receiver.address = "adr"
    mocker.patch("robot_processor.client.trade_client.get_trade_by_tid", return_value=mock_trade)

    resp = client.get(f"/v1/trades/123?sid={shop.sid}")
    assert resp.status_code == 200

    mock_buyer = {
        "receiver_name": "石",
        "receiver_mobile": "17627337264",
        "receiver_state": "上海",
        "receiver_city": "上海市",
        "receiver_district": "徐汇区",
        "receiver_town": "徐家街道",
        "receiver_address": "凯旋路4",
        "receiver_zip": "200030",
    }
    mock_logistics = GetReceiverInfoResponse(code=DgtResponseCode.OK, error_msg="", receiver_info=mock_buyer)
    mocker.patch(
        "robot_processor.client.buyer_client.client.GetReceiverInfo",
        return_value=mock_logistics,
    )

    mocker.patch("robot_processor.plugin.api.current_app.config.get", return_value="123,456")

    resp = client.get(f"/v1/trades/123?sid={shop.sid}")
    assert resp.status_code == 200


def test_get_trade_by_tid_from_jst(mocker, client_unauthorized, mock_erp_info):
    client, _, shop = client_unauthorized.login()
    mock_trade = {
        "trade_id": "220531-***************",
        "status": 4,
        "orders": [
            {
                "sku_description": "长颈鹿,73 身高60-73cm肩开扣",
                "title": "儿童t恤长袖男童春秋装女孩上衣宝宝婴儿纯棉打底衫潮小童0-3-6岁",
                "quantity": 2,
                "sku_id": "685494362393",
                "oid": "220531-***************_685494362393",
                "price": 18.9,
                "payment": 18.9,
                "pic_path": "https://img.pddpic.com/mms-material-img/2020-09-12/b415bf0b.a.jpeg",
                "outer_sku_id": "SG1800173",
                "outer_sku_id_real": "SG1800173",
                "status": 0,
                "spu_id": "",
                "taobao_id": "",
                "refund_status": 0,
                "estimate_delivery_time": "",
                "total_fee": 0.0,
                "divide_order_fee": 0.0,
                "end_time": "",
                "refund_id": "",
            }
        ],
        "created_at": "2022-05-31 14:47:02",
        "paid_at": "2022-05-31 14:47:02",
        "buyer_nick": "14464107",
        "payment": 32.13,
        "type": "DEFAULT_TRADE_TYPE",
        "modified_at": "",
        "finished_at": "",
        "price": 0.0,
        "buyer_rated": False,
        "store_id": "",
        "memo": "",
        "discount": 0.0,
        "total_fee": 0.0,
        "jdp_modified_at": "",
        "step_paid_fee": 0.0,
        "step_trade_status": "INVALID_STEP_TRADE",
        "send_time": "",
        "seller_nick": "",
        "jdp_created_at": "",
        "post_fee": "",
        "seller_flag": 0,
        "buyer_alipay_no": "",
    }
    shop.platform = "PDD"
    mock_erp_info.erp_type = ErpType.JST
    mock_erp_info.shop_id = shop.id
    mocker.patch("rpa.erp.jst.sdk.JstSDK.get_order_of_nonetb", return_value=mock_trade)

    resp = client.get(f"/v1/trades/123?sid={shop.sid}")
    assert resp.status_code == 200
    assert "220531-***************" == resp.json["trade_id"]


def test_get_store_info_by_trade(mocker, client_unauthorized, auth_detail):
    client, _, shop = client_unauthorized.login()
    trade = TradeChannelInfo()
    trade.channel_no = "123"
    trade.channel_name = "test"
    trade.channel_type = ChannelType.KUAISHOU
    shop.platform = "KUAISHOU"
    auth_detail.return_value = {
        "orgId": 1,
        "authAccount": "**********",
        "accessToken": "test_token",
    }
    mocker.patch(
        "robot_processor.client.trade_client.get_channel_info_by_tid_list",
        return_value=[trade],
    )
    headers = {"Authentication-Token": "#"}
    resp = client.get("/v1/shops/info/by-trade?tid=123", headers=headers)
    assert resp.status_code == 200


def test_get_store_info_by_trade_failed(mocker, client_unauthorized, auth_detail):
    client, _, shop = client_unauthorized.login()
    shop.platform = "KUAISHOU"
    auth_detail.return_value = {
        "orgId": 1,
        "authAccount": "**********",
        "accessToken": "test_token",
    }
    mocker.patch(
        "robot_processor.client.trade_client.get_channel_info_by_tid_list",
        return_value=None,
    )
    headers = {"Authentication-Token": "#"}
    resp = client.get("/v1/shops/info/by-trade?tid=123", headers=headers)
    assert resp.json.get("succeed") is False
    assert resp.json.get("error") == "无匹配店铺"


def test_batch_get_trade_info(mocker, client_unauthorized, auth_detail):
    client, _, shop = client_unauthorized.login()
    mock_trade = GetTradesByTidListAndChannelResp()
    # ks
    shop.platform = "KUAISHOU"
    auth_detail.return_value = {
        "orgId": 1,
        "authAccount": "**********",
        "accessToken": "test_token",
    }
    mocker.patch(
        "robot_processor.client.trade_client.get_trade_by_tid_and_channel",
        return_value=mock_trade,
    )
    ks_trade_info = CommonTradeInfo(
        trade_id="220808-***************",
        trade_type=2,
        create_time="2022-08-08 00:39:54",
        pay_time="2022-08-08 00:39:56",
        pay_amount=5.6,
    )
    trade_info = GetTradesByTidListAndChannelResp.TradeInfo(ks_trade_info=ks_trade_info)
    mock_trade.trade_info_list.extend([trade_info])
    mocker.patch(
        "robot_processor.client.trade_client.get_trade_by_tid_and_channel",
        return_value=mock_trade,
    )
    resp = client.get(f"/v1/batch-trade-info/123?sid={shop.sid}")
    assert resp.status_code == 200
    assert resp.json["trades"][0]["create_time"] == "2022-08-08 00:39:54"

    auth_detail.return_value = {}
    resp = client.get(f"/v1/batch-trade-info/123?sid={shop.sid}")
    assert resp.json["status_code"] == 200


def test_get_trade_by_tid_and_channel(mocker, client_unauthorized, mock_erp_info, auth_detail):
    client, _, shop = client_unauthorized.login()
    mock_trade = GetTradesByTidListAndChannelResp()
    pdd_trade_info = PddTradeInfo(
        trade_id="220808-***************",
        trade_type=2,
        create_time="2022-08-08 00:39:54",
        pay_time="2022-08-08 00:39:56",
        pay_amount=5.6,
    )
    trade_info = GetTradesByTidListAndChannelResp.TradeInfo(pdd_trade_info=pdd_trade_info)
    mock_trade.trade_info_list.extend([trade_info])
    shop.platform = "PDD"
    mock_erp_info.shop_id = shop.id
    mocker.patch(
        "robot_processor.client.trade_client.get_trade_by_tid_and_channel",
        return_value=mock_trade,
    )

    resp = client.get(f"/v1/trade-info/123?sid={shop.sid}")
    assert resp.status_code == 200
    assert resp.json["create_time"] == "2022-08-08 00:39:54"

    # ks
    shop.platform = "KUAISHOU"
    mocker.patch("robot_processor.shop.models.Shop.get_access_token", return_value="test_token")
    ks_trade_info = CommonTradeInfo(
        trade_id="220808-***************",
        trade_type=2,
        create_time="2022-08-08 00:39:54",
        pay_time="2022-08-08 00:39:56",
        pay_amount=5.6,
    )
    trade_info = GetTradesByTidListAndChannelResp.TradeInfo(ks_trade_info=ks_trade_info)
    mock_trade.trade_info_list.pop(0)
    mock_trade.trade_info_list.extend([trade_info])
    mocker.patch(
        "robot_processor.client.trade_client.get_trade_by_tid_and_channel",
        return_value=mock_trade,
    )
    resp = client.get(f"/v1/trade-info/123?sid={shop.sid}")
    assert resp.status_code == 200
    assert resp.json["create_time"] == "2022-08-08 00:39:54"


def test_get_sku(mocker, client_unauthorized, mock_grant_record, mock_erp_info, auth_detail, mock_get_item_by_spu_id):
    client, _, shop = client_unauthorized.login()
    shop.platform = "TAOBAO"
    mock_erp_info.erp_type = ErpType.JST
    mock_erp_info.shop_id = shop.id

    # bad sid
    resp = client.get("/v1/spu/123/sku?sid=0")
    assert resp.json["status_code"] == 400

    # non taobao
    shop.platform = "DOUDIAN"
    db.session.commit()
    mocker.patch("rpa.doudian.client.DoudianRpaClient.get_sku_by_spu_id", return_value={})
    resp = client.get(f"/v1/spu/123/sku?sid={shop.sid}")
    data = resp.json
    assert resp.json["status_code"] == 200
    data.pop("status_code")
    assert data == {}

    shop.platform = "TAOBAO"
    db.session.commit()

    # no grant
    resp = client.get(f"/v1/spu/123/sku?sid={shop.sid}")
    data = resp.json
    status_code = data.pop("status_code", None)
    assert status_code == 200
    assert data == {}

    shop.records.append(mock_grant_record)
    db.session.commit()

    resp = client.get(f"/v1/spu/123/sku?sid={shop.sid}")
    data = resp.json
    assert resp.json["status_code"] == 200
    data.pop("status_code")
    assert data == {}

    # doudian
    shop.platform = "DOUDIAN"
    db.session.commit()
    item_info = ItemInfo()
    item_info.spu_id = "123"
    sku_info = SkuInfo()
    sku_info.outer_sku_id = "1"
    item_info.sku_infos.append(sku_info)
    mock_get_item_by_spu_id.return_value = item_info

    resp = client.get(f"/v1/spu/123/sku?sid={shop.sid}")
    assert resp.status_code == 200
    assert len(resp.json.get("sku_infos")) == 1

    mock_resp = DgtProviderSkuResponse()
    sku_info = DgtProviderSkuResponse.DgtProviderSkuInfo()
    sku_info.outer_sku_id = "sku_id1"
    sku_info.spu_id = "spu_id1"
    mock_resp.skuInfo.extend([sku_info])
    mock_resp.page_no = 1
    mock_resp.page_size = 20

    mocker.patch(
        "robot_processor.client.item_client.client.DgtProviderSku",
        return_value=mock_resp,
    )
    resp = client.get(f"/v1/spu/123/sku?sid={shop.sid}&source=erp")
    assert resp.status_code == 200
    print(f"===resp:{resp.json}")
    assert len(resp.json.get("sku_infos")) == 1
    assert resp.json.get("sku_infos")[0].get("outer_sku_id") == "sku_id1"
    assert resp.json.get("spu_id") == "spu_id1"

    shop.platform = "KUAISHOU"
    auth_detail.return_value = {
        "orgId": 1,
        "authAccount": "**********",
        "accessToken": "test_token",
    }

    resp = client.get(f"/v1/spu/123/sku?sid={shop.sid}&source=platform")
    assert resp.status_code == 200


def test_get_available_forms(mocker, client, monkeypatch, form_factory, step_factory):
    resp = client.get("/v1/available-forms?category=ALIPAY")
    assert resp.status_code == 200
    assert not resp.json["forms"]

    form1, form2, form3, form4 = form_factory.create_batch(size=4, category="ALIPAY")
    for form in (form1, form2, form3, form4):
        form.subscribe(client.shop, True)

    begin1, begin2, begin3, begin4 = step_factory.create_batch(4, step_type=StepType.begin)
    step1, step2, step3, step4 = step_factory.create_batch(4, step_type=StepType.human)
    begin1.next_step_ids, step1.prev_step_ids = [step1.step_uuid], [begin1.step_uuid]
    begin1.form_id = step1.form_id = form1.id
    step1.assistants_v2 = {
        "select_type": 2,
        "details": [
            {
                "user_id": client.another_assistant.user_id,
                "user_type": client.another_assistant.user_type,
            }
        ],
    }
    begin2.next_step_ids, step2.prev_step_ids = [step2.step_uuid], [begin2.step_uuid]
    begin2.form_id = step2.form_id = form2.id
    step2.assistants_v2 = {
        "select_type": 2,
        "details": [
            {
                "user_id": client.assistant.user_id,
                "user_type": client.assistant.user_type,
            }
        ],
    }
    begin3.next_step_ids, step3.prev_step_ids = [step3.step_uuid], [begin3.step_uuid]
    begin3.form_id = step3.form_id = form3.id
    step3.assistants_v2 = {
        "select_type": 2,
        "details": [
            {
                "user_id": client.assistant.user_id,
                "user_type": client.assistant.user_type,
            }
        ],
    }
    begin4.next_step_ids, step4.prev_step_ids = [step4.step_uuid], [begin4.step_uuid]
    begin4.form_id = step4.form_id = form4.id
    step4.assistants_v2 = {
        "select_type": 1,
    }
    db.session.commit()
    resp = client.get("/v1/available-forms?category=ALIPAY")
    assert resp.status_code == 200
    forms = resp.json["forms"]
    assert len(forms) == 3

    user_config = UserCustomizeConfig()
    user_config.org_id = client.shop.org_id
    user_config.sid = client.shop.sid
    user_config.user_id = client.assistant.user_id
    user_config.user_type = UserType.ASSISTANT
    user_config.model = UserCustomizeConfig.Type.FORM_QUERY
    user_config.config = {"sorts": [form3.id, form2.id, form4.id]}
    db.session.add(user_config)

    resp = client.get("/v1/available-forms?category=ALIPAY")
    assert resp.status_code == 200
    forms = resp.json["forms"]
    form_ids = [form["id"] for form in forms]
    assert form_ids == [form3.id, form2.id, form4.id]


@mark.order(1)
def test_update_memo(mocker, client, mock_grant_record):
    # no sid
    resp = client.put(
        "/v1/trades/1/memo",
        json={"content": "123", "memo_flag": False, "flag": 1},
    )
    assert resp.json["status_code"] == 400

    # no user nick
    resp = client.put(
        f"/v1/trades/1/memo?sid={client.shop.sid}",
        json={"content": "123", "memo_flag": False, "flag": 1},
    )
    assert resp.json["status_code"] == 400

    # no grant record
    resp = client.put(
        f"/v1/trades/1/memo?sid={client.shop.sid}&user_nick=hello",
        json={"content": "123", "memo_flag": False, "flag": 1},
    )
    assert resp.json["status_code"] == 400

    client.shop.records.append(mock_grant_record)
    db.session.commit()

    mocker.patch("robot_processor.client.trade_client.update_memo", return_value=(1, ""))

    resp = client.put(
        f"/v1/trades/1/memo?sid={client.shop.sid}&user_nick=hello",
        json={"content": "123", "memo_flag": False, "flag": 1},
    )
    assert resp.status_code == 200
    assert resp.json["succeed"]


@mark.order(1)
def test_upload_image(mocker, client):
    # bad image
    requests_response = requests.Response()
    requests_response.status_code = 200
    mocker.patch(
        "robot_processor.client.image_oss_client.bucket.put_object",
        return_value=PutObjectResult(Response(requests_response)),
    )
    resp = client.post(
        "/v1/uploads/image",
        data={
            "image": (BytesIO(b"test"), "test.jpg"),
        },
    )
    assert resp.json["status_code"] == 400

    # good image
    resp = client.post(
        "/v1/uploads/image",
        data={
            "image": (BytesIO(b"012345JFIF"), "test.jpeg"),
        },
    )
    assert resp.status_code == 200
    assert resp.json["url"] == f"https://test.oss.com/images/{client.shop.org_id}/f25b2b88ff1e0f9b3af6774a7291cd73.jpeg"

    # upload failed
    requests_response.status_code = 400
    mocker.patch(
        "robot_processor.client.image_oss_client.bucket.put_object",
        return_value=PutObjectResult(Response(requests_response)),
    )
    resp = client.post(
        "/v1/uploads/image",
        data={
            "image": (BytesIO(b"012345JFIF"), "test.jpeg"),
        },
    )
    assert resp.json["status_code"] == 406


@mark.order(1)
def test_upload_video(mocker, client):
    # good video
    requests_response = requests.Response()
    requests_response.status_code = 200
    mocker.patch(
        "robot_processor.client.video_oss_client.bucket.put_object",
        return_value=PutObjectResult(Response(requests_response)),
    )
    resp = client.post(
        "/v1/uploads/video",
        data={
            "video": (BytesIO(b"test"), "test.flv"),
        },
    )
    assert resp.status_code == 200
    assert resp.json["url"] == f"https://test.oss.com/videos/{client.shop.org_id}/098f6bcd4621d373cade4e832627b4f6.flv"

    # upload failed
    requests_response.status_code = 400
    mocker.patch(
        "robot_processor.client.video_oss_client.bucket.put_object",
        return_value=PutObjectResult(Response(requests_response)),
    )
    resp = client.post(
        "/v1/uploads/video",
        data={
            "video": (BytesIO(b"test"), "test.flv"),
        },
    )
    assert resp.json["status_code"] == 406


@mark.order(1)
def test_upload_file(mocker, client):
    # good file
    requests_response = requests.Response()
    requests_response.status_code = 200
    mocker.patch(
        "robot_processor.client.file_oss_client.bucket.put_object",
        return_value=PutObjectResult(Response(requests_response)),
    )
    resp = client.post(
        "/v1/uploads/file",
        data={
            "file": (BytesIO(b"test"), "test.txt"),
        },
    )
    assert resp.status_code == 200
    assert resp.json["url"] == f"https://test.oss.com/files/{client.shop.org_id}/098f6bcd4621d373cade4e832627b4f6.txt"

    # upload failed
    requests_response.status_code = 400
    mocker.patch(
        "robot_processor.client.file_oss_client.bucket.put_object",
        return_value=PutObjectResult(Response(requests_response)),
    )
    resp = client.post(
        "/v1/uploads/file",
        data={
            "file": (BytesIO(b"test"), "test.txt"),
        },
    )
    assert resp.json["status_code"] == 406


def test_get_spu_list(mocker, client_unauthorized, mock_grant_record, mock_erp_info, auth_detail):
    client, _, shop = client_unauthorized.login()
    shop.platform = "TAOBAO"
    mock_erp_info.erp_type = ErpType.JST
    mock_erp_info.shop_id = shop.id
    db.session.commit()

    # no sid
    resp = client.get(
        "/v1/spus?query_param=testparam&page_no=1&page_size=10",
    )
    assert resp.json["status_code"] == 400

    # no grant record
    resp = client.get(
        f"/v1/spus?query_param=testparam&page_no=1&page_size=10" f"&sid={shop.sid}",
    )
    assert resp.json["status_code"] == 400

    shop.records.append(mock_grant_record)
    db.session.commit()

    mock_resp = DgtGetItemListResponse()
    mock_resp.page_no = 1
    mock_resp.page_size = 10

    mock_spu = mock_resp.item_detail_infos.add()
    mock_spu.spu_id = "spu_id111"
    mock_spu.item_title = "item_title111"
    mock_spu.pic_url = "pic_url111"
    mocker.patch("robot_processor.client.item_client.client.GetItemList", return_value=mock_resp)

    resp = client.get(
        f"/v1/spus?query_param=testparam&page_no=1&page_size=10" f"&sid={shop.sid}",
    )
    assert resp.status_code == 200
    assert resp.json["item_detail_infos"][0].get("spu_id") == "spu_id111"

    mock_resp = DgtGetItemListResponse()
    mock_resp.page_no = 1
    mock_resp.page_size = 10

    mock_spu = mock_resp.item_detail_infos.add()
    mock_spu.spu_id = "spu_id112"
    mock_spu.item_title = "item_title112"
    mock_spu.pic_url = "pic_url112"
    mocker.patch(
        "robot_processor.client.item_client.client.DgtProviderItem",
        return_value=mock_resp,
    )
    resp = client.get(
        f"/v1/spus?query_param=testparam&page_no=1&page_size=10&source=erp" f"&sid={shop.sid}",
    )
    assert resp.status_code == 200
    assert resp.json["item_detail_infos"][0].get("spu_id") == "spu_id112"

    shop.platform = "KUAISHOU"
    auth_detail.return_value = {
        "orgId": 1,
        "authAccount": "**********",
        "accessToken": "test_token",
    }
    resp = client.get(
        f"/v1/spus?query_param=testparam&page_no=1&page_size=10&source=platform" f"&sid={shop.sid}",
    )
    assert resp.status_code == 200


def test_get_spu(mocker, client_unauthorized, mock_grant_record, mock_erp_info, auth_detail):
    client, _, shop = client_unauthorized.login()
    mock_erp_info.erp_type = ErpType.JST
    mock_erp_info.shop_id = shop.id
    db.session.commit()

    mock_resp = DgtGetSpuBySpuIdResponse()
    mock_resp.item_detail_info.spu_id = "1234"
    mocker.patch(
        "robot_processor.client.item_client.client.GetSpuBySpuId",
        return_value=mock_resp,
    )

    # no grant record
    resp = client.get(
        f"/v1/spus/1234?sid={shop.sid}",
    )
    assert resp.json["status_code"] == 404

    shop.records.append(mock_grant_record)
    db.session.commit()

    resp = client.get(
        f"/v1/spus/1234?sid={shop.sid}",
    )
    assert resp.json["status_code"] == 404

    mock_resp.item_detail_info.item_title = "title"
    mock_resp.item_detail_info.pic_url = "pic"
    mocker.patch(
        "robot_processor.client.item_client.client.GetSpuBySpuId",
        return_value=mock_resp,
    )

    resp = client.get(
        f"/v1/spus/1234?sid={shop.sid}",
    )
    assert resp.status_code == 200
    assert resp.json.get("item_title") == "title"

    # doudian
    shop.platform = "DOUDIAN"
    db.session.commit()
    item_detail_info = dict()
    item_detail_info["spu_id"] = "1234"
    item_detail_info["item_title"] = "title"

    mocker.patch("rpa.doudian.doudian_client.get_spu_by_spu_id", return_value=item_detail_info)

    resp = client.get(
        f"/v1/spus/1234?sid={shop.sid}",
    )
    assert resp.status_code == 200
    assert resp.json.get("item_title") == "title"

    mock_resp = DgtGetItemListResponse()
    mock_resp.page_no = 1
    mock_resp.page_size = 10

    mock_spu = mock_resp.item_detail_infos.add()
    mock_spu.spu_id = "spu_id112"
    mock_spu.item_title = "item_title112"
    mock_spu.pic_url = "pic_url112"
    mocker.patch(
        "robot_processor.client.item_client.client.DgtProviderItem",
        return_value=mock_resp,
    )
    resp = client.get(
        f"/v1/spus/1234?sid={shop.sid}&source=erp",
    )
    assert resp.status_code == 200
    assert resp.json.get("item_title") == "item_title112"
    # ks
    shop.platform = "KUAISHOU"
    db.session.commit()
    item_detail_info = dict()
    item_detail_info["spu_id"] = "1234"
    item_detail_info["item_title"] = "title"

    mocker.patch("robot_processor.shop.models.Shop.get_access_token", return_value="test_token")

    resp = client.get(f"/v1/spus/1234?sid={shop.sid}")
    assert resp.status_code == 200
    assert resp.json.get("item_title") == "title"

    shop.platform = "KUAISHOU"
    resp = client.get(
        f"/v1/spus/1234?sid={shop.sid}",
    )
    assert resp.status_code == 200


def test_form_schema(client, mock_form, mock_step):
    mock_form.subscribe(client.shop, True)
    mock_step.form_id = mock_form.id
    mock_step.step_type = mock_step.step_type.human
    db.session.commit()

    resp = client.get(
        f"/v1/forms/{mock_form.id}/schema",
    )

    data = resp.json
    assert resp.status_code == 200
    assert data["current_step"]["id"] == mock_step.id
    assert data["step_history"][0]["id"] == mock_step.id

    resp2 = client.get(
        f"/v1/forms/{mock_form.id}/schema?step_id={mock_step.id}",
    )

    data2 = resp2.json
    assert resp2.status_code == 200
    assert data2["current_step"]["id"] == mock_step.id
    assert len(data2["step_history"]) == 0


def test_trade_rates(mocker, client_unauthorized, mock_grant_record):
    client, _, shop = client_unauthorized.login()
    shop.platform = "TAOBAO"
    resp = client.post(
        "/v1/trade/trade-rates",
        json={
            "trade_ids": [
                "1947899592245044230",
                "1501247415383220650",
                "1501247415383220650",
            ]
        },
    )
    data = resp.json
    assert resp.json["status_code"] == 400
    assert data["reason"] == "缺失授权记录"

    shop.records.append(mock_grant_record)
    db.session.commit()

    mock_resp = TradeRateResponse()
    mock_resp.code = 1
    mocker.patch(
        "robot_processor.client.trade_client.client.GetTradeRateByTid",
        return_value=mock_resp,
    )
    resp = client.post(
        "/v1/trade/trade-rates",
        json={
            "trade_ids": [
                "1947899592245044230",
                "1501247415383220650",
                "1501247415383220650",
            ]
        },
    )
    assert resp.status_code == 200

    resp = client.post(
        "/v1/trade/trade-rates",
        json={
            "trade_ids": [
                "1947899592245044230",
                "1501247415383220650",
                "1501247415383220650",
            ]
        },
    )
    assert resp.status_code == 200


def test_get_erp_items(mocker, client, mock_erp_info):
    mock_response = DgtGetErpSkuListResponse()
    mocker.patch(
        "robot_processor.client.erp_item_client.client.GetErpSkuList",
        return_value=mock_response,
    )
    mock_erp_info.shop_id = client.shop.id
    mock_erp_info.erp_type = ErpType.JST
    client.shop.erps = [mock_erp_info]
    resp = client.post(
        "/v1/items/erp",
        json={"page_size": 50, "page_no": 1, "keywords": "123", "items": []},
    )

    assert resp.status_code == 200
    assert not resp.json["item_infos"]


def test_get_items_by_channel(mocker, client, mock_erp_info):
    mock_response = DgtGetErpSkuListResponse()
    mocker.patch(
        "robot_processor.client.erp_item_client.client.GetErpSkuList",
        return_value=mock_response,
    )
    mock_erp_info.shop_id = client.shop.id
    mock_erp_info.erp_type = ErpType.JST
    client.shop.erps = [mock_erp_info]
    resp = client.post(
        "/v1/items_sku/erp",
        json={
            "page_size": 50,
            "page_no": 1,
            "keywords": "123",
            "channel_type": client.shop.platform,
            "channel_no": client.shop.sid,
        },
    )
    assert resp.status_code == 200
    assert not resp.json["item_infos"]


def test_get_trade_upload_log(mocker, client):
    mocker.patch(
        "robot_processor.shop.models.ErpInfo" ".get_newest_erp_by_sid",
        return_value=MagicMock(erp_type=ErpType.JST),
    )
    mocker.patch(
        "robot_processor.shop.models.ErpInfo" ".get_by_sid",
        return_value=MagicMock(erp_type=ErpType.JST, token="token"),
    )
    mocker.patch(
        "robot_processor.job.jst_trade.TradesFinder.process",
        return_value=[
            MagicMock(
                o_id=456,
                type="普通订单",
                l_id="11",
                logistics_company="测试",
                f_weight=1,
            )
        ],
    )
    mocker.patch(
        "rpa.erp.jst.JstNewSDK.order_action_query",
        return_value=OrderActionQueryResp(
            data=OrderActionQueryResp.OrderActionList(
                datas=[
                    OrderAction(
                        oa_id=456,
                        name="name",
                        o_id=123,
                        remark="remark",
                        created="2023",
                        creator_name="creator",
                    )
                ]
            )
        ),
    )

    resp = client.get("/v1/trade/upload/log/123")
    resp_data = json.loads(str(resp.data.decode()))
    assert resp.status_code == 200
    assert len(resp_data.get("actions")) == 1
    assert resp_data.get("actions")[0].get("remark") == "remark"
    assert resp_data.get("logistics_no") == "11"
    assert resp_data.get("logistics_company") == "测试"


def test_get_resolved_address_by_ai(mocker, client):
    mocker.patch(
        "robot_processor.client.aliyun_addrp_client.extract_express",
        return_value={
            "receiver_address": "东其里村",
            "receiver_city": "运城市",
            "receiver_country": "中国",
            "receiver_district": "夏县",
            "receiver_mobile": "15534890232",
            "receiver_name": "三石",
            "receiver_phone": "15534890232",
            "receiver_state": "山西省",
            "receiver_town": "禹王镇",
            "receiver_zip": "",
        },
    )
    buyer_address = "三石，山西省运城市夏县禹王东其里村，15534890232"
    resp = client.get(f"/v1/buyer_address/word?&keywords={buyer_address}")

    assert resp.status_code == 200
    assert resp.json["receiver_address"] == "东其里村"


def test_get_address_by_tid(mocker, client_unauthorized, mock_erp_info):
    client, _, shop = client_unauthorized.login()
    mocker.patch(
        "robot_processor.client.buyer_client.get_receiver_info",
        return_value=GetReceiverInfoResponse(
            code=DgtResponseCode.OK,
            error_msg="",
            receiver_info={
                "receiver_name": "联通",
                "receiver_mobile": "***********-8888",
                "receiver_phone": "***********",
                "receiver_zip": "201000",
                "receiver_country": "中国",
                "receiver_state": "上海",
                "receiver_city": "上海市",
                "receiver_district": "长宁区",
                "receiver_town": "华阳路街道",
                "receiver_address": "长宁路1033号",
            },
        ),
    )
    mocker.patch(
        "rpa.erp.duohong.DuohongClient.query_address",
        return_value=Ok(
            {
                "name": "联通",
                "mobile": "***********-6666",
                "phone": "***********",
                "zip": "201000",
                "country": "中国",
                "state": "上海",
                "city": "上海市",
                "district": "长宁区",
                "town": "华阳路街道",
                "address": "长宁路1034号",
            }
        ),
    )
    shop.platform = "TAOBAO"
    resp = client.get(f"/v1/order_address/123?&sid={shop.sid}")

    assert resp.status_code == 200
    assert resp.json["mobile"] == "***********-8888"
    assert resp.json["address"] == "长宁路1033号"

    mocker.patch(
        "robot_processor.client.buyer_client.get_receiver_info",
        return_value=GetReceiverInfoResponse(
            code=DgtResponseCode.INTERNAL_SERVER_ERROR,
            error_msg="未登录",
            receiver_info={},
        ),
    )
    resp = client.get(f"/v1/order_address/123?&sid={shop.sid}")
    assert resp.json["status_code"] == 400
    assert resp.json["reason"] == "未登录"

    shop.platform = "DOUDIAN"
    mock_erp_info.shop_id = shop.id
    mock_erp_info.erp_type = ErpType.DUOHONG
    resp = client.get(f"/v1/order_address/123?&sid={shop.sid}")

    assert resp.status_code == 200
    assert resp.json["mobile"] == "***********-6666"
    assert resp.json["address"] == "长宁路1034号"


mock_receiver_info = ReceiveDetail(
    verify_type="",
    verify_params={
        "send_text": "",
        "send_text_note": "",
        "verify_account": "",
        "decision_conf": "",
        "block_pop": None,
    },
    is_send=0,
    receive_info=ReceiveInfo(
        post_receiver="李芳兰",
        post_tel="***********",
        post_addr=PostAddr(
            province=Location(name="甘肃省", id="62", has_child=False),
            city=Location(name="陇南市", id="621200", has_child=False),
            town=Location(name="成县", id="621221", has_child=False),
            street=Location(name="", id="", has_child=False),
            detail="甘肃省陇南市成县医保局隔壁梁旗路口进来五十米66号",
        ),
        can_view=2,
        post_tel_type=0,
        expire_time=0,
        is_show_edit_address=True,
        can_postpone=False,
        extension_number="",
        post_tel_mask="",
        address_tag=None,
        user_account_infos=None,
        ui_type="",
        buyer_tel_info=None,
    ),
    pre_receive_info=None,
    nick_name="嫣雨萌萌",
    is_success=True,
    error_msg="",
)


def test_get_address_by_tid_of_doudian(mocker, client_unauthorized, mock_erp_info):
    client, _, shop = client_unauthorized.login()
    # 抖店非多鸿erp
    # mock DoudianClient order_decrypt
    mocker.patch(
        "robot_processor.plugin.trade_api.doudian_client.order_decrypt",
        return_value=mock_receiver_info,
    )

    shop.platform = "DOUDIAN"
    mock_erp_info.shop_id = shop.id
    mock_erp_info.erp_type = ErpType.WDTULTI
    resp = client.get(f"/v1/order_address/123?&sid={shop.sid}")
    assert resp.status_code == 200
    assert resp.json["address"] == "甘肃省陇南市成县医保局隔壁梁旗路口进来五十米66号"

    mocker.patch(
        "robot_processor.plugin.trade_api.doudian_client.order_decrypt",
        return_value=ReceiveDetail(is_success=False, error_msg="客户端不在线"),
    )
    resp = client.get(f"/v1/order_address/123?&sid={shop.sid}")
    assert resp.status_code == 200
    assert not resp.json.get("address")


def test_upload_ack(client, cache):
    from robot_processor.currents import g

    g.login_user_detail = None
    resp = client.get(
        "/v1/uploads/image-compress-ack?url=http://localhost.com",
    )
    assert resp.status_code == 200
    assert not resp.json["ack"]
    cache.set("http://localhost.com", 1)
    resp = client.get("/v1/uploads/image-compress-ack?url=http://localhost.com")
    assert resp.status_code == 200
    assert resp.json["ack"]
    assert cache.get.call_count == 2


def test_compress_image(mocker, client):
    requests_response = requests.Response()
    requests_response.status_code = 200
    image = BytesIO(b"012345JFIF")
    requests_response._content = image.read()
    image.seek(0)
    mocker.patch(
        "robot_processor.client.image_oss_client.bucket.put_object",
        return_value=PutObjectResult(Response(requests_response)),
    )
    url = f"{oss_config.IMAGE_OSS_URL_BASE}test.jpeg"
    mocker.patch(
        "oss2.api.Bucket.get_object",
        return_value=GetObjectResult(Response(requests_response)),
    )
    mocker.patch("oss2.models.GetObjectResult.read", return_value=requests_response.content)
    result = async_compress_image(url)
    assert result == url


def test_find_spus(mocker, client_unauthorized, mock_grant_record, mock_erp_info):
    client, _, shop = client_unauthorized.login()
    shop.platform = "TAOBAO"
    mock_erp_info.erp_type = ErpType.JST
    mock_erp_info.shop_id = shop.id
    mock_resp = DgtGetSpuBySpuIdResponse()
    mock_resp.item_detail_info.spu_id = "1234"
    mocker.patch(
        "robot_processor.client.item_client.client.GetSpuBySpuId",
        return_value=mock_resp,
    )

    # no grant record
    resp = client.get(f"/v1/batch/spus?spu_id=1234&sid={shop.sid}")
    assert resp.json["data"] == []

    shop.records.append(mock_grant_record)
    db.session.commit()

    resp = client.get(f"/v1/batch/spus?spu_id=1234&sid={shop.sid}")
    assert resp.json["data"] == []

    resp = client.get(f"/v1/batch/spus?spu_id=1234&sid={shop.sid}")
    assert len(resp.json["data"]) == 0

    mock_resp.item_detail_info.item_title = "title"
    mocker.patch(
        "robot_processor.client.item_client.client.GetSpuBySpuId",
        return_value=mock_resp,
    )

    resp = client.get(f"/v1/batch/spus?spu_id=1234&sid={shop.sid}")
    assert len(resp.json["data"]) == 1

    mock_resp = DgtGetItemListResponse()
    mock_resp.page_no = 1
    mock_resp.page_size = 10

    mock_spu = mock_resp.item_detail_infos.add()
    mock_spu.spu_id = "spu_id112"
    mock_spu.item_title = "item_title112"
    mock_spu.pic_url = "pic_url112"
    mocker.patch(
        "robot_processor.client.item_client.client.DgtProviderItem",
        return_value=mock_resp,
    )
    resp = client.get(f"/v1/batch/spus?spu_id=1234&sid={shop.sid}")
    assert len(resp.json["data"]) == 1


def test_find_sku(mocker, client_unauthorized, mock_grant_record, mock_erp_info):
    client, _, shop = client_unauthorized.login()
    shop.platform = "TAOBAO"
    mock_erp_info.erp_type = ErpType.JST
    mock_erp_info.shop_id = shop.id
    mock_resp = DgtProviderSkuResponse()
    sku_info = DgtProviderSkuResponse.DgtProviderSkuInfo()
    sku_info.outer_sku_id = "sku_id1"
    sku_info.spu_id = "spu_id1"
    mock_resp.skuInfo.extend([sku_info])
    mock_resp.page_no = 1
    mock_resp.page_size = 20

    mocker.patch(
        "robot_processor.client.item_client.client.DgtProviderSku",
        return_value=mock_resp,
    )

    resp = client.get(f"/v1/batch/skus?spu_id=1234&sid={shop.sid}")
    assert len(resp.json["data"]) == 0

    shop.records.append(mock_grant_record)
    db.session.commit()

    resp = client.get(f"/v1/batch/skus?spu_id=1234&sid={shop.sid}")
    assert len(resp.json["data"]) == 0

    resp = client.get(f"/v1/batch/skus?spu_id=1234&sid={shop.sid}&source=erp")
    assert len(resp.json["data"]) == 1


@mark.parametrize("_mock_client_by_platform", ["TAOBAO", "PDD", "JST", "DOUDIAN"], indirect=True)
def test_find_trade(_mock_client_by_platform):
    client = _mock_client_by_platform
    resp = client.get(f"/v1/batch/trades?tid=123&sid={client.shop.sid}")
    assert len(resp.json["data"]) == 1
    item = resp.json["data"][0]
    assert isinstance(item, dict)


@mark.parametrize("_mock_client_by_platform", ["PDD"], indirect=True)
def test_pdd_order_address(_mock_client_by_platform, mocker):
    client = _mock_client_by_platform
    shop = _mock_client_by_platform.shop
    resp = client.get(f"/v1/order_address/123?sid={shop.sid}")
    assert resp.status_code == 200, resp.data.decode()
    address = resp.json
    assert address == {
        "mobile": "phone",
        "name": "name",
        "address": "address",
        "country": "country",
        "state": "province",
        "city": "city",
        "district": "town",
        "town": "",
        "zip": "",
        "status_code": 200,
    }


@mark.parametrize("_mock_client_by_platform", ["PDD"], indirect=True)
def test_address_api_failed(_mock_client_by_platform, mocker):
    client = _mock_client_by_platform
    shop = _mock_client_by_platform.shop
    mocker.patch(
        "robot_processor.client.pdd_bridge_client.batch_decrypt",
        side_effect=PddServiceError("拼多多错误"),
    )
    resp = client.get(f"/v1/order_address/123?sid={shop.sid}")
    assert resp.status_code == 200, resp.data.decode()
    assert resp.json["reason"] == "拼多多错误", resp.json
    assert resp.json["status_code"] == 400, resp.json


@mark.parametrize("_mock_client_by_platform", ["DOUDIAN"], indirect=True)
def test_doudian_order_address_api(_mock_client_by_platform, grant_record_factory, mocker):
    mocker.patch(
        "robot_processor.client.doudian_cloud.receiver_info",
        return_value=DoudianReceiverInfoResp(
            receiver_name="receiver_name",
            receiver_phone="receiver_phone",
            province="province",
            city="city",
            town="town",
            street="street",
            detail="detail",
        ),
    )
    client = _mock_client_by_platform
    shop = _mock_client_by_platform.shop
    grant_record_factory.create(
        shop_id=shop.id,
        app="doudian-xyz",
        expires_at_ms=(arrow.utcnow().to("Asia/Shanghai").int_timestamp + 3600) * 1000,
    )
    resp = client.get(f"/v1/order_address/123?sid={shop.sid}")
    assert resp.status_code == 200, resp.data.decode()
    address = resp.json
    assert address == {
        "mobile": "receiver_phone",
        "name": "receiver_name",
        "address": "detail",
        "state": "province",
        "city": "city",
        "district": "town",
        "town": "street",
        "status_code": 200,
    }


@mark.parametrize("_mock_client_by_platform", ["DOUDIAN"], indirect=True)
def test_doudian_order_address_api_failed(_mock_client_by_platform, grant_record_factory, mocker):
    mocker.patch(
        "robot_processor.client.doudian_cloud.receiver_info",
        side_effect=DoudianCloudServiceError("调用抖店服务失败: 禁止解密"),
    )
    client = _mock_client_by_platform
    shop = _mock_client_by_platform.shop
    grant_record_factory.create(
        shop_id=shop.id,
        app="doudian-xyz",
        expires_at_ms=(arrow.utcnow().to("Asia/Shanghai").int_timestamp + 3600) * 1000,
    )
    resp = client.get(f"/v1/order_address/123?sid={shop.sid}")
    assert resp.status_code == 200, resp.data.decode()
    assert resp.json["reason"] == "调用抖店服务失败: 禁止解密", resp.json
    assert resp.json["status_code"] == 400, resp.json


def test_erp_sku_inventories(client, mocker, erp_info_factory):
    erp_info_factory.create(
        shop_id=client.shop.id,
        erp_type=ErpType.WDT,
        meta={
            "sid": "xxxx",
            "app_key": "aas-ota",
            "app_secret": "07d327c4cc5cb9",
            "after_sale_shop_no": "00000030",
        },
    )
    mocker.patch(
        "rpa.erp.wdt.wdt.WdtOpenAPIClient.query_skus_by_sku_id",
        return_value=WdtQuerySkuResp.parse_raw(WDT_SKU_RESPONSE),
    )
    mocker.patch(
        "rpa.erp.wdt.wdt.WdtClient.stock_query",
        return_value=WdtStockQueryResp.parse_raw(WDT_STOCK_RESPONSE),
    )
    response = client.post(
        "/v1/erp_sku_inventories",
        json={"query_method": "SKU", "keyword": "0391LYBFLB01"},
    )
    assert response.status_code == 200
    assert response.json == json.loads(
        """{
            "succeed": true,
            "code": 200,
            "msg": null,
            "data": {
                "sku_infos": [
                    {
                        "sku": "0391LYBFLB01",
                        "spu": "0391LYBFLB",
                        "name": "0391路亚八编F级绿白",
                        "properties": "0391路亚 八编F级绿白100米/0.6裸盘",
                        "inventories": [
                            {
                                "sku_id": "0391LYBFLB01",
                                "warehouse_name": "主仓库",
                                "qty": 0,
                                "available_qty": 0
                            }
                        ]
                    }
                ],
                "is_completed": true,
                "reason": ""
            },
            "success": true,
            "reason": null,
            "error_display": null,
            "status_code": 200
            }"""
    )

    erp_info_factory.create(
        shop_id=client.shop.id,
        erp_type=ErpType.WDTULTI,
        meta={
            "sid": "xxxx",
            "app_key": "aas-ota",
            "app_secret": "07",
            "after_sale_shop_no": "000",
            "wdt_appkey": "xx",
            "wdt_secret": "xxx",
            "wdt_salt": "xxx",
        },
    )
    mocker.patch(
        "rpa.erp.wdtulti.wdtulti.WdtUltiOpenAPIClient.query_skus_by_sku_id",
        return_value=WdtUltiQuerySkuResp.parse_raw(WDTULTI_SKU_RESPONSE),
    )
    mocker.patch(
        "rpa.erp.wdtulti.wdtulti.WdtUltiOpenAPIClient.query_sku_stocks",
        return_value=WdtUltiStockQueryResp.parse_raw(WDTULTI_STOCK_RESPONSE),
    )
    response = client.post(
        "/v1/erp_sku_inventories",
        json={
            "query_method": "SKU",
            "keyword": "6941963040433",
            "warehouse": {"warehouse_code": "088", "warehouse_name": ""},
        },
    )
    assert response.status_code == 200
    assert response.json == json.loads(
        """{
            "succeed": true,
            "code": 200,
            "msg": null,
            "data": {
                "sku_infos": [
                    {
                        "sku": "6941963040433",
                        "spu": "BF01-005",
                        "name": "国风文字-精装本",
                        "properties": "皆如所愿",
                        "inventories": [
                            {
                                "sku_id": "6941963040433",
                                "warehouse_name": "天猫仓",
                                "qty": 0,
                                "available_qty": 0
                            }
                        ]
                    }
                ],
                "is_completed": true,
                "reason": ""
            },
            "success": true,
            "reason": null,
            "error_display": null,
            "status_code": 200
        }"""
    )

    mocker.patch(
        "rpa.erp.wdtulti.wdtulti.WdtUltiOpenAPIClient.query_skus_by_sku_id",
        return_value=WdtUltiQuerySkuResp.parse_obj({}),
    )
    response = client.post(
        "/v1/erp_sku_inventories",
        json={
            "query_method": "SKU",
            "keyword": "6941963040433",
            "warehouse": {"warehouse_code": "088", "warehouse_name": ""},
        },
    )
    assert response.status_code == 200
    assert response.json["data"] == {
        "is_completed": False,
        "reason": "商品查询失败：未知错误。",
        "sku_infos": [],
    }

    mocker.patch(
        "rpa.erp.wdtulti.wdtulti.WdtUltiOpenAPIClient.query_skus_by_sku_id",
        return_value=WdtUltiQuerySkuResp.parse_obj({"data": {"goods_list": [{"spec_list": []}]}}),
    )
    response = client.post(
        "/v1/erp_sku_inventories",
        json={
            "query_method": "SKU",
            "keyword": "6941963040433",
            "warehouse": {"warehouse_code": "088", "warehouse_name": ""},
        },
    )

    assert response.json == json.loads(
        """{
        "succeed": false,
        "code": 200,
        "msg": "未能找到商品信息",
        "data": null,
        "success": false,
        "reason": "未能找到商品信息",
        "error_display": "未能找到商品信息",
        "status_code": 200
    }"""
    )

    erp_info_factory.create(
        shop_id=client.shop.id,
        erp_type=ErpType.JST,
        meta={
            "co_id": "xxxx",
            "app_key": "aas-ota",
            "app_secret": "07d327c4cc5cb9",
            "after_sale_shop_no": "00000030",
        },
        token="xxxx",
    )
    mocker.patch(
        "rpa.erp.jst.sdk.JstNewSDK.wms_partner_query",
        return_value=JSTWmsPartnerQueryResp.parse_raw(JST_WAREHOUSE_RESPONSE),
    )
    mocker.patch(
        "rpa.erp.jst.sdk.JstNewSDK.query_skus_by_sku_ids",
        return_value=JSTQuerySkuResp.parse_raw(JST_SKU_RESPONSE),
    )
    mocker.patch(
        "rpa.erp.jst.sdk.JstNewSDK.inventory_query",
        return_value=JSTInventoryQueryResp.parse_raw(JST_STOCK_RESPONSE),
    )
    response = client.post(
        "/v1/erp_sku_inventories",
        json={
            "query_method": "SKU",
            "keyword": "6941963040433",
        },
    )
    assert response.status_code == 200
    assert len(response.json.get("data").get("sku_infos")) == 1
    assert len(response.json.get("data").get("sku_infos")[0].get("inventories")) == 8


def test_order_skus(client, mocker, erp_info_factory):
    erp_info_factory.create(
        shop_id=client.shop.id,
        erp_type=ErpType.WDT,
        meta={
            "sid": "xxxx",
            "app_key": "aas-ota",
            "app_secret": "07d327c4cc5cb9",
            "after_sale_shop_no": "00000030",
        },
    )

    mocker.patch(
        "rpa.erp.wdt.wdt.WdtClient.trade_query",
        return_value=TradeQueryResp.parse_raw(WDT_QUERY_TRADE_RESPONSE),
    )

    mocker.patch(
        "rpa.erp.wdt.wdt.WdtOpenAPIClient.query_skus_by_sku_id",
        return_value=WdtQuerySkuResp.parse_raw(WDT_QUERY_SINGLE_ITEMS_RESPONSE),
    )

    response = client.post(
        "/v1/order_skus",
        json={
            "sid": client.shop.sid,
            "query_source": "erp",
            "trades": [{"tid": "*******************", "oid": "*******************"}],
        },
    )

    assert response.json == json.loads(
        """{
        "succeed": true,
        "code": 200,
        "msg": null,
        "data": {
            "trades": [
                {
                    "tid": "*******************",
                    "oid": "*******************",
                    "products": [
                        {
                            "outer_sku_id": "735-665D升级充电版",
                            "outer_spu_id": "735665D",
                            "sku_id": "",
                            "spu_id": "",
                            "qty": 1.0,
                            "is_combine": false,
                            "child_skus": [],
                            "is_full": true,
                            "tid": "*******************",
                            "oid": "*******************",
                            "payment": 91.6,
                            "outer_properties": "跳舞48首小龙人（彩盒+邮购）",
                            "outer_sku_short_name": null,
                            "outer_spu_short_name": "",
                            "outer_sku_name": null,
                            "outer_spu_name": "跳舞48首小龙人（彩盒+邮购）",
                            "outer_sku_picture_url": "https://img.alicdn.com/bao/uploaded/i2/62950023/O1CN01I0SWBP1C2a1IDO1HX_!!62950023.jpg",
                            "outer_spu_picture_url": null,
                            "outer_price": 0.0,
                            "outer_cost_price": null,
                            "properties": null,
                            "sku_short_name": null,
                            "spu_short_name": null,
                            "sku_name": null,
                            "spu_name": null,
                            "sku_picture_url": null,
                            "spu_picture_url": null,
                            "price": null
                        }
                    ],
                    "has_missed": false
                }
            ]
        },
        "success": true,
        "reason": null,
        "error_display": null,
        "status_code": 200
    }"""
    )  # noqa

    erp_info_factory.create(
        shop_id=client.shop.id,
        erp_type=ErpType.JST,
        meta={
            "co_id": "xxxx",
            "app_key": "aas-ota",
            "app_secret": "07d327c4cc5cb9",
            "after_sale_shop_no": "00000030",
        },
        token="xxxx",
    )

    mocker.patch(
        "robot_processor.job.jst_trade.TradesFinder.try_get_orders",
        return_value=[
            JstBaseOrder.parse_obj(i) for i in json.loads(JST_QUERY_TRADE_RESPONSE).get("response").get("orders")
        ],
    )

    mocker.patch(
        "rpa.erp.jst.sdk.JstNewSDK.query_combine_skus_by_combine_sku_ids",
        return_value=JstQueryCombineSkuResp.parse_raw(JST_QUERY_COMBINE_ITEMS_RESPONSE),
    )

    mocker.patch(
        "rpa.erp.jst.sdk.JstNewSDK.query_skus_by_sku_ids",
        return_value=JSTQuerySkuResp.parse_raw(JST_QUERY_SINGLE_ITEMS_RESPONSE),
    )

    response = client.post(
        "/v1/order_skus",
        json={
            "sid": client.shop.sid,
            "query_source": "erp",
            "trades": [{"tid": "**************92609"}],
        },
    )

    assert response.json == json.loads(
        """{
        "succeed": true,
        "code": 200,
        "msg": null,
        "data": {
            "trades": [
                {
                    "tid": "**************92609",
                    "oid": null,
                    "products": [
                        {
                            "outer_sku_id": "G折叠桌-胡桃色-80CM-B12+L折叠椅-胡桃色架-米白-B12",
                            "outer_spu_id": "823979806034",
                            "sku_id": "",
                            "spu_id": "",
                            "qty": 0.0,
                            "is_combine": true,
                            "child_skus": [
                                {
                                    "outer_sku_id": "G折叠桌-胡桃色-80CM-B12",
                                    "outer_spu_id": "823979806034",
                                    "sku_id": "",
                                    "spu_id": "",
                                    "qty": 1.0,
                                    "is_combine": false,
                                    "child_skus": [],
                                    "is_full": true,
                                    "tid": null,
                                    "oid": null,
                                    "payment": null,
                                    "outer_properties": "折叠新升级-胡桃色-80CM-免安装",
                                    "outer_sku_short_name": null,
                                    "outer_spu_short_name": null,
                                    "outer_sku_name": "实木折叠书桌学生家用电脑桌卧室小户型床边学习小桌子简易办公桌",
                                    "outer_spu_name": null,
                                    "outer_sku_picture_url": "https://img.alicdn.com/bao/uploaded/i3/2088817296/O1CN01X1BZTl23lcfyBIybe_!!2088817296.jpg_30x30.jpg",
                                    "outer_spu_picture_url": null,
                                    "outer_price": 747.5,
                                    "outer_cost_price": null,
                                    "properties": null,
                                    "sku_short_name": null,
                                    "spu_short_name": null,
                                    "sku_name": null,
                                    "spu_name": null,
                                    "sku_picture_url": null,
                                    "spu_picture_url": null,
                                    "price": null
                                },
                                {
                                    "outer_sku_id": "L折叠椅-胡桃色架-米白-B12",
                                    "outer_spu_id": "L折叠椅-胡桃色架-米白-B12",
                                    "sku_id": "",
                                    "spu_id": "",
                                    "qty": 1.0,
                                    "is_combine": false,
                                    "child_skus": [],
                                    "is_full": true,
                                    "tid": null,
                                    "oid": null,
                                    "payment": null,
                                    "outer_properties": null,
                                    "outer_sku_short_name": null,
                                    "outer_spu_short_name": null,
                                    "outer_sku_name": "L折叠椅-胡桃色架-米白-B12",
                                    "outer_spu_name": null,
                                    "outer_sku_picture_url": null,
                                    "outer_spu_picture_url": null,
                                    "outer_price": null,
                                    "outer_cost_price": null,
                                    "properties": null,
                                    "sku_short_name": null,
                                    "spu_short_name": null,
                                    "sku_name": null,
                                    "spu_name": null,
                                    "sku_picture_url": null,
                                    "spu_picture_url": null,
                                    "price": null
                                }
                            ],
                            "is_full": true,
                            "tid": "**************92609",
                            "oid": "**************92609",
                            "payment": 469.0,
                            "outer_properties": "折叠新升级-胡桃色 成长椅(灰)80CM-免安装",
                            "outer_sku_short_name": null,
                            "outer_spu_short_name": null,
                            "outer_sku_name": "实木折叠书桌学生家用电脑桌卧室小户型床边学习小桌子简易办公桌",
                            "outer_spu_name": null,
                            "outer_sku_picture_url": "https://img.alicdn.com/bao/uploaded/i3/2088817296/O1CN01fZsclB23lcg0xs0Lt_!!2088817296.jpg_30x30.jpg",
                            "outer_spu_picture_url": null,
                            "outer_price": 1372.5,
                            "outer_cost_price": null,
                            "properties": null,
                            "sku_short_name": null,
                            "spu_short_name": null,
                            "sku_name": null,
                            "spu_name": null,
                            "sku_picture_url": null,
                            "spu_picture_url": null,
                            "price": null
                        }
                    ],
                    "has_missed": false
                }
            ]
        },
        "success": true,
        "reason": null,
        "error_display": null,
        "status_code": 200
    }"""
    )  # noqa

    erp_info_factory.create(
        shop_id=client.shop.id,
        erp_type=ErpType.JACKYUN,
        meta={
            "app_key": "xxx",
            "app_secret": "xxxxx",
            "customer_id": "xxx",
            "erp_account": "xxx",
        },
    )

    mocker.patch(
        "rpa.erp.jackyun.sdk.JackyunQmSDK.order_list",
        return_value=JackyunOrderListResp.parse_raw(JACKYUN_QUERY_TRADE_RESPONSE),
    )

    def mock_query_skus_by_sku_id(goods_no: str):
        if goods_no == "********":
            return JackyunGoodsListResp.parse_raw(JACKYUN_QUERY_SINGLE_FIRST_RESPONSE)
        else:
            return JackyunGoodsListResp.parse_raw(JACKYUN_QUERY_SINGLE_SECOND_RESPONSE)

    mocker.patch(
        "rpa.erp.jackyun.sdk.JackyunSDK.query_skus_by_sku_id",
        side_effect=mock_query_skus_by_sku_id,
    )

    response = client.post(
        "/v1/order_skus",
        json={
            "sid": client.shop.sid,
            "query_source": "erp",
            "trades": [{"tid": "4076103709860479734"}],
        },
    )

    assert response.json == json.loads(
        """{
        "succeed": true,
        "code": 200,
        "msg": null,
        "data": {
            "trades": [
                {
                    "tid": "4076103709860479734",
                    "oid": null,
                    "products": [
                        {
                            "outer_sku_id": "默认规格",
                            "outer_spu_id": "03030040",
                            "sku_id": "",
                            "spu_id": "",
                            "qty": 3.0,
                            "is_combine": false,
                            "child_skus": [],
                            "is_full": true,
                            "tid": "4076103709860479734",
                            "oid": "4076103709860479734",
                            "payment": 7.3217,
                            "outer_properties": null,
                            "outer_sku_short_name": null,
                            "outer_spu_short_name": null,
                            "outer_sku_name": "默认规格",
                            "outer_spu_name": "金标松茸一品鲜（松茸调味汁）40g",
                            "outer_sku_picture_url": "",
                            "outer_spu_picture_url": null,
                            "outer_price": 0.4565,
                            "outer_cost_price": null,
                            "properties": null,
                            "sku_short_name": null,
                            "spu_short_name": null,
                            "sku_name": null,
                            "spu_name": null,
                            "sku_picture_url": null,
                            "spu_picture_url": null,
                            "price": null
                        },
                        {
                            "outer_sku_id": "35克/袋",
                            "outer_spu_id": "********",
                            "sku_id": "",
                            "spu_id": "",
                            "qty": 1.0,
                            "is_combine": false,
                            "child_skus": [],
                            "is_full": true,
                            "tid": "4076103709860479734",
                            "oid": "4076103709860479734",
                            "payment": 17.9351,
                            "outer_properties": null,
                            "outer_sku_short_name": null,
                            "outer_spu_short_name": null,
                            "outer_sku_name": "35克/袋",
                            "outer_spu_name": "松茸调味料35g（香松）",
                            "outer_sku_picture_url": "",
                            "outer_spu_picture_url": null,
                            "outer_price": 0.887,
                            "outer_cost_price": null,
                            "properties": null,
                            "sku_short_name": null,
                            "spu_short_name": null,
                            "sku_name": null,
                            "spu_name": null,
                            "sku_picture_url": null,
                            "spu_picture_url": null,
                            "price": null
                        }
                    ],
                    "has_missed": false
                }
            ]
        },
        "success": true,
        "reason": null,
        "error_display": null,
        "status_code": 200
    }"""
    )  # noqa
