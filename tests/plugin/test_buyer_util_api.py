from leyan_proto.digismart.buyer_server.buyer_server_rpc_pb2 import GetUserNickByBuyerOpenUidResponse
from leyan_proto.digismart.buyer_server.buyer_server_rpc_pb2 import GetUserNickByTidResponse
from leyan_proto.digismart.dgt_common_pb2 import DgtResponseCode
from leyan_proto.digismart.trade.dgt_trade_pb2 import ResponseCode
from leyan_proto.digismart.trade.dgt_trade_pb2 import TryGetPlaintextNickByOpenUidResponse
from pytest import fixture


@fixture(autouse=True)
def setup(mock_trade_get_trade_by_tid):
    yield


def test_find_buyer_nick_by_tid(client, mock_buyer_server_get_buyer_nick_by_tid):
    mock_buyer_server_get_buyer_nick_by_tid.return_value = GetUserNickByTidResponse(
        buyer_nick="ut", code=DgtResponseCode.OK
    )
    response = client.get("/v1/buyer-util/buyer-nick-by-tid", query_string=dict(tid="tid"))

    assert response.json["succeed"]
    assert response.json["data"]["buyer_nick"] == "ut"


def test_cannot_find_buyer_nick_by_tid(client, mock_buyer_server_get_buyer_nick_by_tid):
    mock_buyer_server_get_buyer_nick_by_tid.return_value = GetUserNickByTidResponse(code=DgtResponseCode.NOT_FOUND)
    response = client.get("/v1/buyer-util/buyer-nick-by-tid", query_string=dict(tid="tid"))
    assert not response.json["succeed"]
    assert response.json["error_display"] == "未找到买家昵称"


def test_find_buyer_nick_by_open_uid(client, mock_buyer_server_get_buyer_nick_by_open_uid):
    mock_buyer_server_get_buyer_nick_by_open_uid.return_value = GetUserNickByBuyerOpenUidResponse(
        buyer_nick="ut", code=DgtResponseCode.OK
    )
    response = client.get(
        "/v1/buyer-util/buyer-nick-by-open-uid",
        query_string=dict(buyer_open_uid="open_uid"),
    )
    assert response.json["succeed"]
    assert response.json["data"]["buyer_nick"] == "ut"


def test_get_buyer_nick_from_trade(
    client,
    mock_buyer_server_get_buyer_nick_by_open_uid,
    mock_trade_server_try_get_plaintext_nick_by_open_uid,
):
    mock_buyer_server_get_buyer_nick_by_open_uid.return_value = GetUserNickByBuyerOpenUidResponse(
        code=DgtResponseCode.NOT_FOUND
    )
    mock_trade_server_try_get_plaintext_nick_by_open_uid.return_value = TryGetPlaintextNickByOpenUidResponse(
        code=ResponseCode.API_SUCCESS, plaintext_buyer_nick="ut"
    )
    response = client.get(
        "/v1/buyer-util/buyer-nick-by-open-uid",
        query_string=dict(buyer_open_uid="open_uid"),
    )
    assert response.json["succeed"]
    assert response.json["data"]["buyer_nick"] == "ut"
