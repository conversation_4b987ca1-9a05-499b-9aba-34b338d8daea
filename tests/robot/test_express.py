from rpa_control.ext import db


def test_consignee_decryption_api(client, mock_header_and_user, mocker):
    headers, _ = mock_header_and_user

    mock_cache = {}

    def mock_get(self, key):
        return mock_cache.get(key)

    def mock_set(self, key, value, timeout=None):
        mock_cache[key] = value
        return True

    mocker.patch("flask_caching.Cache.get", mock_get)
    mocker.patch("flask_caching.Cache.set", mock_set)
    rs = client.get("/api/robot/express_number_check?key=1&namespace=pdd&num=10",
                    headers=headers)
    db.session.commit()
    print(rs.text)
    assert rs.json['data'] is True
    rs = client.get("/api/robot/express_number_check?key=1&namespace=pdd&num=1",
                    headers=headers)
    db.session.commit()
    assert rs.json['data'] is True

    rs = client.get("/api/robot/express_number_check?key=1&namespace=pdd&num=11",
                    headers=headers)
    db.session.commit()
    assert rs.json['data'] is False
