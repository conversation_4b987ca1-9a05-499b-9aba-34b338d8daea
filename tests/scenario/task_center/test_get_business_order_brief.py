"""Covered: robot_processor.task_center.getter.get_business_order_brief"""

from robot_processor.ext import db
from robot_processor.task_center.getter import get_business_order_brief


def test_get_business_order_brief(client, mocker, mock_job, mock_business_order, mock_form):
    mock_job.business_order_id = mock_business_order.id
    mock_business_order.form_id = mock_form.id
    mock_business_order.job_history = [mock_job.id]
    mock_business_order.sid = client.shop.sid
    mock_business_order.current_job_id = mock_job.id
    db.session.commit()
    mock_form.subscribe(client.shop, True)

    assert mock_business_order.current_job.id == mock_job.id

    business_order_brief = get_business_order_brief(
        business_order=mock_business_order, form=mock_form.wraps(client.shop), operate_assistant=client.assistant
    )

    assert len(business_order_brief["current_jobs"]) == 1
