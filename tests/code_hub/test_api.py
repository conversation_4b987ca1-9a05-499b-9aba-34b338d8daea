from rpa_control.code_hub.models import VerificationCodeTask
from rpa_control.code_hub.enums import CodeTaskStateEnum
from unittest.mock import MagicMock


def test_create_sms_task(client, mock_user, mock_header, mocker):
    mocker.patch("rpa_control.code_hub.notification.sms_client.send_sms", return_value=True)
    path = "/api/code_hub/create_task"
    body = {
        "payloads": [{
            "channel": "SMS",
            "message": "自定义消息",
            "phone": "123"
        }]
    }
    r = client.post(path, json=body, headers=mock_header)
    assert r.status_code == 200, r.data.decode()
    assert r.json["success"]
    task = VerificationCodeTask.query.filter(VerificationCodeTask.task_id == r.json["task_id"]).first()
    assert task
    assert task.payloads
    assert task.state == CodeTaskStateEnum.PENDING
    assert task.user == mock_user


def test_create_ding_task(client, mock_user, mock_header, mocker):
    mock_resp = MagicMock(
        status_code=200,
        json=MagicMock(return_value={"errcode": 0, "errmsg": None})
    )
    mocker.patch("rpa_control.code_hub.notification.requests.post", return_value=mock_resp)
    path = "/api/code_hub/create_task"
    body = {
        "payloads": [{
            "channel": "DINGTALK",
            "message": "自定义消息",
            "phone": "123",
            "dingtalk_webhook_token": "123456"
        }]
    }
    r = client.post(path, json=body, headers=mock_header)
    assert r.status_code == 200, r.data.decode()
    assert r.json["success"]
    task = VerificationCodeTask.query.filter(VerificationCodeTask.task_id == r.json["task_id"]).first()
    assert task
    assert task.payloads
    assert task.state == CodeTaskStateEnum.PENDING
    assert task.user == mock_user


def test_get_pending_task_result(client, mock_header, mock_sms_pending_task):
    path = "/api/code_hub/get_task_result"
    params = {
        "task_id": mock_sms_pending_task.task_id
    }
    r = client.get(path, query_string=params, headers=mock_header)
    assert r.status_code == 200, r.data.decode()
    assert r.json["state"] == "PENDING"
    assert r.json["success"]


def test_get_pending_task_state(client, mock_sms_pending_task):
    """
    获取任务状态不需要用户登录
    """
    path = "/api/code_hub/get_task_state"
    params = {
        "task_id": mock_sms_pending_task.task_id
    }
    r = client.get(path, query_string=params)
    assert r.status_code == 200, r.data.decode()
    assert r.json["success"]
    assert r.json["state"] == "PENDING"


def test_get_task_result_forbidden(client, mock_sms_pending_task, mock_header2):
    """
    其他用户无法获取task结果
    """
    path = "/api/code_hub/get_task_result"
    params = {
        "task_id": mock_sms_pending_task.task_id
    }
    r = client.get(path, query_string=params, headers=mock_header2)
    assert r.status_code == 403, r.data.decode()
    assert not r.json["success"]


def test_submit_code(client, mock_sms_pending_task):
    """
    提交验证码不需要用户登录
    """
    path = "/api/code_hub/submit_code"
    body = {
        "task_id": mock_sms_pending_task.task_id,
        "code": "123456"
    }
    r = client.put(path, json=body)
    assert r.status_code == 200, r.data.decode()
    assert r.json["success"]
    assert mock_sms_pending_task.state == CodeTaskStateEnum.SUBMITTED
    assert mock_sms_pending_task.code == "123456"


def test_invalid_submit_code(client, mock_sms_finished_task):
    """
    已完成的任务不能继续提交
    """
    path = "/api/code_hub/submit_code"
    body = {
        "task_id": mock_sms_finished_task.task_id,
        "code": "123456"
    }
    r = client.put(path, json=body)
    assert r.status_code == 400, r.data.decode()
    assert not r.json["success"]
