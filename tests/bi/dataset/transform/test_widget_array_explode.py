from pytest import fixture

from robot_processor.bi.dataset.etl.transform import (
    is_column_data_need_explode,
    widget_array_type_explode,
)
from robot_processor.form.types import WidgetDataType


class TestIsColumnDataNeedExplode:
    """测试列数据是否需要拆分，是通过报表设计模式中选择的 x 轴维度 DataDimension 来判断"""

    def test_should_return_true_when_column_data_type_is_enum_object_select(self):
        from robot_processor.bi.generate_schema.dimension.custom import (
            EnumObjectSelectBuilder,
        )

        dimension = EnumObjectSelectBuilder(
            widget_type="select",
            sync_widget_key=None,
            widget_info_list=[{"key": "", "option_value": {"label": ""}}],
            widget_data_type=WidgetDataType.ENUM
        ).build_select()
        assert is_column_data_need_explode(dimension) is True

    def test_should_return_true_when_column_data_type_is_object_order(self):
        from robot_processor.bi.generate_schema.dimension.custom import (
            ObjectOrderBuilder,
        )

        dimension = ObjectOrderBuilder(
            widget_type="order",
            sync_widget_key=None,
            widget_info_list=[{"key": "", "option_value": {"label": ""}}],
            widget_data_type=WidgetDataType.TABLE,
        ).build_order()
        assert is_column_data_need_explode(dimension) is True

    def test_should_return_true_when_column_data_type_is_object_reissue_product(self):
        from robot_processor.bi.generate_schema.dimension.custom import (
            ObjectReissueProductBuilder,
        )

        dimension = ObjectReissueProductBuilder(
            widget_type="reissue-product",
            sync_widget_key=None,
            widget_info_list=[{"key": "", "option_value": {"label": ""}}],
            widget_data_type=WidgetDataType.TABLE,
        ).build_reissue_product()
        assert is_column_data_need_explode(dimension) is True

    def test_should_return_true_when_column_data_type_is_object_product(self):
        from robot_processor.bi.generate_schema.dimension.custom import (
            ObjectProductBuilder,
        )

        dimension = ObjectProductBuilder(
            widget_type="product",
            sync_widget_key=None,
            widget_info_list=[{"key": "", "option_value": {"label": ""}}],
            widget_data_type=WidgetDataType.TABLE,
        ).build_product()
        assert is_column_data_need_explode(dimension) is True

    def test_should_return_false_when_column_object_field_is_spu(self):
        from robot_processor.bi.generate_schema.dimension.custom import (
            ObjectProductBuilder,
        )

        dimension = ObjectProductBuilder(
            widget_type="product",
            sync_widget_key=None,
            widget_info_list=[{"key": "ut", "option_value": {"label": "ut"}}],
            widget_data_type=WidgetDataType.TABLE,
        ).build_product_spu()
        assert is_column_data_need_explode(dimension.dimension) is True

    def test_should_return_false_when_column_object_field_is_sku(self):
        from robot_processor.bi.generate_schema.dimension.custom import (
            ObjectProductBuilder,
        )

        dimension = ObjectProductBuilder(
            widget_type="product",
            sync_widget_key=None,
            widget_info_list=[{"key": "ut", "option_value": {"label": "ut"}}],
            widget_data_type=WidgetDataType.TABLE,
        ).build_product_sku()
        assert is_column_data_need_explode(dimension.dimension) is True


@fixture
def dataset():
    from pkg_resources import resource_stream
    from robot_processor.bi.dataset.etl.extract import dataset_from_dict

    resource_package = "tests.bi.resource.dataset"
    with resource_stream(resource_package, "case_widget_array_explode.py") as stream:
        dataset = dataset_from_dict(eval(stream.read().decode("utf-8")))

    yield dataset


@fixture
def dimensions():
    from robot_processor.bi.generate_schema.dimension import custom

    yield [
        custom.EnumObjectSelectBuilder(
            "select", None, [{"option_value": {"label": "处理结果"}, "key": ""}],
            widget_data_type=WidgetDataType.ENUM
        ).build_select(),
        custom.ObjectOrderBuilder(
            "order", None, [{"option_value": {"label": "订单"}, "key": ""}],
            widget_data_type=WidgetDataType.TABLE
        ).build_order(),
        custom.ObjectReissueProductBuilder(
            "reissue-product", None, [{"option_value": {"label": "补发商品"}, "key": ""}],
            widget_data_type=WidgetDataType.TABLE
        ).build_reissue_product(),
        custom.ObjectProductBuilder(
            "product", None, [{"option_value": {"label": "商品"}, "key": ""}],
            widget_data_type=WidgetDataType.TABLE
        ).build_product(),
        custom.ObjectProductBuilder(
            "product", None, [{"key": "", "option_value": {"label": "商品"}}],
            widget_data_type=WidgetDataType.TABLE
        ).build_product_spu().dimension,
    ]


@fixture
def statistics():
    from google.protobuf.json_format import ParseDict
    from leyan_proto.digismart.robot.bi.generate_pb2 import DataStatistics
    from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimension
    from robot_processor.utils import message_to_dict
    from robot_processor.bi.generate_schema.dimension.system import BO_ID
    from robot_processor.bi.generate_schema.dimension.custom import NumberBuilder

    bo_count = ParseDict(message_to_dict(BO_ID), DataDimension())
    bo_count.title = "工单数量"

    sum_payment = NumberBuilder(
        "number", None, [{"key": "", "option_value": {"label": "总金额"}}],
        widget_data_type=WidgetDataType.NUMBER
    ).build_number()
    avg_payment = NumberBuilder(
        "number", None, [{"key": "", "option_value": {"label": "平均金额"}}],
        widget_data_type=WidgetDataType.NUMBER
    ).build_number()

    yield [
        # 工单数量
        DataStatistics(dimension=bo_count, operator=DataStatistics.OPERATOR_COUNT),
        # 总金额
        DataStatistics(dimension=sum_payment, operator=DataStatistics.OPERATOR_SUM),
        # 平均金额
        DataStatistics(dimension=avg_payment, operator=DataStatistics.OPERATOR_AVERAGE),
    ]


def test_widget_select_list_split(dataset, dimensions, statistics):
    import numpy as np

    df = widget_array_type_explode(dataset, dimensions, statistics)
    expect = np.array(
        [
            [
                "仅退款",
                "123:345",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "{商品1:  SKU:5030620604778, SPU:************, 价格:30, 规格:颜色分类:灰色",
                "衣服",
                5.0,
                20.0,
                10.0,
            ],
            [
                "仅退款",
                "123:345",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "{商品1:  SKU:5030620604778, SPU:************, 价格:30, 规格:颜色分类:灰色",
                "裤子",
                5.0,
                20.0,
                10.0,
            ],
            [
                "仅退款",
                "123:345",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "尺码:155/85, 外部SKU:5030620604778, 商品标题:测试使用同步}",
                "衣服",
                5.0,
                20.0,
                10.0,
            ],
            [
                "仅退款",
                "123:345",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "尺码:155/85, 外部SKU:5030620604778, 商品标题:测试使用同步}",
                "裤子",
                5.0,
                20.0,
                10.0,
            ],
            [
                "仅退款",
                "345:456",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "{商品1:  SKU:5030620604778, SPU:************, 价格:30, 规格:颜色分类:灰色",
                "衣服",
                5.0,
                20.0,
                10.0,
            ],
            [
                "仅退款",
                "345:456",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "{商品1:  SKU:5030620604778, SPU:************, 价格:30, 规格:颜色分类:灰色",
                "裤子",
                5.0,
                20.0,
                10.0,
            ],
            [
                "仅退款",
                "345:456",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "尺码:155/85, 外部SKU:5030620604778, 商品标题:测试使用同步}",
                "衣服",
                5.0,
                20.0,
                10.0,
            ],
            [
                "仅退款",
                "345:456",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "尺码:155/85, 外部SKU:5030620604778, 商品标题:测试使用同步}",
                "裤子",
                5.0,
                20.0,
                10.0,
            ],
            ["仅退款", "<未填写>", "<未填写>", "<未填写>", "裤子", 11.0, 31.0, 42.0],
            ["仅退款", "<未填写>", "<未填写>", "<未填写>", "鞋子", 11.0, 31.0, 42.0],
            ["全额仅退款>返运费险", "<未填写>", "<未填写>", "<未填写>", "衣服", 29.0, 13.0, 24.0],
            [
                "补偿",
                "123:345",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "{商品1:  SKU:5030620604778, SPU:************, 价格:30, 规格:颜色分类:灰色",
                "衣服",
                5.0,
                20.0,
                10.0,
            ],
            [
                "补偿",
                "123:345",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "{商品1:  SKU:5030620604778, SPU:************, 价格:30, 规格:颜色分类:灰色",
                "裤子",
                5.0,
                20.0,
                10.0,
            ],
            [
                "补偿",
                "123:345",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "尺码:155/85, 外部SKU:5030620604778, 商品标题:测试使用同步}",
                "衣服",
                5.0,
                20.0,
                10.0,
            ],
            [
                "补偿",
                "123:345",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "尺码:155/85, 外部SKU:5030620604778, 商品标题:测试使用同步}",
                "裤子",
                5.0,
                20.0,
                10.0,
            ],
            [
                "补偿",
                "345:456",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "{商品1:  SKU:5030620604778, SPU:************, 价格:30, 规格:颜色分类:灰色",
                "衣服",
                5.0,
                20.0,
                10.0,
            ],
            [
                "补偿",
                "345:456",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "{商品1:  SKU:5030620604778, SPU:************, 价格:30, 规格:颜色分类:灰色",
                "裤子",
                5.0,
                20.0,
                10.0,
            ],
            [
                "补偿",
                "345:456",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "尺码:155/85, 外部SKU:5030620604778, 商品标题:测试使用同步}",
                "衣服",
                5.0,
                20.0,
                10.0,
            ],
            [
                "补偿",
                "345:456",
                "SPUID:************,SKUID:4767367422532,补发数量:1",
                "尺码:155/85, 外部SKU:5030620604778, 商品标题:测试使用同步}",
                "裤子",
                5.0,
                20.0,
                10.0,
            ],
            ["补发", "<未填写>", "<未填写>", "<未填写>", "鞋子", 6.0, 21.0, 12.0],
            ["退货>补偿", "<未填写>", "<未填写>", "<未填写>", "鞋子", 6.0, 21.0, 12.0],
        ],
        dtype=object,
    )
    assert np.array_equal(df.values, expect)
