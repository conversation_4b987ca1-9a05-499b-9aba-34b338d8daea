"""为单元测试构建 shop, assistant 等数据, 且这些数据的特征应该尽可能和真实场景相似."""

import random
import time
from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from typing import List
from typing import Literal
from typing import Optional
from typing import Self
from typing import T
from typing import Type

from factory import fuzzy
from flask.testing import FlaskClient
from pytest import fixture
from sqlalchemy.event import listens_for

from robot_processor import auth
from robot_processor.app import app
from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.currents import g
from robot_processor.enums import Creator
from robot_processor.logging import vars as log_vars
from robot_processor.shop import kiosk_models as km
from robot_processor.shop.models import ContractInfo
from robot_processor.shop.models import Shop as ShopOrm
from robot_processor.users import models as um


@dataclass
class Org:
    id: int
    name: str
    contract: Literal["valid", "expired", None]
    roles: dict[str, str] = field(default_factory=dict)


valid_org = Org(1, "org1", "valid", roles={"custom": "BUSINESS_ORDER_FRONT_DESK"})
expired_org = Org(2, "org2", "expired")
no_contract_org = Org(3, "org3", None)
other_valid_org = Org(4, "org4", "valid")
orgs = [valid_org, expired_org, no_contract_org, other_valid_org]


shop_counter = (i for i in range(1, 10000))


@dataclass
class Shop:
    sid: str
    org_id: int
    platform: str
    name: str
    id: int = field(default_factory=lambda: next(shop_counter))


shops = [
    Shop("11", 1, "TAOBAO", "org1_shop1"),
    Shop("12", 1, "TMALL", "org1_shop2"),
    Shop("13", 1, "PDD", "org1_shop3"),
]
shops.extend([Shop("21", 2, "TAOBAO", "org2_shop1")])
shops.extend([Shop("31", 3, "TAOBAO", "org3_shop1")])
shops.extend([Shop("41", 4, "TAOBAO", "org4_shop1")])

all_permissions = "AUTO_APP BATCH_PAY BUSINESS_ORDER_ASSIGN BUSINESS_ORDER_CLOSE BUSINESS_ORDER_DELETE BUSINESS_ORDER_FRONT_DESK BUSINESS_ORDER_HANDLER BUSINESS_ORDER_MANAGER BUSINESS_ORDER_RETRY BUYER_SERVER_BUFFET CUSTOMIZE_BI DATA_REPORT DETAIL_DATA_REPORT DETAIL_DATA_REPORT_MULTI_EXPORT DETAIL_DATA_REPORT_MULTI_VIEW DETAIL_DATA_REPORT_SINGLE_EXPORT DETAIL_DATA_REPORT_SINGLE_VIEW EXCEPTIONAL_EDIT EXCEPTIONAL_VIEW ORG_EXCEPTIONAL_EDIT ORG_EXCEPTIONAL_VIEW ORG_TASK_BUSINESS_ORDER_DELETE ORG_TASK_EDIT ORG_TASK_IMPORT ORG_TASK_VIEW PROVIDER_CENTER PROVIDER_EDIT PROVIDER_EXPORT PROVIDER_VIEW SYSTEM_SETTING TASK_BUSINESS_ORDER_DELETE TASK_CENTER TASK_EDIT TASK_IMPORT TASK_VIEW TEMPLATE_CENTER TEMPLATE_CREATE_AND_EDIT TEMPLATE_VIEW"  # NOQA
default_roles = dict(
    admin="AUTO_APP BATCH_PAY BUSINESS_ORDER_ASSIGN BUSINESS_ORDER_CLOSE BUSINESS_ORDER_RETRY BUYER_SERVER_BUFFET DATA_REPORT DETAIL_DATA_REPORT_MULTI_EXPORT DETAIL_DATA_REPORT_MULTI_VIEW DETAIL_DATA_REPORT_SINGLE_EXPORT DETAIL_DATA_REPORT_SINGLE_VIEW EXCEPTIONAL_EDIT EXCEPTIONAL_VIEW ORG_EXCEPTIONAL_EDIT ORG_EXCEPTIONAL_VIEW BUSINESS_ORDER_DELETE ORG_TASK_BUSINESS_ORDER_DELETE ORG_TASK_EDIT ORG_TASK_IMPORT ORG_TASK_VIEW PROVIDER_CENTER PROVIDER_EDIT PROVIDER_EXPORT PROVIDER_VIEW SYSTEM_SETTING TASK_BUSINESS_ORDER_DELETE TASK_EDIT TASK_IMPORT TASK_VIEW TEMPLATE_CREATE_AND_EDIT TEMPLATE_VIEW",  # NOQA
    assistant="AUTO_APP BATCH_PAY DATA_REPORT TEMPLATE_CREATE_AND_EDIT TEMPLATE_VIEW",
)


@dataclass
class LeyanUser:
    org_id: int
    user_id: int
    user_nick: str
    phone: str
    groups: List[str]
    sids: List[str]
    roles: List[str]

    @classmethod
    def by_name(cls, name) -> Self | None:
        return next((u for u in leyan_users if u.user_nick == name), None)

    def as_account_detail(self) -> AccountDetailV2:
        return AccountDetailV2(
            user_type=Creator.LEYAN, user_id=self.user_id, user_nick=self.user_nick, phone=self.phone
        )

    @property
    def org(self) -> Org:
        return next(org for org in orgs if org.id == self.org_id)

    @property
    def shops(self) -> List[Shop]:
        return [shop for shop in shops if shop.sid in self.sids]


# leyan users for org 1
leyan_users = [
    LeyanUser(1, 1, "org1_user1", "123", ["org1_leyan_group1"], ["11", "12", "13"], ["admin"]),
    LeyanUser(1, 2, "org1_user2", "124", [], ["11", "12", "13"], ["assistant", "custom"]),
]
# leyan users for org 2
leyan_users.extend(
    [
        LeyanUser(2, 3, "org2_user1", "223", [], ["21"], []),
        LeyanUser(2, 4, "org2_user2", "224", ["org2_leyan_group1"], ["21"], []),
        LeyanUser(2, 5, "org2_user3", "225", ["org2_leyan_group1", "org2_leyan_group2"], ["21"], []),
    ]
)
# leyan users for org 3
leyan_users.extend([LeyanUser(3, 6, "org3_user1", "323", [], ["31"], [])])
# leyan users for org 4
leyan_users.extend([LeyanUser(4, 7, "org4_user1", "423", [], ["41"], [])])


@dataclass
class PlatFormUser:
    user_id: int
    user_nick: str
    sid: str
    groups: List[str]
    bound_leyan_user: Optional[str]

    @classmethod
    def by_name(cls, name) -> "AccountDetailV2":
        return next(u for u in platform_users if u.user_nick == name).as_account_detail()

    @classmethod
    def by_sid(cls, sid) -> "PlatFormUser":
        return next(u for u in platform_users if u.sid == sid)

    def as_account_detail(self) -> AccountDetailV2:
        return AccountDetailV2(
            user_type=Creator.ASSISTANT, user_id=self.user_id, user_nick=self.user_nick, sub_id=self.user_nick
        )

    @property
    def shop(self) -> Shop:
        return next(shop for shop in shops if shop.sid == self.sid)


# platform users for shop 11
platform_users = [
    PlatFormUser(1, "org1_shop1:user1", "11", ["org1_platform_group1"], "org1_user1"),
    PlatFormUser(2, "org1_shop1:user2", "11", ["org1_platform_group1"], "org1_user2"),
    PlatFormUser(3, "org1_shop1:user3", "11", [], None),
]
# platform users for shop 12
platform_users.extend([PlatFormUser(4, "org1_shop2:user1", "12", ["org1_platform_group1"], "org1_user1")])
# platform users for shop 13
platform_users.extend([PlatFormUser(5, "org1_shop3:user1", "13", [], None)])
# platform users for shop 21
platform_users.extend(
    [PlatFormUser(6, "org2_shop1:user1", "21", [], "org2_user1"), PlatFormUser(7, "org2_shop1:user2", "21", [], None)]
)
# platform users for shop 31
platform_users.extend([PlatFormUser(8, "org3_shop1:user1", "31", [], None)])
# platform users for shop 41
platform_users.extend([PlatFormUser(9, "org4_shop1:user1", "41", [], None)])


@fixture(scope="session", autouse=True)
def create_db_records_for_mock_shops(db):
    with app.app_context():
        permission_ids = {}
        for permission in all_permissions.split():
            record = um.Permission(
                function_code=permission,
                function_type=1,
                function_name=permission,
                function_display_name="",
                function_desc="",
                module_level="",
                created_by="test",
                updated_by="test",
            )
            db.session.add(record)
            db.session.flush()
            permission_ids[permission] = record.id

        for role_name, permissions in default_roles.items():
            role = um.Role(
                org_id=0,
                role_code=role_name,
                role_name=role_name,
                role_desc="",
                role_type=1,
                created_by="test",
                updated_by="test",
            )
            db.session.add(role)
            db.session.flush()
            for permission in permissions.split():
                role_permission = um.RolePermissionMapping(
                    role_id=role.id, function_id=permission_ids[permission], created_by="test", updated_by="test"
                )
                db.session.add(role_permission)
        shops_, contracts_ = [], []
        for shop in shops:
            shop_orm = ShopOrm(
                id=shop.id,
                title=shop.name,
                nick=shop.name,
                seller_id=shop.name,
                sid=shop.sid,
                org_id=str(shop.org_id),
                open_id=shop.sid,
                platform=shop.platform,
                channel_id=shop.id + 10,
            )  # 故意加 10 以示区分
            kiosk_shop = km.KioskShop(
                id=shop.id + 10, org_id=shop.org_id, sid=shop.sid, platform=shop.platform.lower(), nick=shop.name
            )
            db.session.add(shop_orm)
            db.session.add(kiosk_shop)
            shops_.append(shop_orm)
        for org in orgs:
            kiosk_org = km.KioskOrg(id=org.id, org_name=org.name)
            db.session.add(kiosk_org)
            if org.contract == "valid":
                contract = ContractInfo(org_id=str(org.id), end_ts=int(time.time() + 10**6))
                db.session.add(contract)
                contracts_.append(contract)
            elif org.contract == "expired":
                contract = ContractInfo(org_id=str(org.id), end_ts=int(time.time() - 10**6))
                db.session.add(contract)
                contracts_.append(contract)
            for role_name in default_roles:
                role = um.Role(
                    org_id=org.id,
                    role_code=role_name,
                    role_name=role_name,
                    role_desc="",
                    role_type=1,
                    created_by="test",
                    updated_by="test",
                )
                db.session.add(role)
                db.session.flush()
            for role_name, permissions in org.roles.items():
                role = um.Role(
                    org_id=org.id,
                    role_code=role_name,
                    role_name=role_name,
                    role_desc="",
                    role_type=2,
                    created_by="test",
                    updated_by="test",
                )
                db.session.add(role)
                db.session.flush()
                for permission in permissions.split():
                    role_permission = um.RolePermissionMapping(
                        role_id=role.id, function_id=permission_ids[permission], created_by="test", updated_by="test"
                    )
                    db.session.add(role_permission)

        for leyan_user in leyan_users:
            db_leyan_user = um.LeyanUser(
                id=leyan_user.user_id,
                org_id=leyan_user.org_id,
                phone=leyan_user.phone,
                nickname=leyan_user.user_nick,
                status=1,
                locked=0,
            )
            db.session.add(db_leyan_user)
            for shop in leyan_user.shops:
                mapping = um.LeyanUserShopMapping(
                    user_id=leyan_user.user_id, shop_id=shop.id + 10, updated_by="test", updated_at=datetime.now()
                )
                db.session.add(mapping)
            for group_uuid in leyan_user.groups:
                group = um.UserGroup.query.filter_by(group_uuid=group_uuid).first()
                if not group:
                    group = um.UserGroup(
                        group_uuid=group_uuid, name=group_uuid, org_id=leyan_user.org_id, type=um.UserGroup.TYPE_LEYAN
                    )
                    db.session.add(group)
                mapping = um.GroupUserMapping(
                    group_uuid=group_uuid, user_id=leyan_user.user_id, user_type=Creator.LEYAN.value
                )
                db.session.add(mapping)
                db.session.flush()
            for role in leyan_user.roles:
                role = um.Role.query.filter_by(role_code=role, org_id=leyan_user.org_id).first()
                mapping = um.UserRoleMapping(
                    user_id=leyan_user.user_id, role_id=role.id, created_by="test", updated_by="test"
                )
                db.session.add(mapping)
        for platform_user in platform_users:
            channel_id = platform_user.shop.id + 10
            db_platform_user = um.PlatformUser(
                id=platform_user.user_id,
                channel_id=channel_id,
                nick=platform_user.user_nick,
                sid=platform_user.sid,
                platform=platform_user.shop.platform.lower(),
                status=1,
                sub_id=platform_user.user_nick,
            )
            db.session.add(db_platform_user)
            leyan_user = LeyanUser.by_name(platform_user.bound_leyan_user)
            if leyan_user:
                mapping = um.PlatformUserMapping(
                    org_id=leyan_user.org_id,
                    channel_id=channel_id,
                    user_id=leyan_user.user_id,
                    platform_user_id=platform_user.user_id,
                    platform_nickname=platform_user.user_nick,
                )
                db.session.add(mapping)
            for group_uuid in platform_user.groups:
                group = um.UserGroup.query.filter_by(group_uuid=group_uuid).first()
                if not group:
                    group = um.UserGroup(
                        group_uuid=group_uuid,
                        name=group_uuid,
                        org_id=leyan_user.org_id,
                        type=um.UserGroup.TYPE_PLATFORM,
                    )
                    db.session.add(group)
                mapping = um.GroupUserMapping(
                    group_uuid=group_uuid, user_id=platform_user.user_id, user_type=Creator.ASSISTANT.value
                )
                db.session.add(mapping)
                db.session.flush()
        db.session.commit()
        shop_ids = set(s.id for s in shops_)
        contract_ids = set(c.id for c in contracts_)

    @listens_for(Shop, "before_update")
    def prevent_modify_builtin_shops(mapper, connection, shop):
        if shop and shop.id in shop_ids:
            raise ValueError("不能修改内置店铺, 请用 shop_factory 生成新的店铺")

    @listens_for(ContractInfo, "before_update")
    def prevent_modify_builtin_contracts(mapper, connection, contract):
        if contract and contract.id in contract_ids:
            raise ValueError("不能修改内置合同, 请用 contract_info_factory 生成新的合同")


class Org1Client(FlaskClient):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.org_id = 1
        self.sid = "11"
        self.shop: ShopOrm = ShopOrm.query.filter_by(sid="11").first()
        self.assistant: AccountDetailV2 = PlatFormUser.by_name("org1_shop1:user1")
        self.leyan_assistant: AccountDetailV2 = self.assistant.get_bound_leyan_user()
        self.another_assistant: AccountDetailV2 = PlatFormUser.by_name("org1_shop1:user2")
        self.another_leyan_assistant: AccountDetailV2 = self.another_assistant.get_bound_leyan_user()

    def other_shop(self, platform: Optional[str] = None, name: Optional[str] = None) -> ShopOrm:
        candidate_sids = []
        for s in shops:
            if s.org_id != int(self.org_id):
                continue
            if platform is not None and s.platform != platform.upper():
                continue
            if s.sid == self.sid:
                continue
            if name and s.name != name:
                continue
            candidate_sids.append(s.sid)
        return ShopOrm.query.filter_by(sid=(random.choice(candidate_sids))).first()

    def other_assistant(self, sid=None, user_nick=None, has_bound_leyan_user=None) -> AccountDetailV2:
        candidates = []
        for u in platform_users:
            if self.assistant.user_type == Creator.ASSISTANT and u.user_id == self.assistant.user_id:
                continue
            if u.shop.org_id != int(self.org_id):
                continue
            if sid is not None and u.sid != sid:
                continue
            if user_nick is not None and u.user_nick != user_nick:
                continue
            if has_bound_leyan_user is True and u.bound_leyan_user is None:
                continue
            if has_bound_leyan_user is False and u.bound_leyan_user is not None:
                continue
            candidates.append(u.as_account_detail())
        return random.choice(candidates)


class Org2Client(FlaskClient):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.org_id = 2
        self.sid = "21"
        self.shop: ShopOrm = ShopOrm.query.filter_by(sid="21").first()
        self.assistant: AccountDetailV2 = PlatFormUser.by_name("org2_shop1:user1")
        self.leyan_assistant: AccountDetailV2 = self.assistant.get_bound_leyan_user()
        self.another_assistant: AccountDetailV2 = PlatFormUser.by_name("org2_shop1:user2")
        self.another_leyan_assistant: AccountDetailV2 = self.another_assistant.get_bound_leyan_user()


@fixture
def gen_fresh_client(kiosk_org_factory, shop_factory, _client_factory):
    from robot_processor.ext import db

    def create_org_client():
        shop = shop_factory.create()
        kiosk_org_factory.create(id=shop.org_id, status=1)
        platform_user = um.PlatformUser(
            channel_id=shop.channel_id,
            nick=f"org{shop.org_id}:user",
            sid=shop.sid,
            platform=shop.platform.lower(),
            status=1,
            sub_id=f"org{shop.org_id}:user",
        )
        leyan_user = um.LeyanUser(
            org_id=shop.org_id, phone=str(shop.org_id), nickname=f"org{shop.org_id}:user", status=1, locked=0
        )
        db.session.add_all([platform_user, leyan_user])
        db.session.commit()
        db.session.add(
            um.PlatformUserMapping(
                org_id=shop.org_id,
                channel_id=shop.channel_id,
                user_id=leyan_user.id,
                platform_user_id=platform_user.id,
                platform_nickname=platform_user.nick,
            )
        )
        db.session.commit()

        class MockClient(FlaskClient):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self.shop = shop
                self.org_id = int(shop.org_id)
                self.sid = shop.sid
                self.assistant = AccountDetailV2(
                    user_type=Creator.ASSISTANT.value, user_id=platform_user.id, user_nick=platform_user.nick
                )
                self.leyan_assistant = AccountDetailV2(
                    user_type=Creator.LEYAN.value,
                    user_id=leyan_user.id,
                    user_nick=leyan_user.nickname,
                    phone=leyan_user.phone,
                )

        return _client_factory(MockClient)

    yield create_org_client


@fixture
def _client_factory(app_context, mocker, create_db_records_for_mock_shops) -> T:
    def create_client(test_client_cls: Type[T]) -> T:
        mocker.patch.object(auth, "has_jwt", return_value=True)
        mocker.patch.object(auth, "auth")
        app.test_client_class = test_client_cls
        client = test_client_cls(app, app.response_class, use_cookies=True)
        assistant: AccountDetailV2 | None = getattr(client, "assistant", None)
        leyan_user: AccountDetailV2 | None = getattr(client, "leyan_assistant", None)
        if assistant and leyan_user:
            jwt_payload = auth.KioskJwtPayload(
                iss="kiosk",
                org_id=client.org_id,
                user_id=leyan_user.user_id,
                nick_name=leyan_user.user_nick,
                phone_number=leyan_user.phone,
                login_type="org_user",
                channel_id=client.shop.channel_id,
                channel_type="taobao",
                store_id=client.sid,
                store_name=client.shop.nick,
                login_user_type=Creator.ASSISTANT,
                login_user_id=assistant.user_id,
                login_user_nick=assistant.user_nick,
                nick=assistant.user_nick,
                iat=int(time.time()),
                exp=int(time.time() + 7 * 86400),
            )
        else:
            jwt_payload = auth.empty_jwt
        g.auth = jwt_payload
        g.login_user_detail = assistant
        log_vars.OrgId.set(jwt_payload.org_id)
        log_vars.Sid.set(jwt_payload.store_id)
        log_vars.LoginUserNick.set(jwt_payload.nick)
        return client

    return create_client


@fixture
def client(_client_factory) -> Org1Client:
    """一个保证带登录态的 client fixture, 且对应租户的合同还有效."""
    return _client_factory(Org1Client)


@fixture
def client_contract_expired(_client_factory) -> Org2Client:
    """一个保证带登录态的 client fixture, 且对应租户的合同已过期."""
    return _client_factory(Org2Client)


class UnauthorizedClient(FlaskClient):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logout()

    def login(self):
        """随机构造一个 assistant 和 shop，用他们登录，并将他们返回给调用方"""
        from tests.factories import ContractInfoFactory
        from tests.factories import ShopFactory

        contract = ContractInfoFactory()
        self.org_id = contract.org_id

        assistant = AccountDetailV2(
            user_id=fuzzy.FuzzyInteger(low=10, high=100).fuzz(),
            user_type=Creator.ASSISTANT,
            user_nick=fuzzy.FuzzyText(prefix="user").fuzz(),
        )
        self.sid = fuzzy.FuzzyText(prefix="sid").fuzz()
        self.shop = ShopFactory.create(org_id=self.org_id, sid=self.sid)
        self.assistant = assistant
        jwt_payload = auth.KioskJwtPayload(
            iss="kiosk",
            org_id=self.org_id,
            user_id=0,
            nick_name="",
            phone_number="",
            login_type="org_user",
            channel_id=self.shop.channel_id,
            channel_type=self.shop.platform,
            store_id=self.sid,
            store_name=self.shop.nick,
            login_user_type=Creator.ASSISTANT,
            login_user_id=assistant.user_id,
            login_user_nick=assistant.user_nick,
            nick=assistant.user_nick,
            iat=int(time.time()),
            exp=int(time.time() + 7 * 86400),
        )
        g.auth = jwt_payload
        g.login_user_detail = assistant
        log_vars.OrgId.set(jwt_payload.org_id)
        log_vars.Sid.set(jwt_payload.store_id)
        log_vars.LoginUserNick.set(jwt_payload.nick)
        return self, assistant, self.shop

    def logout(self):
        """清空登录态."""
        self.org_id = 0
        self.sid = ""
        self.shop: Optional[ShopOrm] = None
        self.assistant: AccountDetailV2 = AccountDetailV2(
            user_id=0, user_nick="", user_type=Creator.ASSISTANT, phone="", sub_id=""
        )
        g.auth = None
        g.login_user_detail = None
        log_vars.OrgId.set("")
        log_vars.Sid.set("")
        log_vars.LoginUserNick.set("")


@fixture
def client_unauthorized(_client_factory) -> UnauthorizedClient:
    """一个保证不带登录态的 client fixture."""
    return _client_factory(UnauthorizedClient)
