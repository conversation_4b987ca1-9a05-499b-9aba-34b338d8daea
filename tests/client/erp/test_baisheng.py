import json

from robot_processor.enums import ErpType
from robot_processor.shop.models import ErpInfo
from rpa.erp.baisheng import convert_order, BaiShengClient
from tests.client.test_mola import MockResponse

mock_order_list = MockResponse(200, json.dumps({
    "status": "api-success",
    "message": "success",
    "data": {
        "page": {
            "totalResult": 1,
            "pageSize": 100,
            "pageNo": 1,
            "pageTotal": 1
        },
        "orderListGets": [
            {
                "order_sn": "121111809618173",
                "deal_code": "2252942964671689575",
                "ori_deal_code": "",
                "ori_order_sn": "",
                "tbfx_id": "",
                "lylx": "1",
                "sd_id": "4",
                "order_status": "0",
                "shipping_status": "0",
                "pay_status": "2",
                "shipping_code": "dbkd",
                "shipping_name": "德邦快递",
                "shipping_sn": "",
                "shipping_fee": "0.00",
                "service_order_fee": "0.00",
                "total_amount": "0.00",
                "payment": "1.01",
                "is_split": "0",
                "is_split_new": "0",
                "is_combine": "0",
                "is_combine_new": "0",
                "is_copy": "0",
                "is_hh": "0",
                "order_msg": "",
                "seller_msg": "",
                "seller_flag": "0",
                "buy_msg": "",
                "user_name": "小斌",
                "receiver_name": "小斌",
                "receiver_address": "上海 上海市 长宁区 长宁路1033号",
                "receiver_tel": "",
                "receiver_mobile": "18321807715",
                "weigh": "0.000000",
                "wms_sync_status": "0",
                "invoice_type": "",
                "invoice_content": "",
                "invoice_pay": "",
                "invoice_title": "",
                "invoice_amount": "",
                "invoice_number": "",
                "invoice_tax_no": "",
                "discount_fee": "0.00",
                "other_discount_fee": "0.00",
                "complete_time": "0",
                "pay_sn": "",
                "trans_time": "2021-11-18 21:24:43",
                "sync_time": "1970-01-01 08:00:00",
                "user_id": "1099113",
                "confirm_time": "",
                "shipping_time_tzph": "",
                "is_electronic": "0",
                "o2o_guide_id": "",
                "o2o_shop_id": "",
                "receiver_province_name": "上海",
                "receiver_city_name": "上海市",
                "receiver_district_name": "长宁区",
                "sd_code": "yuwell_tm",
                "sd_name": "鱼跃官方旗舰店",
                "fhck": "",
                "qd_code": "000",
                "qd_name": "总部",
                "shipping_time_fh": "1970-01-01 08:00:00",
                "shipping_time_ck": "1970-01-01 08:00:00",
                "add_time": "2021-11-18 21:18:41",
                "pay_time": "2021-11-18 21:24:43",
                "last_update": "2021-11-18 21:24:43",
                "service_order_list": [],
                "promotion_info": [],
                "order_qrr": "最后确认人:",
                "i_4": "0",
                "orderDetailGets": []
            }]}}))

sku_list = [{
    "outer_sku_id": "sku",
    "qty": 2
}]

receiver = {
    "name": "test",
    "mobile": "***********",
    "address": "asf",
    "state": "北京市",
    "city": "北京市市辖区",
    "zone": "东城区"
}
business_order = {
    "tid": "2252942964671689575",
    "address": receiver,
}

def test_convert_order(client):
    erp_info = ErpInfo(meta={
        'baseUrl': 'point',
        'appKey': "baisheng_appkey",
        'appSecret': "baisheng_secret"
    })
    BaiShengClient(erp_info=erp_info)
    new_order = convert_order("new_tid", "sd_code", sku_list, receiver, None)
    assert new_order["total"] == len(sku_list)


def test_upload(mocker, client, mock_erp_info):
    mock_erp_info.shop_id = client.shop.id
    mock_erp_info.erp_type = ErpType.BAISHENG
    mock_erp_info.meta["appKey"] = "key"
    mock_erp_info.meta["appSecret"] = "secret"
    mock_erp_info.meta["baseUrl"] = "point"
    mock_erp_info.meta["sid"] = "123"
    # mock success
    mocker.patch("rpa.erp.baisheng.BaiShengClient.get_order_list",
                 return_value=mock_order_list)
    mocker.patch(
        "rpa.erp.baisheng.BaiShengClient.session.request",
        return_value=MockResponse(200, json.dumps({
            "status": "api-success",
            "message": "success",
            "data": {"order_sn": "111"}
        })))
    res = BaiShengClient(sid=client.shop.sid).after_sale_upload(business_order, sku_list)
    assert res.is_ok()

    # 创建补发单失败1
    mocker.patch(
        "rpa.erp.baisheng.BaiShengClient.session.request",
        return_value=MockResponse(400, json.dumps({
            "status": "api-failed",
            "message": "failed"
        })))
    res = BaiShengClient(sid=client.shop.sid).after_sale_upload(business_order, sku_list)
    assert not res.is_ok()
    assert "创建补发单失败" in res.err()

    # 创建补发单失败2
    mocker.patch(
        "rpa.erp.baisheng.BaiShengClient.session.request",
        return_value=MockResponse(200, json.dumps({
            "status": "api-failed",
            "message": "failed"
        })))
    res = BaiShengClient(sid=client.shop.sid).after_sale_upload(business_order, sku_list)
    assert not res.is_ok()
    assert "创建补发单失败" in res.err()

    # 获取订单失败1
    mocker.patch("rpa.erp.baisheng.BaiShengClient.get_order_list",
                 return_value=MockResponse(400, json.dumps({
                     "status": "failed",
                     "message": "error"
                 })))
    res = BaiShengClient(sid=client.shop.sid).after_sale_upload(business_order, sku_list)
    assert not res.is_ok()

    # 获取订单失败2
    mocker.patch("rpa.erp.baisheng.BaiShengClient.get_order_list",
                 return_value=MockResponse(200, json.dumps({
                     "status": "failed",
                     "message": "error"
                 })))
    res = BaiShengClient(sid=client.shop.sid).after_sale_upload(business_order, sku_list)
    assert not res.is_ok()

    # 获取的是复制单
    mocker.patch("rpa.erp.baisheng.BaiShengClient.get_order_list",
                 return_value=MockResponse(200, json.dumps({
                     "status": "api-success",
                     "message": "success",
                     "data": {
                         "page": {
                             "totalResult": 1,
                             "pageSize": 100,
                             "pageNo": 1,
                             "pageTotal": 1
                         },
                         "orderListGets": [
                             {
                                 "order_sn": "121111809618173",
                                 "deal_code": "2252942964671689575",
                                 "is_copy": "1"}
                         ]}
                 })))
    res = BaiShengClient(sid=client.shop.sid).after_sale_upload(business_order, sku_list)
    assert not res.is_ok()
    assert res.err() == "未匹配到订单信息"
