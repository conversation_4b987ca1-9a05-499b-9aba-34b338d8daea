from robot_processor.client import doudian_cloud
import arrow
from unittest.mock import <PERSON><PERSON>ock
import pytest

from robot_processor.client.errors import DoudianCloudServiceError
from robot_processor.shop.models import GrantRecord


@pytest.fixture
def mock_shop_with_doudian_xyz(shop_factory, grant_record_factory):
    mock_shop = shop_factory.create(platform="DOUDIAN")
    grant_record_factory.create(
        shop_id=mock_shop.id,
        access_token="access token",
        app="doudian-xyz",
        expires_at_ms=arrow.now().shift(days=1).int_timestamp*1000,
    )
    yield mock_shop


def test_get_access_token(client, grant_record_factory):
    with pytest.raises(DoudianCloudServiceError) as ex:
        doudian_cloud._get_access_token(store_id=client.shop.sid)
        assert str(ex) == f"缺失抖店小柚子授权: {client.shop.sid}"
    grant_record_factory.create(
        shop_id=client.shop.id,
        access_token="access_token",
        refresh_token="refresh_token",
        expires_at_ms=arrow.now().shift(days=1).int_timestamp * 1000,
        app="doudian-xyz"
    )
    gr = doudian_cloud._get_access_token(store_id=client.sid) == 'access_token'
    assert gr is not None


def test_refresh_token_succeed(client, grant_record_factory, requests_mock):
    gr = grant_record_factory.create(
        shop_id=client.shop.id,
        access_token="access_token",
        refresh_token="refresh_token",
        expires_at_ms=arrow.now().int_timestamp * 1000,
        app="doudian-xyz"
    )
    requests_mock.post('/token/refresh', json={
        'code': 10000,
        'data': {'access_token': 'refreshed', 'expires_in': 86400,
                 'refresh_token': 'refresh_token_2', 'shop_id': 1, 'shop_name': 'shop_name'}
    })
    assert doudian_cloud._get_access_token(store_id=client.shop.sid) == 'refreshed'
    updated = GrantRecord.query.get(gr.id)
    assert updated.refresh_token == 'refresh_token_2'
    assert updated.access_token == 'refreshed'
    assert updated.expires_at_ms > arrow.now().shift(hours=23).int_timestamp * 1000


def test_refresh_token_fail(client, grant_record_factory, requests_mock, db):
    gr = grant_record_factory.create(
        shop_id=client.shop.id,
        access_token="access_token",
        refresh_token="refresh_token",
        expires_at_ms=arrow.now().shift(minutes=15).int_timestamp * 1000,
        app="doudian-xyz"
    )
    requests_mock.post('/token/refresh', json={
        'code': 10001,
        'msg': 'mock fail'
    })
    assert doudian_cloud._get_access_token(store_id=client.shop.sid) == 'access_token'
    gr.expires_at_ms = arrow.now().int_timestamp * 1000
    db.session.commit()
    with pytest.raises(DoudianCloudServiceError) as ex:
        doudian_cloud._get_access_token(store_id=client.shop.sid)
        assert str(ex) == "刷新抖店 access token 失败: mock fail"


def test_update_memo(mocker, client, mock_shop_with_doudian_xyz):
    mocker.patch("robot_processor.client.doudian_cloud.session.post",
                 return_value=MagicMock(
                     raise_for_status=MagicMock(),
                     json=MagicMock(
                         return_value={
                             "success": True,
                             "code": 200,
                             "error": None
                         }
                     )
                 ))
    doudian_cloud.update_memo(
        store_id=mock_shop_with_doudian_xyz.sid,
        order_id="123",
        memo="memo",
        star="0"
    )


def test_update_memo_failed(mocker, client, mock_shop_with_doudian_xyz):
    mocker.patch("robot_processor.client.doudian_cloud.session.post",
                 return_value=MagicMock(
                     raise_for_status=MagicMock(),
                     json=MagicMock(
                         return_value={
                             "success": False,
                             "code": 400,
                             "error": "error"
                         }
                     )
                 ))
    with pytest.raises(Exception) as ex:
        doudian_cloud.update_memo(
            store_id=mock_shop_with_doudian_xyz.sid,
            order_id="123",
            memo="memo",
            star="0"
        )
        assert str(ex) == "Doudian cloud request error: error"


def test_get_order_detail(mocker, client, mock_shop_with_doudian_xyz):
    mock_order = {
        "order_id": "123",
        "order_status": 3,
        "create_time": arrow.Arrow(2023, 11, 11, 11, 11, 11, tzinfo="Asia/Shanghai").int_timestamp,
        "pay_time": arrow.Arrow(2023, 11, 11, 11, 11, 11, tzinfo="Asia/Shanghai").int_timestamp,
        "pay_amount": 1234,
        "post_addr": {
            "city": {
                "id": "1",
                "name": "city"
            },
            "province": {
                "id": "2",
                "name": "province"
            },
            "street": {
                "id": "3",
                "name": "street"
            },
            "town": {
                "id": "4",
                "name": "town"
            },
            "encrypt_detail": "encrypted_detail"
        },
        "encrypt_post_tel": "encrypt_post_tel",
        "encrypt_post_receiver": "encrypt_post_receiver",
        "seller_words": "",
        "seller_remark_stars": 0,
        "sku_order_list": [{
            "product_id": 1,
            "sku_id": 2,
            "goods_price": 1234,
            "product_name": "product_name",
            "out_product_id": "out_product_id",
            "out_sku_id": "out_sku_id",
            "item_num": 1,
        }],
        "logistics_info": [{
            "tracking_no": "tracking_no",
            "company": "company",
            "ship_time": arrow.Arrow(2023, 11, 11, 11, 11, 11, tzinfo="Asia/Shanghai").int_timestamp,
            "delivery_id": 1,
            "company_name": "company_name"
        }]
    }
    mocker.patch("robot_processor.client.doudian_cloud._request",
                 return_value={
                     "decrypted_data": mock_order
                 })
    trade = doudian_cloud.get_order_detail(store_id="123", order_id="123")
    assert trade.order_id == "123"
    assert trade.order_status == 3
