import arrow

from robot_processor.openapi.schema import ResponseCode
from tests.openapi.api.mixin import RequestMixin


class TestDispatcher(RequestMixin):
    def test_公共参数缺失(self, client, credential):
        qs, body = dict(), dict()
        response = client.post("/v1/open", query_string=qs, json=body)
        assert response.json["code"] == str(ResponseCode.INVALID_ARGUMENT.code)
        assert response.json["details"] == [
            {"loc": ["method"], "msg": "field required"},
            {"loc": ["app_key"], "msg": "field required"},
            {"loc": ["timestamp"], "msg": "field required"},
            {"loc": ["sign"], "msg": "field required"},
        ]

    def test_timestamp误差(self, client, credential):
        qs = dict(
            app_key=credential.app_key,
            method="fs.order.create",
            timestamp=arrow.now().shift(minutes=-11).int_timestamp,
        )
        body = dict()
        response = self._request_with_sign(client, qs, body, credential.app_secret)

        assert response.json["code"] == str(ResponseCode.INVALID_ARGUMENT.code)
        assert response.json["details"] == [{"loc": ["timestamp"], "msg": "误差超过 10 分钟"}]

    def test_AppKey无效(self, client, credential):
        qs = dict(
            app_key="invalid",
            method="fs.order.create",
            timestamp=arrow.now().int_timestamp,
        )
        body = dict()
        response = self._request_with_sign(client, qs, body, credential.app_secret)
        assert response.json["code"] == str(ResponseCode.INVALID_ARGUMENT_APP_KEY.code)
        assert response.json["details"] == [
            {"loc": ["query_string", "app_key"], "msg": "认证失败，请求使用的 app_key 不存在"}
        ]

    def test_无效签名(self, client, credential):
        qs = dict(
            app_key=credential.app_key,
            method="fs.order.create",
            timestamp=arrow.now().int_timestamp,
        )
        body = dict()
        response = self._request_with_sign(client, qs, body, "invalid")
        assert response.json["code"] == str(
            ResponseCode.INVALID_ARGUMENT_SIGNATURE.code
        )
        assert response.json["details"] == [
            {"loc": ["query_string", "sign"], "msg": "无效签名"}
        ]

    def test_访问控制_接口调用频率限制(self, client, credential):
        credential.limit = {"fs.order.create": "0/1minute"}
        qs = dict(
            app_key=credential.app_key,
            method="fs.order.create",
            timestamp=arrow.now().int_timestamp,
        )
        body = dict()
        response = self._request_with_sign(client, qs, body, credential.app_secret)
        assert response.json["code"] == str(
            ResponseCode.ACCESS_CONTROL_API_CALL_LIMITED.code
        )
