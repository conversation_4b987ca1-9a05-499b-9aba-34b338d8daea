import arrow
from pytest import fixture
from pytest import mark
from robot_types.core import Symbol
from robot_types.core import TypeSpec

from robot_processor.db import db
from robot_processor.form.models import Form
from robot_processor.form.models import Step
from robot_processor.openapi.schema import ResponseCode
from robot_processor.openapi.schema import fs_order_create
from tests.openapi.api.mixin import RequestMixin


class TestFsOrderCreate(RequestMixin):
    @fixture
    def client(self, gen_fresh_client):
        yield gen_fresh_client()

    def get_default_qs(self, credential):
        return dict(
            app_key=credential.app_key,
            method="fs.order.create",
            timestamp=arrow.now().int_timestamp,
        )

    def test_表单不存在(self, client, credential):
        qs = self.get_default_qs(credential)
        body = fs_order_create.Request(form_id=1, entries=[]).dict()
        response = self._request_with_sign(client, qs, body, credential.app_secret)
        assert response.json["code"] == str(ResponseCode.INVALID_ARGUMENT.code)
        assert response.json["details"] == [{"loc": ["body", "form_id"], "msg": "表单不存在"}]

    def test_缺少店铺信息(self, client, credential, human_only_form_testbed):
        form = human_only_form_testbed(shop=client.shop).form
        qs = self.get_default_qs(credential)
        body = fs_order_create.Request(form_id=form.id, entries=[]).dict()
        response = self._request_with_sign(client, qs, body, credential.app_secret)
        assert response.json["code"] == str(ResponseCode.INVALID_ARGUMENT.code)
        assert response.json["details"] == [{"loc": ["body", "entries"], "msg": "缺少 所属店铺"}]

    def test_所属店铺不存在(self, client, credential, human_only_form_testbed):
        form = human_only_form_testbed(shop=client.shop).form
        qs = self.get_default_qs(credential)
        body = fs_order_create.Request(
            form_id=form.id,
            entries=[
                fs_order_create.StaticEntry(
                    key="system_business_order_shop_nick",
                    value="invalid",
                )
            ],
        ).dict()
        response = self._request_with_sign(client, qs, body, credential.app_secret)
        assert response.json["code"] == str(ResponseCode.INVALID_ARGUMENT.code)
        assert response.json["details"] == [{"loc": ["body", "entries"], "msg": "所属店铺不存在"}]

    @mark.order(1)
    def test_工单模板未订阅(self, client, credential, human_only_form_testbed):
        testbed = human_only_form_testbed(
            shop=client.shop,
            symbols=[
                Symbol(
                    name="usernick",
                    type_spec=TypeSpec("string"),
                    label="买家昵称",
                    render_config=dict(label="买家昵称"),
                    component_id="string",
                )
            ],
        )
        form = testbed.form
        form.unsubscribe(client.shop, False)
        qs = self.get_default_qs(credential)
        body = fs_order_create.Request(
            form_id=form.id,
            entries=[
                fs_order_create.StaticEntry(key="system_business_order_shop_nick", value=client.shop.nick),
                fs_order_create.StaticEntry(key=self.get_widget_key_by_label(form, "买家昵称"), value="测试"),
            ],
        ).dict()
        response = self._request_with_sign(client, qs, body, credential.app_secret)
        assert response.json["code"] == str(ResponseCode.BUSINESS_ORDER_CREATE_FAILED.code)
        assert response.json["details"] == [{"loc": None, "msg": "抱歉，当前工单模板及任务已被删除，无法创建"}]

    @mark.order(1)
    def test_工单模板未启用(self, client, credential, human_only_form_testbed):
        testbed = human_only_form_testbed(
            shop=client.shop,
            symbols=[
                Symbol(
                    name="usernick",
                    type_spec=TypeSpec("string"),
                    label="买家昵称",
                    render_config=dict(label="买家昵称"),
                    component_id="string",
                )
            ],
        )
        form = testbed.form
        form.subscribe(client.shop, False)
        qs = self.get_default_qs(credential)
        body = fs_order_create.Request(
            form_id=form.id,
            entries=[fs_order_create.StaticEntry(key="system_business_order_shop_nick", value=client.shop.nick)],
        ).dict()
        response = self._request_with_sign(client, qs, body, credential.app_secret)
        assert response.json["code"] == str(ResponseCode.BUSINESS_ORDER_CREATE_FAILED.code)
        assert response.json["details"] == [{"loc": None, "msg": "抱歉，当前工单模板及任务未启用，无法创建"}]

    def get_widget_key_by_label(self, form: Form, label: str):
        steps = db.session.query(Step).where(Step.id.in_(form.versions.first().step_id)).all()
        ui_schema = []
        for step in steps:
            ui_schema.extend(Step.Utils.raw_ui_schema(step))
        widget_info = next(filter(lambda w: w["option_value"]["label"] == label, ui_schema))
        return widget_info["key"]

    @mark.order(1)
    @mark.usefixtures("mock_action_client")
    def test_创建工单(self, client, credential, human_only_form_testbed):
        testbed = human_only_form_testbed(
            shop=client.shop,
            symbols=[
                Symbol(
                    name="usernick",
                    type_spec=TypeSpec("string"),
                    label="买家昵称",
                    render_config=dict(label="买家昵称"),
                    component_id="string",
                )
            ],
        )
        form = testbed.form
        qs = self.get_default_qs(credential)
        body = fs_order_create.Request(
            form_id=form.id,
            entries=[
                fs_order_create.StaticEntry(key="system_business_order_shop_nick", value=client.shop.nick),
                fs_order_create.StaticEntry(key=self.get_widget_key_by_label(form, "买家昵称"), value="测试"),
            ],
        ).dict()
        response = self._request_with_sign(client, qs, body, credential.app_secret)
        assert response.json["code"] == str(ResponseCode.SUCCESS.code)

        from robot_processor.business_order.models import BusinessOrder

        bo = BusinessOrder.query.get(response.json["result"]["business_order_id"])

        widgets = bo.get_non_ref_widgets()
        widget_key = next(filter(lambda key: widgets[key]["option_value"]["label"] == "买家昵称", widgets))
        assert bo.data[widget_key] == "测试"
