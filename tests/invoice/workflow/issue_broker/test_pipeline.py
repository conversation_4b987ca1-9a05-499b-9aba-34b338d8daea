import re
from datetime import datetime

from arrow import now
from google.protobuf.struct_pb2 import Struct
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceIssued as pb_InvoiceIssued
from leyan_proto.digismart.robot.invoice.workflow_pb2 import UserInfo as pb_UserInfo
from pytest import fixture

from robot_processor.db import db
from robot_processor.db import in_transaction
from robot_processor.invoice.common.models import rpa_user_info
from robot_processor.invoice.workflow.models import BuyerType
from robot_processor.invoice.workflow.models import InvoiceRequest
from robot_processor.invoice.workflow.models import InvoiceType
from robot_processor.invoice.workflow.models import InvoiceWorkflow
from robot_processor.invoice.workflow.models import IssuingType
from robot_processor.invoice.workflow.service import IssueBroker


@fixture(autouse=True)
def setup(setup_auth):
    yield


@fixture
def mock_rpa_control(requests_mock):
    yield requests_mock.post(re.compile(r".*/invoice/issue-blue"), json=dict(succeed=True, data=dict(succeed=True)))


@fixture
def invoice_workflow(testbed):
    with in_transaction():
        invoice_workflow = InvoiceWorkflow(
            org_id=testbed["org_id"],
            serial_number="FP20240101000005",
            state=InvoiceWorkflow.State.PENDING_INVOICING,
            applicant_id=1,
            applicant_type=pb_UserInfo.User.Type.LEYAN,
            applicant_name="unittest",
            created_at=datetime(2024, 3, 1, 0, 0, 0),
            updated_at=datetime(2024, 3, 1, 0, 0, 0),
            invoice_type=InvoiceType.VAT_GENERAL,
            issuing_type=IssuingType.BLUE,
            seller_name="Test Seller",
            seller_credit_id=testbed["corporates"][0].credit_id,
            buyer_type=BuyerType.INDIVIDUAL,
            buyer_name="Test Buyer",
            is_tax_included=True,
            issuing_items=[
                dict(
                    title="可乐",
                    amount="1",
                    tax_rate="0.01",
                    tax_code="*********",
                ),
            ],
        )
        db.session.add(invoice_workflow)
    yield invoice_workflow


def test_issue_blue(testbed, invoice_workflow, mock_rpa_control):
    assert invoice_workflow.state is InvoiceWorkflow.State.PENDING_INVOICING
    init_result = IssueBroker.init_issue_blue(
        invoice_workflow.to_request_view(),
        [invoice_workflow],
        "zhejiang",
        testbed["corporates"][0].taxers[0].account,
    )
    assert init_result.is_ok(), init_result.unwrap_err()
    issue_broker = init_result.unwrap()
    assert issue_broker.invoice_request.state is InvoiceRequest.State.QUEUED
    assert invoice_workflow.state is InvoiceWorkflow.State.PENDING_INVOICING

    # 执行开票
    issue_broker.do_issue(rpa_user_info)
    assert mock_rpa_control.called_once

    # 上报 invoice number
    issuing_time = now().datetime
    issue_broker.issued(rpa_user_info, "TEST-123", issuing_time)
    assert invoice_workflow.state is InvoiceWorkflow.State.INVOICING
    assert invoice_workflow.invoice_number == "TEST-123"
    invoice_issued = issue_broker.invoice_issued

    # 上报 invoice info
    assert invoice_issued.receipt is None
    raw = Struct()
    raw.update({"fphm": "123456"})
    invoice_info = pb_InvoiceIssued(
        total_amount_without_tax="100.00",
        total_tax_amount="10.00",
        issuing_amount="110.00",
        issuing_amount_ch="壹佰壹拾元整",
        raw=raw,
        receipt=pb_InvoiceIssued.Receipt(pdf="xxx.pdf", ofd="xxx.ofd", xml="xxx.xml"),
    )
    issue_broker.issued_with_detail(invoice_info)
    assert invoice_workflow.state is InvoiceWorkflow.State.INVOICED
    assert invoice_issued.receipt == {"pdf": "xxx.pdf", "ofd": "xxx.ofd", "xml": "xxx.xml"}
    assert invoice_issued.total_amount_without_tax == 100.00
    assert invoice_issued.raw == {"fphm": "123456"}
