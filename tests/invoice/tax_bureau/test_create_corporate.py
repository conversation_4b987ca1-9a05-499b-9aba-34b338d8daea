from pytest import fixture
from leyan_proto.digismart.robot.invoice import tax_bureau_pb2
from leyan_proto.digismart.robot_web.invoice.tax_bureau_pb2 import (
    CreateOrUpdateCorporateBody,
)
from robot_processor.invoice.tax_bureau.models import Corporate


@fixture(autouse=True)
def setup(setup_auth, testbed):
    yield


@fixture
def teardown():
    corporate_id = None

    def set_corporate_id(new):
        nonlocal corporate_id
        corporate_id = new

    yield set_corporate_id

    if corporate_id is not None:
        Corporate.query.filter_by(id=corporate_id).delete()


def test纳税人识别号重复(tax_bureau_servicer, mock_context):
    request = CreateOrUpdateCorporateBody(
        corporate=tax_bureau_pb2.Corporate.EditableView(credit_id="91310104MA1FR2RL99")
    )
    response = tax_bureau_servicer.CreateCorporate(request, mock_context)
    assert not response.succeed
    assert response.msg == "统一社会信用代码/纳税人识别号 91310104MA1FR2RL99 已绑定，请勿重复绑定"


def test创建成功(tax_bureau_servicer, mock_context, teardown):
    request = CreateOrUpdateCorporateBody(
        corporate=tax_bureau_pb2.Corporate.EditableView(
            credit_id="unittest",
            name="上海炯信信息科技有限公司",
            address="上海市闵行区景联路855号14幢D107室",
            phone="021-********",
            bank="交通银行上海徐汇滨江支行",
            bank_account="310066807018800005902",
            competent_tax_bureau=tax_bureau_pb2.Corporate.SHANGHAI,
        )
    )
    response = tax_bureau_servicer.CreateCorporate(request, mock_context)
    assert response.succeed
    teardown(response.data.corporate.id)
    corporate = response.data.corporate
    assert corporate.credit_id == "unittest"
    assert corporate.name == "上海炯信信息科技有限公司"
    assert corporate.address == "上海市闵行区景联路855号14幢D107室"
    assert corporate.phone == "021-********"
    assert corporate.bank == "交通银行上海徐汇滨江支行"
    assert corporate.bank_account == "310066807018800005902"
    assert corporate.competent_tax_bureau == Corporate.TaxBureau.SHANGHAI
