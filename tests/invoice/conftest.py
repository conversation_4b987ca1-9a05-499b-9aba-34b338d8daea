from pytest import fixture


@fixture
def testbed():
    from leyan_proto.digismart.robot.invoice.workflow_pb2 import UserInfo

    from robot_processor.db import in_transaction
    from robot_processor.enums import ErpType
    from robot_processor.invoice.tax_bureau.models import Corporate
    from robot_processor.invoice.tax_bureau.models import CorporateShop
    from robot_processor.invoice.tax_bureau.models import CorporateTaxer
    from robot_processor.shop.auth_manager import Credentials
    from robot_processor.shop.auth_manager import CredentialShopMapping
    from robot_processor.shop.models import ErpInfo
    from robot_processor.shop.models import Shop

    org_id = 40485
    user_id = 111
    user_type = UserInfo.User.Type.LEYAN

    defer = []
    with in_transaction() as trx:
        shop = Shop(
            id=709940,
            sid="7099207840",
            nick="发票项目-抖店测试店铺",
            org_id=str(org_id),
            platform="DOUDIAN",
            channel_id=709940,
        )
        defer.append(shop)

        wdt_erp = ErpInfo(
            shop_id=shop.id,
            erp_type=ErpType.WDT,
            meta={"sid": "unittest", "app_key": "appkey", "app_secret": "appsecret", "after_sale_shop_no": ""},
            erp_id=shop.id,
        )
        defer.append(wdt_erp)
        wdt_credentials = Credentials(
            id=1001,
            org_id=shop.org_id,
            auth_type=ErpType.WDT.name,
            auth_account="ut",
            auth_extra_data={},
            auth_status="SUCCEED",
        )
        credential_mapping = CredentialShopMapping(
            channel_id=shop.channel_id,
            auth_id=wdt_credentials.id,
            erp_id=0,
            erp_platform_sid=1001,
        )
        defer.append(credential_mapping)
        defer.append(wdt_credentials)

        corporate_1 = Corporate(
            id=1,
            org_id=org_id,
            credit_id="91310104MA1FR2RL99",
            name="上海乐言科技股份有限公司",
            address="上海市长宁区长宁路1033号联通大厦16层（名义19层）",
            phone="021-********",
            bank="交通银行上海徐汇滨江支行",
            bank_account="310066807018800005902",
            competent_tax_bureau=Corporate.TaxBureau.SHANGHAI,
        )
        defer.append(corporate_1)
        corporate_1.shops.append(CorporateShop(id=1, sid=shop.sid, platform=shop.platform))
        defer.extend(corporate_1.shops)
        corporate_1.taxers.append(
            CorporateTaxer(
                id=1,
                account="unittest-1",
                password="*******",
                name="unittest-1",
                phone="***********",
                role=CorporateTaxer.Role.TAX_AGENT,
                status=CorporateTaxer.Status.ACTIVE,
            )
        )
        corporate_1.taxers.append(
            CorporateTaxer(
                id=2,
                account="unittest-2",
                password="*******",
                name="unittest-2",
                phone="***********",
                role=CorporateTaxer.Role.TAX_AGENT,
                status=CorporateTaxer.Status.ACTIVE,
            )
        )
        defer.extend(corporate_1.taxers)
        corporate_2 = Corporate(
            id=2,
            org_id=org_id,
            credit_id="913101160693147378",
            name="上海炯信信息科技有限公司",
            address="上海市闵行区景联路855号14幢D107室",
            phone="021-********",
            bank="交通银行上海徐汇滨江支行",
            bank_account="310066807018800005902",
            competent_tax_bureau=Corporate.TaxBureau.SHANGHAI,
        )
        defer.append(corporate_2)
        corporate_2.taxers.append(
            CorporateTaxer(
                id=3,
                account="unittest-3",
                password="*******",
                name="unittest-3",
                phone="***********",
                role=CorporateTaxer.Role.TAX_AGENT,
                status=CorporateTaxer.Status.ACTIVE,
            )
        )
        corporate_2.taxers.append(
            CorporateTaxer(
                id=4,
                account="unittest-4",
                password="*******",
                name="unittest-4",
                phone="***********",
                role=CorporateTaxer.Role.TAX_AGENT,
                status=CorporateTaxer.Status.ACTIVE,
            )
        )
        defer.extend(corporate_2.taxers)

        for record in defer:
            trx.add(record)
            trx.flush()

    yield {
        "org_id": org_id,
        "user_id": user_id,
        "user_type": user_type,
        "shop": shop,
        "corporates": [corporate_1, corporate_2],
        "wdt_shop_id": credential_mapping.erp_platform_sid,
    }

    with in_transaction() as trx:
        for record in reversed(defer):
            trx.delete(record)
            trx.flush()


@fixture
def setup_auth(testbed):
    from robot_processor.assistant.schema import AccountDetailV2
    from robot_processor.auth import KioskJwtPayload
    from robot_processor.currents import g

    g.auth = KioskJwtPayload(
        iss="",
        org_id=testbed["org_id"],
        user_id=testbed["user_id"],
        nick_name="unittest",
        phone_number="",
        login_type="org_user",
        channel_type="doudian",
        store_id="",
        store_name="",
        login_user_type=testbed["user_type"],
        login_user_id=testbed["user_id"],
        login_user_nick="unittest",
        channel_id=0,
        nick="unittest",
        iat=0,
        exp=0,
    )
    g.org_id = testbed["org_id"]
    g.login_user_detail = AccountDetailV2(
        user_id=testbed["user_id"], user_type=testbed["user_type"], user_nick="unittest"
    )
    yield
    del g.login_user_detail
    del g.org_id
    del g.auth


@fixture
def mock_context():
    from unittest.mock import MagicMock

    from grpc import ServicerContext

    yield MagicMock(spec=ServicerContext)


@fixture
def mock_goods_tax_info():
    from robot_processor.db import in_transaction
    from robot_processor.invoice.goods.models import TaxCode

    with in_transaction() as trx:
        tax_info = TaxCode(code="100010", name="测试用增值税", name_abbr="测试", tax_rate=0.03)
        trx.add(tax_info)

    yield {"tax_info": tax_info}


@fixture(autouse=True)
def mock_oss(mocker):
    from robot_processor.client import rpa_control_oss

    def gen_download_url(oss_key):
        return oss_key

    yield mocker.patch.object(rpa_control_oss, "gen_download_url", wraps=gen_download_url)


@fixture(autouse=True)
def mock_redis():
    import fakeredis

    from robot_processor.invoice.workflow.service import IssueBroker

    def provide_redis(*args):
        return fakeredis.FakeRedis()

    IssueBroker.provide_redis = provide_redis
