import random

from pytest import fixture
from pytest_mock import Mock<PERSON>ixture

from robot_processor.enums import Form<PERSON>old
from robot_processor.ext import db
from robot_processor.form.api.form_editor_api import PutFormRequest
from robot_processor.form.models import FormVersion, Step


@fixture
def request_body(client):
    """发布工单的请求体"""
    another_shop = client.other_shop()

    yield PutFormRequest(
        category=random.choice([None, "ALIPAY", "BUYER_TABLE"]),
        name="测试工单模板",
        description="测试工单模板描述",
        form_mold=random.choice(list(FormMold)),
        co_eidt=random.choice([True, False]),
        tags=["测试", "工单"],
        enable_service_count=random.choice([True, False]),
        need_archive=random.choice([True, False]),
        sync_shop_dict={
            client.shop.sid: True,
            another_shop.sid: False,
        }
    )


@fixture
def mock_bind_provider(mocker: MockFixture):
    from robot_processor.client import asgard_client

    yield mocker.patch.object(asgard_client, "bind_provider")


@fixture
def mock_form_publish_check(mocker: MockFixture):
    return mocker.patch("robot_processor.form.form_publish_check.form_publish_check")


@fixture
def mock_form_change_producer(mocker):
    from robot_processor.ext import robot_form_change_producer

    yield mocker.patch(
        "robot_processor.ext.robot_form_change_producer",
        autospec=robot_form_change_producer
    )


def test_success(client, request_body, testbed, mock_form_publish_check, mock_bind_provider,
                 mock_action_client, mock_form_event_producer, mock_form_change_producer):
    """发布工单模板"""
    mock_form_publish_check.return_value = []
    form = testbed.form
    response = client.put(f"/v1/forms/{form.id}", json=request_body.dict())
    assert response.status_code == 200
    assert response.json["success"]

    db.session.refresh(form)
    # 基础信息更新
    assert form.name == request_body.name
    assert form.description == request_body.description
    assert form.tags == request_body.tags
    assert form.category == request_body.category
    assert form.co_edit == request_body.co_edit
    assert form.form_mold == request_body.form_mold
    assert form.need_archive == request_body.need_archive
    # 最近更新人是当前操作人
    assert form.update_user == client.leyan_assistant.user_nick
    # 发布之后，重置 can_publish 状态
    assert form.can_publish is False
    assert db.session.query(
        form.backend_steps.filter(Step.is_dirty.is_(True)).exists()
    ).scalar() is False
    # step 同步执行 publish 操作
    for step in form.job_steps.filter_by(deleted=False):
        assert step.is_dirty is False
        if step.widget_collection:
            assert step.widget_collection.is_dirty is False
    # 工单模板快照信息
    assert form.versions.count() == 1
    form_version = form.versions.first()
    assert sorted(form_version.step_id) == sorted([
        testbed.begin_step.id,
        testbed.begin.id, testbed.gateway.id,
        testbed.branch_1.id, testbed.branch_2.id, testbed.end.id
    ])
    StepNode = FormVersion.Schema.StepNode
    assert form_version.job_road == {"road_map": {
        StepNode.BEGIN: StepNode(current=testbed.begin_step.id, next=testbed.begin.step_uuid).dict(),
        testbed.begin_step.step_uuid: StepNode(current=testbed.begin_step.id, next=testbed.begin.step_uuid).dict(),
        testbed.begin.step_uuid: StepNode(current=testbed.begin.id, next=testbed.gateway.step_uuid).dict(),
        testbed.gateway.step_uuid: StepNode(current=testbed.gateway.id, next=StepNode.BRANCH).dict(),
        testbed.branch_1.step_uuid: StepNode(current=testbed.branch_1.id, next=testbed.end.step_uuid).dict(),
        testbed.branch_2.step_uuid: StepNode(current=testbed.branch_2.id, next=testbed.end.step_uuid).dict(),
        testbed.end.step_uuid: StepNode(current=testbed.end.id, next=StepNode.END).dict(),
    }}
    # 工单模板快照信息的最近更新人是工单的最近更新人
    assert form_version.updator == client.leyan_assistant.user_nick
    # 最新的版本固定为 HEAD
    assert form_version.version_descriptor == "HEAD"
    # 在这个 test case 中，这是工单模板的第一次发布
    assert form_version.version_no == "V0.0.1"
    # 由 Form.View.FormVersionMeta 的单元测试保证数据准确性，这里只校验 meta 信息有没有写进去
    assert form_version.meta

    # on_publish 事件触发
    # 记录操作日志 record_form_publish_log
    assert mock_action_client.create_action_log_by_kafka.call_count == 1
    # 下发工单变更事件的消息 produce_form_publish_message
    assert mock_form_event_producer.call_count == 2
    # 更新商家商品数据源 rebind_product_provider
    assert mock_bind_provider.call_count == 2


def test_failure_name_unique_error(client, request_body, testbed, mock_form_publish_check, mock_form):
    # 准备一个已经存在的工单模板，用于测试名称唯一性校验
    mock_form.subscribe(client.shop, True)
    mock_form.name = request_body.name
    db.session.commit()
    form = testbed.form
    response = client.put(f"/v1/forms/{form.id}", json=request_body.dict())
    assert response.status_code == 400, response.json
    assert response.json["succeed"] is False
    assert response.json["msg"] == "工单模板名称已存在"


def test_failure_form_not_found(client, request_body, testbed, mock_form_publish_check):
    """发布不存在的工单模板"""
    response = client.put("/v1/forms/999999", json=request_body.dict())
    assert response.status_code == 404
    assert response.json["succeed"] is False
    assert response.json["msg"] == "当前店铺未订阅该工单模板"


def test_failure_publish_check_error(client, request_body, testbed, mock_form_publish_check):
    """发布校验失败"""
    from robot_processor.form.form_publish_check import PublishCheckDescription
    from robot_processor.form.form_publish_check import CheckCategory

    mock_form_publish_check.return_value = [
        PublishCheckDescription(
            metadata=PublishCheckDescription.Metadata(
                step_id=1,
                step_uuid="ut",
                step_type="human",
                field_type="assistant_info",
                branch_id=None,
                concept_key=None,
                argument_name=None,
                widget_info_is_deleted=None
            ),
            display_info=PublishCheckDescription.DisplayInfo(
                step_name="ut",
                field_display="ut",
                error_display="ut"
            ),
            stats_info=PublishCheckDescription.StatsInfo(
                check_category=CheckCategory.assistant,
                check_field="ut"
            )
        )
    ]
    form = testbed.form
    response = client.put(f"/v1/forms/{form.id}", json=request_body.dict())
    assert response.status_code == 400
    assert response.json["success"] is False
    assert response.json["msg"] == "工单发布失败"
    assert response.json["data"] == [{
        'error_display': 'ut', 'field_display': 'ut',
        'metadata': {'argument_name': None, 'branch_id': None, 'callback_type': None, 'concept_key': None,
                     'field_type': 'assistant_info', 'step_id': 1, 'step_type': 'human', 'step_uuid': 'ut',
                     'widget_info_is_deleted': None}, 'step_name': 'ut'
    }]


def test_failure_lock_acquire(client, request_body, testbed, mock_form_publish_check, cache):
    """获取编辑锁失败"""
    form = testbed.form
    cache.set(f'edit_form_{form.id}', {"key": "edit_form_1", "nick": "somebody", "last_updated": 1})
    response = client.put(f"/v1/forms/{form.id}", json=request_body.dict())
    assert response.status_code == 200
    assert response.json["success"] is False
    assert response.json["msg"] == "somebody 正在编辑此工单模板"


def test_edit_lock_acquire_release(client, mock_form):
    mock_form.subscribe(client.shop, True)
    resp = client.put('/v1/forms/edit-lock', json={"form_id": mock_form.id})
    assert resp.status_code == 200
    assert resp.json['succeed']
    assert resp.json['nick'] == client.leyan_assistant.user_nick

    resp = client.delete('/v1/forms/edit-lock', json={"form_id": mock_form.id})
    assert resp.status_code == 200
    assert resp.json['succeed']


def test_edit_lock_acquire_failed(client, mock_form):
    from robot_processor.form.form_edit_lock import FormEditLock
    mock_form.subscribe(client.shop, True)
    FormEditLock('test-user-123', mock_form).acquire()

    resp = client.put('/v1/forms/edit-lock', json={"form_id": mock_form.id})
    assert resp.status_code == 200
    assert not resp.json['succeed']
    assert resp.json['error_display'] == 'test-user-123 正在编辑此工单模板'


def test_form_version_upgrade(
        client, request_body, testbed, mock_form_publish_check,
        mock_bind_provider, mock_action_client, mock_form_event_producer,
        mock_form_change_producer
):
    """这个 case 主要测试工单模板的版本升级是否正确"""
    form = testbed.form
    # 先发布一次，拿到第一个版本
    response = client.put(f"/v1/forms/{form.id}", json=request_body.dict())
    assert response.status_code == 200
    assert response.json["succeed"]
    assert form.versions.count() == 1, "第一次发布后，生成了一个工单版本"
    first_version = form.versions.first()
    assert first_version, "第一次发布后，工单模板的版本信息应该存在"
    assert first_version.version_no == "V0.0.1"
    assert first_version.version_descriptor == "HEAD"

    # 先保存一下现在的 meta 信息
    first_version_meta = first_version.meta
    db.session.commit()

    form = db.session.merge(form)
    # 第二次发布，仅修改 meta 信息，预期不会生成新的版本，而是更新原有版本的 meta 信息
    request_body.description = "修改一下"
    response = client.put(f"/v1/forms/{form.id}", json=request_body.dict())
    assert response.status_code == 200
    assert response.json["succeed"]
    assert form.versions.count() == 1, "第二次发布不会生成新的版本"
    second_publish_version = form.versions.first()
    assert second_publish_version.version_descriptor == "HEAD"
    assert second_publish_version.version_no == "V0.0.1"
    assert second_publish_version.meta != first_version_meta
