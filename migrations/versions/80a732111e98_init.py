"""init

Revision ID: 80a732111e98
Revises: 
Create Date: 2021-06-01 19:26:42.046881

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '80a732111e98'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('business_order',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('sid', sa.String(length=32), nullable=True, comment='店铺 id'),
    sa.Column('aid', sa.String(length=128), nullable=True, comment='客服 id'),
    sa.Column('uid', sa.String(length=128), nullable=True, comment='买家 id'),
    sa.Column('form_id', sa.Integer(), nullable=True, comment='关联的后台工单 id'),
    sa.Column('data', sa.JSON(), nullable=True, comment='表单数据'),
    sa.Column('creator_type', sa.Enum('USER', 'ASSISTANT', name='creator'), nullable=True, comment='创建人类别'),
    sa.Column('job_history', sa.JSON(), nullable=True),
    sa.Column('status', sa.Enum('NEW', 'RUNNING', 'PENDING', 'FAILED', 'SUCCEED', name='jobstatus'), nullable=True, comment='工单的状态'),
    sa.Column('update_user', sa.String(length=128), nullable=True, comment='最新更新人'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_business_order_sid'), 'business_order', ['sid'], unique=False)
    op.create_table('form',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('sid', sa.String(length=64), nullable=True),
    sa.Column('name', sa.String(length=64), nullable=True, comment='工单名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='工单描述'),
    sa.Column('enabled', sa.Boolean(), nullable=True, comment='工单开关'),
    sa.Column('update_user', sa.String(length=128), nullable=True, comment='最新更新人'),
    sa.Column('uuid', sa.String(length=32), nullable=True, comment='官方模版ID'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_form_sid'), 'form', ['sid'], unique=False)
    op.create_table('form_template',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=True, comment='工单名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='工单描述'),
    sa.Column('enabled', sa.Boolean(), nullable=True, comment='工单开关'),
    sa.Column('update_user', sa.String(length=128), nullable=True, comment='最新更新人'),
    sa.Column('uuid', sa.String(length=32), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('form_timeline',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('sid', sa.String(length=32), nullable=True),
    sa.Column('form_id', sa.Integer(), nullable=True, comment='对应的form id'),
    sa.Column('data', sa.JSON(), nullable=True, comment='dict出来的json'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('sid_form_id', 'form_timeline', ['sid', 'form_id'], unique=False)
    op.create_table('shop',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=128), nullable=True),
    sa.Column('sid', sa.String(length=32), nullable=True),
    sa.Column('nick', sa.String(length=64), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_shop_sid'), 'shop', ['sid'], unique=False)
    op.create_table('task',
    sa.Column('ui_schema', sa.JSON(), nullable=True),
    sa.Column('json_schema', sa.JSON(), nullable=True),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('sid', sa.String(length=64), nullable=True),
    sa.Column('name', sa.String(length=32), nullable=True, comment='任务名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='任务描述'),
    sa.Column('tags', sa.JSON(), nullable=True, comment='应用标签'),
    sa.Column('uuid', sa.String(length=32), nullable=True),
    sa.Column('task_type', sa.String(length=32), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_task_sid'), 'task', ['sid'], unique=False)
    op.create_table('task_template',
    sa.Column('ui_schema', sa.JSON(), nullable=True),
    sa.Column('json_schema', sa.JSON(), nullable=True),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=32), nullable=True, comment='任务名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='任务描述'),
    sa.Column('tags', sa.JSON(), nullable=True, comment='应用标签'),
    sa.Column('uuid', sa.String(length=32), nullable=True),
    sa.Column('task_type', sa.String(length=32), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('grant_record',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('shop_id', sa.Integer(), nullable=True),
    sa.Column('app', sa.String(length=32), nullable=True),
    sa.Column('expires_at_ms', sa.Integer(), nullable=True),
    sa.Column('access_token', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['shop_id'], ['shop.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('job',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('status', sa.Enum('NEW', 'RUNNING', 'PENDING', 'FAILED', 'SUCCEED', name='jobstatus'), nullable=True, comment='job 的状态'),
    sa.Column('step_id', sa.Integer(), nullable=True, comment='关联步骤'),
    sa.Column('business_order_id', sa.Integer(), nullable=True, comment='关联工单'),
    sa.Column('message_id', sa.String(length=128), nullable=True, comment='队列里的job_id'),
    sa.ForeignKeyConstraint(['business_order_id'], ['business_order.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('step',
    sa.Column('ui_schema', sa.JSON(), nullable=True),
    sa.Column('json_schema', sa.JSON(), nullable=True),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('form_id', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(length=64), nullable=True, comment='步骤名称'),
    sa.Column('description', sa.TEXT(), nullable=True, comment='步骤说明'),
    sa.Column('step_type', sa.Enum('human', 'auto', name='steptype'), nullable=True, comment='步骤类型'),
    sa.Column('assistants', sa.JSON(), nullable=True, comment='执行客服'),
    sa.Column('buyer_edit', sa.Boolean(), nullable=True, comment='是否允许买家填写'),
    sa.Column('buyer_reply', sa.Text(), nullable=True, comment='买家引导话术'),
    sa.Column('status', sa.Enum('pending', 'completed', name='businessorderstatus'), nullable=True),
    sa.Column('prev_step_ids', sa.JSON(), nullable=True),
    sa.Column('next_step_ids', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['form_id'], ['form.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('step_template',
    sa.Column('ui_schema', sa.JSON(), nullable=True),
    sa.Column('json_schema', sa.JSON(), nullable=True),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=True, comment='步骤名称'),
    sa.Column('description', sa.TEXT(), nullable=True, comment='步骤说明'),
    sa.Column('step_type', sa.Enum('human', 'auto', name='steptype'), nullable=True, comment='步骤类型'),
    sa.Column('assistants', sa.JSON(), nullable=True, comment='执行客服'),
    sa.Column('buyer_edit', sa.Boolean(), nullable=True, comment='是否允许买家填写'),
    sa.Column('buyer_reply', sa.Text(), nullable=True, comment='买家引导话术'),
    sa.Column('status', sa.Enum('pending', 'completed', name='businessorderstatus'), nullable=True),
    sa.Column('prev_step_ids', sa.JSON(), nullable=True),
    sa.Column('next_step_ids', sa.JSON(), nullable=True),
    sa.Column('uuid', sa.String(length=32), nullable=True),
    sa.Column('form_template_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['form_template_id'], ['form_template.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('step_template')
    op.drop_table('step')
    op.drop_table('job')
    op.drop_table('grant_record')
    op.drop_table('task_template')
    op.drop_index(op.f('ix_task_sid'), table_name='task')
    op.drop_table('task')
    op.drop_index(op.f('ix_shop_sid'), table_name='shop')
    op.drop_table('shop')
    op.drop_index('sid_form_id', table_name='form_timeline')
    op.drop_table('form_timeline')
    op.drop_table('form_template')
    op.drop_index(op.f('ix_form_sid'), table_name='form')
    op.drop_table('form')
    op.drop_index(op.f('ix_business_order_sid'), table_name='business_order')
    op.drop_table('business_order')
    # ### end Alembic commands ###
