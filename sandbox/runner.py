import json
from datetime import date
from datetime import datetime
from datetime import time
from decimal import Decimal
from typing import Any
from typing import Callable
from typing import TypedDict

from result import Err
from result import Ok
from robot_types.core import Const
from robot_types.core import FnSignature
from robot_types.core import Value
from robot_types.helper import ValueResolver
from robot_types.helper import deserialize
from robot_types.helper import serialize
from robot_types.helper import type_spec_to_py_type


class CodeRunner:
    def __init__(self, code: str, signature: FnSignature):
        self.code = code
        self.signature = signature
        self.runner_ns = {"datetime": datetime, "date": date, "time": time, "TypedDict": TypedDict, "Any": Any}
        exec(self.code, self.runner_ns)
        self.callable: Callable[[dict], dict] = self.runner_ns[self.signature.name]

    def run(self, params, context=None):
        try:
            self.check_params(params)
            result = self.callable(self.prepare_params(params, context))
            self.check_return(result)
            return Ok(result)
        except Exception as e:
            return Err(e)

    def check_params(self, params: dict[str, Any]):
        for key, type_spec in self.signature.params.items():
            value = params.get(key)
            if value is None:
                continue
            try:
                deserialize(value, type_spec_to_py_type(type_spec))
            except:  # noqa
                raise TypeError(f"参数 {key} 类型不匹配")

    def check_return(self, return_value: dict[str, Any]):
        for key, type_spec in self.signature.rtype.properties.items():
            value = return_value.get(key)
            if value is None:
                continue
            try:
                deserialize(value, type_spec_to_py_type(type_spec))
            except:  # noqa
                raise TypeError(f"返回值 {key} 类型不匹配")

    def prepare_params(self, params: dict[str, dict], context: dict | None = None):
        value_resolver = ValueResolver((context or dict()).copy())
        prepared = dict()
        for key, type_spec in self.signature.params.items():
            raw_value = params.get(key)
            if raw_value is None:
                value = None
            else:
                value = Value(type_spec, const=Const(raw_value)).with_resolver(value_resolver).resolve().unwrap()
                if isinstance(value, Decimal):
                    value = serialize(value)
            prepared[key] = value
        return prepared

    @staticmethod
    def jsonify_return_value(return_value: dict[str, Any]):
        def convert(o):
            if isinstance(o, datetime):
                return o.strftime("%Y-%m-%d %H:%M:%S")
            elif isinstance(o, date):
                return o.strftime("%Y-%m-%d")
            elif isinstance(o, time):
                return o.strftime("%H:%M:%S")
            else:
                raise TypeError(f"Object of type {o.__class__.__name__} is not JSON serializable")

        return json.loads(json.dumps(return_value, default=convert))
