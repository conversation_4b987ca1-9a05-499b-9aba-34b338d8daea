

import functools
from typing import Optional

from temporalio import activity, workflow

with workflow.unsafe.imports_passed_through():
    import asyncio
    import typing
    from concurrent.futures import ThreadPoolExecutor
    from datetime import datetime, timedelta

    import sqlalchemy as sa
    from loguru import logger
    from rpa_control.app import app  # NOQA
    from rpa_control.control.enums import ExecutionStateEnum, WorkflowTypeEnum
    from rpa_control.control.models import (Robot, Runtime, VersionedRobot,
                                            Workflow, WorkflowExecution,
                                            WorkflowStep)
    from rpa_control.control.utils import notify_feisuo
    from rpa_control.ext import aio_gateway_client, aio_sa
    from rpa_control.temporal import utils
    from rpa_control.temporal.context import ExecutionContext
    from rpa_control.temporal.events import (ExecutionCallbackSchema,
                                             RichEvent, RobotResultSchema)
    from rpa_control.temporal.execution_router import RouteManager
    from rpa_control.temporal.utils import get_instance


executor = ThreadPoolExecutor(max_workers=4)


def activity_logger():
    return logger.bind(**activity.info()._logger_details()) if activity.in_activity() else logger


def workflow_logger():
    try:
        return logger.bind(**workflow.info()._logger_details())
    except:  # noqa
        return logger


@activity.defn(name="load_execution")
async def load_execution(id_: int) -> Optional[ExecutionContext]:
    activity_logger().info(f"start load workflow execution[{id_}]")
    instance: WorkflowExecution = await get_instance(
        WorkflowExecution,
        WorkflowExecution.id == id_
    )
    if not instance:
        return None
    ret = ExecutionContext.from_instance(instance)
    activity_logger().info(f"load workflow execution success[{ret.id}]")
    return ret


@activity.defn(name="flush_execution")
async def flush_execution(ctx: ExecutionContext):
    async with aio_sa.make_session() as session:
        stmt = sa.select(WorkflowExecution).where(
            WorkflowExecution.id == ctx.id
        )
        r = await session.execute(stmt)
        instance: WorkflowExecution = r.scalars().first()
        instance.start_at = datetime.fromtimestamp(ctx.start_at)
        instance.runtime_id = ctx.runtime_id
        instance.state = ctx.state
        instance.result_json = ctx.result_json
        if ctx.end_at:
            instance.end_at = datetime.fromtimestamp(ctx.end_at)
        else:
            instance.end_at = None
        await session.commit()
        stmt = sa.select(Workflow).where(
            Workflow.id == instance.workflow_id
        ).limit(1)
        r = await session.execute(stmt)
        workflow: Workflow = r.scalar_one()
    if instance.result_json:
        if workflow.workflow_type == WorkflowTypeEnum.FEISUO_AUTO:
            # 只有FEISUO_AUTO类型的workflow才需要通知飞梭
            loop = asyncio.get_running_loop()
            # 通知飞梭, 因为发kafka消息是同步IO操作，会阻塞协程，所以使用run_in_executor异步操作
            loop.run_in_executor(
                executor,
                functools.partial(
                    notify_feisuo,
                    workflow_id=instance.workflow_id,
                    execution_id=instance.id,
                    robot_result=RobotResultSchema.parse_obj(instance.result_json)
                ))


@activity.defn(name="dispatch_activity")
async def dispatch_activity(ctx: ExecutionContext) -> int:
    """
    使用长时间运行的activity执行任务下发
    heartbeat_details存储了
    (next_retry_time.timestamp(), count)
    """
    activity_info = activity.info()
    if not activity_info.heartbeat_details or len(activity_info.heartbeat_details) != 2:
        next_retry_time = datetime.utcnow()
        count = 0
    else:
        # activity heartbeat超时重启, 需要恢复上一次执行的上下文信息
        next_retry_time, count = activity_info.heartbeat_details
        next_retry_time = datetime.fromtimestamp(next_retry_time)
        activity_logger().info(f"dispatch activity restarted, execution[{ctx.id}], attempts[{activity_info.attempt}]")
    base_sleep_time = min(5, ctx.timeout)
    max_sleep_time = min(120, ctx.timeout)
    heartbeat_interval = 0.5
    mgr = RouteManager(activity_logger())
    try:
        while True:
            if datetime.utcnow() >= next_retry_time:
                activity_logger().info(f"start dispatch, execution[{ctx.id}], count[{count}]")
                runtime_id = await mgr.select_idle_runtime(ctx.id)
                if runtime_id == -1:
                    activity_logger().info("no online runtime, exit")
                    return runtime_id
                elif runtime_id:
                    ctx.runtime_id = runtime_id
                    success = await call_gateway(ctx)
                    if success:
                        return runtime_id
                # exponential backoff增长, 最大值120s
                sleep_time = min(max_sleep_time, base_sleep_time * (2 ** count))
                next_retry_time = datetime.utcnow() + timedelta(seconds=sleep_time)
                # count限制最大值，防止指数增长过大
                count = min(count + 1, 50)
            else:
                await asyncio.sleep(heartbeat_interval)
                activity.heartbeat(next_retry_time.timestamp(), count)
    except asyncio.CancelledError:
        logger.info("dispatch activity cancelled.")
        raise


async def call_gateway(ctx: ExecutionContext) -> bool:
    async with aio_sa.make_session() as session:
        # 获取runtime
        runtime_stmt = sa.select(Runtime).where(Runtime.id == ctx.runtime_id)
        # 获取step
        step_stmt = sa.select(WorkflowStep).where(
            WorkflowStep.workflow_id == ctx.workflow_id).limit(1)
        # 获取robot
        robot_stmt = sa.select(Robot).join(
            VersionedRobot,
            VersionedRobot.robot_id == Robot.id
        ).where(
            VersionedRobot.id == ctx.versioned_robot_id
        ).limit(1)
        # 获取workflow
        workflow_stmt = sa.select(Workflow).where(Workflow.id == ctx.workflow_id).limit(1)
        runtime_task, step_task, robot_task, workflow_task = (
            asyncio.create_task(session.execute(runtime_stmt)),
            asyncio.create_task(session.execute(step_stmt)),
            asyncio.create_task(session.execute(robot_stmt)),
            asyncio.create_task(session.execute(workflow_stmt))
        )
        await asyncio.wait([
            runtime_task, step_task, robot_task, workflow_task
        ])
        runtime: Runtime = runtime_task.result().scalar_one()
        step: WorkflowStep = step_task.result().scalar_one()
        robot: Robot = robot_task.result().scalar_one()
        workflow: Workflow = workflow_task.result().scalar_one()
        data = {
            "user_id": runtime.user_id,
            "execution_id": ctx.id,
            "execution_seq": ctx.seq,
            "workflow_id": step.workflow_id,
            "workflow_type": workflow.workflow_type.value,
            "workflow_step": {
                "id": step.id,
                "workflow_id": step.workflow_id,
                "seq": step.seq,
                # execution.versioned_robot不一定和step.versioned_robot相等，因为机器人升级有可能导致step.versioned_robot更新
                "versioned_robot_id": ctx.versioned_robot_id,
                "robot_id": robot.id
            }
        }
        try:
            ret = await aio_gateway_client.execute(data)
        except Exception:
            ret = False
            logger.opt(exception=True).error("Call gateway exception.")
        return ret


@workflow.defn(name="execution_fsm")
class ExecutionFsm:

    def __init__(self):
        self.time_up_ev = RichEvent()
        self.cancel_ev = RichEvent()
        self.dispatch_ok_ev = RichEvent()
        self.run_ok_ev = RichEvent()
        self.run_ex_ev = RichEvent()
        self.retry_ev = RichEvent()

        self.dispatch_task: typing.Optional[asyncio.Task] = None

        self.ctx = None

        self.wait_retry_interval: int = 7 * 24 * 3600  # workflow_execution等待重试最大时间，超过这个时间，直接关闭这个workflow

    def create_wait_event(self, ev) -> asyncio.Task:
        ret = asyncio.create_task(ev.wait())
        return ret

    async def create_timer(self):
        passed = utils.get_workflow_now().timestamp() - self.ctx.start_at
        duration = self.ctx.timeout - passed
        duration = 0 if duration < 0 else duration
        workflow_logger().info(f"Start timer: {duration}")
        await asyncio.sleep(duration)
        self.time_up_ev.set()

    def start_timer(self) -> asyncio.Task:
        timer = self.create_timer()
        return asyncio.create_task(timer,
                                   name="timer")

    async def flush(self):
        # 刷数据库
        await workflow.execute_activity(
            flush_execution,
            self.ctx,
            start_to_close_timeout=timedelta(seconds=10)
        )

    @workflow.signal
    async def execution_callback(self, payload: ExecutionCallbackSchema):
        state = payload.state
        if not self.ctx:
            workflow_logger().error("Unable to handle callback, execution context not loaded.")
        else:
            workflow_logger().info(f"Execution callback received [{self.ctx.id}], payload: {payload.dict()}")
            if state == ExecutionStateEnum.SUCCESS:
                self.run_ok_ev.set(payload)
            elif state == ExecutionStateEnum.EXCEPTION:
                self.run_ex_ev.set(payload)
            elif state == ExecutionStateEnum.CANCELED:
                self.cancel_ev.set(payload)
            else:
                workflow_logger().info(f"Unsupported state in execution callback: {state.value}")

    @workflow.signal
    async def run_exception(self, payload: Optional[ExecutionCallbackSchema] = None):
        # deprecated, use execution_callback instead
        self.run_ex_ev.set(payload)

    @workflow.signal
    async def run_ok(self, payload: Optional[ExecutionCallbackSchema] = None):
        # deprecated, use execution_callback instead
        self.run_ok_ev.set(payload)

    @workflow.signal
    async def cancel(self, payload: Optional[ExecutionCallbackSchema] = None):
        # deprecated, use execution_callback instead
        self.cancel_ev.set(payload)

    @workflow.signal
    async def retry(self):
        if not self.ctx:
            workflow_logger().error("Unable to retry, execution context not loaded.")
        else:
            workflow_logger().info(f"Received retry signal, execution_id: [{self.ctx.id}]")
            self.retry_ev.set()

    @workflow.run
    async def run(self, execution_id: int):
        self.ctx: Optional[ExecutionContext] = await workflow.execute_activity(
            load_execution,
            execution_id,
            start_to_close_timeout=timedelta(seconds=10)
        )
        if not self.ctx:
            workflow_logger().error(f"Cannot find execution: [{execution_id}]")
            return
        wait_timeout = utils.create_wait_ev_task(self.time_up_ev)
        wait_cancel = utils.create_wait_ev_task(self.cancel_ev)
        while True:
            if self.ctx.state == ExecutionStateEnum.PENDING.value:
                self.start_timer()
                # self.start_dispatcher()
                dispatch_task = workflow.start_activity(
                    dispatch_activity,
                    self.ctx,
                    start_to_close_timeout=timedelta(seconds=self.ctx.timeout * 2),
                    heartbeat_timeout=timedelta(seconds=5)
                )
                dispatch_task.add_done_callback(lambda fut: setattr(fut, "ts", workflow.now().timestamp()))
                done, _ = await asyncio.wait([
                    wait_timeout,
                    wait_cancel,
                    dispatch_task
                ], return_when=asyncio.FIRST_COMPLETED)
                finished = utils.get_first_completed(done)
                if finished is wait_timeout:
                    # timeout
                    self.ctx.set_timeout()
                elif finished is wait_cancel:
                    # canceled
                    robot_result = (self.cancel_ev.payload.robot_result
                                    if self.cancel_ev.payload else None)
                    self.ctx.set_canceled(robot_result)
                elif finished is dispatch_task:
                    # dispatch_task done
                    runtime_id = dispatch_task.result()
                    if runtime_id == -1:
                        # 没有在线客户端时响应很快，飞梭可能job都还没有结束执行
                        # 改善飞梭的job_id not found for execution_id的错误
                        await asyncio.sleep(5)
                        self.ctx.set_timeout()
                    else:
                        self.ctx.runtime_id = dispatch_task.result()
                        self.ctx.set_running()
                workflow_logger().info("cancel dispatch task.")
                dispatch_task.cancel()
                # TODO: add state transition hook to auto flush db
                await self.flush()
            elif self.ctx.state == ExecutionStateEnum.RUNNING.value:
                wait_run_ok = utils.create_wait_ev_task(self.run_ok_ev)
                wait_run_ex = utils.create_wait_ev_task(self.run_ex_ev)
                done, _ = await asyncio.wait([
                    wait_timeout,
                    wait_cancel,
                    wait_run_ex,
                    wait_run_ok
                ], return_when=asyncio.FIRST_COMPLETED)
                finished: asyncio.Task = utils.get_first_completed(done)
                if finished is wait_timeout:
                    # timeout
                    self.ctx.set_timeout()
                elif finished is wait_cancel:
                    # canceled
                    robot_result = (self.cancel_ev.payload.robot_result
                                    if self.cancel_ev.payload else None)
                    self.ctx.set_canceled(robot_result)
                elif finished is wait_run_ex:
                    # run exception
                    robot_result = (self.run_ex_ev.payload.robot_result
                                    if self.run_ex_ev.payload else None)
                    self.ctx.set_exception(robot_result)
                else:
                    # run ok
                    robot_result = (self.run_ok_ev.payload.robot_result
                                    if self.run_ok_ev.payload else None)
                    self.ctx.set_success(robot_result)
                await self.flush()
            elif self.ctx.state in (ExecutionStateEnum.CANCELED.value,
                                    ExecutionStateEnum.EXCEPTION.value,
                                    ExecutionStateEnum.TIMEOUT.value):
                self.retry_ev.clear()
                wait_retry = utils.create_wait_ev_task(self.retry_ev)
                wait_terminate = asyncio.create_task(asyncio.sleep(self.wait_retry_interval))
                done, _ = await asyncio.wait([
                    wait_retry,
                    wait_terminate
                ], return_when=asyncio.FIRST_COMPLETED)
                if wait_retry in done:
                    self.ctx.set_retry()
                    await self.flush()
                    workflow.continue_as_new(self.ctx.id)
                else:
                    workflow_logger().warning(
                        "execution finished with wait retry timeout.[{}], timeout[{}]".format(
                            self.ctx.id, self.wait_retry_interval)
                    )
                    break
            else:
                # success状态，跳出状态机
                workflow_logger().info(f"execution finished with success.[{self.ctx.id}]")
                break
