-r requirements.txt
certifi==2022.12.7        # via -c requirements.txt, requests
charset-normalizer==3.1.0  # via -c requirements.txt, requests
coverage[toml]==7.3.2     # via -c requirements.txt, pytest-cov
execnet==1.9.0            # via pytest-xdist
factory-boy==3.2.1        # via pytest-factoryboy
faker==19.12.1            # via factory-boy, pydantic-factories
fakeredis[lua]
flake8==7.0.0             # via pytest-flake8
idna==3.4                 # via -c requirements.txt, requests
inflection==0.5.1         # via pytest-factoryboy
iniconfig==2.0.0          # via pytest
mccabe==0.7.0             # via flake8
more-itertools==9.1.0     # via -c requirements.txt, pytest
packaging           # via -c requirements.txt, pytest
pluggy==0.13.1            # via pytest
py==1.11.0                # via pytest, pytest-forked
pycodestyle==2.11.1       # via flake8
pydantic==1.10.13         # via -c requirements.txt, pydantic-factories
pydantic-factories==1.4.1  # via -r dev-requirements.in
pyflakes==3.2.0           # via flake8
pyparsing==3.0.9          # via -c requirements.txt, packaging
pytest==6.2.5             # via leyan-test, pytest-cov, pytest-factoryboy, pytest-flake8, pytest-forked, pytest-mock, pytest-xdist
pytest-cov==4.0.0         # via leyan-test
pytest-flake8==1.1.0      # via leyan-test
pytest-forked==1.6.0      # via pytest-xdist
pytest-mock==3.3.1        # via leyan-test
pytest-order==1.2.1
pytest-reraise==2.1.2
pytest-xdist==2.4.0       # via -r dev-requirements.in
python-dateutil==2.8.2    # via -c requirements.txt, faker
requests==2.28.2          # via -c requirements.txt, requests-mock
requests-mock==1.10.0     # via -r dev-requirements.in
six==1.16.0               # via -c requirements.txt, python-dateutil, requests-mock
toml==0.10.2              # via pytest
tomli==2.0.1              # via coverage
typing-extensions==4.7.0  # via -c requirements.txt, faker, pydantic, pydantic-factories, pytest-factoryboy
urllib3==1.26.15          # via -c requirements.txt, requests
xeger==0.3.5              # via pydantic-factories
isort
black

mypy==1.7.0
mypy-extensions==1.0.0
types-pytz==2022.7.1
types-Pillow==9.0.0
types-opentracing==2.4.0
types-requests==2.28.2
pandas-stubs==1.2.*
types-openpyxl==3.1.*
types-jmespath==0.10.0
types-python-dateutil==2.8.2
grpc-stubs==1.24.8
types-cachetools==5.5.0.20240820
