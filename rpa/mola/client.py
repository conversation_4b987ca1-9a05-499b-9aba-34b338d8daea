import datetime
import json
import re
from collections import defaultdict
from dataclasses import asdict
from dataclasses import dataclass
from typing import List
from typing import Optional

import jwt
from dramatiq import Retry
from loguru import logger
from result import Err
from result import Ok
from result import Result

from robot_processor.client import token_bucket_limiter
from robot_processor.client_mixins import Session
from robot_processor.utils import filter_none
from robot_processor.utils import response_to_log
from rpa.conf import rpa_config
from rpa.mola.schemas import QNInvoiceGetAgreed
from rpa.mola.schemas import QNInvoiceGetAll


def format_price(price: Optional[int]) -> Optional[str]:
    if price is None:
        return None
    return "{:.2f}".format(price / 100)


def format_timestamp(ts: Optional[int]) -> Optional[str]:
    if ts is None:
        return None
    return datetime.datetime.fromtimestamp(ts).strftime("%Y-%m-%d %H:%M:%S")


def format_trade(trade: dict) -> dict:
    return {k: str(v) for k, v in trade.items() if v is not None}


class MolaClient:
    session = Session()
    auto_retry_session = Session(need_retry=True)

    def __init__(self, auth_id: str, need_retry: bool = False):
        """大部分情况下 auth_id 就是 shop.sid；对于 发送 微信/qq 消息的情况，auth_id 是 wechat_id/qq_id."""
        self.auth_id = auth_id
        self.auth_token = jwt.encode(
            {"shop": {"id": self.auth_id}}, rpa_config.MOLA_CLIENT_SECRET_KEY, algorithm="HS256"
        )
        self.session = MolaClient.auto_retry_session if need_retry else MolaClient.session

    def _post(self, url: str, data: dict, token: str, params: dict | None = None) -> Result[dict, str]:
        logger.info(f"post {url=} {params=} {token=} {data=}")
        try:
            resp = self.session.post(
                url,
                json=data,
                headers={"Authorization": f"Bearer {token}"},
                params=params,
                timeout=rpa_config.MOLA_CLIENT_REQUEST_TIMEOUT,
            )
            if resp.ok:
                logger.info(f"response: {resp.text}")
            else:
                logger.warning(f"response: {resp.text}")
        except Exception as e:
            return Err(str(e))

        if not resp.ok:
            return Err(response_to_log(resp))

        resp_json = resp.json()
        if not resp_json.get("success"):
            return Err(response_to_log(resp))
        else:
            return Ok(resp_json)

    def call_namespace_method(
        self, namespace: str, method: str, data: dict, params=None, check_rate_limit: bool = False
    ) -> Result[dict, str]:
        if check_rate_limit:
            token_bucket_key = f"mola:{namespace}:{method}"
            if not token_bucket_limiter.try_acquire_token_for_store(token_bucket_key, self.auth_id):
                raise Retry(message=f"{token_bucket_key}:{self.auth_id} is rate limited", delay=3000)
        url = f"{rpa_config.MOLA_GATEWAY_ENDPOINT}/namespaces/{namespace}/methods/{method}"
        return self._post(url, data, self.auth_token, params=params)

    def reset_limit(self, namespace: str, method: str) -> bool:
        """
        解锁
        """
        token_bucket_key = f"mola:{namespace}:{method}"
        return token_bucket_limiter.reset_acquire_token_for_store(token_bucket_key, self.auth_id)

    def call_platform_method(self, platform: str, method: str, data: dict) -> Result[dict, str]:
        url = f"{rpa_config.MOLA_GATEWAY_ENDPOINT}/platforms/{platform}/methods/{method}"
        return self._post(url, data, self.auth_token)

    def query_tmall_logistics(self, dispute_id: str) -> Result[dict, str]:
        mola_res = self.call_namespace_method("tmall-exchange", "get-original-trade-detail", {"disputeId": dispute_id})
        match mola_res:
            case Err():
                return mola_res
            case Ok(resp_json):
                pattern_change = re.compile(".*买家.*创建了换货申请.*换货原因：(.*)换货说明：(.*)", re.M | re.I)
                pattern_logistics = re.compile(
                    ".*商家.*已经发出换新货.*物流公司：(.*)发货单号：(.*)收货地址：(.*)", re.M | re.I
                )
                logistics = {}
                data_list = resp_json.get("result", {}).get("data")
                for key, value in data_list.items():
                    if value.get("fields") and value.get("fields").get("info"):
                        info = value["fields"].get("info").replace("\n", "")
                        logistics_info = re.findall(pattern_logistics, info)
                        if logistics_info:
                            logistics["logistics_name"] = logistics_info[0][0]
                            logistics["logistics_no"] = logistics_info[0][1]
                            logistics["logistics_address"] = logistics_info[0][2]
                            # 物流更新时间
                            if value.get("fields").get("time"):
                                logistics["logistics_updatetime"] = value.get("fields").get("time")
                            continue
                        change_info = re.findall(pattern_change, info)
                        if change_info:
                            logistics["logistics_reason"] = change_info[0][0]
                            logistics["logistics_remark"] = change_info[0][1]
                            logistics["logistics_require"] = "换货"
                            continue
                    # 兼容另外一种情况
                    if key.startswith("logisticsDetail"):
                        logistics["logistics_name"] = value.get("fields").get("companyName")
                        logistics["logistics_no"] = value.get("fields").get("trackingNum")
                        continue
                if not logistics.get("logistics_no"):
                    return Err(f"query tmall logistics error: <{resp_json}>")
                else:
                    return Ok(logistics)

    @dataclass
    class YtoKhSendMessageOptions:
        fallbacks: str = "_online_"
        subUserNicks: Optional[str] = None

    @dataclass
    class YtoKhSendMessagePayload:
        toName: str
        text: str
        imgUrl: Optional[List[str]]

    def yto_kh_send_message(
        self, options: YtoKhSendMessageOptions, payload: YtoKhSendMessagePayload
    ) -> Result[dict, str]:
        """圆钉发消息"""
        return self.call_namespace_method(
            "yto-kh", "send-message", data=filter_none(asdict(payload)), params=filter_none(asdict(options))
        )

    def query_online_qq_accounts(self, qq_ids: list) -> Result[list[str], str]:
        token = jwt.encode({"sids": qq_ids}, rpa_config.MOLA_CLIENT_SECRET_KEY, algorithm="HS256")
        try:
            resp = self.session.get(
                f"{rpa_config.MOLA_GATEWAY_ENDPOINT}/namespaces/qq/sockets",
                headers={"Authorization": f"Bearer {token}"},
                timeout=rpa_config.MOLA_CLIENT_REQUEST_TIMEOUT,
            )
            logger.info(f"GET {rpa_config.MOLA_GATEWAY_ENDPOINT}/namespaces/qq/sockets, resp: {resp.text}")
            resp_json = resp.json()
        except Exception as e:
            return Err(str(e))
        else:
            if resp.ok:
                qq_ids = [obj.get("data", {}).get("shopId") for obj in resp_json.get("sockets", {})]
                online_qq_ids: list[str] = [qq_id for qq_id in qq_ids if qq_id]
                return Ok(online_qq_ids)
            else:
                return Err(response_to_log(resp))

    def query_online_taobao_accounts(self) -> dict:
        """
        mola返回值
        {
            "count": 5,
            "sockets": [
                {
                    "id": "ZM7AKsHGm8w6sYC_Aytt",
                    "data": {
                        "version": "0.7.9",
                        "site": "taobao",
                        "namespace": "qianniu",
                        "shopId": "123",
                        "title": "xxx",
                        "nickname": "xxxx",
                        "relatedShops": [],
                        "subUserId": "*********",
                        "subUserNick": "xxx:xxx"
                    },
                    "origin": "alires://",
                    "userAgent": "",
                    "issued": *************,
                    "issuedAt": "2022-03-23 08:30:22.766",
                    "called": 0,
                    "calledAt": ""
                }
            ]
        }
        """
        try:
            resp = self.session.get(
                f"{rpa_config.MOLA_GATEWAY_ENDPOINT}/sites/taobao/sockets",
                headers={"Authorization": f"Bearer {self.auth_token}"},
                timeout=rpa_config.MOLA_CLIENT_REQUEST_TIMEOUT,
            )
            logger.info(f"GET {rpa_config.MOLA_GATEWAY_ENDPOINT}/sites/taobao/sockets, resp: {resp.text}")
            data = resp.json()
        except Exception:
            logger.exception("get_shop_online_accessors failed")
            return {}

        if not resp.ok:
            return {}

        assistants_from_mola = defaultdict(list)

        for assistant_info in data["sockets"]:
            data = assistant_info.get("data", {})
            if data.get("namespace") != "qianniu":
                # namespace==qianniu代表在线
                continue
            assistants_from_mola[data.get("site")].append(data["subUserId"])
        logger.info(f"Get Online assistants from mola resp {assistants_from_mola}")

        # mola 对于 taobao / tmall 不敏感，会影响平台账号的查询，所以做一次兼容
        assistants_from_mola_dict = dict(assistants_from_mola)
        taobao_tmall = list(set(assistants_from_mola["taobao"] + assistants_from_mola["tmall"]))
        if taobao_tmall:
            assistants_from_mola_dict["taobao"] = taobao_tmall
            assistants_from_mola_dict["tmall"] = taobao_tmall
        return assistants_from_mola_dict

    def change_order_sku(self, oid: str, spu_id: int, sku_id: str):
        return self.call_namespace_method(
            "qianniu", "change-order-sku", {"orderId": oid, "itemNumId": spu_id, "skuId": sku_id}
        )

    def send_qianniu_message(self, sub_user_ids, contact, text, image) -> Result[dict, str]:
        return self.call_namespace_method(
            "qianniu",
            "send-text",
            data={"contact": contact, "text": text, "image": image},
            params={"fallbacks": "_online_", "subUserIds": sub_user_ids},
        )

    def get_buyer_trade_list(self, buyer_id: str) -> Result[dict, str]:
        data = {
            "api": "invokeMTopChannelService",
            "query": {
                "method": "mtop.taobao.qianniu.cs.trade.query",
                "param": {
                    "securityBuyerUid": buyer_id,
                },
                "httpMethod": "get",
                "version": "1.0",
            },
            "timeout": 5000,
        }
        return self.call_namespace_method("qianniu", "invoke", data)

    def get_qianniu_delivery_consult_trade(self, tid: str) -> Result[dict, str]:
        return self.call_namespace_method(
            "qianniu",
            "invoke",
            data={
                "api": "invokeMTopChannelService",
                "query": {
                    "method": "mtop.taobao.delivery.consult.trade.order.get",
                    "param": {"tradeOrderId": tid},
                    "httpMethod": "post",
                    "version": "1.0",
                },
                "timeout": 5000,
            },
        )

    def create_qianniu_delivery_consult_trade(self, tid: str, final_consign_time: str) -> Result[dict, str]:
        res = self.get_qianniu_delivery_consult_trade(tid)
        if res.is_err():
            return res
        consult_trade = res.unwrap()
        if consult_trade["success"] and consult_trade["result"]:
            data = {
                "api": "invokeMTopChannelService",
                "query": {
                    "method": "mtop.taobao.delivery.consult.order.create",
                    "param": {
                        "consultOrder": json.dumps(
                            {
                                "tradeOrderId": tid,
                                "buyerId": consult_trade["result"]["data"]["buyerId"],
                                "subOrderVOList": [
                                    {"subOrderId": subOrder["subOrderId"]}
                                    for subOrder in consult_trade["result"]["data"]["subOrderVOList"]
                                ],
                            }
                        ),
                        "finalConsignTime": final_consign_time,
                        "buyerId": consult_trade["result"]["data"]["buyerId"],
                    },
                    "httpMethod": "post",
                    "version": "1.0",
                },
                "timeout": 5000,
            }
            return self.call_namespace_method("qianniu", "invoke", data)
        else:
            return Err(consult_trade["message"])

    def create_after_sale_trade(self, payload: dict) -> Result[dict, str]:
        """新建售后单.

        @param payload: 创建售后单的参数
        """
        return self.call_namespace_method("wanliniu-erp", "create-after-sale-trade", payload)

    def create_trade_form_exchange(
        self,
        exchange_uid: str,
        pics: list[str] | None = None,
        storage_name: str | None = None,
        delivery_name: str | None = None,
    ) -> Result[dict, str]:
        """
        万里牛下单接口
        """
        data = {"exchange_uid": exchange_uid}
        if pics:
            data["pics"] = pics  # type: ignore[assignment]
        if storage_name:
            data["storage_name"] = storage_name
        if delivery_name:
            data["delivery_name"] = delivery_name
        return self.call_namespace_method("wanliniu-erp", "create-trade-form-exchange", data)

    def update_doudian_memo(self, payload: dict, check_rate_limit=True) -> Result[dict, str]:
        """修改抖音备注."""
        return self.call_namespace_method("doudian", "modify-remarks", payload, check_rate_limit=check_rate_limit)

    def send_wechat_message(self, wechat_group: str, messages, check_rate_limit=True) -> Result[dict, str]:
        """发送消息到微信群."""
        return self.call_namespace_method(
            "wechat",
            "send-to-group",
            {"groupName": wechat_group, "messages": messages},
            check_rate_limit=check_rate_limit,
        )

    def send_qq_message(self, group_id: str, messages, images, check_rate_limit=True) -> Result[dict, str]:
        """发送消息到QQ群."""
        return self.call_namespace_method(
            "qq",
            "send-to-group",
            {"groupID": group_id, "messages": messages, "images": images},
            check_rate_limit=check_rate_limit,
        )

    def post_ems_trace(self, method: str, sender: str, payload: dict, check_rate_limit=True) -> Result[dict, str]:
        """添加中国邮政快递单号跟踪信息或者添加备注."""
        return self.call_namespace_method(
            "youzheng-package",
            method,
            data=payload,
            params={"fallbacks": "_online_", "subUserNicks": sender},
            check_rate_limit=check_rate_limit,
        )

    def pdd_query_trade(self, tid, check_rate_limit=True) -> Result[dict, str]:
        # ref: https://git.leyantech.com/digismart/mola-service/-/tree/master/src/sites/pinduoduo/mms
        res = self.call_namespace_method(
            "pdd-mms", "get-order-detail", dict(orderSn=tid), check_rate_limit=check_rate_limit
        )
        match res:
            case Ok(body):
                result = body["result"]
                return Ok(
                    format_trade(
                        {
                            "shipping_name": result.get("shipping_name"),
                            "shipping_id": result.get("shipping_id"),
                            "tracking_number": result.get("tracking_number"),
                            "pay_time": format_timestamp(result.get("pay_time")),
                            "goods_name": result.get("goods_name"),
                            "out_sku_sn": result.get("out_sku_sn"),
                            "spec": result.get("spec"),
                            "goods_price": format_price(result.get("goods_price")),
                            "goods_number": result.get("goods_number"),
                            "discount_amount": format_price(result.get("discount_amount")),
                            "order_amount": format_price(result.get("order_amount")),
                            "order_status_str": result.get("order_status_str"),
                        }
                    )
                )
            case Err():
                return res

    def jd_query_trade(self, tid, check_rate_limit=True) -> Result[dict, str]:
        # ref: https://git.leyantech.com/digismart/mola-service/-/tree/master/src/sites/jd
        res = self.call_namespace_method(
            "jd-shop", "get-orders-list", dict(orderId=tid), check_rate_limit=check_rate_limit
        )
        match res:
            case Ok(body):
                trade = {}
                order_list = body["result"]["orderList"]
                if order_list:
                    order = order_list[0]
                    order_item = order["orderItems"][0]
                    if logistics_infos := order.get("logisticsInfos"):
                        logistics_info = logistics_infos[0]
                    else:
                        logistics_info = {}
                    trade = format_trade(
                        {
                            "orderCreateTime": order.get("orderCreateTime"),
                            "paymentConfirmTime": order.get("paymentConfirmTime"),
                            "skuId": order_item.get("skuId"),
                            "skuName": order_item.get("skuName"),
                            "jdPrice": order_item.get("jdPrice"),
                            "skuNum": order_item.get("skuNum"),
                            "shouldPay": order.get("shouldPay"),
                            "orderStatusStrCN": order.get("orderStatusStrCN"),
                            "logiCoprName": logistics_info.get("logiCoprName"),
                            "logiNoList": (
                                logistics_info.get("logiNoList")[0] if logistics_info.get("logiNoList") else None
                            ),
                            "huoHao": order_item.get("huoHao"),
                        }
                    )
                    if sku_id := order_item.get("skuId"):
                        if ware_info := self._jd_query_ware_by_skuid(sku_id, check_rate_limit):
                            trade.update(ware_info)
                return Ok(trade)
            case Err():
                return res

    def _jd_query_ware_by_skuid(self, sku_id, check_rate_limit=True) -> dict:
        """通过sku_id查询仓库信息"""
        res = self.call_namespace_method(
            "jd-shop", "get-goods-list", dict(skuId=sku_id), check_rate_limit=check_rate_limit
        )
        match res:
            case Ok(body):
                if not body.get("success"):
                    return {}
                if data := body.get("result", {}).get("data", []):
                    if ware := data[0].get("ware"):
                        return {"wareId": ware.get("wareId"), "ware_title": ware.get("wareName")}
                return {}
            case Err(error_message):
                logger.warning(f"jd_query_goods_by_skuid error! {error_message}")
                return {}

    def doudian_query_trade(self, tid, check_rate_limit=True) -> Result[dict, str]:
        # TODO: move to DoudianClient
        # ref: https://git.leyantech.com/digismart/mola-service/-/tree/master/src/sites/douyin/fxg
        res = self.call_namespace_method(
            "doudian", "order.detail", dict(order_id=tid), check_rate_limit=check_rate_limit
        )
        match res:
            case Ok(body):
                result = body["result"]
                order = result["order"]
                logistics_info = result.get("logistics_info") or {}
                logistics_desc = logistics_info.get("logistics_desc")
                product_item = order["product_item"][0]
                trade = format_trade(
                    {
                        "product_name": product_item.get("product_name"),
                        "product_id": product_item.get("product_id"),
                        "wms_co_name": logistics_desc and logistics_desc.split(" ")[0],
                        "l_id": self._get_doudian_l_id_from_mola(result),
                        "create_time": format_timestamp(order.get("create_time")),
                        "pay_time": format_timestamp(order.get("pay_time")),
                        "pay_amount": format_price(order.get("pay_amount")),
                        "sku_id": product_item.get("sku_id"),
                        "sku_spec": ";".join([spec["value"] for spec in product_item.get("sku_spec", [])]),
                        "merchant_sku_code": product_item.get("merchant_sku_code"),
                    }
                )
                return Ok(trade)
            case Err():
                return res

    @staticmethod
    def _get_doudian_l_id_from_mola(result):
        logistics_info = result.get("logistics_info", {})
        if not logistics_info:
            return "无法获取"
        # 先尝试从package_info获取
        if package_info := logistics_info.get("package_info"):
            return " ".join([i.get("track_no", "") for i in package_info])
        # 再尝试从描述信息中获取
        logistics_desc = logistics_info.get("logistics_desc")
        if not logistics_desc:
            return "无法获取"
        return " ".join(logistics_desc.split(" "))

    def check_tracking_number_registered(self, l_id) -> bool:
        """检查物流单号是否已经注册"""
        res = self.call_namespace_method("jstwpa", "get-is-express-registered", data={"l_id": l_id})
        match res:
            case Ok(body):
                return body.get("result") is True
            case Err():
                return False

    def get_voc_ask_list(self, spu_id: str, page_no: int, page_size: int) -> Result[dict, str]:
        res = self.call_namespace_method(
            "qianniu", "get-voc-ask-list", data={"kw": spu_id, "pageNum": page_no, "pageSize": page_size}
        )
        match res:
            case Ok(body):
                return Ok(body["result"])
            case Err():
                return res

    def get_voc_answer_list(self, id: str, page_no: int, page_size: int) -> Result[dict, str]:
        res = self.call_namespace_method(
            "qianniu", "get-voc-answer-list", data={"id": id, "pageNum": page_no, "pageSize": page_size}
        )
        match res:
            case Ok(body):
                return Ok(body["result"])
            case Err():
                return res

    def qn_invoice_get_agreed(
        self,
        page_no: int = 1,
        page_size: int = 20,
        start_time: str | None = None,
        end_time: str | None = None,  # 格式 YYYY-MM-DD
        tid: str | None = None,
        payer_name: str | None = None,
    ):
        """千牛 - 查询待开发票"""
        mola_result = self.call_namespace_method(
            "qianniu",
            "invoice-get-agreed",
            data=filter_none(
                {
                    "pageNum": page_no,
                    "pageSize": page_size,
                    "startTime": start_time,
                    "endTime": end_time,
                    "tid": tid,
                    "payerName": payer_name,
                }
            ),
        )
        if mola_result.is_err():
            return Err(Exception(mola_result.unwrap_err()))
        response = mola_result.unwrap()
        try:
            return Ok(QNInvoiceGetAgreed(**response["result"]))
        except Exception as e:
            return Err(e)

    def qn_invoice_get_all(
        self,
        page_no: int = 1,
        page_size: int = 20,
        start_time: str | None = None,
        end_time: str | None = None,
        tid: str | None = None,
        payer_name: str | None = None,
    ):
        """千牛 - 查询所有发票"""
        mola_result = self.call_namespace_method(
            "qianniu",
            "invoice-get-all",
            data=filter_none(
                {
                    "pageNum": page_no,
                    "pageSize": page_size,
                    "startTime": start_time,
                    "endTime": end_time,
                    "tid": tid,
                    "payerName": payer_name,
                }
            ),
        )
        if mola_result.is_err():
            return Err(Exception(mola_result.unwrap_err()))
        response = mola_result.unwrap()
        try:
            return Ok(QNInvoiceGetAll(**response["result"]))
        except Exception as e:
            return Err(e)

    def qn_invoice_submit_manual(
        self,
        invoice_url: str,
        invoice_no: str,
        invoice_date: str,
        serial_no: str,
        tid: str,
        platform_code: str,
        start_time: str,
    ):
        """千牛-录入发票"""
        return self.call_namespace_method(
            "qianniu",
            "invoice-submit-manual",
            data={
                "invoiceUrl": invoice_url,
                "invoiceNo": invoice_no,
                "invoiceDate": invoice_date,
                "serialNo": serial_no,
                "tid": tid,
                "platformCode": platform_code,
                "startTime": start_time,
            },
        ).map(lambda response: response["result"])
