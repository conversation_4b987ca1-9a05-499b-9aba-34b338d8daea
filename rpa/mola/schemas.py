from decimal import Decimal

from pydantic import BaseModel

from robot_processor.utils import make_fields_optional


@make_fields_optional
class QNInvoice(BaseModel):
    tid: str
    startTime: str  # 开票时间 YYYY-MM-DD
    invoiceCode: str
    invoiceNo: str
    invoiceKind: int  # 发票类型 0: 增值税电子普通发票 3: 增值税电子专用发票
    title: str  # 发票抬头
    amount: str  # 发票金额（元）
    serialNo: str  # 统一开票流水号
    applySource: str  # 来源
    payerName: str  # 购方
    payerRegisterNo: str  # 购方税号
    payerPhone: str
    payerAddress: str
    payerBank: str
    payerBankaccount: str
    status: int
    applyStatus: int
    virtualRegisterAgree: str
    invoiceStatus: int
    businessType: int  # 1: 企业 0: 个人
    platformCode: str
    rightsDueDate: int

    def get_status_zh(self):
        return {
            0: "已拒绝",
            1: "待处理申请-待同意",
            2: "待录入开票",
            3: "开票中",
            4: "开票失败",
            5: "开票成功",
            6: "待处理申请-协商中",
            -1: "已取消",
        }.get(self.status, "未知")

    def get_buyer_type(self):
        from robot_processor.invoice.workflow.models import BuyerType

        return {
            0: BuyerType.INDIVIDUAL,
            1: BuyerType.ENTERPRISE,
        }[self.businessType]

    def get_invoice_type(self):
        from robot_processor.invoice.workflow.models import InvoiceType

        return {
            0: InvoiceType.VAT_GENERAL,
            1: InvoiceType.VAT_GENERAL,
            2: InvoiceType.VAT_SPECIAL,
            3: InvoiceType.VAT_SPECIAL,
            4: InvoiceType.VAT_GENERAL,
            5: InvoiceType.VAT_SPECIAL,
        }[self.invoiceKind]

    def get_issuing_type(self):
        from robot_processor.invoice.workflow.models import IssuingType

        if Decimal(self.amount) >= 0:
            return IssuingType.BLUE
        else:
            return IssuingType.RED


@make_fields_optional
class QNInvoiceGetAgreed(BaseModel):
    code: int
    total: int
    data: list[QNInvoice]


@make_fields_optional
class QNInvoiceGetAll(BaseModel):
    code: int
    total: int
    data: list[QNInvoice]
