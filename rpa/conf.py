"""这个 module 内声明了所有 apollo 配置项"""
from lepollo import ApolloConfig, get_config


class RpaConfig(ApolloConfig):
    __namespace__ = "rpa"

    SMARTCALL_CLIENT_ENDPOINT: str = 'https://nta-api.leyanbot.com/smartcall-backend/openapi/'
    SMARTCALL_CLIENT_REQUEST_TIMEOUT: int = 3

    RPA_DOUDIAN_ORDER_DECRYPT_STATUS_IGNORE: str = "[1,4,5]"

    JST_ENDPOINT: str = "https://open.erp321.com/api/open/query.aspx"
    JST_QM_ENDPOINT: str = "http://a1q40taq0j.api.taobao.com/router/qm"
    JST_NEW_ENDPOINT: str = "https://openapi.jushuitan.com"
    JST_REQUEST_TIMEOUT: int = 35
    JST_CACHE_TIMEOUT: int = 60 * 60 * 24 * 7
    JST_PARTNER_ID: str
    JST_PARTNER_KEY: str
    JST_ISV_WHITE_LIST: list
    JST_ISV_APP_KEY: str
    JST_ISV_APP_SECRET: str
    JST_APP_KEY: str
    JST_APP_SECRET: str
    JST_TARGET_SECRET: str

    RPA_JST_CONFIRM_DELAY_INS: float = 0.5
    RPA_JST_SUB_USER_ID_MAP: str = '{}'
    RPA_JST_REQUEST_TIMEOUT: int = 10
    RPA_JST_SHOP_NOTE_MAP: str = '{}'

    WDT_ENDPOINT: str = "http://hu3cgwt0tc.api.taobao.com/router/qmtes"
    WDT_OPENAPI_ENDPOINT: str = "https://api.wangdian.cn/openapi2/"
    WDT_LOGISTICS_TYPE: str = '{}'
    WDT_TARGET_APP_KEY: int = 0
    WDT_APP_KEY: int = 0
    WDT_APP_SECRET: str = ''
    WDT_REQUEST_TIMEOUT: int = 3

    WANLINIU_TRADE_DEFAULT_TYPE: str = '普通订单'
    WANLINIU_TRADE_TYPE: list
    WANLINIU_AFTER_SALES_TRADE_TYPE: list
    WANLINIU_OFFLINE_TRADE_TYPE: list

    MOLA_GATEWAY_ENDPOINT: str = "https://stg-mola-gateway.ganjutech.com"
    MOLA_CLIENT_REQUEST_TIMEOUT: int = 3
    MOLA_CLIENT_SECRET_KEY: str = 'jwtS'
    MOLA_CLIENT_SUB_USER_ID_MAP: str = ''

    MOLA_CLIENT_DISABLE_RATE_LIMIT_ORG_IDS: str = '{"2561": true}'

    RPA_GUANYIYUN_DATETYPE: str = '0,1,2016'
    RPA_GUANYIYUN_SLEEPTIME: float = 0.3

    KM_QM_ORDER_LIST_URL: str = "http://33c367ryyg.api.taobao.com/router/qm"
    KM_TARGET_APP_KEY = "23204092"
    KMSDK_ENDPOINT = "https://gw.superboss.cc/router"

    WDTULTI_ENDPOINT: str = 'http://3ldsmu02o9.api.taobao.com/router/qm'
    WDTULTI_OPENAPI_ENDPOINT: str = "http://wdt.wangdian.cn/openapi"
    WDTULTI_LOGISTICS_TYPE: str = '{}'
    WDTULTI_TARGET_APP_KEY: int
    WDTULTI_APP_KEY: int
    WDTULTI_APP_SECRET: str
    WDTULTI_STG_WDT3_CUSTOMER_ID: str
    WDTULTI_REQUEST_TIMEOUT: int = 3

    ASGARD_ENDPOINT: str = 'http://zb-api.leyanbot.com/asgard/v1'
    ASGARD_TIMEOUT: int = 10

    RPA_SERVER_ENDPOINT: str = 'https://localhost/rpacontrol/api'
    RPA_SERVER_TOKEN: str = ''
    RPA_SERVER_TIMEOUT: int = 3

    DOUDIAN_OPEN_API_REQUEST_URL: str = 'http://localhost'
    DOUDIAN_OPEN_API_TIMEOUT: int = 3
    # 飞鸽侧边栏 ISV
    DOUDIAN_OPEN_API_APP_KEY: str
    DOUDIAN_OPEN_API_APP_SECRET: str
    # 小柚子 ISV
    DOUDIAN_XYZ_APP_KEY: str
    DOUDIAN_XYZ_APP_SECRET: str

    # Express 相关配置
    # 极兔
    JT_BASE_URL: str = "https://uat-openapi.jtexpress.com.cn/webopenplatformapi/api/"
    JT_API_ACCOUNT: str = ""
    JT_PRIVATE_KEY: str = ""
    JT_STRING: str = "jadada236t2"

    # EMS 开放平台
    EMS_API_ENDPOINT: str = "https://api.ems.com.cn/amp-prod-api/f/amp/api/open"

    # STO 开放平台
    STO_ENDPOINT: str = ""
    STO_SECRET_KEY: str = ""
    STO_FROM_APP_KEY: str = ""
    STO_FROM_CODE: str = ""

    # 顺丰开放平台
    SF_API_ENDPOINT: str = "https://sfapi-sbox.sf-express.com/std/service"
    SF_PARTNER_ID: str = "JXXXKcMrUsbh"
    SF_CHECK_WORD: str = "ooCTTj0CEuPmZPjoNaR1F6JY1WJYzpoo"

    INVOICE_CODE_RED_RE_PATTERN: str = "^冲红发票:(FP[0-9]{12})$"
    INVOICE_CODE_RED_TO_BLUE_RE_PATTERN: str = "^手工冲红发票:(FP[0-9]{12})$"

    # 韵达开放平台
    YD_API_ENDPOINT: str = "https://openapi.yundaex.com/openapi"
    YD_API_APP_KEY: str = "002510"
    YD_API_APP_SECRET: str = "8d59786ca06b4cb5bb6ad0ad1d5b3ebe"
    YD_SUBSCRIBE_SLEEP_SECONDS: int = 2

    QIANNIU_TEXT_MAX_LENGTH: int = 1000

    GYY_QM_ENDPOINT: str = "http://tp8z6548i2.api.taobao.com/router/qm"
    GYY_TARGET_APP_KEY: int = 0
    GYY_APP_KEY: int = 0
    GYY_APP_SECRET: str = ""
    GYY_REQUEST_TIMEOUT: int = 5
    GYY_OPENAPI_ENDPOINT: str = "http://v2.api.guanyierp.com/rest/erp_open"

    JACKYUN_QM_ENDPOINT: str = "http://zci2vl4joy.api.taobao.com/router/qm"
    JACKYUN_TARGET_APP_KEY: int = 24869601
    JACKYUN_APP_KEY: int = 0
    JACKYUN_APP_SECRET: str = ""
    JACKYUN_OPENAPI_ENDPOINT: str = "https://open.jackyun.com/open/openapi/do"
    JACKYUN_REQUEST_TIME: int = 5

    JDL_ENDPOINT = "https://api.jdl.com"
    JDL_APP_KEY = "b9fd9b3679e441cc800057a03564deca"
    JDL_APP_SECRET = "884c0607f4ce421ca785cb4f815a003b"

    GYY_TYPE_CODE_MAP: str = "{}"  # {"1": {"快递破损":"001"}}

    WDGJ_QM_ENDPOINT: str = "http://t33hglzv00.api.taobao.com/router/qm"
    WDGJ_TARGET_APP_KEY: int = 24542360
    WDGJ_APP_KEY: int = 32767466
    WDGJ_APP_SECRET: str = ""
    WDGJ_REQUEST_TIMEOUT: int = 5

    @property
    def rpa_client_wait_ack_timeout(self):
        return self.get_duration("rpa-client.timeout.wait-ack", 60)

    @property
    def pdd_shop_metrics_wait_act_timeout(self):
        return self.get_duration("rpa-client.timeout.wait-act", 60 * 2)

rpa_config = get_config(config_class=RpaConfig)


class CellarConfig(ApolloConfig):
    __namespace__ = "cellar"

    CELLAR_CLIENT_ENDPOINT: str = 'https://nta-api.leyanbot.com/cellar/keg'
    CELLAR_CLIENT_TOKEN: str = 'bad_token'
    CELLAR_CLIENT_REQUEST_TIMEOUT: int = 5

    PDD_TID_PATTERN: str = r"\d+-\d+"
    DOUDIAN_TID_PATTERN: str = r"\d+"


cellar_config = get_config(config_class=CellarConfig)
