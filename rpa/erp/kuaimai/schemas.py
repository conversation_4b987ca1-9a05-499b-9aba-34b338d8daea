import enum
from typing import List
from typing import Optional

from pydantic import BaseModel
from pydantic import Field
from pydantic import root_validator

from robot_processor.utils import make_fields_optional
from robot_processor.utils import omit_none_fields_before_validate
from rpa.erp.kuaimai.utils import format_timestamp


class KMException(Exception):
    pass


class KuaimaiSuit(BaseModel):
    """组合装信息"""

    pass


@make_fields_optional
class KuaimaiBaseOrder(BaseModel):
    """
    orders里面的结构和外面大致相同，但是里面的字段更多(比如sku相关的信息只有里面有)
    """

    acPayment: str
    adjustFee: str
    combineId: str
    companyId: int
    consignTime: str
    cost: str
    created: str
    discountFee: str
    discountRate: str
    endTime: str
    estimateConTime: str
    expressCompanyName: str  # 快递公司名称
    forcePackNum: int
    giftNum: int
    id: int
    insufficientCanceled: int
    isCancel: int
    isPresell: int
    isVirtual: int
    itemSysId: int
    modified: str
    netWeight: str
    num: int
    numIid: str
    oid: int
    outerSkuId: str
    outSid: str  # 物流单号
    payAmount: str
    payTime: str
    payment: str  # 实付金额
    picPath: str
    price: str
    ptConsignTime: str
    refundStatus: str
    saleFee: str
    salePrice: str
    shortTitle: str
    sid: int  # 系统订单号
    skuId: str
    skuPropertiesName: str
    skuSysId: int
    skuUnit: str
    soid: str
    source: str
    splitSid: int  # 拆分自哪个订单
    splitType: str
    status: str
    stockNum: int
    stockStatus: str
    sysConsigned: int
    sysItemRemark: str
    sysItemOuterId: str  # 假定该字段为 outer spu id
    sysOuterId: str  # 商家编码
    sysPicPath: str
    sysSkuPropertiesAlias: str
    sysSkuPropertiesName: str  # 商品规格
    sysSkuRemark: str
    sysStatus: str  # 系统订单状态
    sysTitle: str  # 平台商品名称
    taobaoId: int
    templateName: str  # 快递模板名称, 可能没有,与订单状态相关
    tid: str  # 平台订单号
    title: str  # 商品名称 不一定有
    totalFee: str
    type: str
    userId: int  # 店铺编码

    unit: str
    updTime: str
    volume: str
    warehouseName: str  # 发货仓， 可能没有

    # 用来配合rpa输出的字段,需要后期自己转化
    sys_status_output: str  # 平台系统状态
    pay_amount: str  # 实付金额
    logistics_name: str  # 快递公司
    logistics_no: str  # 快递单号
    logistics_template: str  # 快递模板
    warehouse_no: str  # 发货仓名称
    goods_title: str  # 商品名
    sys_goods_title: str  # 平台商品名
    sys_goods_properties: str  # 平台规格
    outer_id: str  # 商家编码
    supplier_name: str  # 供应商名称
    picker: str  # 拣选员
    packer: str  # 打包员
    weigher: str  # 称重员

    def is_ordinary(self):
        return KmOrderTypeEnum.ORDINARY.value in self.type

    def is_reissue(self):
        return KmOrderTypeEnum.REISSUE.value in self.type

    def is_split(self):
        return KmOrderTypeEnum.SPLIT.value in self.type

    def is_merge(self):
        return KmOrderTypeEnum.MERGE.value in self.type

    @property
    def type_as_name(self):
        return ",".join([KmOrderTypeEnum.get_name(i) for i in self.type.split(",")])

    def __repr__(self):
        if self.type:
            readable_type = ",".join([KmOrderTypeEnum.get_name(i) for i in self.type.split(",")])
        else:
            readable_type = "未知"
        return (
            f"<KuaimaiOrder 平台订单号：{self.tid} erp系统订单号{self.sid}>"
            f"系统状态：{self.sysStatus} 订单类型：{readable_type}"
        )

    __str__ = __repr__


@make_fields_optional
class KuaimaiOrder(KuaimaiBaseOrder):
    orders: List["KuaimaiBaseOrder"]  # 合并的订单的orders字段代表它合并了哪些订单,一个order里面只有一个商品

    def __init__(self, **data):
        super().__init__(**data)
        inner_orders = data.get("orders")
        if not inner_orders:
            return

        # 商品相关字段为orders中的join结果
        self.goods_title = self.goods_title or ",".join([o.get("title", "") for o in inner_orders])
        self.sys_goods_title = ",".join([o.get("sysTitle", "") for o in inner_orders])
        self.sys_goods_properties = ",".join([o.get("sysSkuPropertiesName", "") for o in inner_orders])
        self.outer_id = ",".join([o.get("sysOuterId", "") for o in inner_orders])

        # 对客户端一些字段进行转化
        if "expressTemplateName" in data:
            self.templateName = data["expressTemplateName"]

        # 客户端没有返回订单类型，取子订单的第一个的类型
        if "type" not in data:
            self.type = self.orders[0].type

    @staticmethod
    def merge_orders(orders):
        merged_order = KuaimaiOrder()
        for field in merged_order.__fields__.keys():
            if field == "orders":
                pass
            else:
                value = [str(getattr(o, field)) for o in orders if getattr(o, field) is not None]
                setattr(merged_order, field, ",".join(value))
        return merged_order


@make_fields_optional
class KuaimaiApiOrdersResp(BaseModel):
    hasNextPage: bool
    pageNo: int
    pageSize: int
    total: int
    trades: List[KuaimaiOrder]


class KmOrderTypeEnum(enum.Enum):
    """订单类型枚举"""

    ORDINARY = "0"  # 普通订单
    CASH_ON_DELIVERY = "1"  # 货到付款
    PLATFORM = "3"  # 平台订单
    OFFLINE = "4"  # 线下订单
    PRE_SALE = "6"  # 预售订单
    MERGE = "7"  # 合并订单
    SPLIT = "8"  # 拆分订单
    URGENT = "9"  # 加急订单
    EMPTY_PACKAGE = "10"  # 空包订单
    COMBINE_TIP = "11"  # 合单提示
    STORE = "12"  # 门店订单
    EXCHANGE = "13"  # 换货订单
    REISSUE = "14"  # 补发订单
    FOREIGN_WAREHOUSE = "16"  # 海外仓订单
    LAZADA = "17"  # Lazada
    DAMAGED_ORDER = "18"  # 报损单
    ISSUE = "19"  # 领用单
    ADJUSTMENT = "20"  # 调整单
    CUSTOMER_ORDER = "21"  # 客户订单
    TM_DIRECT_DELIVERY = "22"  # 天猫直送
    PLATFORM_PRE_SALE = "23"  # 平台预售
    JD_DIRECT_DELIVERY = "24"  # 京东直发
    DISTRIBUTION_ORDER = "33"  # 分销订单
    SUPPLY_ORDER = "34"  # 供销订单
    JD_DISTRIBUTION = "35"  # 京配订单
    PLATFORM_DISTRIBUTION = "36"  # 平台分销
    TM_TB_STORE_PRE_SALE = "50"  # 天猫淘宝店铺预售
    DOUYIN_FACTORY_DELIVERY = "51"  # 抖音厂商代发
    AMAZON_FBA = "53"  # 亚马逊 FBA
    AMAZON_FBM = "54"  # 亚马逊 FBM
    AMAZON_MULTI_CHANNEL = "55"  # 亚马逊多渠道
    QIMEN_ORDER = "56"  # 奇门订单
    DEW_NORMAL_STOCK = "57"  # 得物普通现货
    DEW_FAST_STOCK = "58"  # 得物极速现货
    FULL_PAYMENT_PRE_SALE = "60"  # 全款预售
    DEW_DIRECT_DELIVERY_ORDER = "61"  # 得物直发订单
    LAZADA_FBL = "62"  # Lazada-FBL
    PLATFORM_SPLIT_ORDER = "66"  # 平台拆单
    OUT_OF_STOCK_ORDER = "99"  # 出库单

    @classmethod
    def get_name(cls, value):
        """根据值获取名称"""
        return cls(value).name

    @property
    def label(self):
        return {
            KmOrderTypeEnum.ORDINARY: "普通订单",
            KmOrderTypeEnum.CASH_ON_DELIVERY: "货到付款",
            KmOrderTypeEnum.PLATFORM: "平台订单",
            KmOrderTypeEnum.OFFLINE: "线下订单",
            KmOrderTypeEnum.PRE_SALE: "预售订单",
            KmOrderTypeEnum.MERGE: "合并订单",
            KmOrderTypeEnum.SPLIT: "拆分订单",
            KmOrderTypeEnum.URGENT: "加急订单",
            KmOrderTypeEnum.EMPTY_PACKAGE: "空包订单",
            KmOrderTypeEnum.COMBINE_TIP: "合单提示",
            KmOrderTypeEnum.STORE: "门店订单",
            KmOrderTypeEnum.EXCHANGE: "换货订单",
            KmOrderTypeEnum.REISSUE: "补发订单",
            KmOrderTypeEnum.FOREIGN_WAREHOUSE: "海外仓订单",
            KmOrderTypeEnum.LAZADA: "Lazada",
            KmOrderTypeEnum.DAMAGED_ORDER: "报损单",
            KmOrderTypeEnum.ISSUE: "领用单",
            KmOrderTypeEnum.ADJUSTMENT: "调整单",
            KmOrderTypeEnum.CUSTOMER_ORDER: "客户订单",
            KmOrderTypeEnum.TM_DIRECT_DELIVERY: "天猫直送",
            KmOrderTypeEnum.PLATFORM_PRE_SALE: "平台预售",
            KmOrderTypeEnum.JD_DIRECT_DELIVERY: "京东直发",
            KmOrderTypeEnum.DISTRIBUTION_ORDER: "分销订单",
            KmOrderTypeEnum.SUPPLY_ORDER: "供销订单",
            KmOrderTypeEnum.JD_DISTRIBUTION: "京配订单",
            KmOrderTypeEnum.PLATFORM_DISTRIBUTION: "平台分销",
            KmOrderTypeEnum.TM_TB_STORE_PRE_SALE: "天猫淘宝店铺预售",
            KmOrderTypeEnum.DOUYIN_FACTORY_DELIVERY: "抖音厂商代发",
            KmOrderTypeEnum.AMAZON_FBA: "亚马逊 FBA",
            KmOrderTypeEnum.AMAZON_FBM: "亚马逊 FBM",
            KmOrderTypeEnum.AMAZON_MULTI_CHANNEL: "亚马逊多渠道",
            KmOrderTypeEnum.QIMEN_ORDER: "奇门订单",
            KmOrderTypeEnum.DEW_NORMAL_STOCK: "得物普通现货",
            KmOrderTypeEnum.DEW_FAST_STOCK: "得物极速现货",
            KmOrderTypeEnum.FULL_PAYMENT_PRE_SALE: "全款预售",
            KmOrderTypeEnum.DEW_DIRECT_DELIVERY_ORDER: "得物直发订单",
            KmOrderTypeEnum.LAZADA_FBL: "Lazada-FBL",
            KmOrderTypeEnum.PLATFORM_SPLIT_ORDER: "平台拆单",
            KmOrderTypeEnum.OUT_OF_STOCK_ORDER: "出库单",
        }[self]


class KmOrderSysStatusEnum(enum.StrEnum):
    """订单系统状态枚举"""

    WAIT_BUYER_PAY = "WAIT_BUYER_PAY"  # 待付款
    WAIT_AUDIT = "WAIT_AUDIT"  # 待审核
    WAIT_FINANCE_AUDIT = "WAIT_FINANCE_AUDIT"  # 待财审
    FINISHED_AUDIT = "FINISHED_AUDIT"  # 审核完成
    WAIT_EXPRESS_PRINT = "WAIT_EXPRESS_PRINT"  # 待打印快递单
    WAIT_PACKAGE = "WAIT_PACKAGE"  # 待打包
    WAIT_WEIGHT = "WAIT_WEIGHT"  # 待称重
    WAIT_SEND_GOODS = "WAIT_SEND_GOODS"  # 待发货
    WAIT_DEST_SEND_GOODS = "WAIT_DEST_SEND_GOODS"  # 待供销商发货
    SELLER_SEND_GOODS = "SELLER_SEND_GOODS"  # 卖家已发货
    FINISHED = "FINISHED"  # 交易完成
    CLOSED = "CLOSED"  # 交易关闭

    @staticmethod
    def get_chinese(value):
        mapping = {
            KmOrderSysStatusEnum.WAIT_BUYER_PAY.value: "待付款",
            KmOrderSysStatusEnum.WAIT_AUDIT.value: "待审核",
            KmOrderSysStatusEnum.WAIT_FINANCE_AUDIT.value: "待财审",
            KmOrderSysStatusEnum.FINISHED_AUDIT.value: "审核完成",
            KmOrderSysStatusEnum.WAIT_EXPRESS_PRINT.value: "待打印快递单",
            KmOrderSysStatusEnum.WAIT_PACKAGE.value: "待打包",
            KmOrderSysStatusEnum.WAIT_WEIGHT.value: "待称重",
            KmOrderSysStatusEnum.WAIT_SEND_GOODS.value: "待发货",
            KmOrderSysStatusEnum.WAIT_DEST_SEND_GOODS.value: "待供销商发货",
            KmOrderSysStatusEnum.SELLER_SEND_GOODS.value: "卖家已发货",
            KmOrderSysStatusEnum.FINISHED.value: "交易完成",
            KmOrderSysStatusEnum.CLOSED.value: "交易关闭",
        }
        return mapping.get(value, "未知状态")


class KmItem(BaseModel):
    """商品详情"""

    outerId: str  # 上传商品编码快麦就能找到那个商品
    count: int  # 数量, 商品数量 实收数量(isOpenPlatform为1 时必填)
    sysSkuId: Optional[int]  # 平台商品skuid


class KmReissue(BaseModel):
    """补发商品详情"""

    itemSnapshot: List[KmItem]
    wareHouseId: int
    type: int = 2  # 1退货 2补发
    remark: Optional[str]

    # 如果要直接审核并生成订单，需要下面这些参数
    receiveProvince: Optional[str]
    receiveCity: Optional[str]
    receiveDistrict: Optional[str]
    receiveAddress: Optional[str]
    receiveName: Optional[str]
    receivePhone: Optional[str]


class KmReissueReq(BaseModel):
    """创建补发单请求"""

    refundWarehouseId: int
    reason: str
    # 文档的解释：系统订单编号(系统销售订单号 1个订单号对应多个工单号),hasOwner为1时必填
    sid: Optional[int]  # 选了有主订单时，必填，且它的含义是erp的内部工单id;
    afterSaleType: int = 3  # 3补发
    reissueOrRefundList: List[KmReissue]
    userId: int
    tid: str
    hasOwner: int = 1  # 1有主 2无主
    msgPicAddress: Optional[str]  # 售后凭证，图片地址，分号分割
    force: bool = False  # 是否强制创建补发单， 默认不强制

    # 如果要直接审核并生成订单，需要下面这些参数
    type: int = 0  # 是否生成并解决，默认不解决
    isOpenPlatform: int = 0  # 强制生成补发单， 默认不生成
    # 系统备注
    remark: str | None
    wangwangNum: str | None

    @root_validator(pre=True)
    def convert_reason(cls, values):
        """
        reason 是一个选择组件，获取到的数据格式可能如下：

        ["xxxas"]
        [{"label": "xxx", "value": "yyy"}]

        :param values:
        :return:
        """
        _reason = values.get("reason")
        if _reason is None:
            return values
        if isinstance(_reason, list):
            first_ele = _reason[0]
            if isinstance(first_ele, dict):
                reason = str(first_ele.get("value") or "")
            else:
                reason = str(first_ele)
        else:
            reason = str(_reason)
        values["reason"] = reason
        return values


@make_fields_optional
class KmWarehouseDetail(BaseModel):
    code: str
    address: str
    city: str
    type: int
    companyId: int
    district: str
    contact: str
    name: str
    id: int
    state: str
    contactPhone: str
    status: int


@make_fields_optional
class KmWarehouseResp(BaseModel):
    code: str
    msg: str
    success: bool
    trace_id: str
    list: List[KmWarehouseDetail]


@make_fields_optional
class AfterSaleUploadRes(BaseModel):
    """上传售后凭证返回"""

    success: bool
    trace_id: str
    id: int  # 售后单号


@make_fields_optional
class ModifySellerMemoAndFlagRes(BaseModel):
    success: bool
    trace_id: str


@make_fields_optional
class AfterSaleDetailItem(BaseModel):
    goodItemCount: int = Field(description="良品数（销退入库时使用）")
    receiveGoodsOperatorNames: str = Field(description="入仓操作员名")
    badItemCount: int = Field(description="次品数 （销退入库时使用）")
    refundMoney: str = Field(description="商品系统实退金额")
    remark: str = Field(description="备注")
    type: int = Field(description="售后类型（商品级别） 1:退货商品 2:补发商品")
    title: str = Field(description="商品名称")
    propertiesName: str = Field(description="商品规格")
    receivableCount: int = Field(description="申请数量")
    isMatch: int = Field(description="商品状态（匹配/未匹配） 0：匹配 1：未匹配")
    receiveGoodsTime: str = Field(description="入仓时间")
    picPath: str = Field(description="商品图片")
    mainOuterId: str = Field(description="主商家编码")
    itemRealQty: int = Field(description="实退数量")
    price: float = Field(description="销售价（单位：元）")
    outerId: str = Field(description="商家编码")
    payment: str = Field(description="实付金额（单位：元）")
    suite: int = Field(description="是否是套件商品：0=普通商品，1=套件")
    suiteSingle: int = Field(description="是否是套件单品：0=套件本身，1=套件下单品")
    combineId: int = Field(description="套件组id,同一套件下套件本身和套件下单品相同")
    rawRefundMoney: int = Field(description="平台实退金额（单位：分）")
    refundableMoney: int = Field(description="商品维度系统应退金额（单位：元）")
    itemSnapshotId: int = Field(description="商品id")
    sysItemId: int = Field(description="系统主商品ID")
    sysSkuId: int = Field(description="系统商品规格skuID")
    isGift: int = Field(description="是否为赠品 0:否 1:是")
    numIid: str = Field(description="平台商品id")
    skuId: str = Field(description="平台skuId")

    receive_goods_time: str = Field(description="入仓时间（格式化后）")

    @root_validator
    def format_receive_goods_time(cls, values):
        values["receive_goods_time"] = format_timestamp(values.get("receiveGoodsTime"))
        return values


@make_fields_optional
class AfterSaleDetail(BaseModel):
    id: int  # 售后单号
    tid: str  # 订单号
    sid: str  # 子订单号
    created: str = Field(description="生成时间，时间戳格式：1640966400000")
    afterSaleType: int = Field(
        description="售后类型 0:其他，1：退款；2：退货；3：补发；4：换货 5:发货前的退款 6:补款 7:拒收退货 8：档口退货 9:维修"
    )
    buyerName: str = Field(description="买家姓名")
    buyerPhone: str = Field(description="买家手机号")
    wangwangNum: str = Field(description="买家旺旺号")
    goodStatus: int = Field(description="货物状态(1:买家未收到货;2:买家已收到货;3:买家已退货;4:卖家已收到退货)")
    status: int = Field(description="工单状态 2:未解决 9:已解决 10:已作废 11:已合并 12:解决中")
    rawRefundMoney: str = Field(description="平台实退金额（单位：元）")
    refundMoney: float = Field(description="系统实退金额（单位：元）")
    refundPostFee: str = Field(description="应退运费")
    reason: str = Field(
        description="售后原因 (1:退运费 2:收到商品破损 3:商品错发/漏发 4:商品需要维修 5:发票问题 6:收到商品与描述不符 7:我不想要了 8:商品质量问题 9:未按约定时间发货)"
    )  # noqa
    textReason: str = Field(description="原订单平台备注")
    refundWarehouseId: int = Field(description="退货仓库ID")
    refundWarehouseName: str = Field(description="退货仓库名称")
    refundExpressCompany: str = Field(description="退回快递公司")
    refundExpressId: str = Field(description="退回快递单号")
    remark: str = Field(description="工单备注")
    dealResult: int = Field(description="处理结果")
    platformStatus: int = Field(description="平台状态")
    refundRemindTimeout: str = Field(description="超时日期")
    shortId: int = Field(description="售后工单短号")
    storageProgress: int = Field(description="退货进度")
    shopName: str = Field(description="店铺名称")
    source: str = Field(description="工单来源具体平台，例如 tb(淘宝或者天猫平台),jd(京东平台),pdd(拼多多)等")
    finished: str = Field(description="完成时间")
    userId: int = Field(description="店铺ID")
    reissueSid: int = Field(description="补发或换货时生成的trade(订单)的sid")
    explains: str = Field(description="售后说明")
    refundWarehouseCode: str = Field(description="退货仓库编码")
    refundableMoney: int = Field(description="系统应退金额（单位：元）")
    modified: int = Field(description="修改时间")
    applyDate: int = Field(description="申请时间")
    sourceId: int = Field(description="分销商id")
    sourceName: str = Field(description="分销商名称")
    receiveGoodsTime: int = Field(description="退货到货时间")
    advanceStatus: int = Field(
        description="退款状态 （未申请:-1;平台未申请:0;退款先行垫付申请中:1;垫付完成:2;卖家拒绝收货:3;垫付关闭:4;垫付分账成功:5）"
    )  # noqa
    advanceStatusText: str = Field(description="退款状态名称")
    platformId: str = Field(description="外部平台售后单号")
    tradeSysMemo: str = Field(description="客服备注")
    handlerStatus: str = Field(description="自动化售后处理状态（待处理:-1;处理中:0;处理成功:1;处理失败:2）")
    handlerStatusText: str = Field(description="自动化售后处理状态文本")
    tradeWarehouseName: str = Field(description="原订单发货仓库名称")
    tradeWarehouseId: int = Field(description="原订单发货仓库ID")
    tradeWarehouseCode: str = Field(description="原订单发货仓库编码")
    onlineStatus: int = Field(
        description="线上状态(空:1;待卖家同意:2;待买家退货:3;待卖家确认收货:4;卖家拒绝退款:5;退款关闭:6;退款成功:7;待发出换货商品:8;待买家收货:9;换货关闭:10;换货成功:11)"
    )  # noqa
    onlineStatusText: str = Field(description="线上状态对应文案")

    # 自主生成的字段
    good_status_zh: str = Field(description="货物状态文本")
    after_sale_type_zh: str = Field(description="售后类型文本")
    status_zh: str = Field(description="工单状态")

    created_time: str = Field(description="生成时间（格式化后）")
    modified_time: str = Field(description="修改时间（格式化后）")
    finished_time: str = Field(description="完成时间（格式化后）")
    apply_date_time: str = Field(description="申请时间（格式化后）")
    receive_goods_time: str = Field(description="退货到货时间（格式化后）")
    refund_remind_timeout_time: str = Field(description="超时日期（格式化后）")

    @root_validator
    def format_timestamp(cls, values):
        values["created_time"] = format_timestamp(values.get("created"))
        values["modified_time"] = format_timestamp(values.get("modified"))
        values["finished_time"] = format_timestamp(values.get("finished"))
        values["apply_date_time"] = format_timestamp(values.get("applyDate"))
        values["receive_goods_time"] = format_timestamp(values.get("receiveGoodsTime"))
        values["refund_remind_timeout_time"] = format_timestamp(values.get("refundRemindTimeout"))
        return values

    @root_validator
    def generate_good_status_zh(cls, values):
        good_status = values.get("goodStatus")
        match good_status:
            case 0:
                values["good_status_zh"] = "空"
            case 1:
                values["good_status_zh"] = "买家未收到货"
            case 2:
                values["good_status_zh"] = "买家已收到货"
            case 3:
                values["good_status_zh"] = "买家已退货"
            case 4:
                values["good_status_zh"] = "卖家已收到退货"
            case _:
                values["good_status_zh"] = "未知"
        return values

    @root_validator
    def generate_after_sale_type_zh(cls, values):
        after_sale_type = values.get("afterSaleType")
        match after_sale_type:
            case 0:
                values["after_sale_type_zh"] = "其他"
            case 1:
                values["after_sale_type_zh"] = "退款"
            case 2:
                values["after_sale_type_zh"] = "退货"
            case 3:
                values["after_sale_type_zh"] = "补发"
            case 4:
                values["after_sale_type_zh"] = "换货"
            case 5:
                values["after_sale_type_zh"] = "发货前的退款"
            case 6:
                values["after_sale_type_zh"] = "补款"
            case 7:
                values["after_sale_type_zh"] = "拒收退货"
            case 8:
                values["after_sale_type_zh"] = "档口退货"
            case 9:
                values["after_sale_type_zh"] = "维修"
            case _:
                values["after_sale_type_zh"] = "未知"
        return values

    @root_validator
    def generate_status_zh(cls, values):
        status = values.get("status")
        match status:
            case 2:
                values["status_zh"] = "未解决"
            case 9:
                values["status_zh"] = "已解决"
            case 10:
                values["status_zh"] = "已作废"
            case 11:
                values["status_zh"] = "已合并"
            case 12:
                values["status_zh"] = "解决中"
            case _:
                values["status_zh"] = "未知"
        return values


@make_fields_optional
class AfterSaleList(BaseModel):
    list: List[AfterSaleDetail]
    total: int


class Supplier(BaseModel):
    supplierName: str
    supplierId: int
    sysSkuId: int


@omit_none_fields_before_validate("suppliers")
class SupplierResp(BaseModel):
    suppliers: List[Supplier] = []


class KuaimaiSupplierOutput(SupplierResp):

    # 下面这些为了方便作为object输出
    supplierName: str
    supplierId: int
    sysSkuId: int


class TradeAction(BaseModel):
    operateTime: int
    action: str
    operator: str
    content: str
    sid: int


class TradeActionResp(BaseModel):
    traceId: str
    list: List[TradeAction]
    success: bool


class GetShopsResponse(BaseModel):
    class KuaiMaiShop(BaseModel):
        user_id: int | None = Field(alias="userId")
        title: str | None
        shop_title: str | None = Field(alias="shortTitle")
        source: str | None
        state: int | None
        active: int | None
        nick: str | None
        shop_id: int | None = Field(alias="shopId")

    shop_list: list[KuaiMaiShop] = Field(alias="list")


class RefreshTokenResponse(BaseModel):
    class Session(BaseModel):
        expires_in: int = Field(alias="expiresIn")

    session: Session
