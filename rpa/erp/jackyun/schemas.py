import json
from typing import Any, Dict
from typing import List
from typing import Optional

from loguru import logger
from pydantic import BaseModel
from pydantic import Field
from pydantic import root_validator

from robot_processor.utils import make_fields_optional


@make_fields_optional
class GoodsDetailItem(BaseModel):
    barcode: str
    customerPrice: str
    goodsName: str
    goodsNo: str
    goodsPlatDiscountFee: str
    isFit: str
    isGift: str
    sellCount: str
    sellPrice: str
    divideSellTotal: str
    shareOrderDiscountFee: str
    shareOrderPlatDiscountFee: str
    sourceSubtradeNo: str
    specId: str
    specName: str
    subTradeId: str
    subTradeNo: str
    taxFee: str
    tradeGoodsNo: str
    platSkuId: str
    platGoodsId: str
    platCode: str

    supplier_name: str  # 清瑶定制


@make_fields_optional
class JackyunOrder(BaseModel):
    flagIds: str
    flagNames: str
    goodsDetail: List[GoodsDetailItem]
    isDelete: str
    logisticName: str
    mainPostid: str
    sellerMemo: str
    shopCode: str
    shopId: int
    shopName: str
    shopTypeCode: str
    tradeId: str
    online_trade_no: str = Field(alias="onlineTradeNo")
    tradeNo: str
    tradeType: str
    warehouseCode: str
    warehouseName: str
    sourceAfterNo: str
    payTime: str
    tradeStatus: int

    readable_trade_status: str  # 非API提供

    @root_validator(pre=True)
    def generate_all_readable_str(cls, values):
        status = values.get("tradeStatus")
        status_map = {
            1010: "待审核",
            1020: "审核中",
            1030: "预售",
            1050: "待复核",
            2000: "备货等待",
            2010: "备货等待等补货",
            2020: "服务等待",
            2030: "备货等待等生产",
            2040: "采购等待",
            3010: "虚拟发货",
            4110: "待发货待递交",
            4111: "待发货递交中",
            4112: "待发货已递交",
            4113: "待发货-递交失败",
            4121: "待发货-取消中",
            4122: "待发货已取消",
            4123: "待发货取消失败",
            4130: "待发货部分发货",
            4040: "代销发货待递交",
            4041: "代销发货已递交",
            5010: "已取消",
            5020: "已取消被合并",
            5030: "已取消被拆分",
            6000: "已取消被拆分",
            9090: "已完成",
        }
        values["readable_trade_status"] = status_map.get(status) or ""
        return values


class OrderListResp(BaseModel):
    @make_fields_optional
    class Response(BaseModel):
        @make_fields_optional
        class JackyunData(BaseModel):
            trades: List[JackyunOrder]

        jackyunCode: str
        jackyunData: JackyunData
        jackyunFlag: str
        jackyunMessage: str

    response: Response


class GoodsBatchInfoListItem(BaseModel):
    batchNo: str
    batchAmount: int
    productionDate: str
    expirationDate: str


class ReturnChangeGoodsDetailItem(BaseModel):
    tradeAfterId: str
    subTradeId: str
    goodsId: str
    goodsNo: str
    goodsName: str
    specId: str
    specName: str
    unit: str
    price: float
    sellCount: int
    reasonDesc: str
    returnCount: int
    defectiveAmount: int
    qcAmount: int
    returnFee: float
    sourceFitGoodsNo: str
    sendCount: int
    claimCount: int
    returnDiscounts: int
    sendDiscounts: int
    sendFee: int
    shouldReturnFee: float
    remark: str
    goodsAttribute: int
    gmtModified: str
    gmtCreate: str
    barcode: str
    sellTotal: int
    deliveryCount: int
    isFit: int
    isGift: int
    shareReturnFee: float
    shareSendFee: int
    shareShouldReturnFee: float
    goodsBatchInfoList: List[GoodsBatchInfoListItem]
    customColumnInfo: Any
    sourceSubtradeNo: str
    brandName: str
    goodsSerial: str
    platGoodsId: str


class ReturnChangePay(BaseModel):
    tradeAfterId: str
    returnAccounts: float
    sendAccounts: int
    receivePostFee: int
    returnTotal: float
    settlementCurrency: str
    discountFee: int
    payAccount: str
    customerBankName: str
    payStatus: int
    payAccounts: int
    payTime: str
    payee: str
    customerAccountType: int
    customerBankAccount: str
    resendType: int
    settlementType: str
    refundType: str
    refundTypeCode: int
    settlementPerson: str
    settlementPersonId: str
    settlementString: str
    gmtCreate: str
    gmtModified: str
    settlementCurrencyCode: str
    payAccountId: str
    paymentNo: str


class ReturnChangeReceiver(BaseModel):
    tradeAfterId: str
    customerId: str
    customerName: str
    customerAccount: str
    customerCode: str
    zip: str
    country: str
    state: str
    city: str
    district: str
    town: str
    gmtModified: str
    gmtCreate: str


@make_fields_optional
class ReturnChangeListItem(BaseModel):
    tradeAfterId: str
    returnChangeNo: str
    deliveryCompleteRemark: str
    tradeAfterFrom: int
    tradeAfterStatus: str
    receiveStatus: int
    receiveStatusExplain: str
    tradeId: str
    tradeNo: str
    sourceTradeNo: str
    sourceTradeAfterNo: str
    warehouseId: str
    warehouseCode: str
    warehouseName: str
    logisticName: str
    sourceLogisticId: str
    sourceLogisticName: str
    sendLogisticId: str
    sendLogisticName: str
    returnLogisticType: str
    returnLogisticTypeExplain: str
    sendWarehouseId: str
    sendWarehouseName: str
    mainPostid: str
    shopId: str
    shopName: str
    shopCode: str
    companyId: str
    companyName: str
    sourceShopId: str
    sourceShopName: str
    reasonDesc: str
    cancelReasonDesc: str
    rejectReasonDesc: str
    probleamDesc: str
    customerRemark: str
    serviceRemark: str
    appendMemo: str
    auditTime: str
    deliveryTime: str
    applyTime: str
    consignTime: str
    goodslist: str
    flagIds: str
    flagNames: str
    registerId: str
    registrant: str
    responsiblePersonCode: str
    responsiblePersonDesc: str
    stockInNo: str
    deliveryNo: str
    deliveryPerson: str
    auditorId: str
    auditor: str
    sourceWarehouseId: str
    sourceWarehouseName: str
    tradeOrderSummary: str
    isFreeze: int
    freezeReason: str
    gmtCreate: str
    gmtModified: str
    sourceTradeStatus: int
    returnChangeGoodsDetail: List[ReturnChangeGoodsDetailItem]
    returnChangePay: ReturnChangePay
    returnChangeReceiver: ReturnChangeReceiver
    delete: int
    completeTime: str
    sourceMainPostid: str

    logisticId: str


class ReturnChangeListResp(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        class Data(BaseModel):
            returnChangeList: List[ReturnChangeListItem]
            totalResults: int

        data: Data

    code: int
    developerInfo: str
    msg: str
    result: Result
    subCode: str


class GoodsBatch(BaseModel):
    batchNo: Optional[str]
    batchAmount: Optional[int]
    productionDate: Optional[str]
    expirationDate: Optional[str]


class ReturnChangeDetail(BaseModel):
    price: float
    barcode: Optional[str]
    unit: Optional[str]
    specName: Optional[str]
    sendDiscounts: Optional[str]
    remark: Optional[str]
    goodsBatch: Optional[GoodsBatch]
    isGift: Optional[str]
    sendFee: Optional[int]
    goodsNo: Optional[str]
    returnDiscounts: Optional[str]
    returnCount: Optional[float]
    sendCount: Optional[float]
    returnFee: Optional[int]
    isFit: Optional[str]
    reasonDesc: str


class ReturnChange(BaseModel):
    discountFee: Optional[str]
    returnPlatDiscount: Optional[int]
    mainPostid: Optional[str]
    flagNames: Optional[str]
    sendOnly: Optional[str]
    returnChangeDetails: List[ReturnChangeDetail]
    payee: Optional[str]
    sendTaxFee: Optional[int]
    resendType: str
    customerBankAccount: Optional[str]
    tradeNo: Optional[str]
    customerAccountType: Optional[str]
    sendWarehouseCode: Optional[str]
    customerName: Optional[str]
    returnChangeNo: Optional[str]
    sendPlatDiscount: Optional[int]
    settlementCurrency: Optional[str]
    sendDiscounts: Optional[int]
    returnTotal: Optional[str]
    sellerMemo: Optional[str]
    registrant: Optional[str]
    platDiscount: Optional[int]
    customerAccount: Optional[str]
    buyerMemo: Optional[str]
    sendWarehouseName: Optional[str]
    shopName: str
    extraMainPostid: Optional[str]
    warehouseName: Optional[str]
    warehouseCode: Optional[str]
    refundTypeCode: str
    returnTaxFee: Optional[int]
    taxFee: Optional[int]
    sendShopName: Optional[str]
    receivedPostFee: Optional[str]
    settlementType: str
    returnDiscounts: Optional[int]
    address: Optional[str]
    onlineTradeNo: Optional[str]
    sendLogisticName: Optional[str]
    receiverName: Optional[str]
    mobile: Optional[str]
    settlementCurrencyCode: Optional[str]
    logisticName: Optional[str]
    customerBankName: Optional[str]
    reasonDesc: str
    probleamDesc: Optional[str]
    phone: Optional[str]
    country: Optional[str]
    state: Optional[str]
    city: Optional[str]
    district: Optional[str]
    town: Optional[str]


class ReturnChangeUpdateDetail(BaseModel):
    exchangeNo: str
    logisticName: str
    mainPostid: str


class ReturnChangeUpdateReq(BaseModel):
    items: List[ReturnChangeUpdateDetail]


@make_fields_optional
class ReturnChangeUpdateResp(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        data: Dict
    code: int
    msg: str
    result: Result


class GetCustomColumnConfigInfoReq(BaseModel):
    columnIdList: Optional[List[int]] = None
    columnBusinessTypeList: Optional[List[int]] = None
    bEnable: bool


@make_fields_optional
class GetCustomColumnConfigInfoResp(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        @make_fields_optional
        class Data(BaseModel):
            @make_fields_optional
            class Item(BaseModel):
                labelName: str
                remark: str
                bEnable: str
                columnId: str
                bRequired: str
                labelType: str
                labelColumn: str
                labelOptions: str
                languageType: str
            items: List[Item]
        data: Data
        contextId: str
    code: int
    msg: str
    subCode: str
    result: Result


class AftersaleUploadReq(BaseModel):
    returnChange: ReturnChange


@make_fields_optional
class AftersaleUploadResp(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        @make_fields_optional
        class Data(BaseModel):
            class ReturnChange(BaseModel):
                returnChangeNo: str

            returnChange: ReturnChange

        data: Data

    code: int
    msg: str
    result: Result


@make_fields_optional
class JackyunGoods(BaseModel):
    goodsNo: str
    goodsName: str
    skuName: str
    isPackageGood: int
    skuCode: str
    sku_img_url: str = Field(alias="skuImgUrl", description="规格图片")
    retail_price: float = Field(alias="retailPrice", description="固定成本价")

    def get_sku_img_url(self) -> str:
        if not self.sku_img_url:
            return ""
        try:
            sku_img_url_dict: dict[str, str] = json.loads(self.sku_img_url)
            if len(sku_img_url_dict.values()) == 0:
                return ""
            if img_url := sku_img_url_dict.get("pic50x50"):
                return str(img_url)
            else:
                return str(list(sku_img_url_dict.values())[0])
        except Exception as e:
            logger.exception(e)
            return ""


@make_fields_optional
class GoodsListResp(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        @make_fields_optional
        class Data(BaseModel):
            goods: List[JackyunGoods]

        data: Data

    code: int
    msg: str
    result: Result


@make_fields_optional
class QueryChannelResp(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        @make_fields_optional
        class Data(BaseModel):
            @make_fields_optional
            class SalesChannelInfo(BaseModel):
                channelId: int
                channelCode: str
                channelName: str
                channelType: str
                onlinePlatTypeCode: str
                onlinePlatTypeName: str

            salesChannelInfo: list[SalesChannelInfo]

        data: Data

    code: int
    msg: str
    result: Result


@make_fields_optional
class CombineDetail(BaseModel):
    @make_fields_optional
    class GoodsPackageDetail(BaseModel):
        package_sku_id: int = Field(alias="packageSkuId", description="组合装规格ID")
        goods_id: int = Field(alias="goodsId", description="子件货品ID")
        sku_id: int = Field(alias="skuId", description="子件规格ID")
        goods_amount: int = Field(alias="goodsAmount", description="数量")
        sku_barcode: str = Field(alias="skuBarcode", description="条码")
        goods_name: str = Field(alias="goodsName", description="货品名称")
        goods_no: str = Field(alias="goodsNo", description="货品编码")
        sku_properites_name: str = Field(alias="skuProperitesName", description="规格名称")

    sku_id: int = Field(alias="skuId", description="规格ID")
    goods_name: str = Field(alias="goodsName", description="货品名称")
    goods_no: str = Field(alias="goodsNo", description="货品编码")
    sku_barcode: str = Field(alias="skuBarcode", description="条码")
    retail_price: str = Field(alias="retailPrice", description="固定成本价")
    goods_package_detail: list[GoodsPackageDetail] = Field(alias="goodsPackageDetail", description="子件信息")


@make_fields_optional
class QueryCombineResp(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        data: list[CombineDetail]

    result: Result


@make_fields_optional
class JackyunShop(BaseModel):
    channelId: int = Field(description="销售渠道id")
    channelCode: str = Field(description="渠道编码")
    channelName: str = Field(description="销售渠道名称")
    channelType: str = Field(description="渠道类型（0：分销办公室； 1：直营网店；" "2：直营门店；3：销售办公室；4：货主虚拟店；" "5：分销虚拟店；6：加盟门店；7：内部交易渠道）")
    onlinePlatTypeCode: str = Field(description="店铺平台编码")
    onlinePlatTypeName: str = Field(description="店铺平台名称")
    channelDepartId: int = Field(description="负责部门id")
    channelDepartName: str = Field(description="负责部门名称")
    linkMan: str = Field(description="联系人")
    linkTel: str = Field(description="联系电话")
    officeAddress: str = Field(description="办公地址")
    groupId: int = Field(description="销售商品分组id")
    email: str = Field(description="邮箱")
    companyId: int = Field(description="公司id")
    postcode: str = Field(description="邮编")
    companyName: str = Field(description="公司名称")
    countryId: int = Field(description="国家id")
    countryName: str = Field(description="国家")
    provinceId: int = Field(description="省id")
    provinceName: str = Field(description="省")
    cityId: int = Field(description="市id")
    cityName: str = Field(description="市")
    townName: str = Field(description="镇，区")
    streetName: str = Field(description="街道")
    townId: int = Field(description="区id")
    streetId: int = Field(description="街道id")
    memo: str = Field(description="备注")
    warehouseCode: str = Field(description="默认仓库编码")
    warehouseName: str = Field(description="默认仓库名称")
    chargeType: str = Field(description="结算方式（1：担保交易；2：银行收款；3：现金收款；4：货到付款；" "5：欠款计应收；6：客户预存款；7：多种结算；8：退换货冲抵；9：电子钱包）")
    cateId: int = Field(description="渠道分类id")
    cateName: str = Field(description="渠道分类")
    departCode: str = Field(description="部门编码")
    companyCode: str = Field(description="公司编码")


@make_fields_optional
class QueryShopResp(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        data: list[JackyunShop] = Field(description="营销渠道信息")

    result: Result = Field(description="返回结果")
    code: str = Field(description="返回码(200为成功，0为失败)")
    msg: str = Field(description="返回消息")
    subCode: str = Field(description="子级编码")


class ReturnChangeGoodItemCustomFields(BaseModel):
    column1: Optional[str] = None
    column2: Optional[str] = None
    column3: Optional[str] = None
    column4: Optional[str] = None
    column5: Optional[str] = None
    column6: Optional[str] = None
    column7: Optional[str] = None
    column8: Optional[str] = None
    column9: Optional[str] = None
    column10: Optional[str] = None
    subTradeId: Optional[str] = None


class ReturnChangeUpdateCustomFieldsDetail(BaseModel):
    exchangeNo: str
    column1: Optional[str] = None
    column2: Optional[str] = None
    column3: Optional[str] = None
    column4: Optional[str] = None
    column5: Optional[str] = None
    column6: Optional[str] = None
    column7: Optional[str] = None
    column8: Optional[str] = None
    column9: Optional[str] = None
    column10: Optional[str] = None
    goodItems: Optional[List[ReturnChangeGoodItemCustomFields]] = None


class ReturnChangeUpdateCustomFieldsReq(BaseModel):
    items: List[ReturnChangeUpdateCustomFieldsDetail]


@make_fields_optional
class ReturnChangeUpdateCustomFieldsResp(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        data: Any
    code: int
    msg: str
    result: Result


class OrderUpdateLogisticsInfoReq(BaseModel):
    erporderNo: str
    logisticCode: str
    logisticNo: str
    sellerMemo: Optional[str] = None


@make_fields_optional
class OrderUpdateLogisticsInfoResp(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        data: Any
    code: int
    msg: str
    result: Result


@make_fields_optional
class LogisticsInfo(BaseModel):
    id: int
    logisticCode: str
    logisticName: str


@make_fields_optional
class LogisticsGetResp(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        @make_fields_optional
        class Data(BaseModel):
            logisticInfo: List[LogisticsInfo]
        data: Data
    code: int
    msg: str
    result: Result
