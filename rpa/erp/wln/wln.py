from typing import TYPE_CHECKING

import arrow
from loguru import logger
from robot_types import model

import rpa.erp.wln.schemas as schemas
from robot_processor.enums import ErpType
from robot_processor.ext import cache
from robot_processor.shop.models import ErpInfo
from robot_processor.utils import req_to_curl
from rpa.erp.wln.hopen import SDK


class WlnClient:
    sdk: SDK
    # 这是一个万里牛erp接口请求的 wrapper 类，如果后续接奇门，需要在这里提供一个 qm_sdk 的实现，而不是新建一个 WlnQmClient 类
    # qm_sdk: QmSDK

    def __init__(self, credential: model.credential.WLN):
        if credential.app_key == "3623528221":
            self.sdk = SDK("http://8.134.33.183:65118/api", credential.app_key,
                           credential.app_secret)
        else:
            self.sdk = SDK("https://open-api.hupun.com/api", credential.app_key,
                           credential.app_secret)

    @classmethod
    def from_erp_info(cls, erp_info: ErpInfo):
        return cls(model.credential.WLN(erp_info.meta["app_key"], erp_info.meta["app_secret"]))

    @classmethod
    def init_by_sid(cls, sid):
        erp_info = ErpInfo.get_by_sid(sid, ErpType.WANLINIU)
        assert erp_info, f"店铺 {sid} 无万里牛授权信息"
        return cls(model.credential.WLN(erp_info.meta["app_key"], erp_info.meta["app_secret"]))

    if TYPE_CHECKING:
        from robot_processor.shop.auth_manager import Credentials

    @classmethod
    def init_by_credentials(cls, credentials: "Credentials"):
        app_key = credentials.auth_extra_data.get("app_key")
        app_secret = credentials.auth_extra_data.get("app_secret")
        assert app_key, "万里牛授权信息缺失 app_key"
        assert app_secret, "万里牛授权信息缺失 app_secret"
        return cls(model.credential.WLN(app_key=app_key, app_secret=app_secret))

    def send(self, method, data: dict) -> dict:
        resp = self.sdk.request(method, data)
        if resp.status_code != 200:
            logger.warning(f"curl: \n {req_to_curl(resp)}")
            raise Exception(f"万里牛请求失败: {resp.json()['message']}")
        else:
            return resp.json()

    def get_shops(self):
        shops: list[schemas.GetShopsResponse.WlnShop] = []
        page = 1
        while True:
            resp = self.send("erp/base/shop/page/get", {"limit": 50, "page": page})
            parsed_resp = schemas.GetShopsResponse.parse_obj(resp)
            if len(parsed_resp.data) == 0:
                break
            shops.extend(parsed_resp.data)
            page += 1
        return shops

    def check_grant_is_valid(self) -> bool:
        try:
            resp = self.send("erp/opentrade/list/trades", {"limit": 10, "page": 1, "bill_code": "1"})
            if resp.get("code") == 0:
                return True
            else:
                return False
        except Exception as e:
            logger.error("万里牛授权不可用 {}", e)
            return False

    def query_trades(self, tids: str | list[str], page: int = 1, limit: int = 200, **kwargs) -> schemas.TradeResp:
        """查询订单"""
        bill_code = ",".join(tids) if isinstance(tids, list) else tids
        data = {"bill_code": bill_code, "page": page, "limit": limit, "query_extend": {"query_relation_trade": "true"}}
        data.update(kwargs)
        resp = self.send("erp/opentrade/list/trades", data)
        return schemas.TradeResp.parse_obj(resp)

    def query_items(self, spec_code=None, item_code=None, bar_code=None):
        data = {"page": 1, "limit": 10}
        if not any([spec_code, item_code, bar_code]):
            data["modify_time"] = arrow.now().shift(years=-20).int_timestamp * 1000
        else:
            if spec_code:
                data["spec_code"] = spec_code
            if item_code:
                data["goods_code"] = item_code
            if bar_code:
                data["barcode"] = bar_code

        resp = self.send("erp/goods/spec/open/query/goodswithspeclist", data)
        return schemas.QueryGoodsResp.parse_obj(resp)

    def query_sent_trades(self, send_goods_time: str, end_time: str, page: int, limit: int):
        data = {
            "send_goods_time": send_goods_time,
            "end_time": end_time,
            "page": page,
            "limit": limit,
            "query_extend": {"query_split_only": "true", "query_oln_shop_id": 1},
        }
        resp = self.send("erp/opentrade/list/trades", data)
        return schemas.TradeResp.parse_obj(resp)

    def modify_trade_remark_with_req(self, req: schemas.WlnModifyTradeRemarkReq):
        """万里牛修改订单备注"""
        resp = self.send("erp/opentrade/modify/remark", req.dict(exclude_none=True))
        return schemas.WlnModifyTradeRemarkResp.parse_obj(resp)

    def aftersale_upload_with_req(self, req: schemas.AftersaleReq):
        """售后单上传"""
        resp = self.send("erp/open/return/order/exchange/save", req.dict(exclude_unset=True, exclude_none=True))
        return schemas.WlnAfterSaleResp.parse_obj(resp)

    def get_after_sale_return_order(self, bill_code: str):
        data = {"page": 1, "limit": 200, "bill_code": bill_code}
        resp = self.send("erp/open/return/order/list", data)
        return schemas.WlnAfterSaleReturnOrderResp.parse_obj(resp)

    def get_all_storage(self):
        """查询所有仓库"""
        data = {"page_no": 1, "page_size": 100}
        storage_list: list[schemas.Storage] = []
        while True:
            resp = schemas.StorageResp.parse_obj(self.send("erp/base/storage/query", data))
            if resp.data:
                storage_list.extend(resp.data)
                data["page_no"] += 1
            else:
                break
        return storage_list

    def get_storage_mapping_with_cache(self):
        cache_key = f"wln_storage_{self.sdk._app}"
        if storage_mapping := cache.get(cache_key):
            return storage_mapping
        storage_mapping = {
            storage.storage_name: storage.storage_code for storage in self.get_all_storage() if storage.status == 1
        }
        cache.set(cache_key, storage_mapping, timeout=3600 * 24)
        return storage_mapping

    def get_storage_name_by_code_in_cache(self, storage_name: str):
        """查询缓存仓库名称"""
        return self.get_storage_mapping_with_cache().get(storage_name)
