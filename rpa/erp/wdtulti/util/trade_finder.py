from enum import StrEnum
from dataclasses import dataclass, field

from loguru import logger
from result import Ok, Err

from robot_processor.job.erp_trade_handler.exceptions import ErpFilteredError
from robot_processor.utils import unwrap_optional
from rpa.erp.wdtulti import schemas, WdtUltiQM


class TradeFromType(StrEnum):
    ALL = "2"
    REISSUE = "1"
    NORMAL = "0"


@dataclass
class TradeFinder:
    """使用策略模式来适配不同的入参"""

    @dataclass
    class Filter:
        trade_from_type: TradeFromType = TradeFromType.ALL
        src_tid_list: list[str] | None = None
        src_oid_list: list[str] | None = None



    @dataclass
    class Query:
        logistics_no_list: list[str] | None = None
        trade_no: str | None = None
        src_tid_list: list[str] | None = None

        @property
        def src_tid_list_as_query(self):
            if self.src_tid_list is None:
                return None
            else:
                return ",".join(self.src_tid_list)

    @dataclass
    class Config:
        check_history: bool = True  # 查询不到订单时，到归档库查询

    qm_sdk: WdtUltiQM
    query: Query
    filter_: Filter = field(default_factory=Filter)
    config: Config = field(default_factory=Config)
    ignore_not_found_error: bool = False

    def fetch_with_logistics_no(self):
        try:
            trade_no_map: dict[str, schemas.WdtultiOrderModel] = dict()
            if not (logistics_no_list := self.query.logistics_no_list):
                logger.warning(f"无效的 logistics_no_list {self.query}")
                return Ok(list(trade_no_map.values()))
            for logistics_no in logistics_no_list:
                params = schemas.TradeQueryParams(
                    trade_no=self.query.trade_no,
                    logistics_no=logistics_no,
                    src_tid=self.query.src_tid_list_as_query,
                )
                pager = schemas.Pager(100, 1, True)
                response: schemas.WdtultiOrderResp | None = None
                while self.has_next(response, pager):
                    response = self.qm_sdk.trade_query(params, pager)
                    pager = pager.get_next()
                    for order in (unwrap_optional(response).order or []):
                        trade_no_map[order.trade_no] = order
            return Ok(list(trade_no_map.values()))
        except Exception as e:
            return Err(e)

    def fetch_with_trade_no(self):
        try:
            orders: list[schemas.WdtultiOrderModel] = []
            if not (trade_no := self.query.trade_no):
                logger.warning(f"无效的 trade_no {self.query}")
                return Ok(orders)
            params = schemas.TradeQueryParams(trade_no=trade_no)
            pager = schemas.Pager(100, 1, True)
            response = self.qm_sdk.trade_query(params, pager)
            orders.extend(response.order or [])
            if len(orders) == 0 and self.config.check_history:
                response = self.qm_sdk.history_trade_query(params, pager)
                orders.extend(response.order or [])
            return Ok(orders)
        except Exception as e:
            return Err(e)

    def fetch_with_src_tid(self):
        try:
            trade_no_map: dict[str, schemas.WdtultiOrderModel] = dict()
            if not (src_tid := self.query.src_tid_list_as_query):
                logger.warning(f"无效的 src_tid {self.query}")
                return Ok(list(trade_no_map.values()))
            params = schemas.TradeQueryParams(src_tid=src_tid)
            pager = schemas.Pager(100, 1, True)
            response: schemas.WdtultiOrderResp | None = None
            while self.has_next(response, pager):
                response = self.qm_sdk.trade_query(params, pager)
                pager = pager.get_next()
                for order in (unwrap_optional(response).order or []):
                    trade_no_map[order.trade_no] = order
            if not trade_no_map and self.config.check_history:
                pager = schemas.Pager(100, 1, True)
                response = None
                while self.has_next(response, pager):
                    response = self.qm_sdk.history_trade_query(params, pager)
                    pager = pager.get_next()
                    for order in (unwrap_optional(response).order or []):
                        trade_no_map[order.trade_no] = order
            return Ok(list(trade_no_map.values()))
        except Exception as e:
            return Err(e)

    def do_filter(
        self, orders: list[schemas.WdtultiOrderModel]
    ) -> Ok[list[schemas.WdtultiOrderModel]] | Err[ErpFilteredError]:
        import math

        if len(orders) == 0:
            return Ok(orders)

        # 根据订单类型进行过滤
        if self.filter_.trade_from_type == TradeFromType.REISSUE:
            orders = [order for order in orders if order.trade_from == 6]
        elif self.filter_.trade_from_type == TradeFromType.NORMAL:
            orders = [order for order in orders if order.trade_from != 6]
        if len(orders) == 0:
            return Err(ErpFilteredError("没有符合筛选条件的订单 筛选条件:订单类型"))

        # 过滤订单详情
        if self.filter_.src_tid_list:
            for order in orders:
                detail_list: list[schemas.WdtultiOrderDetailModel] = []
                for detail in (order.detail_list or []):
                    if not detail.src_tid:
                        continue
                    if not any([detail.src_tid.startswith(src_tid) for src_tid in self.filter_.src_tid_list]):
                        continue
                    detail_list.append(detail)
                if len(detail_list) != len(order.detail_list or []):
                    order.detail_list = detail_list
                    order.paid = str(math.fsum([float(detail.paid) or 0.0 for detail in detail_list]))
            orders = [order for order in orders if order.detail_list]
            if len(orders) == 0:
                return Err(ErpFilteredError("没有符合筛选条件的订单 筛选条件:订单号"))

        if self.filter_.src_oid_list:
            for order in orders:
                detail_list = []
                for detail in (order.detail_list or []):
                    if detail.src_oid and detail.src_oid in self.filter_.src_oid_list:
                        detail_list.append(detail)
                if len(detail_list) != len(order.detail_list or []):
                    order.detail_list = detail_list
                    order.paid = str(math.fsum([float(detail.paid) or 0.0 for detail in detail_list]))
            orders = [order for order in orders if order.detail_list]
            if len(orders) == 0:
                return Err(ErpFilteredError("没有符合筛选条件的订单 筛选条件:子订单号"))

        return Ok(orders)

    @staticmethod
    def has_next(response: schemas.WdtultiOrderResp | None, pager: schemas.Pager):
        if response is None:
            return True
        return pager.get_page_no() * pager.get_page_size() < (response.total_count or 0)
