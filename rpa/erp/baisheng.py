""" 百胜E3erp"""
import datetime
import hashlib
import json
import time

from pydantic import BaseModel, Field
from dateutil.relativedelta import relativedelta
from loguru import logger
from result import Result, Err, Ok

from robot_processor.client_mixins import Session
from robot_processor.enums import ErpType
from robot_processor.shop.models import ErpInfo
from robot_processor.utils import combine_address_with_town, make_fields_optional
from robot_processor.shop.schema import BaiShengGrantMeta


@make_fields_optional
class BaiShengShop(BaseModel):
    id: str = Field(description="商店id")
    khdm: str = Field(description="商店代码")
    khmc: str = Field(description="商店名称")
    lxr: str = Field(description="联系人")
    email: str = Field(description="Email 邮箱")
    sj: str = Field(description="手机号")
    dz: str = Field(description="地址")
    fax: str = Field(description="传真")
    yb: str = Field(description="邮编")
    khh: str = Field(description="开户行")
    hm: str = Field(description="户名")
    yhzh: str = Field(description="银行账户")
    frdb: str = Field(description="法人代表")
    bz: str = Field(description="备注")
    sd_kehu_id: str = Field(description="客户id")
    sd_khdm: str = Field(description="客户代码")
    sd_khmc: str = Field(description="客户名称")
    qddm: str = Field(description="渠道编码")
    lbdm: str = Field(description="类别编码")
    qy_code: str = Field(description="区域编号")
    ckdm: str = Field(description="客户代码")
    sf_no: str = Field(description="操作用户编号")


@make_fields_optional
class GetShopResponse(BaseModel):
    @make_fields_optional
    class Result(BaseModel):
        @make_fields_optional
        class Page(BaseModel):
            totalResult: int
            pageSize: int
            pageNo: int
            pageTotal: int

        page: Page
        sdListGet: list[BaiShengShop]

    status: str
    message: str
    data: Result


class BaiShengOpenPlatformAPIClient:
    session = Session()

    def __init__(self, meta: str | dict):
        if isinstance(meta, str):
            self.meta: BaiShengGrantMeta = BaiShengGrantMeta.parse_raw(meta)
        else:
            self.meta = BaiShengGrantMeta.parse_obj(meta)
        self.app_key = self.meta.appKey
        self.app_secret = self.meta.appSecret
        self.erp_account = self.meta.sid
        self.endpoint = self.meta.baseUrl

    def send(self, api: str, data: dict) -> dict:
        data_json_string = json.dumps(data, indent=2, ensure_ascii=False)
        # params 拼接
        params = {
            "key": self.app_key,
            "requestTime": time.strftime("%Y%m%d%H%M%S", time.localtime()),
            "secret": self.app_secret,
            "version": "3.0",
            "serviceType": api,
            "data": data_json_string
        }
        # 生产签名
        params["sign"] = e3_sign(params)
        # 签名生成后移除 secret
        params.pop("secret")
        url = self.endpoint
        logger.info(f"api: {api}, data: {data}")
        resp = self.session.request("POST", url=url, params=params)
        resp_json = resp.json()
        return resp_json

    def get_shops(self) -> list[BaiShengShop]:
        method = "e3oms.base.sd.get"
        page_no: int = 1
        page_size: int = 20
        shops: list[BaiShengShop] = []
        while True:
            data = {"pageNo": page_no, "pageSize": page_size}
            resp = self.send(method, data)
            parsed_resp: GetShopResponse = GetShopResponse.parse_obj(resp)
            if parsed_resp.status != "api-success":
                break
            if page_no >= parsed_resp.data.page.pageTotal:
                break
            page_no = page_no + 1
            shops.extend(parsed_resp.data.sdListGet)
        return shops

    def check_grant_is_valid(self) -> bool:
        try:
            resp = self.send(
                "e3oms.order.detail.get",
                {
                    "order_sn": "1"
                }
            )
            if resp.get("status") == "api-success":
                return True
            else:
                return False
        except Exception as e:
            logger.error("百胜授权不可用 {}", e)
            return False


class BaiShengClient:
    session = Session()

    def __init__(self, sid=None, credential=None, erp_info=None):
        """
        Raises:
            AssertionError
        """
        if credential:
            erp_info = ErpInfo(meta=credential.dict())
        else:
            erp_info = erp_info or ErpInfo.get_by_sid(sid, ErpType.BAISHENG)
        assert erp_info, f"店铺 {sid} 无百胜授权信息"
        self.erp_info = erp_info

    @property
    def app_key(self):
        return self.erp_info.meta["appKey"]

    @property
    def app_secret(self):
        return self.erp_info.meta["appSecret"]

    @property
    def endpoint(self):
        return self.erp_info.meta["baseUrl"]

    @property
    def erp_account(self):
        return self.erp_info.meta["sid"]

    def get_return_list(self, refund_code: str | None = None,
                        return_order_sn: str | None = None,
                        relating_order_sn: str | None = None):
        return_list_api = "return.list.get"
        req_json = json.dumps({
            "refund_code": refund_code,
            "return_order_sn": return_order_sn,
            "relating_order_sn": relating_order_sn
        }, indent=2, ensure_ascii=False)
        return self.request_api(return_list_api, req_json, "get")

    def get_return_detail(self, return_shipping_sn: str):
        return_detail_api = "return.detail.get"
        req_json = json.dumps({
            "return_shipping_sn": return_shipping_sn
        }, indent=2, ensure_ascii=False)
        return self.request_api(return_detail_api, req_json, "get")


    def after_sale_upload(self, business_data: dict, sku_list: list) -> Result[None, str]:
        """获取订单列表 -> 填充goods -> 创建销售订单"""

        reissue_api = "reissue.copy"
        tid = business_data.get("tid")
        order_list_resp = self.get_order_list(tid)
        o_msg = {order_list_resp.text.encode('utf-8').decode('unicode_escape')}
        if not order_list_resp.ok:
            return Err(
                f"获取原订单 {tid} 失败 {order_list_resp.status_code}, {o_msg}")
        order_list_obj = order_list_resp.json()
        if order_list_obj.get("status") == "api-success" \
                and order_list_obj.get("message") == "success" \
                and order_list_obj.get("data").get("orderListGets"):
            # 获取订单详情
            order_list = order_list_obj.get("data").get("orderListGets")
            orders = [order for order in order_list
                      if order.get("is_copy") == "0"]
            order = orders[0] if orders else None
            if not order:
                return Err("未匹配到订单信息")

            reissue_order = {
                "order_sn": order["order_sn"],
                "copy_type": 3,
                "items": []
            }
            for sku in sku_list:
                reissue_order["items"].append(
                    {
                        "sku": sku.get("outer_sku_id"),
                        "goods_price": 0.0,
                        "shop_price": 0.0,
                        "goods_number": sku.get("qty"),
                        "is_gift": 0
                    }
                )
            reissue_order_json = json.dumps(reissue_order, indent=2,
                                            ensure_ascii=False)
            resp = self.request_api(reissue_api, reissue_order_json, "post")
            msg = {resp.text.encode('utf-8').decode('unicode_escape')}
            if not resp.ok:
                return Err(f"创建补发单失败 {resp.status_code}, {msg}")
            resp_obj = resp.json()
            logger.info(f"resp: {resp_obj}")
            if resp_obj.get("status") == "api-success" and resp_obj.get(
                    "message") == "success":
                return Ok(resp_obj.get("data").get("order_sn"))
            else:
                return Err(f"创建补发单失败 {resp.status_code}, {msg}")
        else:
            return Err(f"获取原订单失败 {order_list_resp.status_code}, {o_msg}")

    def get_order_list(self, tid):
        order_list_api = "order.list.get"
        start_modified = (
                datetime.datetime.now() - relativedelta(years=1)).strftime(
            "%Y-%m-%d %H:%M:%S")
        order_list_data = json.dumps({
            "startModified": start_modified,
            "endModified": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
            "deal_code": tid
        }, indent=2, ensure_ascii=False)
        # 获取原订单
        return self.request_api(order_list_api, order_list_data, "get")

    def get_order_list_by_order_sn(self, order_sn):
        order_list_api = "order.list.get"
        start_modified = (
                datetime.datetime.now() - relativedelta(years=1)).strftime(
            "%Y-%m-%d %H:%M:%S")
        order_list_data = json.dumps({
            "startModified": start_modified,
            "endModified": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
            "orderSn": order_sn
        }, indent=2, ensure_ascii=False)
        # 获取原订单
        return self.request_api(order_list_api, order_list_data, "get")

    def get_order_list_by_shipping_sn(self, shipping_sn):
        order_list_api = "order.list.get"
        start_modified = (
                datetime.datetime.now() - relativedelta(years=1)).strftime(
            "%Y-%m-%d %H:%M:%S")
        order_list_data = json.dumps({
            "startModified": start_modified,
            "endModified": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
            "shipping_sn": shipping_sn
        }, indent=2, ensure_ascii=False)
        # 获取原订单
        return self.request_api(order_list_api, order_list_data, "get")

    def order_qr(self, order_sn: str):
        order_qr_api = "order.qr"
        order_qr_json = json.dumps({"order_sn": order_sn},
                                   indent=2, ensure_ascii=False)
        return self.request_api(order_qr_api, order_qr_json, "get")

    def order_info_change(self, order_sn: str, seller_msg: str):
        order_info_change_api = "order.info.change"
        order_info_change_json = json.dumps({
            "order_sn": order_sn,
            "seller_msg": seller_msg
        }, indent=2, ensure_ascii=False)
        return self.request_api(order_info_change_api, order_info_change_json,
                                "post")

    def request_api(self, api, data, method):
        # params 拼接
        params = {
            'key': self.app_key,
            'requestTime': time.strftime("%Y%m%d%H%M%S", time.localtime()),
            'secret': self.app_secret,
            'version': '3.0',
            'serviceType': api,
            'data': data
        }
        # 生产签名
        params['sign'] = e3_sign(params)
        # 签名生成后移除 secret
        params.pop('secret')
        url = self.endpoint
        logger.info(f"api: {api}, data: {data}")
        return self.session.request(method.upper(), url=url, params=params)


def convert_order(new_tid, sd_code, sku_list, receiver, user_nick):
    new_sku_list = []
    for sku in sku_list:
        new_sku_list.append({
            "sku_sn": sku.get("outer_sku_id"),
            "goods_price": "0.00",
            "transaction_price": "00.00",
            "goods_number": sku.get("qty"),
            "is_gift": "1",
        })
    return {
        "total": len(new_sku_list),
        "data": [{
            "line": "1",
            "add_time": time.strftime("%Y-%m-%d %H:%M:%S",
                                      time.localtime()),
            "order_sn": new_tid,
            "sd_code": sd_code,
            "order_status": 1,
            "pay_status": 2,
            "consignee": receiver.get("name"),
            "province_name": receiver.get("state"),
            "city_name": receiver.get("city"),
            "district_name": receiver.get("zone"),
            "address": combine_address_with_town(
                f"{receiver.get('town') or ''}",
                f"{receiver['address']}"),
            "mobile": receiver.get("mobile"),
            "user_name": user_nick,
            "pay_time": time.strftime("%Y-%m-%d %H:%M:%S",
                                      time.localtime()),
            "pos_code": "",
            "pay_code": "alipay",
            "shipping_code": "",
            "shipping_fee": "0",
            "order_amount": "0.00",
            "payment": "0.00",
            "os_user_id": "",
            "is_copy": "1",
            "items": new_sku_list
        }]
    }


def e3_sign(parameters):
    sign_str = ""
    for key in parameters:
        val = str(parameters[key])
        sign_str += "{}={}".format(key, val)

        if key != next(reversed(parameters)):
            sign_str += '&'

    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    return sign
