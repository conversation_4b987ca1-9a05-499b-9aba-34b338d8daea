"""聚水潭 SDK

配置项：
    RPA_JST_PARENT_ID
    RPA_JST_PARENT_KEY
"""

import hashlib
import json
import re
import urllib
from typing import TYPE_CHECKING
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Type
from typing import TypeVar
from urllib.parse import urljoin

import arrow
from google.protobuf.json_format import MessageToDict
from leyan_proto.digismart.trade.dgt_trade_pb2 import DEFAULT_TRADE_STATUS
from leyan_proto.digismart.trade.dgt_trade_pb2 import TRADE_CLOSED
from leyan_proto.digismart.trade.dgt_trade_pb2 import TRADE_FINISHED
from leyan_proto.digismart.trade.dgt_trade_pb2 import WAIT_BUYER_CONFIRM_GOODS
from leyan_proto.digismart.trade.dgt_trade_pb2 import WAIT_SELLER_SEND_GOODS
from leyan_proto.digismart.trade.dgt_trade_pb2 import Order
from leyan_proto.digismart.trade.dgt_trade_pb2 import TradeInfo
from leyan_proto.trade.trade_pb2 import WAIT_BUYER_PAY
from loguru import logger
from pydantic import BaseModel
from pydantic.json import pydantic_encoder
from result import Err
from result import Ok
from result import Result
from tenacity import retry
from tenacity import retry_if_exception_type
from tenacity import stop_after_attempt
from tenacity import wait_fixed

from robot_processor.client import limiter
from robot_processor.client_mixins import Session
from robot_processor.enums import ErpType
from robot_processor.enums import QueryOrderRule
from robot_processor.enums import TradeStatus
from robot_processor.error.base import BizError
from robot_processor.error.client_request import JstRequestError
from robot_processor.error.client_request import JstRetryableRequestError
from robot_processor.ext import cache
from robot_processor.shop.models import ErpInfo
from robot_processor.utils import response_to_log
from rpa.conf import rpa_config as config
from rpa.erp.jst.schemas import AfterSaleConfirmResp
from rpa.erp.jst.schemas import AfterSaleResponseData
from rpa.erp.jst.schemas import AfterSaleSearchReq
from rpa.erp.jst.schemas import AfterSaleUploadResp
from rpa.erp.jst.schemas import InventoryQueryResp
from rpa.erp.jst.schemas import JstDistributorQueryResp
from rpa.erp.jst.schemas import JstOrderRemarkUploadResp
from rpa.erp.jst.schemas import LogisticsCompanyQueryResp
from rpa.erp.jst.schemas import MySupplierQueryResp
from rpa.erp.jst.schemas import NewSdkBaseResp
from rpa.erp.jst.schemas import OpenOrdersOutSimpleQuery
from rpa.erp.jst.schemas import OrderActionQueryResp
from rpa.erp.jst.schemas import OrdersModifyWmsUploadResp
from rpa.erp.jst.schemas import OrdersSingleQueryResp
from rpa.erp.jst.schemas import OrderUploadReq
from rpa.erp.jst.schemas import OrderUploadResp
from rpa.erp.jst.schemas import OutOrdersQueryResp
from rpa.erp.jst.schemas import QueryCombineSkuResp
from rpa.erp.jst.schemas import QuerySkuResp
from rpa.erp.jst.schemas import QuerySpuResp
from rpa.erp.jst.schemas import ShopsQueryResp
from rpa.erp.jst.schemas import SkuQueryResp
from rpa.erp.jst.schemas import SkuSupplierResp
from rpa.erp.jst.schemas import SupplierResp
from rpa.erp.jst.schemas import WaveQueryResp
from rpa.erp.jst.schemas import WmsPartnerQueryResp
from rpa.erp.jst.schemas import WmsQueryResp
from rpa.mola import MolaClient


class JstLimitError(JstRequestError):
    pass


if TYPE_CHECKING:
    from robot_processor.form import named_type_spec  # noqa: F401

wave_id_pattern = re.compile(r".?(\d+)")

RespT = TypeVar("RespT", bound=BaseModel)


class JstSDK:
    session = Session()

    def __init__(self, sid=None, credential=None, erp_info=None):
        """
        Args:
            sid (str | None): shop.sid
            credential (Credential.JST | None): 聚水潭授权信息
            erp_info (ErpInfo | None):
        Raises:
            AssertionError
        """
        if credential:
            self.token = credential.token
            self.co_id = credential.co_id
            self.partner_id = credential.partner_id
            self.partner_key = credential.partner_key
        else:
            erp_info = erp_info or ErpInfo.get_by_sid(sid, ErpType.JST)
            if erp_info is None:
                raise BizError(f"店铺 {sid} 无聚水潭授权信息")
            self.token = erp_info.token
            self.meta = erp_info.meta or {}
            self.co_id = self.meta.get("co_id", "")
            if self.meta.get("auth_type") == "isv":
                self.partner_id = config.JST_ISV_APP_KEY
                self.partner_key = config.JST_ISV_APP_SECRET
            else:
                self.partner_id = config.JST_PARTNER_ID
                self.partner_key = config.JST_PARTNER_KEY

    def _sign(self, method, ts):
        sign_str = f"{method}{self.partner_id}token{self.token}ts{ts}{self.partner_key}"
        return hashlib.md5(sign_str.encode()).hexdigest().lower()

    def _request(self, method, data, resp_cls: Type[RespT]) -> RespT:
        """
        Raises:
            AssertionError
            JstRequestError
        """
        assert self.token, "未关联店铺信息"
        ts = arrow.now().int_timestamp
        sign = self._sign(method, ts)
        params = {"method": method, "partnerid": self.partner_id, "token": self.token, "ts": ts, "sign": sign}
        if not data:
            data = {}

        body = json.dumps(data, default=pydantic_encoder).encode("utf-8")
        headers = {"Content-Type": "application/json"}
        logger.info(f"jst req: {method} {params} {data}")
        resp = self.session.post(
            config.JST_ENDPOINT, headers=headers, params=params, data=body, timeout=config.JST_REQUEST_TIMEOUT
        )
        logger.info(response_to_log(resp))
        resp_json = resp.json()
        if int(resp_json["code"]) != 0:
            raise JstRequestError(req=(method, params, data), res=resp_json, message=resp_json["msg"])
        return resp_cls.parse_obj(resp_json)

    def aftersale_upload(self, data) -> AfterSaleUploadResp:
        data = [i.dict(exclude_none=True) for i in data]
        return self._request("aftersale.upload", data, AfterSaleUploadResp)

    def shop_query(self, nicks: List[str]) -> ShopsQueryResp:
        return self._request("shops.query", {"nicks": nicks}, ShopsQueryResp)

    def shop_query_by_id(self, shop_ids: List[int]) -> ShopsQueryResp:
        return self._request("shops.query", {"shop_ids": shop_ids}, ShopsQueryResp)

    def all_shop_query(self) -> ShopsQueryResp:
        return self._request("shops.query", {}, ShopsQueryResp)

    def orders_single_query(self, data) -> OrdersSingleQueryResp:
        return self._request("orders.single.query", data, OrdersSingleQueryResp)

    def sku_query(self, sku_ids: List[str]) -> SkuQueryResp:
        # 需要过滤重复的sku_id，否则聚水潭会报错
        filtered_sku_ids = set(sku_ids)
        filtered_sku_ids_clean = [i.replace("\\", "") for i in filtered_sku_ids]
        return self._request("sku.query", {"sku_ids": ",".join(filtered_sku_ids_clean)}, SkuQueryResp)

    def combine_sku_query(self, sku_ids: list[str]) -> SkuQueryResp:
        """组合装商品查询

        References:
            https://open.jushuitan.com/document/2076.html
        """
        filtered_sku_ids = set(sku_ids)
        filtered_sku_ids_clean = [i.replace("\\", "") for i in filtered_sku_ids]
        return self._request("combine.sku.query", {"sku_ids": ",".join(filtered_sku_ids_clean)}, SkuQueryResp)

    def get_wms_info_by_org_id(self, org_id, refresh_cache=False) -> WmsQueryResp:
        cache_key = f"JST_WMS_CACHE_KEY_{org_id}"

        cached = None if refresh_cache else cache.get(cache_key)
        if cached:
            return cached
        else:
            # 有些商家的分仓非常多，一次取完
            wms_info = self._request("wms.partner.query", {"page_size": 1000}, WmsQueryResp)
            cache.set(cache_key, wms_info, timeout=config.JST_CACHE_TIMEOUT)
            return wms_info

    def get_wms_co_id_by_name(self, wms_name, org_id) -> Optional[int]:
        resp: WmsQueryResp = self.get_wms_info_by_org_id(org_id)
        wms_infos = [info for info in resp.datas if info.name == wms_name]
        if not wms_infos:
            logger.warning(f"找不到对应仓库名称： {wms_name} 的仓库")
            return None
        wms_co_id = wms_infos[0].wms_co_id
        return wms_co_id

    def get_distributor_info_by_org_id(self, org_id, drp_co_ids=None) -> JstDistributorQueryResp:
        cache_key = f"JST_DISTRIBUTOR_CACHE_KEY_{org_id}"
        cached = cache.get(cache_key)
        if cached:
            return cached
        else:
            data = {"page_size": 100}
            if drp_co_ids:
                data["drp_co_ids"] = drp_co_ids
            distributor_info = self._request("jushuitan.distributor.query", data, JstDistributorQueryResp)
            cache.set(cache_key, distributor_info, timeout=config.JST_CACHE_TIMEOUT)
            return distributor_info

    def get_skus(self, sku_ids: List[str]):
        """
        通过聚水潭API获取sku信息
        """
        # 有些sku_id会带有转义字符，需要去掉，例如万虎家的sku_id="YT26原木色米色\/PU款\/乳胶"，最终应该检索 YT26原木色米色/PU款/乳胶
        sku_ids = [i.replace("\\", "") for i in sku_ids]
        resp = self.sku_query(sku_ids)
        return resp.skus

    @staticmethod
    def _trade_to_pb(trades) -> TradeInfo:
        # 避免转换异常 无法转换的暂时使用DEFAULT_TRADE_STATUS
        TRADE_STATUS_PB_MAP = {
            "WaitPay": WAIT_BUYER_PAY,
            "Delivering": WAIT_SELLER_SEND_GOODS,
            "Merged": DEFAULT_TRADE_STATUS,
            "Question": DEFAULT_TRADE_STATUS,
            "Split": DEFAULT_TRADE_STATUS,
            "WaitOuterSent": DEFAULT_TRADE_STATUS,
            "WaitConfirm": WAIT_SELLER_SEND_GOODS,
            "WaitFConfirm": DEFAULT_TRADE_STATUS,
            "Sent": WAIT_BUYER_CONFIRM_GOODS,
            "Cancelled": TRADE_CLOSED,
            "Confirmed": TRADE_FINISHED,
        }

        trade_info = TradeInfo()
        # order = trades['orders'][0]
        filter_orders = list(filter(lambda o: o.get("type") == "普通订单", trades["orders"]))
        order = filter_orders[0] if filter_orders else trades["orders"][0]
        order = {key: value for key, value in order.items() if value is not None}
        trade_info.trade_id = order.get("so_id", "")
        trade_info.buyer_nick = str(order.get("buyer_id", ""))
        trade_info.status = TradeStatus(TRADE_STATUS_PB_MAP.get(order.get("status", "Confirmed"), DEFAULT_TRADE_STATUS))
        if order.get("pays") and (
            valid_pays := list(
                filter(lambda pay: pay.get("is_order_pay") and pay.get("status") == "Confirmed", order.get("pays"))
            )
        ):
            trade_info.payment = valid_pays[0].get("amount", 0)
            trade_info.paid_at = valid_pays[0].get("pay_date")
        trade_info.created_at = order.get("order_date")
        for jst_item in order.get("items"):
            jst_item = {key: value for key, value in jst_item.items() if value is not None}
            order = Order()
            order.oid = trade_info.trade_id
            order.title = jst_item.get("name", "")
            order.sku_description = jst_item.get("properties_value", "")
            order.pic_path = jst_item.get("pic", "")
            order.sku_id = jst_item.get("shop_sku_id", "")
            order.outer_sku_id = jst_item.get("sku_id", "")
            order.outer_sku_id_real = jst_item.get("sku_id", "")
            order.quantity = jst_item.get("qty", 0)
            order.price = jst_item.get("price", 0)
            order.payment = jst_item.get("price", 0)
            trade_info.orders.append(order)
        return trade_info

    def get_order_of_nonetb(self, order_ids=None):
        """
        通过聚水潭API查询非淘系店铺的订单
        """
        resp = self.orders_single_query(data={"page_index": 0, "so_ids": order_ids, "page_size": 3})
        if not resp.orders:
            return {}
        # fixme 下面这个函数会只取第一个
        return MessageToDict(
            self._trade_to_pb(resp.dict()), including_default_value_fields=True, preserving_proto_field_name=True
        )


class JstNewSDK:
    """
    !!! 新版聚水潭开放平台接口，要使用这些接口必须使用新版的token，不然会报错
    """

    session = Session()

    def __init__(self, sid=None, credential=None, erp_info=None):
        """
        Args:
            sid (str | None): shop.sid
            credential (named_type_spec._model.credential.JST | None): 聚水潭授权信息
        Raises:
            AssertionError
        """
        if credential:
            self.token = credential.token
            self.co_id = credential.co_id
            self.partner_id = credential.partner_id
            self.partner_key = credential.partner_key
        elif erp_info:
            self.token = erp_info.token
            self.co_id = (erp_info.meta or {}).get("co_id", "")
            # 必须有新版token授权
            if not self.co_id:
                raise BizError(f"店铺 {sid} 无聚水潭授权信息")
        else:
            erp_info = ErpInfo.get_by_sid(sid, ErpType.JST)
            if erp_info is None:
                raise BizError(f"店铺 {sid} 无聚水潭授权信息")
            self.token = erp_info.token
            self.co_id = (erp_info.meta or {}).get("co_id", "")
            # 必须有新版token授权
            if not self.co_id:
                raise BizError(f"店铺 {sid} 无聚水潭授权信息")
        self.limit_key = f"jst:openapi:{self.co_id}"
        self.limit_values = "100/minute; 5/second"

    def limiter_test(self, wait=False):
        interval = 0.1
        timeout = 3 if wait else 0
        return limiter.wait_acquire_permission(self.limit_key, self.limit_values, False, timeout, interval)

    def limiter_hit(self, wait=False):
        interval = 0.1
        timeout = 3 if wait else 0
        return limiter.wait_acquire_permission(self.limit_key, self.limit_values, True, timeout, interval)

    def _sign(self, param: dict):
        _sign_str = config.JST_ISV_APP_SECRET
        for key in sorted(param):
            _sign_str += key + str(param.get(key))
        return hashlib.md5(_sign_str.encode("utf-8")).hexdigest()

    def _request(self, method: str, data, resp_cls: Type[RespT]) -> RespT:
        param = {
            "access_token": self.token,
            "app_key": config.JST_ISV_APP_KEY,
            "timestamp": arrow.now().int_timestamp,
            "version": "2",
            "charset": "utf-8",
            "biz": json.dumps(data),
        }
        sign = self._sign(param)
        param["sign"] = sign

        url = urljoin(config.JST_NEW_ENDPOINT, method)
        resp = self.session.post(
            url,
            headers={"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"},
            timeout=config.JST_REQUEST_TIMEOUT,
            data=urllib.parse.urlencode(param),
        )
        logger.info(f"new jst req: {param['biz']} {url} {resp.text} sign={sign}")
        resp_json = resp.json()
        if int(resp_json.get("code")) in [199, 200]:
            raise JstLimitError(req=(method, param, data), res=resp_json, message="调用频次超过限制!")
        self.limiter_hit()
        if int(resp_json["code"]) != 0:
            raise JstRequestError(req=(method, param, data), res=resp_json, message=resp_json["msg"])
        return resp_cls.parse_obj(resp_json)

    def order_action_query(self, o_id) -> OrderActionQueryResp:
        resp = cache.get(f"jst:order_action_query:{o_id}")
        if resp:
            return resp
        resp = self._request("/open/order/action/query", {"o_id": o_id, "page_size": 300}, OrderActionQueryResp)
        if resp:
            cache.set(f"jst:order_action_query:{o_id}", resp, 30)
        return resp

    def logisticscompany_query(self, wms_co_id: str) -> LogisticsCompanyQueryResp:
        return self._request(
            "/open/logisticscompany/query", {"wms_co_id": wms_co_id, "page_size": 50}, LogisticsCompanyQueryResp
        )

    def all_logisticscompany_query(self):
        page_no = 1
        logistic_companies: List[LogisticsCompanyQueryResp.Data.Item] = []
        while True:
            resp = self._request(
                "/open/logisticscompany/query", {"page_index": page_no, "page_size": 50}, LogisticsCompanyQueryResp
            )
            if not resp.data.has_next:
                break
            logistic_companies.extend(resp.data.datas)
            page_no = page_no + 1
        return LogisticsCompanyQueryResp(
            data=LogisticsCompanyQueryResp.Data(
                datas=logistic_companies,
                has_next=False,
                page_count=1,
                page_index=1,
                page_size=len(logistic_companies),
                data_count=len(logistic_companies),
            ),
            code=0,
            msg="执行成功",
        )

    def wave_query(self, wave_ids) -> WaveQueryResp:
        return self._request("/open/webapi/wmsapi/wavequery/loadwavesbyfilter", {"wave_ids": wave_ids}, WaveQueryResp)

    def orders_modifywms_upload(self, o_id, wms_co_id) -> OrdersModifyWmsUploadResp:
        return self._request(
            "/open/orders/modifywms/upload", [{"o_id": o_id, "wms_co_id": wms_co_id}], OrdersModifyWmsUploadResp
        )

    def confirm_after_sale(
        self, as_ids, no_order=False, exchange_force=False, confirm_refund=False
    ) -> AfterSaleConfirmResp:
        return self._request(
            "/open/webapi/aftersaleapi/open/confirm",
            {
                "as_ids": as_ids,
                "no_order": no_order,
                "exchange_force": exchange_force,
                "confirm_refund": confirm_refund,
            },
            AfterSaleConfirmResp,
        )

    def node_soid_set(self, o_id, so_id, node, shop_id) -> JstOrderRemarkUploadResp:
        """修改线下备注 https://openweb.jushuitan.com/dev-doc?docType=4&docId=118"""
        req_data = {"node": node}
        if o_id:
            req_data["o_id"] = int(o_id)
        if so_id:
            req_data["so_id"] = so_id
        return self._request(
            "/open/order/node/soid/set", {"shop_id": shop_id, "items": [req_data]}, JstOrderRemarkUploadResp
        )

    def change_order_remark(self, o_id, remark, is_append) -> JstOrderRemarkUploadResp:
        """修改订单卖家备注（按内部单号） https://openweb.jushuitan.com/dev-doc?docType=4&docId=196"""
        return self._request(
            "/open/jushuitan/order/remark/upload",
            {"o_id": o_id, "remark": remark, "is_append": is_append},
            JstOrderRemarkUploadResp,
        )

    def inventory_query(
        self,
        sku_ids: List[str],
        wms_co_id: int | None = None,
        page_index: int = 1,
        page_size: int = 100,
    ) -> InventoryQueryResp:
        """商品库存查询

        References:
            https://openweb.jushuitan.com/dev-doc?docType=3&docId=15
        """
        data = {
            "sku_ids": ",".join(set(sku_ids)),
            "page_index": page_index,
            "page_size": page_size,
        }
        if wms_co_id:
            data["wms_co_id"] = wms_co_id

        return self._request("/open/inventory/query", data, InventoryQueryResp)

    def wms_partner_query(self, page_size: int = 1000) -> WmsPartnerQueryResp:
        """仓库查询

        References:
            https://openweb.jushuitan.com/dev-doc?docType=1&docId=3
        """
        return self._request("/open/wms/partner/query", {"page_size": page_size}, WmsPartnerQueryResp)

    def supplier_name_query(self, drp_co_id_to):
        """
        供销商查询
        https://openweb.jushuitan.com/dev-doc?docType=28&docId=430
        """
        return self._request(
            "/open/api/company/inneropen/partner/channel/querymysupplier",
            {"supplier_co_id": drp_co_id_to},
            SupplierResp,
        )

    def get_supplier_by_sku(self, sku_id) -> SkuSupplierResp:
        """
        根据sku查询供应商
        https://openweb.jushuitan.com/dev-doc?docType=2&docId=449
        """
        return self._request("/open/webapi/itemapi/suppliersku/getsupplierskulist", {"skuid": sku_id}, SkuSupplierResp)

    def get_supplier_by_supplier_id(self, supplier_id) -> MySupplierQueryResp:
        return self._request(
            "/open/api/drp/inneropen/partner/channel/querymysupplier",
            {"supplier_co_id": supplier_id, "page_size": 1, "page_num": 100},
            MySupplierQueryResp,
        )

    def modify_wms(self, o_id, wms_co_id):
        """
        指定发货仓
        https://openweb.jushuitan.com/dev-doc?docType=4&docId=362
        @param o_id: 内部订单号
        @type o_id: int
        @param wms_co_id: 仓库编号
        遇到取消的订单会报错
        """
        return self._request("/open/orders/modifywms/upload", [{"o_id": o_id, "wms_co_id": wms_co_id}], NewSdkBaseResp)

    def get_wave_id(self, o_id: str):
        resp = self.order_action_query(o_id)
        if not resp.data.datas:
            return None
        for order_action in resp.data.datas[::-1]:
            if order_action.name == "绑定拣货批次":
                ids = wave_id_pattern.findall(order_action.remark or "")
                return ids[0] if ids else None
        return None

    def get_packer_name(self, o_id: str):
        resp = self.order_action_query(o_id)
        if not resp.data.datas:
            return None
        for order_action in resp.data.datas[::-1]:
            if order_action.name == "打包":
                return order_action.remark
        return None

    def get_inspecter_name(self, o_id: str):
        resp = self.order_action_query(o_id)
        if not resp.data.datas:
            return None
        for order_action in resp.data.datas[::-1]:
            if "自动验货" in order_action.name:
                return "系统"
            if "验货" in order_action.name:
                return order_action.creator_name
        return None

    def get_picker_name(self, wave_id: int):
        resp = self.wave_query([wave_id])
        return resp.data[0].picker_name if resp.data else None

    def update_label(
        self, o_id: Optional[str], shop_id: Optional[int], tid: Optional[str], labels: List[str], action_type: int
    ):
        if not o_id and not (shop_id and tid):
            raise ValueError("o_id 和 shop_id,tid 不能同时为空")
        data: Dict[str, Any] = {}
        if o_id:
            data["o_id"] = o_id
        else:
            data["shop_id"] = shop_id
            data["so_id"] = tid
        data["labels"] = labels
        data["action_type"] = action_type
        return self._request("/open/jushuitan/order/label/upload", data, NewSdkBaseResp)

    def order_upload(self, req: OrderUploadReq):
        data = req.dict(exclude_none=True)
        return self._request("/open/jushuitan/orders/upload", [data], OrderUploadResp)

    def query_skus_by_sku_ids(self, sku_ids: list[str], page_index: int = 1, page_size: int = 50):
        req_sku_ids = ",".join(sku_ids)
        return self._request(
            "/open/sku/query",
            {
                "sku_ids": req_sku_ids,
                "page_index": page_index,
                "page_size": page_size,
            },
            QuerySkuResp,
        )

    def query_skus_by_name(self, name: str, is_exactly: bool = True, page_index: int = 1, page_size: int = 50):
        if is_exactly:
            data = {
                "exactly_name": name,
                "page_index": page_index,
                "page_size": page_size,
            }
        else:
            data = {
                "name": name,
                "page_index": page_index,
                "page_size": page_size,
            }
        return self._request("/open/sku/query", data, QuerySkuResp)

    def query_skus_by_spu_ids(self, spu_ids: list[str], page_index: int = 1, page_size: int = 50):
        return self._request(
            "/open/mall/item/query",
            {
                "i_ids": spu_ids,
                "page_index": page_index,
                "page_size": page_size,
            },
            QuerySpuResp,
        )

    def query_combine_skus_by_combine_sku_ids(
        self, combine_sku_ids: list[str], page_index: int = 1, page_size: int = 50
    ):
        request_combine_sku_ids = ",".join(combine_sku_ids)
        return self._request(
            "/open/combine/sku/query",
            {
                "sku_ids": request_combine_sku_ids,
                "page_index": page_index,
                "page_size": page_size,
            },
            QueryCombineSkuResp,
        )

    def query_out_orders(self, params: OpenOrdersOutSimpleQuery):
        return self._request("/open/orders/out/simple/query", params.dict(), OpenOrdersOutSimpleQuery.Response)


class JstQmSDK(JstSDK):

    @staticmethod
    def qm_sign(param):
        _sign_str = config.JST_APP_SECRET
        # 签名第一步，要求参数排序
        # 第二步：把所有参数名和参数值串在一起，字符串 前后 分别要加secret
        for key in sorted(param):
            _sign_str += key + str(param.get(key))
        _sign_str += config.JST_APP_SECRET
        # md5加密，最后的签名要求大写
        return hashlib.md5(_sign_str.encode("utf-8")).hexdigest().upper()

    def get(self, method, query_params: dict, resp_cls: Type[RespT], retryable: bool = False) -> RespT:
        try:
            if retryable:
                return self._get_with_retry(method, query_params, resp_cls)
            else:
                return self._get(method, query_params, resp_cls)
        except JstRetryableRequestError as e:
            raise JstRequestError(req=e.req, res=e.res, message=e.message)

    @retry(
        retry=retry_if_exception_type(JstRetryableRequestError),
        wait=wait_fixed(2),
        stop=stop_after_attempt(3),
        reraise=True,
    )
    def _get_with_retry(self, method, query_params: dict, resp_cls: Type[RespT]) -> RespT:
        return self._get(method, query_params, resp_cls)

    def _get(self, method, query_params: dict, resp_cls: Type[RespT]) -> RespT:
        """
        奇门api的参数构建
        :param method: 聚水潭api名称
        :param data: 聚水潭api的自身业务参数集合
        :return:
        """
        param = {
            "v": "2.0",
            "app_key": config.JST_APP_KEY,
            "format": "json",
            "target_app_key": config.JST_TARGET_SECRET,
            "timestamp": arrow.now().format("YYYY-MM-DD HH:mm:ss"),
            "customer_id": str(self.co_id),
            "sign_method": "md5",
            "method": method,
        }
        if query_params:
            param.update(query_params)

        url = config.JST_QM_ENDPOINT
        # 把参数进行url编码后拼接到url后面
        param_str = urllib.parse.urlencode(param)
        url = url + "?" + param_str

        # 把奇门签名放在最后拼在url上
        param["sign"] = self.qm_sign(param)
        url = url + "&sign=" + param["sign"]

        resp = self.session.get(url, timeout=config.JST_REQUEST_TIMEOUT)
        logger.info(f"qm req: {url} resp: {resp.text}")
        resp_json = resp.json()
        if not resp_json.get("response"):
            raise JstRetryableRequestError(req=(method, "", param), res=resp_json, message=response_to_log(resp))
        if resp_json.get("response", {}).get("flag") == "failure":
            if resp_json.get("response", {}).get("sub_code") in [
                "isp.http-read-timeout",
                "isp.http-service-unavailable",
            ]:
                raise JstRetryableRequestError(req=(method, "", param), res=resp_json, message=response_to_log(resp))
            raise JstRequestError(req=(method, "", param), res=resp_json, message=response_to_log(resp))
        return resp_cls.parse_obj(resp_json.get("response"))

    def query_orders(self, query_params: dict, page_index="1", page_size="100") -> OrdersSingleQueryResp:
        # https://jushuitan.yuque.com/cscvxv/al2cbn/pqbqlb?#U7QVj
        # 如果传入大量的o_id，要分批查询，20个以上的o_id聚水潭就报错
        if o_ids := query_params.get("o_ids"):
            o_ids = o_ids.split(",")
            length = len(o_ids)
            if length > 20:
                result = OrdersSingleQueryResp(orders=[])
                while o_ids:
                    current_batch_o_ids = o_ids[:20]
                    o_ids = o_ids[20:]
                    batch_result = self.query_orders({"o_ids": ",".join(current_batch_o_ids)})
                    result.orders.extend(batch_result.orders)
                return result

        query_params.update({"page_index": page_index, "page_size": page_size})
        return self.get("jushuitan.order.list.query", query_params, OrdersSingleQueryResp, True)

    def query_refund_trade_list(self, req: AfterSaleSearchReq) -> AfterSaleResponseData:
        # https://open.jushuitan.com/document.aspx?doc_id=2356
        query_params = req.dict(exclude_none=True)
        page_index = "1" if not req.page_index else str(req.page_index)
        query_params.update({"page_index": page_index, "page_size": "100"})
        return self.get("jushuitan.refund.list.query", query_params, AfterSaleResponseData, True)

    def query_out_orders(self, query_params: dict, page_index="1", page_size="100") -> OutOrdersQueryResp:
        query_params.update({"page_index": page_index, "page_size": page_size})
        return self.get("jushuitan.saleout.list.query", query_params, OutOrdersQueryResp, True)


class MolaJstClient:
    def _request(self, sid: str, method: str, data: dict, ignore_subuser: bool = False) -> Result[dict, str]:
        # customize org 1137
        # https://redmine.leyantech.com/issues/534410
        sub_user_id_map = config.RPA_JST_SUB_USER_ID_MAP
        if not isinstance(sub_user_id_map, dict):
            sub_user_id_map = json.loads(sub_user_id_map)
        if ignore_subuser or not sub_user_id_map.get(sid):
            sub_user_params = {}
        else:
            sub_user_params = {"subUserIds": ",".join(sub_user_id_map[sid])}
        return MolaClient(sid, need_retry=True).call_namespace_method(
            "jstwpa", method, data=data, params=sub_user_params
        )

    @staticmethod
    def get_receiver(business_data) -> Dict[str, str]:
        receiver = business_data.get("new_address")
        zone = receiver.pop("zone", "")
        if zone:
            receiver["district"] = zone
        if not receiver.get("district"):
            receiver["district"] = ""
        if not receiver.get("town"):
            receiver["town"] = ""
        return receiver

    def confirm_after_sale_old(self, as_id: str, sid: str, o_id: str) -> Result[None, str]:
        method = "confirm-after-sale"
        match self._request(sid, method, {"asId": as_id, "o_id": o_id}):
            case Err(error_message):
                return Err(error_message)
            case _:
                return Ok(None)

    def confirm_after_sale(self, as_id: str, sid: str, o_id: str) -> Result[str | None, str]:
        shop_note_map = config.RPA_JST_SHOP_NOTE_MAP
        if not isinstance(shop_note_map, dict):
            shop_note_map = json.loads(shop_note_map)
        note = shop_note_map.get(sid, "飞梭")
        match self._request(sid, "modify-note-and-confirm-after-sale", {"asId": as_id, "note": note, "o_id": o_id}):
            case Err(error_message):
                if "方法未注册" in error_message:
                    return self.confirm_after_sale_old(as_id, sid, o_id)
                else:
                    return Err(error_message)
            case Ok(resp_json):
                return Ok(resp_json.get("result", {}).get("o_id", None))
        return Err("confirm-after-sale failed")

    def get_express_traces(self, o_id: str, sid: str) -> Result[list, str]:
        match self._request(sid, "get-order-express-trace", {"o_id": o_id}):
            case Err(error_message):
                return Err(error_message)
            case Ok(resp_json):
                return Ok(resp_json.get("result") or [])
        return Err("get-order-express-trace failed")

    def copy_order(self, tid: str, sid: str) -> Result[tuple[str, str], str]:
        match self._request(sid, "copy-order", {"tid": tid}):
            case Err(error_message):
                return Err(error_message)
            case Ok(resp_json):
                so_id = resp_json.get("result", {}).get("so_id", "")
                o_id = str(resp_json.get("result", {}).get("o_id", ""))
                return Ok((so_id, o_id))
        return Err("copy-order failed")

    def get_erp_trade(self, tid: str, sid: str, only_orders=True) -> Result[list, str]:
        """
        获取erp内的订单信息
        outer_oi_id: 子订单号
        sku_id: sku_id
        """
        match self._request(sid, "get-trade", {"tid": tid}):
            case Err(error_message):
                return Err(error_message)
            case Ok(resp_json):
                result = resp_json.get("result") or {}
                if only_orders:
                    return Ok(result.get("items", []))
                else:
                    return Ok([result])
        return Err("get-trade failed")

    def change_address(self, sid: str, tid: str, o_id=None, order_rule=None, **receivers) -> Result[None, str]:
        """
        此处的o_id指的是确认之后获取的内部售后单号
        """
        body = {
            "tid": tid,
            "newReceiverInfo": receivers,
            "parseFromAddress": False,
            "order_rule": order_rule,
        }
        # 仅当用内部单号查询时才传oid
        if o_id and order_rule == QueryOrderRule.o_id.value:
            body["o_id"] = o_id
        match self._request(sid, "change-receiver-info", body):
            case Err(error_message):
                return Err(error_message)
            case _:
                return Ok(None)

    def modify_sku(self, sid: str, tid: str, oid: str, new_outer_id: str) -> Result[None, str]:
        """修改SKU信息, 子订单号oid不是必填内容，填了的话找对应的子订单的商品进行换货，不然换第一笔"""
        old_outer_id = ""
        match self.get_erp_trade(tid, sid):
            case Err(error_message):
                return Err(error_message)
            case Ok(erp_orders):
                for order in erp_orders or []:
                    if order.get("outer_oi_id") and str(order["outer_oi_id"]) == str(oid):
                        old_outer_id = order["sku_id"]
                        break
                if erp_orders and len(erp_orders) == 1 and not old_outer_id:
                    old_outer_id = erp_orders[0]["sku_id"]

                if not old_outer_id:
                    return Err(f"{str(oid)} not in {erp_orders}")

        match self._request(
            sid,
            "modify-trade-sku-simple",
            {"tid": tid, "origin_outer_sku_id": old_outer_id, "new_outer_sku_id": new_outer_id},
        ):
            case Err(error_message):
                return Err(error_message)
            case _:
                return Ok(None)

    @classmethod
    def transform_order_data(cls, order_data):
        logistics_company = order_data.get("logistics_company")
        weight = order_data.get("weight")
        logistics_no = order_data.get("l_id", "")
        if logistics_no:
            logistics_no = logistics_no.replace("@", "")
        actions = order_data.get("actions")
        ret_actions = [
            {
                "time": action.get("created", ""),
                "name": action.get("name", ""),
                "remark": action.get("remark", ""),
                "operator": action.get("creator_name") if action.get("creator_name") else "",
            }
            for action in actions
        ]
        return {
            "logistics_company": logistics_company,
            "logistics_no": logistics_no,
            "weight": weight,
            "o_id": order_data.get("o_id", ""),
            "outer_oi_id": order_data.get("outer_pay_id", ""),
            "actions": ret_actions,
        }

    def get_erp_order_data(self, order_id: str, sid: str) -> Result[dict, str]:
        """获取erp订单日志列表"""
        match self._request(sid, "get-order-data-by-outer-id", {"tid": order_id}, ignore_subuser=True):
            case Err(error_message):
                return Err(error_message)
            case Ok(resp_json):
                result = resp_json.get("result") or {}
                order_data = MolaJstClient.transform_order_data(result)
                return Ok(order_data)
        return Err("get-order-data-by-outer-id failed")
