import enum
from datetime import datetime
from decimal import Decimal
from functools import wraps
from typing import Any
from typing import List
from typing import Optional
from typing import Union

from pydantic import BaseModel
from pydantic import Field
from pydantic import root_validator

from robot_processor.job.erp_trade_handler.new_abstract_handler import ErpOrder
from robot_processor.job.job_model_wrapper import ReissueSkus
from robot_processor.utils import combine_address_with_town
from robot_processor.utils import get_district_or_zone
from robot_processor.utils import make_fields_optional


class AfterSaleUploadReq(BaseModel):
    """售后上传"""

    class ShopStatus(enum.StrEnum):
        WAIT_SELLER_AGREE = "WAIT_SELLER_AGREE"  # 买家已经申请退款，等待卖家同意
        WAIT_BUYER_RETURN_GOODS = "WAIT_BUYER_RETURN_GOODS"  # 卖家已经同意退款，等待买家退货
        WAIT_SELLER_CONFIRM_GOODS = "WAIT_SELLER_CONFIRM_GOODS"  # 买家已经退货，等待卖家确认收货
        SELLER_REFUSE_BUYER = "SELLER_REFUSE_BUYER"  # 卖家拒绝退款
        CLOSED = "CLOSED"  # 退款关闭
        SUCCESS = "SUCCESS"  # 退款成功

    class GoodStatus(enum.StrEnum):
        BUYER_NOT_RECEIVED = "BUYER_NOT_RECEIVED"  # 买家未收到货
        BUYER_RECEIVED = "BUYER_RECEIVED"  # 买家已收到货
        BUYER_RETURNED_GOODS = "BUYER_RETURNED_GOODS"  # 买家已退货
        SELLER_RECEIVED = "SELLER_RECEIVED"  # 卖家已收到退货

    class Item(BaseModel):
        outer_oi_id: Optional[str]  # 平台订单明细编号，存在则会按此作为唯一性判断，商品为组合装时需要上传订单的明细编号
        sku_id: str  # 商家商品编码
        qty: int  # 退货数量
        amount: float  # SKU退款金额
        type: str  # 售后类型，普通退货，其它，拒收退货，仅退款，投诉，补发，换货，维修
        name: Optional[str]  # 商品名称，outer_oi_id 存在，此处可以不填
        pic: Optional[str]  # 图片地址outer_oi_id 存在，此处可以不填
        properties_value: Optional[str]  # 属性规格outer_oi_id 存在，此处可以不填

    shop_id: int  # 店铺编号
    so_id: Optional[str]  # 平台订单号
    outer_as_id: str  # 退货退款单号，平台唯一
    type: str  # 售后类型，普通退货，其它，拒收退货，仅退款，投诉，补发，换货，维修
    logistics_company: Optional[str]  # 快递公司
    l_id: Optional[str]  # 物流单号
    shop_status: ShopStatus  # 可更新，补发、换货售后单确认时需在系统中手动确认
    remark: Optional[str]  # 备注
    good_status: GoodStatus  # 可更新
    question_type: str  # 问题类型；可更新
    total_amount: float  # 原单据总金额
    refund: float  # 卖家应退金额
    payment: float  # 买家应补偿金额
    items: List[Item]
    receiver_state: Optional[str]  # 收货人省份
    receiver_city: Optional[str]  # 收货人城市
    receiver_district: Optional[str]  # 收货人区县
    receiver_address: Optional[str]  # 收货人详细地址
    receiver_name_en: str | None  # 收货人：仅针对换货补发类型的售后单有效
    receiver_mobile_en: str | None  # 联系手机：仅针对换货补发类型的售后单有效

    # --
    new_address: Optional[dict]  # 新地址

    @classmethod
    def convert_items(cls, skus: ReissueSkus, outer_oi_id: str):
        """过滤没有数量的sku，计算总金额"""
        items = list()
        for sku in skus.sku_list:
            qty = sku.qty
            if not qty:
                continue
            price = sku.price or 0
            amount = float(Decimal(str(price)) * qty)
            items.append(
                cls.Item(
                    outer_oi_id=outer_oi_id,
                    sku_id=sku.outer_sku_id if not sku.is_single() else sku.outer_spu_id,
                    qty=qty,
                    amount=amount,
                    pic=sku.pic,
                    type=sku.type,
                )
            )
        return items

    def fill_address(self):
        if receiver := self.new_address:
            self.receiver_state = receiver.get("state")
            self.receiver_city = receiver.get("city") or receiver.get("zone")
            self.receiver_district = get_district_or_zone(receiver)
            self.receiver_address = combine_address_with_town(
                f"{receiver.get('town') or ''}", f"{receiver.get('address')}"
            )
            self.receiver_name_en = receiver.get("name")
            self.receiver_mobile_en = receiver.get("mobile")
        return self


class AfterSaleUploadResp(BaseModel):
    """售后上传"""

    class Data(BaseModel):
        as_id: int  # 内部售后单号
        o_id: int  # 内部订单号
        so_id: str  # 线上单号
        issuccess: Optional[bool]
        msg: Optional[str]

    datas: List[Data]


class ShopsQueryResp(BaseModel):
    """店铺查询"""

    class Shop(BaseModel):
        shop_id: int  # 店铺编号
        shop_name: Optional[str]  # 店铺名
        shop_site: Optional[str]  # 站点，如：京东
        nick: Optional[str]  # 昵称
        session_expired: Optional[str]  # 授权过期时间 2021-01-01
        created: Optional[str]  # 创建时间
        organization: Optional[str]  # 组织

    datas: List[Shop]


class SkuMapQueryResp(BaseModel):
    """商品映射查询"""

    class SkuInfo(BaseModel):
        shop_id: Optional[int]
        sku_id: str
        i_id: str

    datas: Union[None, List[SkuInfo]]

    @property
    def sku_map(self):
        if self.datas is None:
            return dict()
        return {info.sku_id: info.i_id for info in self.datas}

    def get_outer_oi_id_by_sku_id(self, sku_id):
        return self.sku_map.get(sku_id)


@make_fields_optional
class SkuQueryResp(BaseModel):
    @make_fields_optional
    class SkuInfo(BaseModel):
        sku_id: str
        i_id: str
        name: str
        short_name: str
        sale_price: float
        cost_price: float
        properties_value: str
        category: str
        pic_big: Optional[str]
        pic: Optional[str]
        enabled: int
        weight: float
        market_price: float
        brand: str
        supplier_id: int
        supplier_name: str
        modified: str
        sku_code: str
        color: str
        supplier_sku_id: str
        supplier_i_id: str
        vc_name: str
        sku_type: str
        creator: str
        created: str
        autoid: int
        remark: str
        item_type: str
        stock_disabled: int
        unit: str
        shelf_life: int
        labels: str
        productionbatch_format: str
        production_licence: str
        l: int
        w: int
        h: int
        is_series_number: bool
        other_price_1: float
        other_price_2: float
        other_price_3: float
        other_price_4: float
        other_price_5: float
        other_1: str
        other_2: str
        other_3: str
        other_4: str
        other_5: str
        stock_type: str

    datas: list[SkuInfo]

    @property
    def skus(self) -> list[dict]:
        return [i.dict() for i in self.datas] if self.datas else []


class WmsQueryResp(BaseModel):
    class WmsInfo(BaseModel):
        name: str
        co_id: int
        wms_co_id: int
        is_main: bool

    datas: List[WmsInfo]


class JstDistributorQueryResp(BaseModel):
    class Distributor(BaseModel):
        drp_co_id: int
        co_id: int
        name: str

    datas: List[Distributor]


class JstOrderRemarkUploadResp(BaseModel):
    msg: Optional[str]
    code: Optional[int]


@make_fields_optional
class Item(BaseModel):
    amount: str  # 总金额
    outer_oi_id: str  # 子订单号
    name: str  # 商品名
    properties_value: str  # 属性
    pic: str  # 图片
    shop_sku_id: str  # sku_id
    sku_id: str  # outer_sku_id
    qty: int  # 数量
    price: float
    i_id: str  # 系统商品款号
    shop_i_id: str  # 线上商品款号
    is_gift: str
    is_presale: bool
    item_status: str  # 商品状态
    item_pay_amount: float  # 商品实付金额
    refund_status: str
    src_combine_sku_id: str  # 所属组合装sku_id
    supplier_name: str  # 商品供应商名称
    sku_type: str  # 商品类型 normal/combine

    # 需要额外查询
    erp_name: str  # ERP商品名称
    erp_short_name: str  # ERP商品简称
    category: str  # 商品分类

    def check_is_gift(self):
        # 聚水潭返回的是一个布尔值的字符串
        return self.is_gift == "true"


class Payment(BaseModel):
    status: Optional[str]
    is_order_pay: Optional[bool]  # "true/false"
    pay_date: Optional[str]  # "2022-05-31 14:47:02"
    amount: Optional[float]  # 32.13,


@make_fields_optional
class JstBaseOrder(BaseModel, ErpOrder):
    as_id: Optional[str]  # 售后单号, type为补发订单的时候才有这个字段
    type: str  # 订单类型
    status: str  # 订单状态
    order_from: str  # 订单来源
    pay_amount: float  # 支付金额
    logistics_company: str  # 物流公司， 发货后才有
    l_id: str  # 物流单号， 发货后才有
    lc_id: str  # 快递公司编码
    labels: str  # 订单标签 逗号隔开的
    pays: List[Payment]
    items: List[Item]
    node: str
    buyer_id: int  # 买家id
    shop_id: int
    shop_name: str  # 店铺名称
    shop_buyer_id: str  # 买家昵称
    so_id: str  # 线上订单号
    order_date: str  # 下单时间 2022-05-31 14:47:02",
    shop_status: str  # WAIT_SELLER_SEND_GOODS",
    receiver_country: str  # ,
    receiver_state: str  # ,
    receiver_city: str  # ,
    receiver_district: str  # ,
    receiver_town: str  # ,
    receiver_address: str  # ,
    receiver_name: str  # ,
    receiver_mobile: str  # ,
    wms_co_id: int  # 分仓编号，api没法直接拿到仓库名称
    drp_co_id_to: int  # api返回的是id
    # 时间格式 '2023-06-14 00:25:41'
    pay_date: str  # 支付时间
    send_date: str  # 发货时间 发货了才有，不然没这个字段
    f_weight: str  # 实际重量
    f_weight_float: float  # 实际重量
    has_split_order: str  # 是否有拆分订单
    is_presale: bool  # 是否预售
    question_type: str  # 问题类型，仅当问题订单时有效
    question_desc: str  # 异常描述，仅当问题订单时有效
    seller_flag: int  # 旗帜(1红旗，2黄旗，3绿旗，4蓝旗，5紫旗)）
    created: str  # 订单创建时间
    weight: str  # 重量
    remark: str  # 订单备注；卖家备注

    # 合并拆分相关字段
    link_o_id: str  # 关联内部单号
    is_merge: bool  # 是否被合并
    is_split: bool  # 是否被拆分
    o_id: str  # 内部单号

    # 以下两个字段是客户端有，api没有
    drp_co_name: str  # 分销商
    supplier_name: str  # 供销商
    wave_id: str  # 批次号
    picker_name: str  # 拣货员
    packer_name: str  # 打包员
    wms_company: str  # 仓库名称
    wms_co_name: str  # 仓库名称

    # 飞梭系统字段
    o_id_output: str  # 内部单号(飞梭输出用)
    order_status: str  # 订单状态(飞梭输出用)

    # 聚合商品信息 todo 等待列表组件去支持这种表现,现在简单join返回结果
    items_i_id: str  # 平台款式编码
    items_properties_value: str  # 商品属性
    items_name: str  # 平台商品名称
    items_shop_sku_id: str  # ERP商品编码
    items_shop_i_id: str  # ERP款式编码
    items_erp_name: str  # ERP商品名称
    items_erp_short_name: str  # ERP商品简称
    items_price: str  # 订单商品金额(商品在订单中的金额）
    outer_sku_id: str  # 商品编码,这个需要兼容旧版本数据，不加items前缀

    @classmethod
    def merged_order(cls, split_orders):
        # 基本信息取第一个订单的，其他输出信息合并
        res = split_orders[0]
        need_merge_fields = [
            "o_id",
            "items_i_id",
            "items_properties_value",
            "items_name",
            "supplier_name",
            "status",
            "l_id",
            "logistics_company",
            "wms_co_name",
            "order_status",
            "outer_sku_id",
            "wave_id",
            "picker_name",
            "o_id_output",
            "items_shop_sku_id",
            "items_shop_i_id",
            "items_price",
        ]
        for field in need_merge_fields:
            merged_result = ",".join(
                [str(getattr(i, field)) for i in split_orders if getattr(i, field) or getattr(i, field) == 0]
            )
            if merged_result:
                setattr(res, field, merged_result)
        return res

    def __hash__(self):
        return hash(self.o_id)

    @property
    def tid(self):
        return self.so_id


class OrdersSingleQueryResp(BaseModel):
    """聚水潭奇门获取的订单信息"""

    """非淘系订单查询"""
    orders: List[JstBaseOrder]


@make_fields_optional
class OrderAction(BaseModel):
    oa_id: int  # 日志id
    o_id: int  # 内部单号
    name: str  # 操作名称
    remark: Optional[str]  # 备注
    created: str  # 操作时间
    creator_name: str  # 操作人

    def get_split_action_result(self):
        if self.name != "被拆分":
            return None
        return self.remark  # 拆分后的内部单号

    def get_merge_action_result(self):
        if self.name != "被合并":
            return None
        return self.remark  # 合并后的内部单号

    def convert_created_time_to_datetime(self) -> datetime:
        return datetime.strptime(self.created or "", "%Y-%m-%d %H:%M:%S")


class Wave(BaseModel):
    picker_name: Optional[str]


class OrderActionQueryResp(BaseModel):
    class OrderActionList(BaseModel):
        datas: Optional[List[OrderAction]]

    data: OrderActionList


@make_fields_optional
class LogisticsCompanyQueryResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        @make_fields_optional
        class Item(BaseModel):
            lc_id: str
            lc_name: str
            modified: str

        page_index: int
        page_size: int
        has_next: bool
        data_count: int
        page_count: int
        datas: List[Item]

    data: Data
    msg: str
    code: int


class WaveQueryResp(BaseModel):
    data: List[Wave]


class OrdersModifyWmsUploadResp(BaseModel):
    issuccess: Optional[str]


class AfterSaleDetail(BaseModel):
    as_id: Optional[str]
    outer_as_id: Optional[str]
    message: Optional[str]
    details: Optional[str]
    is_success: Optional[bool]
    new_o_id: Optional[str]  # 新生成的补发的内部订单号


@make_fields_optional
class AfterSaleConfirmResp(BaseModel):
    @make_fields_optional
    class AfterSaleData(BaseModel):
        success: List[AfterSaleDetail]
        fail: List[AfterSaleDetail]
        success_count: int
        fail_count: int

    data: AfterSaleData
    msg: str
    code: int


class JstAfterSaleProcessType(enum.StrEnum):
    api = "api"
    client = "client"


class InventoryQueryResp(BaseModel):
    class Data(BaseModel):
        class Inventory(BaseModel):
            sku_id: Optional[str] = Field(description="商品编码")
            i_id: Optional[str] = Field(description="款式编码")
            qty: Optional[int] = Field(description="主仓实际库存")
            order_lock: int | None = Field(description="订单占有数")
            virtual_qty: int | None = Field(description="虚拟库存")
            purchase_qty: int | None = Field(description="采购在途数")
            in_qty: int | None = Field(description="进货仓库存")
            return_qty: int | None = Field(description="销退仓库存")

        has_next: Optional[bool]
        page_index: Optional[int]
        page_size: Optional[int]
        inventorys: List[Inventory] = Field(default_factory=list)

    code: Optional[int]
    msg: Optional[str]
    data: Optional[Data]


class WmsPartnerQueryResp(BaseModel):
    class Data(BaseModel):
        class WmsPartner(BaseModel):
            co_id: Optional[int] = Field(description="主仓公司编号")
            wms_co_id: Optional[int] = Field(description="分仓编号")
            is_main: Optional[bool] = Field(description="是否主仓")
            name: Optional[str] = Field(description="分仓名称")

        datas: List[WmsPartner]

    code: Optional[int]
    msg: Optional[str]
    data: Optional[Data]


class SupplierResp(BaseModel):
    class Data(BaseModel):
        class Supplier(BaseModel):
            supplier_co_id: str = Field(description="供应商编号")
            co_name: str = Field(description="供应商名称")
            status: int = Field(description="状态")

        supplier_vos: List[Supplier]

    code: Optional[int]
    msg: Optional[str]
    data: Data


class SkuSupplierResp(BaseModel):
    class Data(BaseModel):
        class SkuSupplier(BaseModel):
            sku_id: str = Field(description="商品编码")
            supplier_code: Optional[str] = Field(description="供应商编号")
            supplier_name: str = Field(description="供应商名称")

        list: List[SkuSupplier]

    code: Optional[int]
    msg: Optional[str]
    data: Data


class MySupplierQueryResp(BaseModel):
    class Data(BaseModel):
        class Supplier(BaseModel):
            status: int = Field(description="合作状态")
            supplier_co_id: Optional[str] = Field(description="供应商编号")
            co_name: str = Field(description="供应商名称")

        list: List[Supplier]
        total: int

    code: Optional[int]
    msg: Optional[str]
    data: Data


class NewSdkBaseResp(BaseModel):
    code: Optional[int]
    msg: Optional[str]


class OrderUploadResp(BaseModel):
    class Datas(BaseModel):
        class Data(BaseModel):
            o_id: int
            so_id: str
            issuccess: bool
            msg: str

        datas: List[Data]

    code: Optional[int]
    msg: Optional[str]
    data: Optional[Datas]


class OrderUploadReq(BaseModel):
    class Item(BaseModel):
        sku_id: str
        shop_sku_id: str
        amount: float
        base_price: float
        price: float | None
        qty: int
        name: str
        outer_oi_id: str

    class Pay(BaseModel):
        outer_pay_id: str
        pay_date: str
        payment: str
        seller_account: str
        buyer_account: str
        amount: int

    shop_id: int
    so_id: str
    order_date: str
    shop_status: str
    shop_buyer_id: str = ""
    receiver_state: str
    receiver_city: str
    receiver_district: str
    receiver_town: Optional[str]
    receiver_address: str
    receiver_name: str
    receiver_phone: str
    receiver_mobile: str | None
    pay_amount: float = 0
    freight: float = 0
    remark: Optional[str]
    items: List[Item]
    pay: Optional[Pay]
    buyer_message: str | None


class AfterSaleSearchReq(BaseModel):
    start_time: str | None = Field(None, description="开始时间")
    end_time: str | None = Field(None, description="结束时间")
    shop_id: Optional[int] = Field(None, description="店铺编号")
    is_offline_shop: Optional[bool] = Field(None, description="shop_id为0且is_offline_shop为true查询线下店铺单据")
    modified_begin: Optional[str] = Field(
        None,
        description="修改起始时间，和结束时间必须同时存在，时间间隔不能超过七天，与线上单号不能同时"
        "为空；tips:用时间条件查询由于查询中数据存在变动的可能会由于排序问题引"
        "发分页查询漏单的问题，建议使用ts时间戳增量查询的方式",
    )
    modified_end: Optional[str] = Field(
        None, description="修改结束时间，和起始时间必须同时存在，时间间隔不能超过七天，与线上单号不能同时为空"
    )
    so_ids: Optional[str] = Field(None, description="指定线上订单号，和时间段不能同时为空")
    shop_buyer_ids: Optional[str] = Field(None, description="指定买家账号，最多50")
    page_index: Optional[int] = Field(None, description="第几页，从第一页开始，默认1")
    page_size: Optional[int] = Field(None, description="每页多少条，默认30，最大50")
    o_ids: Optional[str] = Field(None, description="指定内部单号，和时间段不能同时为空")
    as_ids: Optional[str] = Field(None, description="售后单号（商家维度下售后唯一值），和时间段不能同时为空")
    status: Optional[str] = Field(
        None, description="售后单状态（WaitConfirm:待确认,Confirmed:已确认,Cancelled:作废,Merged:被合并）"
    )
    start_ts: Optional[int] = Field(None, description="时间戳，sql server中的行版本号，该字段查询防止分页过程中漏单")
    is_get_total: Optional[str] = Field(
        None, description="是否查询总条数默认true，如果使用start_ts查询，该值传false否则影响查询效率"
    )
    good_status: Optional[str] = Field(
        None,
        description="货物状态（BUYER_NOT_RECEIVED:买家未收到货,BUYER_RECEIVED:买家已收到货,"
        "BUYER_RETURNED_GOODS:买家已退货,SELLER_RECEIVED:卖家已收到退货）",
    )
    type: Optional[str] = Field(None, description="售后类型，普通退货，其它，拒收退货，仅退款，投诉，补发，换货，维修")
    owner_co_id: Optional[str] = Field(None, description="货主编码")


@make_fields_optional
class AfterSaleItem(BaseModel):
    amount: str
    as_id: int
    asi_id: int
    i_id: str
    name: str
    pic: str
    price: str
    properties_value: str
    qty: int
    r_qty: int = 0  # 聚水潭偶尔会不返回
    receive_date: str
    remark: str
    sku_id: str
    sku_type: str
    type: str
    is_gift: bool

    qty_diff: int  # 不是API返回的


@make_fields_optional
class AfterSaleOrder(BaseModel):
    as_date: str
    as_id: int
    confirm_date: str
    created: str
    creator_name: str
    free_amount: str
    freight: str
    good_status: str
    items: list[AfterSaleItem]
    labels: str
    logistics_company: str
    l_id: str
    modified: str
    node: str
    o_id: int
    order_label: str
    order_status: str
    order_type: str
    outer_as_id: str
    payment: str
    question_type: str
    refund: str
    refund_version: str
    remark: str
    shop_freight: str
    shop_id: str
    shop_name: str
    shop_status: str
    so_id: str
    status: str
    readable_status: str
    ts: str
    type: str
    urefund: str
    warehouse: str
    wh_id: int
    wms_co_id: int
    drp_co_id_to: int
    # ---
    original_l_id: str  # 原订单的物流单号, 这个字段接口没有，需要在订单接口里面去查

    original_items: List[AfterSaleItem]  # 不是API返回的
    qty_diff: int  # 不是API返回的
    qty_sum: int  # 不是API返回的
    r_qty_sum: int  # 不是API返回的
    readable_good_status: str  # 不是API返回的
    supplier_name: str  # 不是API返回的

    @root_validator(pre=True)
    def generate_good_status_readable_str(cls, values):
        status = values.get("good_status")
        status_map = {
            "BUYER_NOT_RECEIVED": "买家未收到货",
            "BUYER_RECEIVED": "买家已收到货",
            "BUYER_RETURNED_GOODS": "买家已退货",
            "SELLER_RECEIVED": "卖家已收到退货",
        }
        values["readable_good_status"] = status_map.get(status) or ""
        return values


@make_fields_optional
class AfterSaleResponseData(BaseModel):
    data_count: int
    datas: list[AfterSaleOrder]
    has_next: bool
    page_count: int
    page_index: int
    page_size: int


@make_fields_optional
class OutOrderItem(BaseModel):
    combine_sku_id: str
    i_id: str
    ioi_id: int
    is_gift: bool
    name: str
    oi_id: int
    outer_oi_id: str
    properties_value: str
    qty: int
    raw_so_id: str
    sale_amount: float
    sale_base_price: float
    sale_price: float
    sku_id: str
    supplier_id: int

    batch_no: str
    supplier_name: str


@make_fields_optional
class Batch(BaseModel):
    batch_no: str
    sku_id: str
    qty: int
    supplier_id: int
    supplier_name: str


@make_fields_optional
class OutOrder(BaseModel):
    @make_fields_optional
    class SNInfo(BaseModel):
        sku_id: str
        sn: str

    business_staff: str
    buyer_tax_no: str
    co_id: int
    created: str
    drp_co_id_from: str
    free_amount: float
    freight: float
    io_date: str
    io_id: int
    is_cod: bool
    is_print: str
    is_print_express: str
    items: List[OutOrderItem]
    l_id: str
    labels: str
    lc_id: str
    logistics_company: str
    sns: list[SNInfo]
    modified: str
    o_id: int
    open_id: str
    order_staff_id: int
    order_staff_name: str
    order_type: str
    paid_amount: float
    pay_amount: float
    pay_date: str
    receiver_city: str
    receiver_country: str
    receiver_district: str
    receiver_state: str
    shop_buyer_id: str
    shop_id: int
    shop_name: str
    so_id: str
    status: str
    stock_enabled: str
    ts: int
    warehouse: str
    weight: float
    wms_co_id: int
    f_weight: float
    batchs: List[Batch]

    readable_status: str  # 非API提供
    wave_id: str  # 批次号
    picker_name: str  # 拣货员
    packer_name: str  # 打包员
    inspecter_name: str  # 验货员
    wms_co_name: str  # 仓库

    @root_validator(pre=True)
    def generate_all_readable_str(cls, values):
        status = values.get("status")
        status_map = {
            "WaitConfirm": "待出库",
            "Confirmed": "已出库",
            "Delete": "作废",
            "OuterConfirming": "外部发货中",
            "Cancelled": "取消",
        }
        values["readable_status"] = status_map.get(status) or status
        return values


@make_fields_optional
class OutOrdersQueryResp(BaseModel):
    has_next: bool
    page_index: int
    page_size: int
    page_count: int
    datas: List[OutOrder]
    data_count: int


class QuerySkuResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        page_index: int
        page_size: int
        data_count: int
        page_count: int
        has_next: bool
        datas: list[SkuQueryResp.SkuInfo]

    data: Data


@make_fields_optional
class SpuInfo(BaseModel):
    @make_fields_optional
    class SkuInfo(BaseModel):
        sku_id: str
        i_id: str
        name: str
        properties_value: str
        enabled: int

    i_id: str
    name: str
    skus: list[SkuInfo]


class QuerySpuResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        page_index: int
        page_size: int
        data_count: int
        page_count: int
        has_next: bool
        datas: list[SpuInfo]

    data: Data


@make_fields_optional
class SingleSkuInfoForCombine(BaseModel):
    qty: int = Field(description="子商品数量")
    combine_sku_id: str = Field(description="组合装商品编码")
    src_sku_id: str = Field(description="子商品编码")
    sale_price: float = Field(description="应占售价")


@make_fields_optional
class CombineSkuInfo(BaseModel):
    i_id: str  # outer spu id
    sku_id: str  # outer sku id
    name: str = Field(description="组合装商品名称")
    short_name: str = Field(description="组合装简称")
    properties_value: str
    pic: str = Field(description="图片地址")
    sale_price: float = Field(description="组合装售价")
    item_type: str = Field(description="商品属性")
    sku_qty: int = Field(description="子商品数量")
    cost_price: float = Field(description="成本价")
    items: list[SingleSkuInfoForCombine]

    enabled: int = Field(description="是否启用 1表示启用；0表示备用；-1表示禁用")


class QueryCombineSkuResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        page_index: int
        page_size: int
        data_count: int
        page_count: int
        has_next: bool
        datas: list[CombineSkuInfo]

    data: Data


class OpenOrdersOutSimpleQuery(BaseModel):
    shop_id: int | None = None
    so_ids: list[str] | None = None
    o_ids: list[str] | None = None
    modified_begin: str | None = None
    modified_end: str | None = None
    page_index: int = 1
    page_size: int = 100

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        kwargs["exclude_none"] = True
        return super().dict(**kwargs)

    def next_page(self):
        return self.copy(update={"page_index": self.page_index + 1})

    def to_qm_params(self):
        qm_params: dict[str, Any] = dict()
        if self.o_ids:
            qm_params["o_ids"] = ",".join(self.o_ids)
        elif self.so_ids:
            qm_params["so_ids"] = ",".join(self.so_ids)
        if self.shop_id:
            qm_params["shop_id"] = self.shop_id
        if self.modified_begin:
            qm_params["start_time"] = self.modified_begin
        if self.modified_end:
            qm_params["end_time"] = self.modified_end
        return qm_params

    @make_fields_optional
    class Response(BaseModel):
        code: int
        msg: str
        data: OutOrdersQueryResp
