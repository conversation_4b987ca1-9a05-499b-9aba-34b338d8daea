from __future__ import annotations

from dataclasses import dataclass
from dataclasses import field
from dataclasses import replace
from typing import Any

from src.core import Value
from src.core import Var


@dataclass
class ProjectionRule:
    # 目标模板的表单 symbol
    target: Value
    # symbol 的值，可以是引用当前表单值 var, 可以是常量 const
    source: Value
    children: list[ProjectionRule] = field(default_factory=list)

    @property
    def children_with_relative_path(self):
        def process_nested(parent: ProjectionRule, children: list[ProjectionRule]):
            processed_children = []
            for raw_child in children:
                child = replace(raw_child)
                if child.children:
                    child.children = process_nested(child, child.children)
                match parent.source.qualifier, raw_child.source.qualifier:
                    case "var", "var":
                        source_indicators = parent.source.var.indicators
                        child_indicators = child.source.var.indicators
                        cursor = 0
                        for cursor, (parent_indicator, child_indicator) in enumerate(
                            zip(source_indicators, child_indicators, strict=False)
                        ):
                            if parent_indicator != child_indicator:
                                break
                        if cursor + 1 == len(source_indicators):
                            cursor += 1
                        relative_indicators = child_indicators[cursor:]
                        child.source.var = Var.init_by_indicators(relative_indicators)
                match parent.target.qualifier, raw_child.target.qualifier:
                    case "var", "var":
                        target_indicators = parent.target.var.indicators
                        child_indicators = child.target.var.indicators
                        cursor = 0
                        for cursor, (parent_indicator, child_indicator) in enumerate(
                            zip(target_indicators, child_indicators, strict=False)
                        ):
                            if parent_indicator != child_indicator:
                                break
                        if cursor + 1 == len(target_indicators):
                            cursor += 1
                        relative_indicators = child_indicators[cursor:]
                        child.target.var = Var.init_by_indicators(relative_indicators)
                processed_children.append(child)
            return processed_children

        return process_nested(self, self.children)


def parse_projection_rules(
    projection_rules: list[ProjectionRule],
) -> tuple[dict[str, str], dict[str, Any]]:
    from uuid import uuid4

    projection: dict[str, str] = dict()
    const_context = dict()
    for rule in projection_rules:
        if rule.children:
            child_projection, child_const_context = parse_projection_rules(rule.children)
            projection.update(child_projection)
            const_context.update(child_const_context)
        else:
            target_path = rule.target.var.path
            if rule.source.qualifier == "var":
                source_path = rule.source.var.path
                projection[source_path] = target_path
            elif rule.source.qualifier == "const":
                indicator = str(uuid4())
                const_context[indicator] = rule.source.const.value
                projection[f"const.{indicator}"] = target_path

    return projection, const_context


@dataclass
class ProjectionRuleAnalyzer:
    """
    功能:
        分析 projection rule 影响字段
        构建 schema 或字段依赖图
    """

    projection_rule: dict[str, str]

    def top_level_affected_fields(self):
        """提取 projection 中影响的最外层 target 字段"""
        from src.core.value import PathIndicator

        affected_fields: set[str] = set()
        for path in self.projection_rule.values():
            outer = PathIndicator.init_by_path(path.split(".")[0])
            affected_fields.add(outer.name)
        return list(affected_fields)
