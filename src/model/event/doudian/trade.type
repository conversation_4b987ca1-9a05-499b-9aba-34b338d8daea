@event(
    id="DOUDIAN_TRADE",
    label="抖店订单变更[抖音]",
    description="抖店订单变更后，会触发此消息（需要授权小柚子）",
    icon="DouYin",
    schedule_config={"job_timeout": 600, "schedule_cron": "@every 600s"}
)
type Trade struct {

    @label("订单/子订单")
    @component(id="order", type="order")
    fs_trade_no                         component.TradeNoList

    @label("商家备注")
    memo                                string

	@label("商品")
	@component(id="product", type="product")
	fs_product_list                     component.ProductList

}
