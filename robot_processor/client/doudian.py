import base64
import enum
import json
import time
import typing as t

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher
from cryptography.hazmat.primitives.ciphers import algorithms
from cryptography.hazmat.primitives.ciphers import modes
from loguru import logger
from pydantic import BaseModel
from pydantic import Field
from pydantic.types import Json
from result import Err
from result import Ok

from robot_metrics import Stats
from robot_processor.client.conf import app_config as config
from robot_processor.client.errors import DoudianCloudServiceError
from robot_processor.client.schema import DoudianAfterSaleListResp
from robot_processor.client.schema import DoudianAftersaleOperateResp
from robot_processor.client.schema import DoudianEvidence
from robot_processor.client.schema import DoudianOrderDetailSchema
from robot_processor.client.schema import DoudianReceiverInfoResp
from robot_processor.client_mixins import Session
from robot_processor.db import db
from robot_processor.shop.models import App
from robot_processor.shop.models import Grant<PERSON><PERSON><PERSON>
from robot_processor.shop.models import Shop
from robot_processor.shop.utils import bind_kiosk_auth
from robot_processor.utils import filter_none
from robot_processor.utils import make_fields_optional

_statsd_it = Stats.Client.timer(client_name="doudian-cloud-client")


class DoudianCloudClient:
    """
    和DoudianRpaClient的区别
    DoudianRpaClient基于RPA实现，此客户端基于抖店开放平台实现，依赖小柚子授权。
    - 支持卖家备注 √
    - 支持订单收货地址解密 √
    - 订单详情请使用trade_client查询 ×
    - 商品信息未实现，请使用DoudianRpaClient查询 ×
    """

    session = Session()

    def __init__(self) -> None:
        self.backend = default_backend()
        self.b_key = bytes.fromhex(self.aes_key)

    def _encrypt(self, raw: str) -> str:
        padder = padding.PKCS7(algorithms.AES(self.b_key).block_size).padder()
        padded_data = padder.update(raw.encode()) + padder.finalize()
        encryptor = Cipher(algorithms.AES(self.b_key), modes.ECB(), self.backend).encryptor()
        encrypted = encryptor.update(padded_data) + encryptor.finalize()
        return base64.urlsafe_b64encode(encrypted).decode()

    def _decrypt(self, ciphered: str) -> str:
        data = base64.urlsafe_b64decode(ciphered.encode())
        decryptor = Cipher(algorithms.AES(self.b_key), modes.ECB(), self.backend).decryptor()
        unpadder = padding.PKCS7(algorithms.AES(self.b_key).block_size).unpadder()
        decrypted_data = decryptor.update(data)
        unpadded = unpadder.update(decrypted_data) + unpadder.finalize()
        return unpadded.decode()

    @property
    def host(self) -> str:
        return config.DOUDIAN_CLOUD_HOST

    @property
    def token(self) -> str:
        return config.DOUDIAN_CLOUD_TOKEN

    @property
    def aes_key(self) -> str:
        return config.DOUDIAN_CLOUD_AES_SECRET

    @property
    def common_headers(self) -> t.Dict[str, str]:
        return {"Authentication-Token": self.token}

    def _wrap_payload(self, biz_params: t.Dict[str, t.Any]) -> t.Dict[str, str]:
        data_str = json.dumps(biz_params, ensure_ascii=False)
        return {"payload": self._encrypt(data_str)}

    @classmethod
    def refresh_token_on_need(cls, gr: GrantRecord):
        from rpa.doudian.schemas import TokenRefreshParam

        now_ms = int(time.time() * 1000)
        # 选取离过期还差 30 min 内的授权做刷新
        expire_window = 1800 * 1000
        # 参考 https://op.jinritemai.com/docs/api-docs/162/1601
        if not gr.expires_at_ms or gr.expires_at_ms < now_ms + expire_window:
            req = TokenRefreshParam(refresh_token=gr.refresh_token)
            openapi = gr.app.doudian_openapi_client
            res = openapi.token_refresh(req, gr.access_token)
            match res:
                case Err(exc):
                    if gr.expires_at_ms and gr.expires_at_ms < now_ms:
                        logger.opt(exception=exc).error("刷新抖店 token {} 失败, 且当前 token 已过期.", gr.id)
                        raise DoudianCloudServiceError(f"刷新抖店 access token 失败: {exc}")
                    else:
                        logger.opt(exception=exc).warning("刷新抖店 token 失败, 将继续使用老 token {}", gr.id)
                case Ok(refresh_res):
                    gr.access_token = refresh_res.access_token
                    gr.refresh_token = refresh_res.refresh_token
                    new_expire_time = time.time() + refresh_res.expires_in
                    gr.expires_at_ms = int(new_expire_time * 1000)
                    db.session.commit()
                    bind_kiosk_auth(grant_record=gr)
                    logger.info("已刷新抖店 access token {}，有效期延长至 {}", gr.id, time.ctime(new_expire_time))

    def _get_access_token(self, store_id: str) -> str:
        app = App.DOUDIAN_XYZ
        # 取最新的一条记录。
        # 因为可能会存在先授权，后开店铺，然后再次授权时发现 access_token 不一致，又新建了一条授权记录的情况。
        gr = (
            GrantRecord.query.join(Shop, Shop.id == GrantRecord.shop_id)
            .filter(
                Shop.sid == store_id,
                GrantRecord.app == app,  # 小柚子授权
            )
            .order_by(GrantRecord.id.desc())
            .first()
        )
        if not gr or not gr.access_token:
            raise DoudianCloudServiceError(f"缺失抖店小柚子授权: {store_id}")
        self.refresh_token_on_need(gr)
        return gr.access_token

    def _request(
        self, path: str, store_id: str, biz_params: t.Dict[str, t.Any], need_decrypt=True
    ) -> t.Dict[str, t.Any]:
        access_token = self._get_access_token(store_id)
        logger.info("call doudian cloud - path: {}, body: {}", path, biz_params)
        biz_params.update({"access_token": access_token})
        body = self._wrap_payload(biz_params)
        url = f"{self.host}/{path}"
        r = self.session.post(url, json=body, headers=self.common_headers)
        r.raise_for_status()
        resp = r.json()
        logger.info("call doudian cloud return - {}", resp)
        if resp.get("success") is not True:
            raise DoudianCloudServiceError(f"调用抖店服务失败: {resp.get('error')}")
        if need_decrypt:
            if data := resp.get("data"):
                decrypted = self._decrypt(data)
                logger.info("call doudian resp decrypted - {}", decrypted)
                resp["decrypted_data"] = json.loads(decrypted)
            else:
                resp["decrypted_data"] = None
        return resp

    @_statsd_it
    def update_memo(self, *, store_id: str, order_id: str, memo: str, star: t.Optional[str] = None):
        payload = {"order_id": order_id, "remark": memo, "is_add_star": star is not None, "star": star}
        self._request("/order/add_remark", store_id, payload)

    @_statsd_it
    def receiver_info(
        self,
        *,
        store_id: str,
        order_id: str,
    ) -> DoudianReceiverInfoResp:
        payload = {"order_id": order_id}
        resp = self._request("/order/receiver_info", store_id, payload)
        return DoudianReceiverInfoResp.parse_obj(resp["decrypted_data"])

    @_statsd_it
    def get_order_detail(self, *, store_id: str, order_id: str) -> DoudianOrderDetailSchema:
        payload = {"order_id": order_id}
        resp = self._request("/order/detail", store_id, payload)
        return DoudianOrderDetailSchema.parse_obj(resp["decrypted_data"])

    @_statsd_it
    def get_aftersale_list(
        self,
        *,
        store_id: str,
        update_start_time: int | None = None,
        update_end_time: int | None = None,
        aftersale_type: int | None = None,
        aftersale_id: str | None = None,
        order_id: str | None = None,
        page: int = 0,
        size: int = 10,
    ) -> DoudianAfterSaleListResp:
        payload = filter_none(
            {
                "update_start_time": update_start_time,
                "update_end_time": update_end_time,
                "order_by": ["update_time"],
                "aftersale_type": aftersale_type,
                "aftersale_id": aftersale_id,
                "order_id": order_id,
                "page": page,
                "size": size,
            }
        )
        resp = self._request("/aftersale/aftersale_list", store_id, payload)
        return DoudianAfterSaleListResp.parse_obj(resp["decrypted_data"])

    def get_aftersale_detail(self, *, store_id: str, after_sale_id: int, need_operation_record: bool = False):
        resp = self._request(
            "/aftersale/aftersale_detail",
            store_id,
            dict(after_sale_id=after_sale_id, need_operation_record=need_operation_record),
        )
        return DoudianAfterSalesDetailResponse.parse_obj(resp["decrypted_data"])

    def doudian_refund_agree(
        self, *, store_id: str, aftersale_id: str, operate_type: int
    ) -> DoudianAftersaleOperateResp:
        payload = {"type": operate_type, "items": [{"aftersale_id": aftersale_id}]}
        resp = self._request("/aftersale/aftersale_operate", store_id, payload)
        return DoudianAftersaleOperateResp.parse_obj(resp["decrypted_data"])

    def doudian_refund_refuse(
        self,
        *,
        store_id: str,
        aftersale_id: str,
        operate_type: int,
        evidence: t.List[DoudianEvidence],
        reject_reason_code: int,
        reason: str | None = None,
        remark: str | None = None,
    ) -> DoudianAftersaleOperateResp:
        payload = {
            "type": operate_type,
            "items": [
                {
                    "aftersale_id": aftersale_id,
                    "evidence": evidence,
                    "reject_reason_code": reject_reason_code,
                    "reason": reason,
                    "remark": remark,
                }
            ],
        }
        resp = self._request("/aftersale/aftersale_operate", store_id, payload)
        return DoudianAftersaleOperateResp.parse_obj(resp["decrypted_data"])

    def invoice_list(
        self,
        *,
        store_id,
        order_id=None,
        order_status=None,
        start_time=None,
        end_time=None,
        status=None,
        page_no=0,
        page_size=20,
        order_by="asc",
        order_field="create_time",
    ):
        """发票列表

        Args:
            store_id (str): 店铺ID
            order_id (str): 订单号
            order_status (DoudianOrderStatus): 订单状态
            start_time (int): 开始时间
            end_time (int): 结束时间
            status (DoudianInvoiceStatus): 开票状态
            page_no (int | None): 页数. Defaults to 0.
            page_size (int | None): 每页数量. Defaults to 20.
            order_by (str | None): 排序. Defaults to "asc".
            order_field (str | None): 排序字段. Defaults to "create_time".
        Returns:
            DoudianInvoiceListResponse: 发票列表
        """
        payload = filter_none(
            dict(
                order_id=order_id,
                order_status=order_status,
                start_time=start_time,
                end_time=end_time,
                status=status,
                page_no=page_no,
                page_size=page_size,
                order_by=order_by,
                order_field=order_field,
            )
        )
        raw_response = self._request("/order/invoiceList", store_id, payload, need_decrypt=False)
        raw_data = raw_response["data"]["data"]
        return DoudianInvoiceListResponse.validate(raw_data)

    def invoice_upload(self, *, store_id, order_id: str, receipt: str):
        payload = {"order_id": order_id, "receipt": receipt}
        raw_response = self._request("/order/invoiceUpload", store_id, payload, need_decrypt=False)
        raw_data = raw_response["data"]
        return DoudianInvoiceUploadResponse.validate(raw_data)

    def query_traces(self, *, store_id, logistics_code: str, track_no: str):
        payload = {"logistics_code": logistics_code, "track_no": track_no}
        raw_response = self._request("/order/traces", store_id, payload, need_decrypt=False)
        raw_data = raw_response["data"]
        return DoudianTracesResponse.validate(raw_data)


class DoudianInvoiceStatus(enum.IntEnum):
    """抖店订单开票状态"""

    PENDING = 1  # 待开票
    INVOICED = 2  # 已开票
    CLOSED = 3  # 已关闭

    @property
    def label(self):
        return {
            DoudianInvoiceStatus.PENDING: "待开票",
            DoudianInvoiceStatus.INVOICED: "已开票",
            DoudianInvoiceStatus.CLOSED: "已关闭",
        }[self]


class DoudianOrderStatus(enum.IntEnum):
    """订单状态"""

    WAIT_CONFIRM = 1  # 待确认/待支付（订单创建完毕）
    PAID = 105  # 已支付
    PREPARING = 2  # 备货中
    PART_SHIPPED = 101  # 部分发货
    SHIPPED = 3  # 已发货（全部发货）
    CANCELLED = 4  # 已取消
    COMPLETED = 5  # 已完成（已收货）


class DoudianInvoiceType(enum.IntEnum):
    """发票种类"""

    VAT_GENERAL = 2  # 增值税普通发票[电子]
    VAT_SPECIAL = 4  # 增值税专用发票[电子]
    VAT_GENERAL_PAPER = 1  # 增值税普通发票[纸质]
    VAT_SPECIAL_PAPER = 3  # 增值税专用发票[纸质]


class DoudianInvoiceStatusType(enum.IntEnum):
    """发票类型"""

    BLUE = 1  # 蓝票


class DoudianInvoiceTitleType(enum.IntEnum):
    """发票抬头类型"""

    INDIVIDUAL = 1  # 个人
    ENTERPRISE = 2  # 企业
    ELECTRONIC_INDIVIDUAL = 3  # 数电发票-个人
    ELECTRONIC_INSTITUTION = 4  # 数电发票-事业单位

    @property
    def buyer_type(self):
        from robot_processor.invoice.workflow.models import BuyerType

        return {
            DoudianInvoiceTitleType.INDIVIDUAL: BuyerType.INDIVIDUAL,
            DoudianInvoiceTitleType.ENTERPRISE: BuyerType.ENTERPRISE,
            DoudianInvoiceTitleType.ELECTRONIC_INDIVIDUAL: BuyerType.INDIVIDUAL,
            DoudianInvoiceTitleType.ELECTRONIC_INSTITUTION: BuyerType.ENTERPRISE,
        }[self]


class DoudianSkuSpec(BaseModel):
    Name: str
    Value: str


class DoudianInvoiceDetailValue(BaseModel):
    ProductId: str
    ProductCount: int
    ProductName: str
    ProductPrice: str
    SkuSpecs: list[DoudianSkuSpec]
    SkuCode: str

    def to_description(self):
        return ";".join(f"{spec.Name}:{spec.Value}" for spec in self.SkuSpecs)


class DoudianInvoiceEndReason(enum.IntEnum):
    """关闭原因"""

    NONE = 0
    REFUND_COMPLETED = 1


class DoudianInvoiceFile(BaseModel):
    """发票文件列表"""

    file_name: str = Field(description="文件名")
    tos_url: str = Field(description="文件url")


class DoudianInvoiceDetailExtra(BaseModel):
    post_amount: str | None = Field(description="运费开票金额")


class DoudianInvoice(BaseModel):
    registation_id: str = Field(description="开票id")
    shop_id: int = Field(description="店铺id")
    shop_order_id: str = Field(description="主订单号")
    invoice_type: DoudianInvoiceType = Field(description="发票种类")
    apply_time: int = Field(description="开票申请创建时间", examples="**********")
    invoice_status_type: DoudianInvoiceStatusType = Field(description="发票类型")
    title_type: DoudianInvoiceTitleType = Field(description="发票抬头类型")
    title: str = Field(description="发票抬头")
    tax_no: str | None = Field(description="税号")
    bank_name: str | None = Field(description="开户行")
    bank_no: str | None = Field(description="银行账号")
    company_address: str | None = Field(description="企业地址")
    company_mobile: str | None = Field(description="企业电话")
    invoice_amount: int = Field(description="开票金额，单位分")
    invoice_detail: Json[dict[str, DoudianInvoiceDetailValue]] | dict[str, DoudianInvoiceDetailValue] = Field(
        description="开票详情"
    )
    invoice_status: DoudianInvoiceStatus = Field(description="开票状态")
    order_status: DoudianOrderStatus = Field(description="订单状态")
    end_reason: DoudianInvoiceEndReason | None = Field(description="关闭原因")
    tos_url: str | None = Field(description="发票url")
    file_name: str | None = Field(description="发票文件名")
    upload_time: int | None = Field(description="首次发票上传时间")
    end_time: int | None = Field(description="关闭时间")
    latest_upload_time: int | None = Field(description="最新发票上传时间")
    invoice_file_list: list
    invoice_detail_extra: dict

    def get_invoice_type(self):
        from robot_processor.invoice.workflow.models import InvoiceType

        return {
            DoudianInvoiceType.VAT_GENERAL: InvoiceType.VAT_GENERAL,
            DoudianInvoiceType.VAT_SPECIAL: InvoiceType.VAT_SPECIAL,
            DoudianInvoiceType.VAT_GENERAL_PAPER: InvoiceType.VAT_GENERAL,
            DoudianInvoiceType.VAT_SPECIAL_PAPER: InvoiceType.VAT_SPECIAL,
        }[self.invoice_type]

    def get_issuing_type(self):
        from robot_processor.invoice.workflow.models import IssuingType

        return IssuingType[self.invoice_status_type.name]


class DoudianInvoiceListResponse(BaseModel):
    invoice_list: list[DoudianInvoice] = Field(default_factory=list)
    total: int


@make_fields_optional
class DoudianInvoiceUploadResponse(BaseModel):
    code: int
    err_no: int
    log_id: str
    message: str
    msg: str
    sub_code: str
    sub_msg: str


@make_fields_optional
class DoudianLogisticsInfo(BaseModel):
    tracking_no: str
    company_name: str
    company_code: str
    logistics_time: int  # 物流状态到达时间
    logistics_state: int  # 正向物流状态


class DoudianAfterSalesProcessInfo(BaseModel):
    """售后单信息

    References:
        https://op.jinritemai.com/docs/api-docs/17/1095
    """

    @make_fields_optional
    class AfterSaleInfo(BaseModel):
        """售后单信息"""

        after_sale_id: int
        after_sale_status: int
        after_sale_type: int
        apply_role: int  # 售后申请角色
        apply_time: int  # 申请时间
        refund_status: int  # 退款状态
        refund_total_amount: int  # 售后总金额（含运费）单位分
        reason: str  # 申请原因
        reason_remark: str  # 申请描述
        evidence: list[str]  # 买家申请退款图片凭证；仅支持图片，最大返回8张图片。
        got_pkg: int  # 买家是否收到货
        refund_time: int  # 退款时间

    @make_fields_optional
    class LogisticsInfo(BaseModel):
        """物流信息"""

        return_: DoudianLogisticsInfo = Field(alias="return")  # 买家退货物流信息
        exchange: DoudianLogisticsInfo  # 买家换货物流信息
        order: list[DoudianLogisticsInfo]  # 卖家发货物流信息
        resend: DoudianLogisticsInfo  # 卖家补发物流信息

    @make_fields_optional
    class RecordLog(BaseModel):
        operator: str
        time: str
        text: str
        action: str
        role: int

    after_sale_info: AfterSaleInfo
    logistics_info: LogisticsInfo
    record_logs_list: t.List[RecordLog]


class DoudianAfterSalesOrderInfo(BaseModel):
    @make_fields_optional
    class SkuOrderInfo(BaseModel):
        sku_order_id: int  # 子订单ID
        order_status: int
        pay_amount: int  # 实付金额，单位分
        item_quantity: int  # 订单件数
        product_name: str  # 商品名称
        product_id: int  # 商品ID
        product_image: str  # 商品图片
        shop_sku_code: str  # 商家sku自定义编码
        sku_id: int  # sku ID
        after_sale_item_count: int  # 商品单对应的售后数量
        sku_pay_amount: int  # 商品实际支付金额，单位分

    shop_order_id: int
    sku_order_infos: list[SkuOrderInfo]


class DoudianAfterSalesDetailResponse(BaseModel):
    process_info: DoudianAfterSalesProcessInfo
    order_info: DoudianAfterSalesOrderInfo


@make_fields_optional
class DoudianTrace(BaseModel):
    state: str
    state_description: str
    site_name: str
    timestamp: str
    content: str


@make_fields_optional
class DoudianTracesResponse(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        route__node_list: list[DoudianTrace]

    code: int
    log_id: str
    msg: str
    sub_code: str
    sub_msg: str
    data: Data


class DoudianTraceAction:
    GOT: t.Final = "已揽收"
    TRANSPORTING: t.Final = "运输中"
    DELIVERING: t.Final = "派送中"

    @classmethod
    def has_got_action(cls, traces: list[DoudianTrace]):
        """已揽收"""
        return any(cls.GOT in trace.state_description for trace in traces)

    @classmethod
    def only_got_action(cls, traces: list[DoudianTrace]):
        """仅揽收"""
        return cls.has_got_action(traces) and len(traces) == 1

    @classmethod
    def in_transporting_state(cls, traces: list[DoudianTrace]):
        """当前为运输中状态"""
        return cls.TRANSPORTING in traces[0].state_description

    @classmethod
    def in_delivering_state(cls, traces: list[DoudianTrace]):
        """当前为派送中状态"""
        return cls.DELIVERING in traces[0].state_description
