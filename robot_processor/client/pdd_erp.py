from dataclasses import dataclass
from dataclasses import field
from enum import IntEnum
from json import dumps as json_dumps
from typing import ClassVar
from typing import Generic
from typing import TypeVar

from cryptography.fernet import Fernet
from lepollo import ApolloConfig
from lepollo import get_config
from loguru import logger
from requests_opentracing import SessionTracing
from result import Err
from result import Ok

from robot_processor.logging import to_log
from robot_processor.utils import DataclassMixin
from robot_processor.utils import tap


class PddErpError(Exception):
    pass


class PddErpHttpError(PddErpError):
    def __init__(self, status_code, content, body):
        self.status_code = status_code
        self.content = content
        self.body = body
        message = f"[{body['type']}] request: {to_log(body)} response: {status_code} {content}"
        super().__init__(message)


class PddErpBizError(PddErpError):
    def __init__(self, error_response: dict, body):
        error_msg = error_response.get("error_msg")
        self.sub_msg = error_response.get("sub_msg")
        self.error_code = error_response.get("error_code")
        self.error_response = error_response
        self.body = body
        message = f"[{body['type']}] error: {error_msg} request: {to_log(body)} response: {to_log(error_response)}"
        super().__init__(message)


class PddErpConfig(ApolloConfig):
    __namespace__ = "client"

    PDD_ERP_CRYPT_KEY: str = "o5UdxFvynjztuOJw97TrxAHN0yN2evYnGXn1k8Oed_g="
    PDD_ERP_CLOUD_HOST: str
    PDD_ERP_CLOUD_TOKEN: str


conf = get_config(config_class=PddErpConfig)

T = TypeVar("T", bound=DataclassMixin)


class ForwardMixin(DataclassMixin, Generic[T]):
    Response: ClassVar[type[T]]  # type: ignore[misc]

    def execute(self) -> Ok[T] | Err[PddErpError]:
        return pdd_erp_client.request(self.to_dict()).map(self.Response.from_dict)


class PddInvoiceApplicationStatus(IntEnum):
    REJECTED = 0  # 已拒绝
    PENDING = 1  # 申请中
    APPROVED = 2  # 已同意[DEPRECATED]
    FINISHED = 3  # 已回传

    @property
    def label(self):
        return {
            PddInvoiceApplicationStatus.REJECTED: "已拒绝",
            PddInvoiceApplicationStatus.PENDING: "申请中",
            PddInvoiceApplicationStatus.APPROVED: "已同意",
            PddInvoiceApplicationStatus.FINISHED: "已回传",
        }[self]


@dataclass
class PddInvoiceApplicationQuery(ForwardMixin["PddInvoiceApplicationQuery.Response"]):
    type: str = field(default="pdd.invoice.application.query", init=False)
    access_token: str
    order_sn: str | None = None  # 订单号；订单号和申请时间必填其一
    page: int = 1  # 页码，默认1
    page_size: int = 50  # 每页返回数目，默认50
    quality_goods_invoice: int | None = None  # 是否正品发票 0=非正品发票 1=是正品发票
    status: int | None = None  # 申请状态：0-已拒绝，1-申请中，2-已同意
    update_end_time: int | None = None  # 申请结束时间, 时间戳（单位毫秒，查询时间间隔不可超过15天）
    update_start_time: int | None = None  # 申请开始时间, 时间戳（单位毫秒，查询时间间隔不可超过15天）

    @dataclass
    class Response(DataclassMixin):
        @dataclass
        class InvoiceApplicationQueryResponse(DataclassMixin):
            @dataclass
            class InvoiceApplicationQueryItem(DataclassMixin):
                application_status: PddInvoiceApplicationStatus  # 申请状态：0-已拒绝，1-申请中，2-已同意
                apply_time: int  # 申请时间
                business_type: int  # 抬头类型：0-个人，1-企业
                invoice_amount: str  # 开票金额，单位：分
                invoice_kind: int  # 发票种类：0-电子，1-纸质，2-专票；目前只支持0和2
                invoice_type: int  # 发票类型：0-蓝票，1-红票
                invoice_way: int  # 开票方式 0=手动开票,1=自动开票
                mall_id: int  # 店铺id
                memo: str  # 备注
                order_sn: str  # 订单号
                payer_account: str | None  # （企业抬头）开户账号
                payer_address: str | None  # （企业抬头）地址
                payer_bank: str | None  # （企业抬头）开户银行
                payer_name: str | None  # 发票抬头
                payer_phone: str | None  # （企业抬头）电话
                payer_register_no: str | None  # 企业税号，抬头为企业类型必填
                quality_goods_invoice: int  # 是否正品发票：0=非正品发票，1=正品发票
                reason: str | None  # 驳回原因
                sum_price: str | None  # 不含税金额，暂为null
                sum_tax: str | None  # 总税额，暂为null
                tax_rate: str | None  # 税率，暂为null
                trigger_status: int  # 开票申请触发类型：1-申请开票，2-改抬头

                def get_buyer_type(self):
                    from robot_processor.invoice.workflow.models import BuyerType

                    return {
                        0: BuyerType.INDIVIDUAL,
                        1: BuyerType.ENTERPRISE,
                    }[self.business_type]

                def get_invoice_type(self):
                    from robot_processor.invoice.workflow.models import InvoiceType

                    return {0: InvoiceType.VAT_GENERAL, 2: InvoiceType.VAT_SPECIAL}[self.invoice_kind]

                def get_issuing_type(self):
                    from robot_processor.invoice.workflow.models import IssuingType

                    return {
                        0: IssuingType.BLUE,
                        1: IssuingType.RED,
                    }[self.invoice_type]

            invoice_application_list: list[InvoiceApplicationQueryItem]

        invoice_application_query_response: InvoiceApplicationQueryResponse

        @classmethod
        def from_dict(cls, data, config=None):
            from dacite import Config

            config = config or Config()
            config.cast.append(PddInvoiceApplicationStatus)
            return super().from_dict(data, config)


@dataclass(kw_only=True)
class PddInvoiceDetailUpload(ForwardMixin["PddInvoiceDetailUpload.Response"]):
    type: str = field(default="pdd.invoice.detail.upload", init=False)
    invoice_kind: int = field(default=0, init=False)
    invoice_type: int = field(default=0, init=False)
    access_token: str
    order_sn: str
    business_type: int
    payee_operator: str
    payer_name: str
    payer_bank: str | None = None
    payer_account: str | None = None
    payer_address: str | None = None
    payer_phone: str | None = None
    invoice_time: int
    sum_price: int  # 不含税金额，单位：分
    sum_tax: int  # 总税额，单位：分
    tax_rate: int  # 税率
    invoice_code: str
    invoice_no: str
    invoice_file_content: str
    invoice_amount: int  # 单位：分
    payer_register_no: str | None

    def init_payer_from_apply_info(self, apply_info: dict):
        # 抬头字段规则 https://open.pinduoduo.com/application/document/announcement?id=108
        if payer_bank := apply_info.get("payer_bank"):
            self.payer_bank = payer_bank
        if payer_account := apply_info.get("payer_account"):
            self.payer_account = payer_account
        if payer_address := apply_info.get("payer_address"):
            self.payer_address = payer_address
        if payer_phone := apply_info.get("payer_phone"):
            self.payer_phone = payer_phone
        return self

    @dataclass
    class Response(DataclassMixin):
        @dataclass
        class InvoiceDetailUploadResponse(DataclassMixin):
            serial_no: str

        invoice_detail_upload_response: InvoiceDetailUploadResponse


@dataclass
class PddOrderInformationGet(ForwardMixin["PddOrderInformationGet.Response"]):
    type: str = field(default="pdd.order.information.get", init=False)
    access_token: str
    order_sn: str

    @dataclass
    class Response(DataclassMixin):
        @dataclass
        class OrderInfoGetResponse(DataclassMixin):
            @dataclass
            class OrderInfo(DataclassMixin):
                @dataclass
                class ItemListItem(DataclassMixin):
                    goods_name: str  # 商品名称
                    goods_count: int  # 商品数量
                    goods_id: int  # 商品编号
                    outer_goods_id: str  # 商家外部编码（商品）
                    sku_id: int  # 商品规格编码
                    outer_id: str  # 商家外部编码（sku）
                    goods_price: float  # 商品销售价格
                    goods_spec: str  # 商品规格，使用（规格值1,规格值2）组合作为sku的表示，中间以英文逗号隔开
                    goods_img: str  # 商品图片

                item_list: list[ItemListItem]

            order_info: OrderInfo

        order_info_get_response: OrderInfoGetResponse


class PddErpClient:
    def __init__(self):
        self.session = SessionTracing()
        self.cipher_suite = Fernet(conf.PDD_ERP_CRYPT_KEY.encode())
        self.auth_headers = {"Authorization": conf.PDD_ERP_CLOUD_TOKEN}

    def request(self, data):
        data_for_encrypt = dict(action="forward", body=data)
        encrypted_data = self.cipher_suite.encrypt(json_dumps(data_for_encrypt, ensure_ascii=False).encode()).decode()
        request_body = {"encrypt": encrypted_data}
        response = self.session.post(
            conf.PDD_ERP_CLOUD_HOST + "/open",
            json=request_body,
            headers=self.auth_headers,
        )
        if response.status_code != 200:
            return Err(PddErpHttpError(response.status_code, response.content, data)).map_err(
                tap(lambda e: logger.error(str(e)))
            )
        response_data: dict = response.json()
        if "error_response" in response_data:
            return Err(PddErpBizError(response_data["error_response"], data)).map_err(
                tap(lambda e: logger.error(str(e)))
            )
        logger.info(f"[{data['type']}] response: {to_log(response_data)} body: {to_log(data)}")
        return Ok(response_data)


pdd_erp_client = PddErpClient()
