import json
from typing import Dict
from typing import Optional

from aliyunsdkaddress_purification.request.v20191118.ExtractExpressRequest import ExtractExpressRequest
from aliyunsdkcore.client import AcsClient
from flask import Flask
from loguru import logger
from pydantic import BaseModel
from pydantic import Field


class ExtractExpressResult(BaseModel):  # type: ignore[no-redef] #
    receiver_country: Optional[str] = Field(default="中国")
    receiver_zip: Optional[str] = Field(default="")
    receiver_state: Optional[str] = Field(default="", alias="prov")
    receiver_city: Optional[str] = Field(default="", alias="city")
    receiver_district: Optional[str] = Field(default="", alias="district")
    receiver_town: Optional[str] = Field(default="", alias="town")
    receiver_address: Optional[str] = Field(default="", alias="addr_info")
    receiver_name: Optional[str] = Field(default="", alias="per")
    receiver_mobile: Optional[str] = Field(default="", alias="tel")
    receiver_phone: Optional[str] = Field(default="", alias="tel")

    def to_bill(self) -> dict[str, str]:
        return {
            "state": self.receiver_state or "",
            "city": self.receiver_city or "",
            "zone": self.receiver_district or "",
            "town": self.receiver_town or "",
            "address": self.receiver_address or "",
            "mobile": self.receiver_mobile or "",
            "name": self.receiver_name or "",
        }

    def to_address(self) -> dict[str, str]:
        return {
            "state": self.receiver_state or "",
            "city": self.receiver_city or "",
            "zone": self.receiver_district or "",
            "town": self.receiver_town or "",
            "address": self.receiver_address or "",
        }


class AliyunAddrpClient:
    def __init__(self) -> None:
        # 阿里云 AccessKey ID
        self.access_key_id: str = ""
        # 阿里云 AccessKey Secret
        self.access_key_secret: str = ""
        # 阿里云 应用的 AppKey
        self.app_key: str = ""
        self._client: Optional[AcsClient] = None

    def init_app(self, app: Flask) -> None:
        """
        初始化配置。
        :param app:
        :return:
        """
        self.access_key_id = app.config.get("EXPRESS_ACCESS_KEY_ID", "")
        self.access_key_secret = app.config.get("EXPRESS_ACCESS_KEY_SECRET", "")
        self.app_key = app.config.get("EXPRESS_PROJECT_APP_KEY", "")
        self._client = AcsClient(ak=self.access_key_id, secret=self.access_key_secret)

    @property
    def client(self) -> AcsClient:
        if self._client is None:
            raise Exception("client 尚未完成初始化")
        return self._client

    def extract_express_to_result(self, express: str) -> ExtractExpressResult:
        """
        解析物流参数。
        :param express:     需要解析的物流面单信息。
        :return:
        """
        # 配置必需参数。
        request = ExtractExpressRequest()
        request.set_ServiceCode("addrp")
        request.set_AppKey(self.app_key)
        # 传入需要解析的文本内容。
        request.set_Text(express)
        try:
            # 请求阿里云物理面单解析。
            response = self.client.do_action_with_exception(request)
            logger.info("addrp response: {}", response)
            resp_obj = json.loads(response)
            # 由于 Data 是一个 JSON 字符串，所以还需要解析一次。
            data = json.loads(resp_obj.get("Data") or "{}")
        except Exception as e:
            logger.error("物流面单信息解析失败：{}", e)
            return ExtractExpressResult(**{})
        # 格式化结果数据。
        message = data.get("express_extract") or {}
        return ExtractExpressResult(**message)

    def extract_express(self, express: str) -> Dict[str, str]:
        result = self.extract_express_to_result(express)
        return result.dict()
