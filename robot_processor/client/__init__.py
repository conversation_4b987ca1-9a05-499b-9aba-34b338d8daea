import redis as _redis
from loguru import logger

from robot_processor.client.oss import FileOSSClient
from robot_processor.client.oss import ImageOSSClient
from robot_processor.client.oss import OSSClient
from robot_processor.client.oss import RpaControlOSSClient
from robot_processor.client.oss import VideoOSSClient
from robot_processor.signals import booting

from ._token_bucket_limiter import TokenBucket<PERSON>imiter
from .action import ActionClient
from .aliyun_addrp import AliyunAddrpClient
from .asgard import AsgardClient
from .buyer import BuyerClient
from .chat import ChatClient
from .chat_history import ChatHistoryClient
from .conf import app_config
from .doudian import DoudianCloudClient
from .erp_item import ErpItemClient
from .extra_api import api
from .global_search import GlobalSearchClient
from .heart_beat import HeartBeat
from .item import ItemClient
from .jd import JdLyzrClient
from .jd_sdk import JdSDK
from .kiosk import KioskClient
from .kuaishou import KuaishouClient
from .limiter import Limiter
from .logistics import LogisticsClient
from .logistics_clients.sf_domain import SFClient
from .logistics_clients.sto_domain import <PERSON>oClient
from .pdd_bridge import Pdd<PERSON>ridgetClient
from .risk_control import RiskControlClient
from .rpa_client import RpaServerClient
from .sms import SMSClient
from .superbook import super_book_client
from .taobao import TaobaoClient
from .trade import TradeClient
from .transfer import RobotTransferClient
from .xiaohongshu_client import XiaohongshuClient

trade_client = TradeClient()
oss_client = OSSClient()
image_oss_client = ImageOSSClient()
video_oss_client = VideoOSSClient()
file_oss_client = FileOSSClient()
rpa_control_oss = RpaControlOSSClient()
taobao_client = TaobaoClient()
spzs_taobao_client = TaobaoClient()
hyws_taobao_client = TaobaoClient()
lxk_taobao_client = TaobaoClient()
sidebar_taobao_client = TaobaoClient()  # 千牛新版侧边栏
global_search_client = GlobalSearchClient()
item_client = ItemClient()
erp_item_client = ErpItemClient()
logistics_client = LogisticsClient()
robot_transfer = RobotTransferClient()
action_client = ActionClient()
kiosk_client = KioskClient()
heart_beat_client = HeartBeat()
buyer_client = BuyerClient()
chat_client = ChatClient()
asgard_client = AsgardClient()
limiter = Limiter()
risk_control_client = RiskControlClient()
rpa_client = RpaServerClient()
token_bucket_limiter = TokenBucketLimiter()
chat_history_client = ChatHistoryClient()
aliyun_addrp_client = AliyunAddrpClient()
pdd_bridge_client = PddBridgetClient()
doudian_cloud = DoudianCloudClient()
sto_client = StoClient()
sf_client = SFClient()
sms_client = SMSClient()
jd_lyzr_client = JdLyzrClient()
ks_client = KuaishouClient()
xiaohongshu_client = XiaohongshuClient()
jd_sdk = JdSDK()
redis = _redis.Redis(connection_pool=_redis.ConnectionPool.from_url(app_config.redis_client_connection_url))


@booting.connect
def init_app(app):
    trade_client.init_app(app)
    erp_item_client.init_app(app)
    item_client.init_app(app)
    logistics_client.init_app(app)
    limiter.init_app(app)
    token_bucket_limiter.init_app(app)
    global_search_client.init_app(app)
    aliyun_addrp_client.init_app(app)
    taobao_client.init_app(app_config.TAOBAO_APP_KEY, app_config.TAOBAO_APP_SECRET)
    spzs_taobao_client.init_app(app_config.SPZS_TAOBAO_APP_KEY, app_config.SPZS_TAOBAO_APP_SECRET)
    hyws_taobao_client.init_app(app_config.HYWS_TAOBAO_APP_KEY, app_config.HYWS_TAOBAO_APP_SECRET)
    lxk_taobao_client.init_app(app_config.LXK_TAOBAO_APP_KEY, app_config.LXK_TAOBAO_APP_SECRET)
    sidebar_taobao_client.init_app(app_config.TAOBAO_MIX_NICK_APP_KEY, app_config.TAOBAO_MIX_NICK_APP_SECRET)
    ks_client.init_app(app_config.KS_APP_KEY, app_config.KS_SIGN_SECRET)
    jd_sdk.init_app(app_config.JD_APP_KEY, app_config.JD_APP_SECRET)
    xiaohongshu_client.init_app(app_config.XHS_APP_KEY, app_config.XHS_APP_SECRET)
    # api
    app.register_blueprint(api)

    return super_book_client


def get_buyer_nick(sid, buyer_open_uid=None, tid=None):
    """尝试用各种方式获取买家昵称."""

    from robot_processor.client.conf import app_config
    from robot_processor.shop.models import Shop

    if not tid and not buyer_open_uid:
        logger.info("未传入tid和buyer_open_uid, 无法获取买家昵称")
        return None

    if tid and not buyer_open_uid:
        trade_info = trade_client.get_trade_by_tid(sid, tid)
        buyer_open_uid = trade_info.buyer_open_uid
        logger.info(f"通过tid {tid} 获取到买家open_uid: {buyer_open_uid}")

    if buyer_open_uid:
        # 尝试直接通过 open_uid 获取
        rpc_res = buyer_client.get_buyer_nick_by_open_uid(
            buyer_open_uid=buyer_open_uid, app_id=app_config.TAOBAO_APP_KEY
        )
        if rpc_res and rpc_res.buyer_nick:
            logger.info(f"通过open_uid {buyer_open_uid} 直接获取到买家昵称: {rpc_res.buyer_nick}")
            return rpc_res.buyer_nick
        # 从trade中获取
        if buyer_nick := trade_client.try_get_plaintext_nick_from_trade(buyer_open_uid=buyer_open_uid):
            logger.info(f"通过open_uid {buyer_open_uid} 从订单信息中获取到买家昵称: {buyer_nick}")
            return buyer_nick
        logger.warning(f"通过open_uid {buyer_open_uid} 未获取到买家昵称")
    if tid:
        shop = Shop.query.filter_by(sid=sid).first()
        seller_id = shop.seller_id if shop else None
        if seller_id:
            nick = buyer_client.get_buyer_nick_by_tid(tid, seller_id)
            if nick:
                logger.info(f"通过tid {tid} 和 seller_id: {seller_id} 查找到买家昵称: {nick}")
                return nick
            else:
                logger.warning(f"通过tid {tid} 和 seller_id: {seller_id} 未查找到买家昵称")
        else:
            logger.warning(f"未找到 {sid=} 店铺的 seller_id")
    return None
