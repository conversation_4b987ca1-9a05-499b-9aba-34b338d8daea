"""定义从erp获取商品的client"""

from typing import List
from typing import Optional

from google.protobuf.json_format import MessageToDict
from leyan_grpc.client.venice import GaiaStub
from leyan_proto.digismart.item.dgt_erp_item_pb2 import Condition
from leyan_proto.digismart.item.dgt_erp_item_pb2 import DgtGetErpSkuListRequest
from leyan_proto.digismart.item.dgt_erp_item_pb2 import MultiValues
from leyan_proto.digismart.item.dgt_erp_item_pb2 import Operator as PbOperator
from leyan_proto.digismart.item.dgt_erp_item_pb2 import QueryItemBrief
from leyan_proto.digismart.item.dgt_erp_item_pb2 import Relation
from leyan_proto.digismart.item.dgt_erp_item_pb2_grpc import DgtErpManifestServiceStub
from loguru import logger

from robot_metrics import Stats
from robot_processor.plugin.schema import ErpItemFilter
from robot_processor.plugin.schema import HighLevelSearchItemSchema
from robot_processor.plugin.schema import OperatorItem
from robot_processor.plugin.schema import PlatformItemSchema
from robot_processor.symbol_table.models import TypeEnum
from robot_processor.utils import unquote_jmespath

_statsd_it = Stats.Client.timer(client_name="erp-item-server")


class ErpItemClient:
    def __init__(self):
        self._skip = False
        self._timeout = 5
        self.endpoint = None
        self._stub = None

    def init_app(self, app):
        self._skip = app.config.get("ITEM_SKIP", False)
        self._timeout = int(app.config.get("ITEM_TIMEOUT", 5))
        self._stub = GaiaStub(DgtErpManifestServiceStub, "dgt-item-server", tracing_enabled=True)

    @property
    def client(self):
        return self._stub

    @_statsd_it
    def get_erp_sku_list(
        self,
        *,
        source: str,
        keywords: str,
        sku_ids: str,
        query_entity: Optional[HighLevelSearchItemSchema],
        items: Optional[List[PlatformItemSchema]],
        outer_sku_ids: str,
        sid: str,
        seller_nick: str,
        channel_type: str,
        access_token: str,
        page_no: int,
        page_size: int,
        query_type: Optional[str] = None,
        erp_item_filter: ErpItemFilter | None = None,
    ):
        if any([sku_ids, outer_sku_ids]):
            logger.warning("[DEPRECATED] 非法输入")
        # sku_ids和outer_sku_ids待废弃，使用新的items
        req = DgtGetErpSkuListRequest()
        req.keywords = keywords
        req.source = source
        req.sku_ids = sku_ids
        req.outer_sku_ids = outer_sku_ids
        req.sid = sid
        req.seller_nick = seller_nick
        req.channel_type = channel_type
        req.page_no = page_no
        req.page_size = page_size
        req.query_type = query_type or ""
        if query_entity:
            req.query_entity.title = query_entity.title
            req.query_entity.spu = query_entity.spu
            req.query_entity.sku = query_entity.sku
            req.query_entity.outer_sku = query_entity.outer_sku
            req.query_entity.outer_spu = query_entity.outer_spu
            req.query_entity.sku_props = query_entity.sku_props
        if erp_item_filter and erp_item_filter.enable:
            req.filter.relation = Relation.AND if erp_item_filter.relation == "and" else Relation.OR
            conditions = []
            for condition in erp_item_filter.conditions:
                first_operator: OperatorItem = condition.o[0]
                quoted_path = condition.a.var.path if condition.a.var is not None else ""
                unquoted_path = unquote_jmespath(quoted_path)
                if condition.b is None or condition.b.const is None:
                    continue
                elif condition.b.type_spec.type == TypeEnum.ARRAY:
                    filter_condition = Condition(
                        path=unquoted_path,
                        operator=PbOperator.Value(first_operator.operator),
                        multi=MultiValues(
                            values=[
                                str(i) if condition.b is not None and condition.b.const is not None else ""
                                for i in condition.b.const.value
                            ]
                        ),
                    )
                else:
                    filter_condition = Condition(
                        path=unquoted_path,
                        operator=PbOperator.Value(first_operator.operator),
                        single=(
                            str(condition.b.const.value)
                            if condition.b is not None and condition.b.const is not None
                            else ""
                        ),
                    )
                conditions.append(filter_condition)
            req.filter.conditions.extend(conditions)

        if items:
            req.items.extend([QueryItemBrief(**item.dict()) for item in items])
        if access_token:
            req.access_token = access_token

        try:
            resp = self.client.GetErpSkuList(req, timeout=self._timeout)
        except BaseException as exception:
            logger.exception(f"call getErpSkuList error: {exception}")
            return {}
        else:
            return MessageToDict(resp, including_default_value_fields=True, preserving_proto_field_name=True)
