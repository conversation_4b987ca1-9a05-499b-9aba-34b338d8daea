class PddBridgeError(Exception):
    pass


class PddServiceError(Exception):
    pass


class PddRateLimitError(PddServiceError):
    error_code = 70031

    def __init__(self, *, raw_response):
        super().__init__("调用过于频繁，请调整调用频率")
        self.raw_response = raw_response


class PddOrderNotFoundError(PddServiceError):
    pass


class DoudianCloudServiceError(Exception):
    pass


class XiaohongshuServiceError(Exception):
    pass
