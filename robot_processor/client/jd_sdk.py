import hashlib
import json
import time
from decimal import Decimal
from typing import List
from typing import Op<PERSON>
from typing import Type
from typing import TypeVar

from loguru import logger
from pydantic import BaseModel

from robot_processor.client_mixins import Session
from robot_processor.error.client_request import JdRequestError
from robot_processor.utils import make_fields_optional
from robot_processor.utils import unwrap_optional

RespT = TypeVar("RespT", bound=BaseModel)


class InvoiceData(BaseModel):
    orderType: int
    invoiceTime: str
    orderId: str
    totalPrice: str
    receiverName: str
    venderId: str
    ivcContentName: str
    invoiceType: int
    ivcTitle: str
    ivcContentType: int
    id: str
    receiverTaxNo: str
    pdfInfo: str
    status: int


class QueryInvoiceResult(BaseModel):
    total: int
    data: List[InvoiceData]
    success: bool


class QueryInvoiceResponse(BaseModel):
    code: str
    request_id: str
    queryinvoiceforown_result: QueryInvoiceResult


class OrderShouldInvoiceAmountDetail(BaseModel):
    detailType: int
    shouldInvoiceAmount: float
    productId: str
    price: float
    num: int
    productName: str


class InvoiceApplyData(BaseModel):
    sourceId: int
    consumerAddress: Optional[str]
    orderId: int
    venderId: int
    ivcContentName: Optional[str]
    applyId: int
    orderCompleteTime: Optional[str]
    shouldInvoiceAmount: Optional[float]
    invoiceTitleType: int
    consumerTaxId: Optional[str]
    invoiceType: int
    ivcContentType: Optional[int]
    applyTime: str
    invoiceStatus: int
    invoiceTitle: str
    consumerBankName: Optional[str]
    consumerPhone: Optional[str]
    consumerBankAccount: Optional[str]
    orderShouldInvoiceAmountDetailList: Optional[List[OrderShouldInvoiceAmountDetail]]

    def get_buyer_type(self):
        from robot_processor.invoice.workflow.models import BuyerType

        return {
            4: BuyerType.INDIVIDUAL,
            5: BuyerType.ENTERPRISE,
        }[self.invoiceTitleType]

    def get_invoice_status_zh(self):
        return {
            1: "待开票",
            3: "开票中",
            4: "开票成功",
            5: "开票失败",
            6: "冲红中",
            7: "冲红成功",
            8: "冲红失败",
            9: "已驳回",
            11: "蓝票审核失败",
            12: "红票审核失败",
            13: "待换开",
            14: "换开中",
            15: "换开驳回",
            16: "退款关闭",
            17: "驳回关闭",
        }[self.invoiceStatus]

    def get_invoice_type(self):
        from robot_processor.invoice.workflow.models import InvoiceType

        return {
            1: InvoiceType.VAT_GENERAL,
            2: InvoiceType.VAT_GENERAL,
            3: InvoiceType.VAT_GENERAL,
            4: InvoiceType.VAT_SPECIAL,
        }[self.invoiceType]

    def get_issuing_type(self):
        from robot_processor.invoice.workflow.models import IssuingType

        if Decimal(unwrap_optional(self.shouldInvoiceAmount)) >= 0:
            return IssuingType.BLUE
        else:
            return IssuingType.RED


@make_fields_optional
class Response(BaseModel):
    code: str
    data: Optional[InvoiceApplyData]
    success: bool
    message: Optional[str]


class JingdongPopCinvoiceApplyOrderResponse(BaseModel):
    code: str
    request_id: str
    response: Response


class BlueInvoiceUploadReq(BaseModel):
    orderId: str
    receiverTaxNo: str
    receiverName: str
    invoiceNo: str
    ivcTitle: str
    totalPrice: str
    invoiceTime: str
    pdfInfo: str


class ApplyInvoiceForOwnResult(BaseModel):
    message: Optional[str]
    success: bool


class BlueInvoiceUploadResp(BaseModel):
    code: str
    request_id: str
    applyinvoiceforown_result: ApplyInvoiceForOwnResult


class JdSDK:
    session = Session()

    def __init__(self):
        self.app_key: str | None = None
        self.app_secret: str | None = None

    def init_app(self, app_key, app_secret):
        self.app_key = app_key
        self.app_secret = app_secret

    @property
    def endpoint(self):
        return "http://api.jd.com/routerjson"

    def _sign(self, data: dict):
        params = data.copy()
        str_to_sign = "".join(f"{key}{params[key]}" for key in sorted(params.keys()))
        app_secret = self.app_secret
        str_to_sign = f"{app_secret}{str_to_sign}{app_secret}"
        return hashlib.md5(str_to_sign.encode("utf8")).hexdigest().upper()

    def api_request(self, method: str, access_token, data: dict, resp_cls: Type[RespT]) -> RespT:
        data["method"] = method
        data["access_token"] = access_token
        data["360buy_param_json"] = json.dumps({})
        data["v"] = "2.0"
        data["timestamp"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        data["app_key"] = self.app_key
        sign = self._sign(data)
        data["sign"] = sign
        resp = self.session.post(self.endpoint, data=data)
        logger.info(f"jd resp: {resp.text}")
        result = resp.json()
        jd_resp = next(iter(result.values()))
        if jd_resp["code"] != "0":
            raise JdRequestError(req=data, res=result, message=result.get("errorMessage"))
        return resp_cls.parse_obj(jd_resp)

    def blue_invoice_upload(self, access_token: str, req: BlueInvoiceUploadReq):
        return self.api_request(
            "jingdong.pop.invoice.self.apply",
            access_token,
            req.dict(),
            BlueInvoiceUploadResp,
        )

    def query_invoice_apply(self, access_token: str, order_id: str):
        return self.api_request(
            "jingdong.pop.cinvoice.apply.order",
            access_token,
            {"orderId": order_id},
            JingdongPopCinvoiceApplyOrderResponse,
        )
