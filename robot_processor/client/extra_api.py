import json
from base64 import b64decode, b64encode

from Crypto.Cipher import AES
from flask import Blueprint, request, current_app
from loguru import logger

from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.client.logistics_clients.logistics_client import \
    AbstractLogisticsIntercept

api = Blueprint("client-extra-api", __name__)


@api.post('/v1/<logistics_type>/intercept/callback')
def logistics_webhook(logistics_type: str):
    logger.info(
        "收到物流拦截下发回执，物流公司为: {}, 表单信息为 {}, headers 信息为 {}, data 信息为 {}".format(
            logistics_type,
            request.form,
            request.headers,
            request.get_data().decode('utf-8'),
        ))

    try:
        formatted_logistics_type = LogisticsType(logistics_type)
    except Exception as e:
        logger.error(f"接收到未记录在案的物流公司信息：{logistics_type}, {e}")
        return

    logistics_domain = AbstractLogisticsIntercept(
        formatted_logistics_type).dispatch()

    match formatted_logistics_type:
        case LogisticsType.YTO:
            logistics_domain.event_callback(body=request.get_json())
        case _:
            logistics_domain.event_callback(body=request.form)
    return logistics_domain.event_callback_response()


@api.post('/v1/jd/service/<org_id>/push')
def jd_service_webhook(org_id: str):
    logger.info(
        "收到推送，表单信息为 {}, headers 信息为 {}, data 信息为 {}".format(
            request.form,
            request.headers,
            request.get_data().decode('utf-8'),
        ))
    return dict(resultCode="1", resultMsg="成功")


@api.post('/v1/lbdj/<org_id>/push')
def lbdj_webhook(org_id: str):
    logger.info(
        "收到推送，表单信息为 {}, headers 信息为 {}, data 信息为 {}".format(
            request.form,
            request.headers,
            request.get_data().decode('utf-8'),
        ))

    luban_credentials = current_app.config.get(
        "LUBAN_CREDENTIALS",
        {"3043": {"access_key": "8zMnDNYKqxp7c1un", "business_id": "352029"}})
    credential = luban_credentials.get(org_id)
    if not credential:
        logger.warning("无鲁班到家授权")
        return
    access_key = credential["access_key"]
    cryptor = AES.new(access_key.encode('utf-8'), AES.MODE_CBC,
                      access_key.encode('utf-8'))

    data = json.loads(request.get_data().decode('utf-8'))
    encrypted_data = b64decode(data["data"])
    original = cryptor.decrypt(encrypted_data)
    original_string = original.decode('utf-8').rstrip('\x00')
    data_json = json.loads(original_string)
    ret = []
    for order in data_json:
        ret.append(
            {
                "code": 200,
                "msg": "sussess 更新接单信息成功",
                "id": order["id"]
            }
        )
    ret_str = json.dumps(ret, separators=(",", ":"), ensure_ascii=True)

    block_size = AES.block_size
    data_bytes = ret_str.encode('utf-8')
    plaintext_length = len(data_bytes)

    if plaintext_length % block_size != 0:
        plaintext_length += (block_size - (plaintext_length % block_size))

    plaintext = bytearray(plaintext_length)
    plaintext[:len(data_bytes)] = data_bytes
    cryptor = AES.new(access_key.encode('utf-8'), AES.MODE_CBC,
                      access_key.encode('utf-8'))
    encrypted = b64encode(cryptor.encrypt(bytes(plaintext))).decode('utf-8')
    return encrypted
