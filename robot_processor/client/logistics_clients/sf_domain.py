import datetime
import json
from base64 import b64encode
from hashlib import md5
from typing import Tuple
from urllib.parse import quote
from uuid import uuid4

from loguru import logger

from robot_processor.client.logistics_clients.enum import \
    LogisticsType
from robot_processor.client.logistics_clients.exception_manager import \
    AbstractLogisticsClientException
from robot_processor.client.logistics_clients.logistics_client import \
    AbstractLogisticsClient, AbstractLogisticsIntercept
from robot_processor.client_mixins import Session
from rpa.conf import rpa_config as conf


class SFClientExceptionManager(AbstractLogisticsClientException):
    def common_error_code_path(self) -> str:
        return "$.errorCode"

    def common_error_msg_path(self) -> str:
        return "$.errorMsg"


class SFClient(AbstractLogisticsClient):
    session = Session()

    def __init__(self, logistics_type: LogisticsType | None = None) -> None:
        super().__init__(logistics_type)
        # 合作伙伴编码（即顾客编码）
        self.partner_id = conf.SF_PARTNER_ID
        # 校验码
        self.check_word = conf.SF_CHECK_WORD
        self.monthly_cards: list[str] = []

    def init(self, content: dict[str, list[str]] | None = None) -> None:
        if not content:
            return
        self.monthly_cards = content.get("monthly_cards") or []

    @property
    def endpoint(self):
        return conf.SF_API_ENDPOINT

    def generate_msg_data_and_digest(self, body: dict, timestamp: int) -> tuple[str, str]:
        msg_data = json.dumps(body, separators=(",", ":"), ensure_ascii=False)

        url_encode_string = quote(
            msg_data + str(timestamp) + self.check_word,
            encoding="UTF-8"
        )

        msg_digest = b64encode(
            md5(url_encode_string.encode()).digest()
        ).decode()

        return msg_data, msg_digest

    def send(self, api_code: str, body: dict) -> dict:
        now_time = datetime.datetime.now()
        now_time_timestamp = int(now_time.timestamp())
        now_time_string = now_time.strftime(
            "%Y%m%d%H%M%S"
        )
        request_id = str(uuid4())
        msg_data, msg_digest = self.generate_msg_data_and_digest(
            body, now_time_timestamp
        )
        data = {
            # 合作伙伴编码（即顾客编码）
            "partnerID": self.partner_id,
            # 请求唯一号UUID
            "requestID": request_id,
            # 接口服务代码
            "serviceCode": api_code,
            # 调用接口时间戳，long 类型
            "timestamp": now_time_timestamp,
            # 数字签名
            "msgDigest": msg_digest,
            # 业务数据报文
            "msgData": msg_data
        }
        headers = {
            # 时间戳字符串，时间格式yyyyMMddHHmmssSSS，如20190918185544911
            "timestamp": now_time_string,
            # 接口流水号，18位随机字符串
            "requestId": request_id[:18],
            # 请求头必须添加"Content-type","application/x-www-form-urlencoded"，字符集编码统一使用UTF-8
            "Content-Type": "application/x-www-form-urlencoded"
        }
        try:
            resp = self.session.post(
                self.endpoint,
                data=data,
                headers=headers
            )
            resp.raise_for_status()
            logger.info("本次 SF 请求体为 {}, 响应为：{}".format(
                data,
                resp.content.decode("utf-8")
            ))
            resp_content = resp.json()
            return resp_content
        except Exception as e:
            logger.exception(f"SF 接口请求发生异常 {e}")
            return {}

    @staticmethod
    def handler_response(resp: dict) -> tuple[bool, str]:
        if resp.get("apiResultCode") != "A1000":
            return False, resp.get("apiErrorMsg") or "顺丰服务异常"
        return True, resp.get("apiResultData") or "{}"

    def query_logistic(self, mail_no: str, phone_number: str | None = None) -> tuple[bool, str]:
        """
        查询物流轨迹。
        docs: https://qiao.sf-express.com/Api/ApiDetails?level3=397&interName=%E8%B7%AF%E7%94%B1%E6%9F%A5%E8%AF%A2%E6%8E%A5%E5%8F%A3-EXP_RECE_SEARCH_ROUTES

        该接口有三类传参：
        1. 入参运单号；验证顾客编码绑定的月结与运单号使用的月结是否一致；（限月结）
        示例说明：查询的顾客编码需要绑定运单号所用月结卡号，否则响应空路由信息

        2. 入参订单号；验证顾客编码与所请求的订单号是否存在归属关系（即需下单接口下单的）；
        运单号A是通过顾客编码A下单的，可以使用接口入参的orderid入参查询即可；否则返回为空。

        3. 入参运单号与收寄任意一方号码后4位，无其他限制条件（可查询全网运单路由）；
        示例：{
            "language": "0",
            "trackingType": "1",
            "trackingNumber": ["SF1603733433570"],
            "methodType": "1",
            "checkPhoneNo": "6039"
        }

        以上路由查询可查询3个月内路由信息。

        :param mail_no:
        :param phone_number:
        :return:
        """  # noqa
        body = {
            "language": "zh-CN",
            # 查询号类别:
            # 1: 根据顺丰运单号查询, trackingNumber将被当作顺丰运单号处理
            # 2: 根据客户订单号查询, trackingNumber将被当作客户订单号处理
            "trackingType": 1,
            "trackingNumber": [mail_no],
        }
        if phone_number is not None:
            body.update({"checkPhoneNo": phone_number})
        api_code = "EXP_RECE_SEARCH_ROUTES"
        resp = self.send(api_code, body)
        return self.handler_response(resp)

    def query_sfwaybill(self, mail_no: str, phone_number: str | None = None):
        body = {
            "trackingType": 2,
            "trackingNum": mail_no,
        }
        if phone_number:
            body["phone"] = phone_number
        api_code = "EXP_RECE_QUERY_SFWAYBILL"
        resp = self.send(api_code, body)
        return self.handler_response(resp)

    def batch_query_logistic(self, mail_nos: list[str],
                             phone_number: str | None = None) -> tuple[bool, str]:
        """
        批量查询物流轨迹，和查询物流轨迹使用的是同一个接口。
        :param mail_nos:
        :param phone_number:
        :return:
        """
        if len(mail_nos) > 10:
            return False, "一次最多查询10个单号"
        body = {
            "language": "zh-CN",
            # 查询号类别:
            # 1:根据顺丰运单号查询, trackingNumber将被当作顺丰运单号处理
            # 2: 根据客户订单号查询, trackingNumber将被当作客户订单号处理
            "trackingType": 1,
            "trackingNumber": mail_nos
        }
        if phone_number is not None:
            body.update({"checkPhoneNo": phone_number})
        api_code = "EXP_RECE_SEARCH_ROUTES"
        resp = self.send(api_code, body)
        return self.handler_response(resp)

    def create_interception(self, content=None) -> Tuple[bool, str | None, dict | None]:
        """
        发起拦截请求
        :param content:
        :return: 是否成功，错误信息，完整的response
        """
        api_code = "EXP_RECE_WANTED_INTERCEPT"

        error_messages = set()
        latest_error_message: str = ""
        all_request_data_json = {}
        latest_error_data_json = {}

        for monthly_card in self.monthly_cards:
            body = {
                "waybillNo": content.get("waybill_no"),
                "serviceCode": "2",
                "role": "1",
                "payMode": "4",
                "selfPickPoint": "1",
                "monthlyCardNo": monthly_card
            }
            resp = self.send(api_code, body)
            api_success, data = self.handler_response(resp)
            if not api_success:
                error_messages.add("{}: {}".format(monthly_card, data))
                latest_error_message = data
                continue
            data_json = json.loads(data)
            if data_json.get("success"):
                return True, "", None
            else:
                # 重复拦截，视为发起拦截成功
                if data_json.get("errorCode") == "20078":
                    return True, "", None
                latest_error_message = data_json.get("errorMsg")
                error_messages.add("{}: {}".format(monthly_card, data_json.get("errorMsg")))
                all_request_data_json.update({
                    monthly_card: data_json
                })
                latest_error_data_json = data_json
        if len(self.monthly_cards) > 1:
            logger.error("SF 请求的完整错误原因如下: \n{}\n\n完整的响应内容如下: \n{}".format(
                "\n".join(list(error_messages)),
                all_request_data_json
            ))
        return False, latest_error_message, latest_error_data_json


class SFIntercept(AbstractLogisticsIntercept):
    pass
