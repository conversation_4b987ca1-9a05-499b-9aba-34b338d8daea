import hashlib
import json
from typing import List, Optional
from urllib.parse import urlencode

import arrow
from loguru import logger
from pydantic import BaseModel

from robot_processor.client_mixins import Session
from rpa.conf import rpa_config as conf


class GenericGoodsInfo(BaseModel):
    goodsCode: str
    goodsQty: int


class CustomerAddress(BaseModel):
    addressType: int
    customerContactName: str
    customerContactPhone: str
    customerFullAddress: str


class GenericOrderRequest(BaseModel):
    orderNumber: str
    goodsInfoList: List[GenericGoodsInfo]
    customerAddressList: List[CustomerAddress]
    remark: Optional[str]


class JdlSDK:
    session = Session()

    def __init__(self, access_token: str, customer_code: str | None = None):
        #     self.app_key = app_key
        #     self.app_secret = app_secret
        self.access_token = access_token
        self.customer_code = customer_code

    @property
    def endpoint(self):
        return conf.JDL_ENDPOINT

    def _request(self, api_path: str, lop_dn: str, data: dict):
        try:
            params = {
                "app_key": conf.JDL_APP_KEY,
                "access_token": self.access_token,
                "timestamp": arrow.now().format("YYYY-MM-DD HH:mm:ss"),
                "v": "2.0",
                "LOP-DN": lop_dn,
                "algorithm": "md5-salt"
            }
            data_json = json.dumps([data], separators=(",", ":"))
            content = (conf.JDL_APP_SECRET + "access_token" +
                       self.access_token +
                       "app_key" + conf.JDL_APP_KEY + "method" + api_path +
                       "param_json" + data_json + "timestamp" +
                       params["timestamp"] + "v" + params[
                           "v"] + conf.JDL_APP_SECRET)
            sign = hashlib.md5(content.encode()).hexdigest()
            params["sign"] = sign
            resp = self.session.request(
                "POST", self.endpoint + api_path + "?" + urlencode(params),
                data=data_json)
            result = resp.json()
            logger.info(f"京东物流 接口请求返回: {result}")
            return result
        except Exception as e:
            logger.error(f"京东物流 接口请求发生异常: {e}")
            return {}

    def intercept_report(self, waybill_no: str):
        data = {
            "waybillCode": waybill_no,
            "orderOrigin": 1,
            "customerCode": self.customer_code,
            "cancelReason": "用户发起取消",
            "cancelReasonCode": 1
        }
        return self._request("/ecap/v1/orders/cancel", "ECAP", data)

    def query_traces(self, waybill_no: str):
        data = {
            "waybillCode": waybill_no,
            "orderOrigin": 1,
            "customerCode": self.customer_code
        }
        return self._request("/ecap/v1/orders/trace/query", "ECAP", data)

    def generic_order_create(self, req: GenericOrderRequest):
        return self._request(
            "/genericorderservice/submitspcorder",
            "fuwujiashangjia",
            req.dict(exclude_none=True)
        )

    def generic_order_cancel(self, order_number: str, reason: str):
        data = {
            "orderNumber": order_number,
            "cancelReason": reason
        }
        return self._request(
            "/genericorderservice/cancelorder4spc",
            "fuwujiashangjia",
            data
        )
