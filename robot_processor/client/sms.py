import arrow
from lepollo import ApolloConfig
from lepollo import get_config
from loguru import logger
from result import Err
from result import Ok

from robot_processor.error.client_request import SMSError


class SMSClient:
    """短信服务

    References:
        https://api.aliyun.com/api-tools/sdk/Dysmsapi?version=2017-05-25&language=python-tea&tab=primer-doc
    """

    def __init__(self, config_manager=None):
        import logging

        from alibabacloud_dysmsapi20170525.client import Client
        from alibabacloud_tea_openapi.models import Config

        logging.getLogger().setLevel(logging.INFO)  # https://github.com/aliyun/alibabacloud-python-sdk/issues/35
        self.config = config_manager or sms_config
        self.client = Client(
            Config(
                endpoint="dysmsapi.aliyuncs.com",
                access_key_id=self.config.ALIBABA_SMS_ACCESS_KEY_ID,
                access_key_secret=self.config.ALIBABA_SMS_ACCESS_KEY_SECRET,
            )
        )

    def send_sms(self, mobile: str, code: str):
        from alibabacloud_dysmsapi20170525.models import SendSmsRequest

        send_sms_request = SendSmsRequest(
            phone_numbers=mobile,
            sign_name=self.config.ALIBABA_SMS_SIGN_NAME,
            template_code=self.config.ALIBABA_SMS_TEMPLATE_CODE,
            template_param='{"code":"' + code + '"}',
        )
        response = self.client.send_sms(send_sms_request)
        logger.info(f"send sms with {send_sms_request.to_map()}, got {response.to_map()}")
        if response.body.code == "OK":
            return Ok(response)
        else:
            return Err(SMSError(req=send_sms_request.to_map(), res=response.to_map(), message=response.body.message))

    def query_send_details(self, mobile: str, biz_id: str | None = None, send_date: str | None = None):
        from alibabacloud_dysmsapi20170525.models import QuerySendDetailsRequest

        send_date = send_date or arrow.now().format("YYYYMMDD")
        request = QuerySendDetailsRequest(
            phone_number=mobile, biz_id=biz_id, send_date=send_date, page_size=50, current_page=1
        )
        return self.client.query_send_details(request)


class SMSConfig(ApolloConfig):
    __namespace__ = "client"

    ALIBABA_SMS_ACCESS_KEY_ID: str
    ALIBABA_SMS_ACCESS_KEY_SECRET: str
    ALIBABA_SMS_SIGN_NAME: str
    ALIBABA_SMS_TEMPLATE_CODE: str


sms_config = get_config(config_class=SMSConfig)
