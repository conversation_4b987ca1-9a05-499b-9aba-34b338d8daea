import time
import typing as t

from loguru import logger

from robot_metrics import Stats
from robot_processor.client.conf import app_config as config
from robot_processor.client.errors import PddBridgeError
from robot_processor.client.errors import PddOrderNotFoundError
from robot_processor.client.errors import PddRateLimitError
from robot_processor.client.errors import PddServiceError
from robot_processor.client.schema import PddBatchDecryptPayload, \
    PddOrderUpdateAddressPayload, PddOrderUpdateAddressResponse
from robot_processor.client.schema import PddBatchDecryptResponse
from robot_processor.client.schema import PddLogisticsCompaniesGetResponse
from robot_processor.client.schema import PddOrderInformationGet
from robot_processor.client.schema import PddOrderUploadExtraLogisticsPayload
from robot_processor.client.schema import PddOrderUploadExtraLogisticsResponse
from robot_processor.client.schema import PddRefundAgreePayload
from robot_processor.client.schema import PddRefundAgreeResponse
from robot_processor.client.schema import PddRefundDetailPayload
from robot_processor.client.schema import PddRefundDetailResponse
from robot_processor.client.schema import PddRefundListPayload
from robot_processor.client.schema import PddRefundListResponse
from robot_processor.client.schema import PddRefundStatusCheckPayload
from robot_processor.client.schema import PddRefundStatusCheckResponse
from robot_processor.client.schema import PopClientRequest
from robot_processor.client.schema import PopClientResponse
from robot_processor.client_mixins import Session
from robot_processor.ext import cache

_statsd_it = Stats.Client.timer(client_name="pdd-bridget-client")

if t.TYPE_CHECKING:
    # 延迟加载, 防止环形 import
    from external.schema import PddPlatformAckBody


class PddBridgetClient:
    session = Session()

    @property
    def host(self) -> str:
        return config.PDD_BRIDGE_HOST

    @property
    def auth_check_host(self) -> str:
        return config.PDD_AUTH_CHECK_HOST

    @property
    def auth_check_token(self) -> str:
        return config.PDD_AUTH_CHECK_TOKEN

    @property
    def common_headers(self) -> t.Dict[str, str]:
        return {"RequestService": "Feisuo"}

    def _wrap_payload(self, method: str, store_id: str, biz_params: t.Dict[str, t.Any]) -> PopClientRequest:
        callback_url = config.PDD_BRIDGE_CALLBACK
        return PopClientRequest(method=method, sellerId=store_id, callbackUrl=callback_url, payload=biz_params)

    def _cache_key(self, trace_id: str) -> str:
        return f"PDD_POP_CLIENT::{trace_id}"

    def _wait_ack(self, trace_id: str, timeout: int) -> "PddPlatformAckBody":
        from external.schema import PddPlatformAckBody

        eta = time.time() + timeout
        ret = None
        key = self._cache_key(trace_id)
        while time.time() < eta:
            ret = cache.get(key)
            if ret:
                break
            time.sleep(0.3)
        if not ret:
            raise PddBridgeError("ack timeout")
        logger.info("ack response - {}", ret)
        ret = t.cast(t.Dict[str, t.Any], ret)
        return PddPlatformAckBody.parse_obj(ret)

    def ack(self, trace_id: str, result: t.Dict[str, t.Any]):
        key = self._cache_key(trace_id)
        cache.set(key, result, timeout=60)

    def request(
        self, method: str, store_id: str, biz_params: t.Dict[str, t.Any], timeout: int | None = None
    ) -> "PddPlatformAckBody":
        body = self._wrap_payload(method, store_id, biz_params).dict()
        logger.info("call popclient - method: {}, body: {}", method, biz_params)
        url = f"{self.host}/pdd/pop-client"
        r = self.session.post(url, json=body, headers=self.common_headers)
        r.raise_for_status()
        logger.info("call popclient return - {}", r.json())
        resp = PopClientResponse.parse_obj(r.json())
        if not resp.success:
            raise PddBridgeError(f"请求pop client失败 - {resp.errorMessage}({resp.errorCode})")
        callback_ret = self._wait_ack(resp.traceId, timeout or config.PDD_BRIDGE_ACK_TIMEOUT)
        if not callback_ret.success:
            error = f"{resp.traceId} - {callback_ret.message}"
            raise PddBridgeError(f"pdd platform返回失败: {error}")
        result = callback_ret.result
        if result and "error_response" in result:
            error_response = result["error_response"]
            if error_response.get("error_code") == PddRateLimitError.error_code:
                raise PddRateLimitError(raw_response=callback_ret)
            if error_response.get("error_msg") == "订单不属于当前店铺或订单不存在":
                raise PddOrderNotFoundError("订单不属于当前店铺或订单不存在")
            error = f"{error_response.get('error_msg')} - {error_response.get('sub_msg')}"
            raise PddServiceError(f"拼多多返回异常: {error}")
        return callback_ret

    @_statsd_it
    def get_auth_products(self, *, store_id: str) -> t.List[t.Dict[str, t.Any]]:
        url = f"{self.auth_check_host}/third/v1/fs/service_time"
        params = {"sid": store_id}
        headers = {"Authorization": f"Bearer {self.auth_check_token}"}
        r = self.session.get(url, params=params, headers=headers)
        r.raise_for_status()
        resp = r.json()
        if resp.get("sub_code") != 200:
            raise PddBridgeError(f"获取店铺{store_id}拼多多授权信息失败: {resp.get('msg')}")
        return resp["data"]

    @staticmethod
    def memo_tag_mapper():
        return {"红色": 1, "黄色": 2, "绿色": 3, "蓝色": 4, "紫色": 5}

    @_statsd_it
    def update_memo(self, *, store_id: str, order_sn: str, memo: str, tag_name: t.Optional[str] = None):
        method = "pdd.order.note.update"
        biz_params: t.Dict[str, t.Any] = {"note": memo, "order_sn": order_sn}
        if tag_name:
            tag_id = self.memo_tag_mapper().get(tag_name)
            if not tag_id:
                raise PddBridgeError(f"无效的插旗颜色: {tag_name}")
            biz_params.update({"tag": tag_id, "tag_name": tag_name})
        resp = self.request(method, store_id, biz_params)
        result = t.cast(t.Dict[str, t.Any], resp.result)
        if "response" in result:
            success = result["response"]["success"]
            if not success:
                error = result["response"]["error_msg"]
                raise PddServiceError(f"更新订单备注失败: {error}")
            return
        else:
            raise PddServiceError(f"更新订单备注失败, 未知异常: {result}")

    @_statsd_it
    def batch_decrypt(self, *, store_id: str, payload: PddBatchDecryptPayload) -> PddBatchDecryptResponse:
        """
        批量解密
        """
        method = "pdd.open.decrypt.batch"
        biz_params = payload.dict()
        resp = self.request(method, store_id, biz_params)
        result = t.cast(t.Dict[str, t.Any], resp.result)
        if "open_decrypt_batch_response" in result:
            parsed_result = PddBatchDecryptResponse.parse_obj(result["open_decrypt_batch_response"])
            self._check_decrypted_result(parsed_result)
            return parsed_result
        else:
            raise PddServiceError(f"拼多多批量解密失败，未知异常: {result}")

    def refund_increment_list(self, *, store_id, payload: PddRefundListPayload):
        """增量获取售后单列表

        Args:
            store_id (str): 店铺ID
            payload (PddRefundListPayload):
        """
        method = "pdd.refund.list.increment.get"
        resp = self.request(method, store_id, payload.dict())
        return PddRefundListResponse.validate(resp.result)

    @_statsd_it
    def refund_detail(self, *, store_id: str, payload: PddRefundDetailPayload) -> PddRefundDetailResponse:
        """
        售后详情
        """
        method = "pdd.refund.information.get"
        biz_params = payload.dict()
        resp = self.request(method, store_id, biz_params)
        result = t.cast(t.Dict[str, t.Any], resp.result)
        return PddRefundDetailResponse.parse_obj(result)

    def refund_agree(self, *, store_id: str, payload: PddRefundAgreePayload) -> PddRefundAgreeResponse:
        """
        同意退款
        """
        method = "pdd.refund.agree"
        biz_params = payload.dict()
        resp = self.request(method, store_id, biz_params)
        result = t.cast(t.Dict[str, t.Any], resp.result)
        return PddRefundAgreeResponse.parse_obj(result)

    @_statsd_it
    def order_upload_extra_logistics(
        self, *, store_id: str, payload: PddOrderUploadExtraLogisticsPayload
    ) -> PddOrderUploadExtraLogisticsResponse:
        """
        订单额外运单信息上传
        """
        method = "pdd.order.upload.extra.logistics"
        biz_params = payload.dict()
        resp = self.request(method, store_id, biz_params)
        result = t.cast(t.Dict[str, t.Any], resp.result)
        return PddOrderUploadExtraLogisticsResponse.parse_obj(result)

    def logistics_companies_get(self, *, store_id: str):
        """
        快递公司查看接口
        """
        method = "pdd.logistics.companies.get"
        resp = self.request(method, store_id, {})
        result = t.cast(t.Dict[str, t.Any], resp.result)
        return PddLogisticsCompaniesGetResponse.parse_obj(result)

    def order_information_get(
        self, *, store_id: str, payload: PddOrderInformationGet
    ) -> PddOrderInformationGet.Response:
        """
        订单详情
        """
        method = "pdd.order.information.get"
        resp = self.request(method, store_id, payload.dict())
        return PddOrderInformationGet.Response.parse_obj(resp.result)

    def refund_status_check(
        self, *, store_id: str, payload: PddRefundStatusCheckPayload
    ) -> PddRefundStatusCheckResponse:
        """
        售后校验接口
        """
        method = "pdd.refund.status.check"
        resp = self.request(method, store_id, payload.dict())
        return PddRefundStatusCheckResponse.parse_obj(resp.result)

    def order_update_address(
            self, *, store_id: str, payload: PddOrderUpdateAddressPayload
    ) -> PddOrderUpdateAddressResponse:
        """
        修改订单地址
        """
        method = "pdd.order.update.address"
        resp = self.request(method, store_id, payload.dict())
        return PddOrderUpdateAddressResponse.parse_obj(resp.result)

    def _check_decrypted_result(self, resp: PddBatchDecryptResponse):
        for decrypted in resp.data_decrypt_list:
            if decrypted.error_code != 0:
                raise PddServiceError(f"拼多多解密失败，原因: {decrypted.error_msg}")
