from dataclasses import dataclass
from enum import StrEnum
from enum import auto
from random import randint

from result import Err
from result import Ok

from robot_processor.client import sms_client
from robot_processor.enums import Creator
from robot_processor.ext import cache
from robot_processor.users.models import GroupUserMapping
from robot_processor.users.models import LeyanUser
from robot_processor.users.models import PlatformUser
from robot_processor.users.models import PlatformUserMapping
from robot_processor.users.models import UserGroup


class SMSCodeAuthenticationService:

    class BizType(StrEnum):
        # 办税人信息校验
        CORPORATE_TAXER = auto()

    def __init__(self):
        self.cache = cache
        self.sms_client = sms_client
        self.cache_key_template = "auth:verification_code:{biz_type}:{phone_number}"
        self.limit_key_template = "auth:limit:{biz_type}:{phone_number}"

    def generate_code(self):
        """生成验证码

        Returns:
            str: 验证码
        """
        verification_code = str(randint(0, 9999)).zfill(4)

        return verification_code

    def acquire(self, phone_number, biz_type):
        """获取验证码

        Args:
            phone_number (str): 手机号
            biz_type (SMSCodeAuthenticationService.BizType): 业务类型

        Returns:
            Ok[dict] | Err[Exception]
        """
        verification_code = self.generate_code()

        limit_key = self.limit_key_template.format(biz_type=biz_type, phone_number=phone_number)
        if self.cache.get(limit_key):
            return Err(Exception("请求过于频繁，请稍后再试"))

        result = self.sms_client.send_sms(phone_number, verification_code)
        if result.is_ok():
            cache_key = self.cache_key_template.format(biz_type=biz_type, phone_number=phone_number)
            self.cache.set(cache_key, verification_code, timeout=10 * 60)
            self.cache.set(limit_key, 1, timeout=60)

        return result

    def check(self, phone_number, biz_type, code):
        """验证验证码

        Args:
            phone_number (str): 手机号
            biz_type (SMSCodeAuthenticationService.BizType): 业务类型
            code (str): 验证码

        Returns:
            Ok | Err[Exception]
        """
        cache_key = self.cache_key_template.format(biz_type=biz_type, phone_number=phone_number)
        cached_code = self.cache.get(cache_key)
        if not cached_code:
            return Err(Exception("验证码已过期，请重新获取"))
        if code != cached_code:
            return Err(Exception("验证码错误"))
        return Ok(None)


@dataclass
class AssistantService:
    leyan_users: list[LeyanUser]
    plat_users: list[PlatformUser]
    platform_user_mappings: list[PlatformUserMapping]
    groups: list[UserGroup]
    group_user_mappings: list[GroupUserMapping]

    @classmethod
    def from_org(cls, org_id: int):
        from sqlalchemy import and_

        from robot_processor.shop.kiosk_models import KioskShop

        leyan_users = LeyanUser.query.filter(LeyanUser.org_id == org_id).all()
        platform_users = (
            PlatformUser.query.join(
                KioskShop, and_(KioskShop.sid == PlatformUser.sid, KioskShop.platform == PlatformUser.platform)
            )
            .filter(KioskShop.org_id == org_id)
            .all()
        )
        platform_user_mappings = PlatformUserMapping.query.filter(
            PlatformUserMapping.user_id.in_([user.id for user in leyan_users])
        ).all()
        user_groups = UserGroup.query.filter(
            UserGroup.org_id == org_id, UserGroup.deleted == 0, UserGroup.status == 1
        ).all()
        group_user_mappings = GroupUserMapping.query.filter(
            GroupUserMapping.group_uuid.in_([group.group_uuid for group in user_groups])
        ).all()
        return cls(leyan_users, platform_users, platform_user_mappings, user_groups, group_user_mappings)

    @property
    def plat_leyan_id_mapping(self):
        return {
            platform_user_mapping.platform_user_id: platform_user_mapping.user_id
            for platform_user_mapping in self.platform_user_mappings
        }

    @property
    def plat_user_id_mapping(self):
        return {plat_user.id: plat_user for plat_user in self.plat_users}

    @property
    def leyan_user_mapping(self):
        return {leyan_user.id: leyan_user for leyan_user in self.leyan_users}

    def filter_leyan_users_by_users_and_groups(self, users: list, group_uuids: list[str]):
        """在指定店铺内查找符合下列条件之一的平台账号:

        - id 出现在 users 中，且 users 中对应的 user_type 是 LEYAN
        - 所绑定的平台账号 id 出现在 users 中，且 users 中对应的 user_type 是 ASSISTANT
        - id 或者所绑定的平台账号 id，关联在了任意一个 groups 上
        """
        leyan_user_ids, platform_user_ids = set(), set()
        for user in users:
            match user.user_type:
                case Creator.LEYAN:
                    leyan_user_ids.add(user.user_id)
                case Creator.ASSISTANT:
                    platform_user_ids.add(user.user_id)
                case _:
                    continue
        for group_uuid in group_uuids:
            for group_user_mapping in filter(lambda x: x.group_uuid == group_uuid, self.group_user_mappings):
                match group_user_mapping.user_type:
                    case Creator.LEYAN:
                        leyan_user_ids.add(group_user_mapping.user_id)
                    case Creator.ASSISTANT:
                        platform_user_ids.add(group_user_mapping.user_id)
        for platform_user_id in platform_user_ids:
            if platform_user_id in self.plat_leyan_id_mapping:
                leyan_user_ids.add(self.plat_leyan_id_mapping[platform_user_id])
        leyan_users: list[LeyanUser] = []
        for leyan_user_id in leyan_user_ids:
            if leyan_user_id not in self.leyan_user_mapping:
                continue
            leyan_user = self.leyan_user_mapping[leyan_user_id]
            if leyan_user.status != 1:
                continue
            leyan_users.append(leyan_user)
        return leyan_users


sms_code_auth_service = SMSCodeAuthenticationService()
