from typing import List
from typing import Literal
from typing import TypedDict

from typing_extensions import NotRequired


class MiniappTemplateInstantiate(TypedDict):
    """实例化saas化的小程序应用，创建一个小程序应用实例

    References:
        https://open.taobao.com/api.htm?docId=46628&docType=2
    """

    appkey: str
    app_id: str  # 小程序 app_id
    app_name: str  # 小程序名称按平台规则自动生成。在授权弹窗标题、「关于」页面展示名称。
    app_description: str  # 小程序描述
    app_icon: str  # 小程序 icon
    app_version: str  # 当前新生成的预览版本号
    pre_view_url: str  # 当前新生成的预览版本的链接，仅当前商家有权限预览。
    app_alias: str  # 小程序简称。在小程序Loading动画、首页标题、「更多」菜单标题上优先展示简称。


class MiniappTemplateUpdateapp(TypedDict):
    """商家应用c端模板实例化小程序更新，生成新的版本，但不会自动上线新版本

    References:
          https://open.taobao.com/api.htm?docId=47799&docType=2
    """

    appkey: str
    app_id: str  # 小程序 app_id
    app_name: str  # 小程序名称按平台规则自动生成。在授权弹窗标题、「关于」页面展示名称。
    app_description: str  # 小程序描述
    app_icon: str  # 小程序 icon
    app_version: str  # 当前新生成的预览版本号
    pre_view_url: str  # 当前新生成的预览版本的链接，仅当前商家有权限预览。
    app_alias: str  # 小程序简称。在小程序Loading动画、首页标题、「更多」菜单标题上优先展示简称。


class _MiniAppEntityTemplateDto(TypedDict):
    appkey: str
    app_id: str  # 小程序 app_id
    app_name: str  # 小程序名称按平台规则自动生成。在授权弹窗标题、「关于」页面展示名称。
    app_description: str  # 小程序描述
    app_icon: str  # 小程序 icon
    app_alias: NotRequired[str]  # queryapp 小程序简称。在小程序Loading动画、首页标题、「更多」菜单标题上优先展示简称。
    online_url: NotRequired[str]  # onlineapp 线上正式版本的链接，所有消费者可访问。


class _MiniAppInstanceVersionDto(TypedDict):
    app_version: str  # 小程序版本号
    client: str  # 发布端
    app_id: str  # 小程序 app_id
    status: str  # 版本状态 [ONLINE, ]
    template_id: str  # 模板id
    template_version: str  # 模板版本
    ext_json: str  # 扩展信息
    app_url: str  # 版本链接。上线状态为线上地址，预览状态为预览地址，下线状态为空。


class _MiniappInstanceAppAllVersionsDto(TypedDict):
    app_version_list: List[_MiniAppInstanceVersionDto]
    app_info: _MiniAppEntityTemplateDto


class MiniappTemplateQueryapp(TypedDict):
    """根据模板id和商家信息，查询实例化小程序版本查询

    References:
        https://open.taobao.com/api.htm?docId=47798&docType=2
    """

    all_version_infos: List[_MiniappInstanceAppAllVersionsDto]


class _MiniappInstanceAppOnlineDto(TypedDict):
    client: str  # 分端上线结果
    fail_msg: str  # 错误信息
    success: str  # 是否成功 [true, ]


class MiniappTemplateOnlineapp(TypedDict):
    """上线小程序实例

    References:
        https://open.taobao.com/api.htm?docId=47754&docType=2
    """

    online_results: List[_MiniappInstanceAppOnlineDto]
    app_info: _MiniAppEntityTemplateDto


class _TmallTradeRateFeedsGet(TypedDict):
    append_content: str
    content: str
    comment_time: str
    has_negtv: bool
    append_has_negtv: bool
    append_time: str


class TmallTradeRateFeedsGet(TypedDict):
    """查询子订单对应的评价、追评以及语义标签

    References:
         https://open.taobao.com/api.htm?docId=22532&docType=2
    """

    tmall_rate_info: _TmallTradeRateFeedsGet


class _TradeRate(TypedDict):
    tid: int
    oid: int


class TaobaoTradeRatesGet(TypedDict):
    """搜索评价信息

    References:
        https://open.taobao.com/v2/doc?spm=a219a.15212433.0.0.231c669acceils#/apiFile?docType=2&docId=55
    """

    total_results: int
    has_next: bool
    trade_rates: List[_TradeRate]


class _ShopSellerGetShop(TypedDict):
    sid: int  # 店铺编号
    title: str  # 店铺标题
    pic_path: str  # 店标地址。返回相对路径，可以用"http://logo.taobao.com/shop-logo"来拼接成绝对路径


class ShopSellerGet(TypedDict):
    """获取卖家店铺的基本信息

    References:
        https://open.taobao.com/api.htm?docId=42908&docType=2
    """

    shop: _ShopSellerGetShop


class TransitStepInfo(TypedDict):
    status_time: str
    status_desc: str
    action: str


class TransitStepResult(TypedDict):
    out_sid: str
    company_name: str
    tid: int
    status: str
    trace_list: List[TransitStepInfo]


class TaobaoLogisticsTraceGet(TypedDict):
    result: List[TransitStepResult]


class _RefundMappingResult(TypedDict):
    message: str
    refund_id: str
    succ: bool


class RefundsAgree(TypedDict):
    """同意退款

    References:
        https://open.taobao.com/api.htm?docId=22465&docType=2
    """

    message: str
    succ: bool
    results: List[_RefundMappingResult]
    msg_code: str


class SmsSendByOaidResult(TypedDict):
    """基于OAID的短信发送接口
    References:
        https://open.taobao.com/api.htm?spm=a219a.7386797.0.0.6628669aRmX4sa&source=search&docId=54973&docType=2
    """

    module: str


class TempalteCreate(TypedDict):
    """淘宝短信模板创建

    References:
        https://open.taobao.com/api.htm?docId=60274&docType=2&scopeId=11815
    """

    r_code: str
    r_success: bool
    module: str
    message: str


class RefundsReview(TypedDict):
    """核退款单

    References:
        https://open.taobao.com/api.htm?spm=a219a.7386653.1.186.1a504de0qosGhB&source=search&docId=23875&docType=2
    """

    is_success: bool


class _OrderSkuUpdateOrder(TypedDict):
    oid: int
    modified: str


class OrderSkuUpdateResult(TypedDict):
    """更新交易的销售属性

    References:
        https://open.taobao.com/api.htm?docId=240&docType=2
    """

    order: _OrderSkuUpdateOrder


class _SkusQuantityUpdateResult(TypedDict):
    iid: str
    num_iid: int
    modified: str
    num: int


class _SkusQuantityUpdateSku(TypedDict):
    sku_id: int
    modified: str
    quantity: int


class SkusQuantityUpdateResult(TypedDict):
    """提供按照全量/增量的方式批量修改SKU库存的功能

    References:
        https://open.taobao.com/api.htm?spm=a219a.7386797.0.0.4c3f669a97Zriz&source=search&docId=21169&docType=2
    """

    item: _SkusQuantityUpdateResult
    skus: List[_SkusQuantityUpdateSku]


class _SubAccountInfo(TypedDict):
    user_id: int  # 卖家 seller id
    sub_id: int  # 子账号id
    sub_status: int  # 子账号状态
    sub_nick: str  # 子账号昵称


class SubUsersGet(TypedDict):
    """获取指定账户的子账号简易信息列表

    References:
        https://open.taobao.com/api.htm?docId=21686&docType=2
    """

    subaccounts: List[_SubAccountInfo]


class _MiniappShorturlCreateResultModel(TypedDict):
    short_url: str  # 短链接地址


class _MiniappShorturlCreateResult(TypedDict):
    model: _MiniappShorturlCreateResultModel


class MiniappShorturlCreate(TypedDict):
    """生成淘宝小程序短链接

    References:
        https://open.taobao.com/api.htm?docId=52233&docType=2
    """

    result: _MiniappShorturlCreateResult


class JdpUser(TypedDict):
    """开通订单同步服务的用户

    session失效超过1个月，用户将被删除
    """

    user_nick: str
    status: Literal[0, 1, 2, 3]  # 0: 暂停; 1: 正常; 2: session失效，停止; 3: 已删除


class JushitaJdpUsersGet(TypedDict):
    """获取开通的订单同步服务的用户

    References:
        https://open.taobao.com/api.htm?docId=20736&docType=2
    """

    total_results: int  # 总记录数
    users: List[JdpUser]  # 用户列表


class JushitaJdpUserDelete(TypedDict):
    """删除数据推送用户

    References:
        https://open.taobao.com/api.htm?docId=21732&docType=2
    """

    is_success: bool  # 是否删除成功


class Receiver(TypedDict):
    name: str
    mobile: str
    address_detail: str
    phone: str
    oaid: str
    tid: str
    town: str
    district: str
    city: str
    state: str
    country: str
    matched: bool
    secret_no_expire_time: str
    privacy_protection: bool
    desensitize_rule: str


class OaidDecryptResult(TypedDict):
    receiver_list: List[Receiver]


class RefundMessage(TypedDict):
    content: str
    created: str
    owner_nick: str


class RefundMessagesResult(TypedDict):
    refund_messages: List[RefundMessage]


class SellerMemosGetMemosItem(TypedDict):
    tid: int
    memo: str
    flag_id: int
    flag_tag: str
    timestamp: int
    operator_id: int
    operator_nick: str


class SellerMemosGetResult(TypedDict):
    memos: list[SellerMemosGetMemosItem]
