import json
import os
import signal
import urllib.parse

import sentry_sdk
from flask import Flask
from flask import abort
from flask import current_app
from flask import make_response
from flask import request
from lepollo import get_json_config
from loguru import logger
from sentry_dramatiq import DramatiqIntegration
from sentry_sdk.integrations.flask import FlaskIntegration
from werkzeug.exceptions import InternalServerError

import external  # noqa
from robot_processor import alipay  # noqa
from robot_processor import bi  # NOQA
from robot_processor import business_order  # NOQA
from robot_processor import business_order_stats  # noqa
from robot_processor import cli  # NOQA
from robot_processor import client  # NOQA
from robot_processor import ext  # noqa
from robot_processor import form  # NOQA
from robot_processor import invoice  # noqa
from robot_processor import job  # noqa
from robot_processor import kafka_event  # noqa
from robot_processor import notify  # NOQA
from robot_processor import openapi  # noqa
from robot_processor import plugin  # NOQA
from robot_processor import provider  # NOQA
from robot_processor import refund  # NOQA
from robot_processor import rpa_service  # noqa
from robot_processor import shop  # NOQA
from robot_processor import task_center  # NOQA
from robot_processor import user_task  # NOQA
from robot_processor import users  # noqa
from robot_processor import wanshifu  # NOQA
from robot_processor.base_schemas import Response
from robot_processor.constants import local_config_file
from robot_processor.constants import sample_config_file
from robot_processor.flask_exts import debug_tool
from robot_processor.signals import after_boot
from robot_processor.signals import booting
from robot_processor.utils import unwrap_optional
from robot_processor.validator import enable_pydantic_model_response
from rpa import cellar  # noqa
from rpa import doudian  # noqa
from rpa import erp  # noqa
from rpa import mola  # noqa
from rpa import n8n  # noqa
from rpa import pdd  # noqa
from rpa import smartcall  # noqa

app = Flask(__name__)
debug_tool.init_app(app)
enable_pydantic_model_response(app)

if "LAIN_APPNAME" in os.environ:
    # 处于 LAIN 集群内，使用 apollo 从 cfb.json namespace 读取配置
    cfb_config = get_json_config("cfb.json")
    cfb_config.sync_to_flask_config(app)
elif os.path.exists(local_config_file):
    # 处于开发或 CI 测试环境内，使用本地配置
    app.config.from_file(local_config_file, json.load)
else:
    # 处于开发或 CI 测试环境内，使用仓库内的样本配置
    app.config.from_file(sample_config_file, json.load)

# 设置全局变量表示应用是否收到 SIGTERM 信号
shutdown = False

booting.send(app)
after_boot.send(app)

# 勿删. lain.yaml 里 dramatiq 的启动参数会依赖到 robot_processor.app:broker
broker = ext.task_queue.broker


def handle_sigterm(signum, frame):
    global shutdown
    shutdown = True
    logger.info("received SIGTERM signal")


# 注册 SIGTERM 信号处理函数
signal.signal(signal.SIGTERM, handle_sigterm)


if app.config.get("ENABLED_SENTRY"):

    def is_grpc_abort_exception(exception):
        frames = exception.get("stacktrace", {}).get("frames", [])
        return frames and frames[-1].get("function") == "abort" and frames[-1].get("module") == "grpc._server"

    def truncate_grpc_abort_exception(event, hint):
        exceptions = event.get("exception", {}).get("values", [])
        if len(exceptions) > 1 and is_grpc_abort_exception(exceptions[-1]):
            exceptions.pop()
        return event

    sentry_sdk.init(
        traces_sample_rate=0.2,
        integrations=[FlaskIntegration(), DramatiqIntegration()],
        before_send=truncate_grpc_abort_exception,
    )


@app.after_request
def access_after_log(resp):
    """默认 DEBUG 级别打印访问日志
    改为请求后执行的目的是可以带上接口中的一些上下文信息 sid org_id等
    """

    try:
        ignore_list = current_app.config.get("LOG_IGNORE_REQUEST_ENDPOINT", [])
        white_list = current_app.config.get("LOG_WHITELIST_REQUEST_ENDPOINT", [])
        endpoint = request.endpoint
        if endpoint in ignore_list:
            return resp

        full_path = urllib.parse.unquote(request.full_path)
        level = "INFO" if endpoint in white_list else "DEBUG"

        logger.log(level, f"{full_path} with {request.get_json(silent=True)}")
    except Exception as e:
        logger.opt(exception=e).error("access_before_log")
    return resp


@app.before_request
def load_authorization():
    from robot_processor.auth import auth_flask_request
    from robot_processor.auth import empty_jwt
    from robot_processor.auth import has_jwt
    from robot_processor.currents import g

    if request.method == "OPTIONS":
        return make_response()

    # 这部分 API 是注册在淘宝小程序上的 web hook，不会携带常规的 jwt token
    is_miniapp_request = request.endpoint and request.endpoint.startswith("mini-app")

    if is_miniapp_request:
        # https://miniapp.open.taobao.com/docV3.htm?docId=118538&docType=1&tag=dev
        # g.mini_app_id = request.args.get("my_app_id")
        # 提审的时候通过mode参数(前端自定义的参数)来定位到固定店铺，其他时间应该是取阿里云的source_app_id
        if request.args.get("mode"):
            g.mini_app_id = current_app.config.get("ALI_CHECK_APP_ID")
        else:
            g.mini_app_id = request.args.get("source_app_id")
        g.user_nick = unwrap_optional(request.args.get("user_nick"))
        g.buyer_open_uid = request.args.get("open_id")
        return None

    # 对于 /health_check 等接口来说，确实不需要登陆
    # 不过更合适的做法是：每个 endpoint 声明自己是否需要登陆，这里依据 endpoint 上的声明情况决定检查 jwt 是否存在和合法
    if not has_jwt(request):
        g.auth = empty_jwt
        return

    auth_flask_request(request)
    if not (g.login_user_detail and g.login_user_detail.is_valid()):
        if request.method == "GET":
            # todo fix 临时放行 需要前端修复
            return
        logger.bind(status=401).warning(f"用户不存在或已被禁用: {g.login_user_detail}")
        abort(401)

    from flask import jsonify

    from robot_processor.utils import api_degrade

    try:
        api_degrade.degrade()
    except api_degrade.DegradeError as e:
        abort(jsonify(e.response))
    except:  # noqa
        pass


@app.errorhandler(InternalServerError)
def handle_exception(e: InternalServerError):
    from urllib.parse import unquote

    from flask import request
    from leyan_tracing import get_tracer
    from loguru import logger

    full_path = unquote(request.full_path)
    request_json = request.get_json(silent=True)
    logger.opt(exception=e).error(f"{full_path=}, {request_json=}")

    trace_id = ""
    if tracer := get_tracer():
        if span := tracer.active_span:
            trace_id = "{:x}".format(span.trace_id)

    message = "后台发生了一点意外，请联系实施同学处理"
    if trace_id:
        message = f"{message},追踪码: {trace_id}"

    return Response(succeed=False, error="unhandled error", error_display=message), InternalServerError.code


@app.get("/health")
def health_check():
    if shutdown:
        logger.info("server is shutting down")
        return "server is shutting down", 503
    return {"success": True}
