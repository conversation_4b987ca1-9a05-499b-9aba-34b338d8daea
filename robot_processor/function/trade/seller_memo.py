import arrow
from loguru import logger
from more_itertools import first
from robot_types.func.trade import QueryTradeLatestSellerMemoABC
from robot_types.func.trade import UpdateTradeSellerMemoABC
from robot_types.model.resource import RemarkUpdatePolicy

from robot_processor.enums import AuthType


class QueryTradeLatestSellerMemo(QueryTradeLatestSellerMemoABC):
    def _validate_parameters(self):
        if not self.is_shop_set():
            raise ValueError("未提供店铺平台信息")
        if not self.is_tid_set():
            raise ValueError("未提供订单号信息")

    def _execute(self):
        from robot_processor.enums import ShopPlatform

        match self.shop.platform:
            case ShopPlatform.TAOBAO | ShopPlatform.TMALL:
                return self.taobao()
            case ShopPlatform.PDD:
                return self.pdd()
            case ShopPlatform.DOUDIAN:
                return self.doudian()
            case ShopPlatform.KUAISHOU:
                return self.ks()
            case ShopPlatform.JD:
                return self.jd()
            case ShopPlatform.XIAOHONGSHU:
                return self.xhs()
            case _:
                raise NotImplementedError("未支持的平台店铺")

    def memo_skeleton(self):
        from robot_types.model.component import SellerMemo

        return SellerMemo(_sid=self.shop.sid, _platform=self.shop.platform, _tid=self.tid)

    def taobao(self):
        """淘宝"""
        from robot_processor.client import taobao_client
        from robot_processor.enums import MemoFlag
        from robot_processor.shop.models import Shop
        from robot_processor.utils import raise_exception

        shop = Shop.Queries.optimal_shop_by_sid(self.shop.sid, platform=self.shop.platform)
        if not shop:
            raise ValueError("未查询到店铺信息")
        grant_record = shop.get_recent_record()
        if not grant_record:
            raise ValueError("未查询到店铺授权信息")
        access_token = grant_record.access_token
        response = taobao_client.trade_seller_memos_get(access_token, self.tid).unwrap_or_else(raise_exception)
        memo = self.memo_skeleton()
        if "memos" not in response:
            memo.flag = MemoFlag.BLANK  # type: ignore[unreachable]
        else:
            taobao_memo = sorted(response["memos"], key=lambda x: x["timestamp"], reverse=True)[0]
            memo._raw_flag = str(taobao_memo["flag_id"])
            memo.flag = MemoFlag.from_taobao_flag_id(taobao_memo["flag_id"])
            memo.content = taobao_memo["memo"]
        return memo

    def pdd(self):
        """拼多多"""
        from robot_processor.client import pdd_bridge_client
        from robot_processor.client.schema import PddOrderInformationGet
        from robot_processor.enums import MemoFlag

        payload = PddOrderInformationGet(order_sn=self.tid)
        response = pdd_bridge_client.order_information_get(store_id=self.shop.sid, payload=payload)
        memo = self.memo_skeleton()
        if response.order_info_get_response.order_info.remark_tag is None:
            memo.flag = MemoFlag.BLANK  # type: ignore[unreachable]
        else:
            memo._raw_flag = str(response.order_info_get_response.order_info.remark_tag)
            memo.flag = MemoFlag.from_pdd_flag_id(response.order_info_get_response.order_info.remark_tag)
        memo.content = response.order_info_get_response.order_info.remark
        return memo

    def doudian(self):
        """抖店"""
        from robot_processor.client import doudian_cloud
        from robot_processor.enums import MemoFlag

        trade = doudian_cloud.get_order_detail(store_id=self.shop.sid, order_id=self.tid)
        memo = self.memo_skeleton()
        memo._raw_flag = str(trade.seller_remark_stars)
        memo.flag = MemoFlag.from_doudian_flag_id(trade.seller_remark_stars)
        memo.content = trade.seller_words
        return memo

    def jd(self):
        """京东"""
        from robot_processor.client import jd_lyzr_client
        from robot_processor.enums import MemoFlag
        from robot_processor.error.client_request import JdRequestError

        response = jd_lyzr_client.query_vender_remark(store_id=self.shop.sid, order_id=self.tid)
        if not (
            response.venderRemarkQueryResult
            and response.venderRemarkQueryResult.api_jos_result
            and response.venderRemarkQueryResult.api_jos_result.success
        ):
            raise JdRequestError(req=f"sid={self.shop.sid}, tid={self.tid}", res=response.json(ensure_ascii=False))
        memo = self.memo_skeleton()
        if response.venderRemarkQueryResult.vender_remark:
            memo._raw_flag = str(response.venderRemarkQueryResult.vender_remark.flag)
            memo.flag = MemoFlag.from_jd_flag_id(response.venderRemarkQueryResult.vender_remark.flag)
            memo.content = response.venderRemarkQueryResult.vender_remark.remark
        return memo

    def xhs(self):
        from robot_processor.client import xiaohongshu_client
        from robot_processor.enums import MemoFlag
        from robot_processor.shop.models import Shop

        shop = Shop.Queries.optimal_shop_by_sid(self.shop.sid, platform=self.shop.platform)
        if not shop:
            raise ValueError("未查询到店铺信息")

        response = xiaohongshu_client.order_detail(shop.get_access_token(AuthType.XHS_ERP), order_id=self.tid)
        if not response.success:
            raise ValueError("找不到订单")
        memo = self.memo_skeleton()
        memo._raw_flag = str(response.data.sellerRemarkFlag)
        memo.flag = MemoFlag.from_doudian_flag_id(response.data.sellerRemarkFlag)
        memo.content = response.data.sellerRemark
        return memo

    def ks(self):
        """快手"""
        from robot_processor.client import ks_client
        from robot_processor.enums import MemoFlag
        from robot_processor.shop.models import Shop

        shop = Shop.Queries.optimal_shop_by_sid(self.shop.sid, platform=self.shop.platform)
        if not shop:
            raise ValueError("未查询到店铺信息")

        access_token = shop.get_access_token()
        response = ks_client.query_order(access_token, self.tid)
        memo = self.memo_skeleton()
        if response.data.orderNote and response.data.orderNote.orderNoteInfo:
            latest = first(sorted(response.data.orderNote.orderNoteInfo, key=lambda x: x.createTime, reverse=True))
            memo._raw_flag = latest.flagTagCode
            memo.flag = MemoFlag.from_ks_flag_id(latest.flagTagCode)
            memo.content = latest.note
        return memo


class UpdateTradeSellerMemo(UpdateTradeSellerMemoABC):
    def _validate_parameters(self):
        if not self.is_shop_set():
            raise ValueError("未提供店铺平台信息")
        if not self.is_tid_set():
            raise ValueError("未提供订单号信息")
        if not (self.is_content_set() or self.is_flag_set()):
            raise ValueError("未提供备注内容")
        if not self.is_update_policy_set():
            raise ValueError("未提供更新策略")
        self.fetcher = QueryTradeLatestSellerMemo(shop=self.shop, tid=self.tid)

    @property
    def logger(self):
        return logger.bind(sid=self.shop.sid, tid=self.tid)

    def time_stamp_str(self):
        now = arrow.now(tz="Asia/Shanghai")
        match self.time_stamp:
            case "月/日/具体时间":
                return now.format("MM/DD HH:mm:ss")
            case "年/月/日/具体时间":
                return now.format("YYYY/MM/DD HH:mm:ss")
            case "月/日":
                return now.format("MM/DD")
            case _:
                return ""

    def _execute(self):
        from robot_processor.enums import ShopPlatform

        match self.shop.platform:
            case ShopPlatform.TAOBAO | ShopPlatform.TMALL:
                self.taobao()
                return self.fetcher.taobao()
            case ShopPlatform.PDD:
                self.pdd()
                return self.fetcher.pdd()
            case ShopPlatform.DOUDIAN:
                self.doudian()
                return self.fetcher.doudian()
            case ShopPlatform.JD:
                self.jd()
                return self.fetcher.jd()
            case ShopPlatform.KUAISHOU:
                self.ks()
                return self.fetcher.ks()
            case ShopPlatform.XIAOHONGSHU:
                self.xhs()
                return self.fetcher.xhs()
        raise NotImplementedError("未支持的平台")

    def taobao(self):
        from robot_processor.client import taobao_client
        from robot_processor.enums import MemoFlag
        from robot_processor.shop.models import Shop
        from robot_processor.utils import raise_exception

        shop = Shop.Queries.optimal_shop_by_sid(self.shop.sid, platform=self.shop.platform)
        if not shop:
            raise ValueError("未查询到店铺信息")
        grant_record = shop.get_recent_record()
        if not grant_record:
            raise ValueError("未查询到店铺授权信息")
        access_token = grant_record.access_token
        if self.is_content_set():
            # 默认是覆盖
            if self.update_policy == RemarkUpdatePolicy.APPEND:
                current_memo = self.fetcher.taobao()
                content = (current_memo.content or "") + self.content
            else:
                content = self.content
            content += self.time_stamp_str()
        else:
            content = None
        if self.is_flag_set():
            flag = MemoFlag(self.flag).taobao_flag_id()
        else:
            flag = None
        self.logger.info(f"更新备注内容: {content}, 旗帜: {flag}")
        taobao_client.trade_memo_update(access_token, tid=self.tid, memo=content, flag=flag).unwrap_or_else(
            raise_exception
        )

    def pdd(self):
        from robot_processor.client import pdd_bridge_client
        from robot_processor.enums import MemoFlag

        if self.is_flag_set():
            pdd_tag_mapper = dict((v, k) for k, v in pdd_bridge_client.memo_tag_mapper().items())
            try:
                tag_name = pdd_tag_mapper[MemoFlag(self.flag).pdd_flag_id()]
            except KeyError:
                raise KeyError(f"拼多多平台不支持 {self.flag}")
        else:
            tag_name = None
        if self.is_content_set():
            if self.update_policy == RemarkUpdatePolicy.APPEND:
                current_memo = self.fetcher.pdd()
                content = (current_memo.content or "") + self.content
            else:
                content = self.content
            content += self.time_stamp_str()
        else:
            # 拼多多备注接口 content 为必填
            content = ""
        self.logger.info(f"更新备注内容: {content}, 旗帜: {tag_name}")
        pdd_bridge_client.update_memo(store_id=self.shop.sid, order_sn=self.tid, memo=content, tag_name=tag_name)

    def doudian(self):
        from robot_processor.client import doudian_cloud
        from robot_processor.enums import MemoFlag

        if self.is_content_set():
            if self.update_policy == RemarkUpdatePolicy.APPEND:
                current_memo = self.fetcher.doudian()
                content = (current_memo.content or "") + self.content
            else:
                content = self.content
            content += self.time_stamp_str()
        else:
            # 抖店备注接口 content 为必填
            content = ""
        if self.is_flag_set():
            flag = str(MemoFlag(self.flag).doudian_flag_id())
        else:
            flag = None
        self.logger.info(f"更新备注内容: {content}, 旗帜: {flag}")
        doudian_cloud.update_memo(store_id=self.shop.sid, order_id=self.tid, memo=content, star=flag)

    def jd(self):
        from robot_processor.client import jd_lyzr_client
        from robot_processor.enums import MemoFlag

        if self.is_content_set():
            if self.update_policy == RemarkUpdatePolicy.APPEND:
                current_memo = self.fetcher.jd()
                content = (current_memo.content or "") + self.content
            else:
                content = self.content
            content += self.time_stamp_str()
        else:
            content = ""
        if self.is_flag_set():
            flag = MemoFlag(self.flag).jd_flag_id()
        else:
            flag = None
        self.logger.info(f"更新备注内容: {content}, 旗帜: {flag}")
        jd_lyzr_client.update_memo(store_id=self.shop.sid, order_id=self.tid, memo=content, flag=flag)

    def ks(self):
        from robot_processor.client import ks_client
        from robot_processor.enums import MemoFlag
        from robot_processor.shop.models import Shop

        shop = Shop.Queries.optimal_shop_by_sid(self.shop.sid, platform=self.shop.platform)
        if not shop:
            raise ValueError("未查询到店铺信息")
        access_token = shop.get_access_token()
        if self.is_content_set():
            if self.update_policy == RemarkUpdatePolicy.OVERWRITE:
                raise TypeError("快手平台仅支持追加备注")
            content = self.content + self.time_stamp_str()
        else:
            content = None
        if self.is_flag_set():
            flag = MemoFlag(self.flag).ks_flag_id()
        else:
            flag = None
        self.logger.info(f"更新备注内容: {content}, 旗帜: {flag}")
        ks_client.memo_add(access_token=access_token, oid=self.tid, note=content, flag=flag)

    def xhs(self):
        from robot_processor.client import xiaohongshu_client
        from robot_processor.enums import AuthType
        from robot_processor.enums import MemoFlag
        from robot_processor.shop.models import Shop

        shop = Shop.Queries.optimal_shop_by_sid(self.shop.sid, platform=self.shop.platform)
        if not shop:
            raise ValueError("未查询到店铺信息")
        access_token = shop.get_access_token(AuthType.XHS_ERP)
        if self.is_content_set():
            if self.update_policy == RemarkUpdatePolicy.APPEND:
                current_memo = self.fetcher.xhs()
                content = (current_memo.content or "") + self.content
            else:
                content = self.content
            content += self.time_stamp_str()
        else:
            content = ""
        if self.is_flag_set():
            flag = MemoFlag(self.flag).xhs_flag_id()
        else:
            flag = MemoFlag.GRAY

        self.logger.info(f"更新备注内容: {content}, 旗帜: {flag}")
        xiaohongshu_client.order_memo(access_token, self.tid, content, "飞梭", flag)
