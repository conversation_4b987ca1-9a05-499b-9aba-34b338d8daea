from google.protobuf.internal import type_checkers
from result import as_result

from robot_processor.client import pdd_bridge_client
from robot_processor.client import trade_client
from robot_processor.client.errors import PddServiceError
from robot_processor.client.schema import PddRefundDetailPayload
from robot_processor.client.schema import PddRefundDetailResponse
from robot_processor.db import db
from robot_processor.form.symbol_table import NamedTypeSpec
from robot_processor.form.symbol_table import PlatformOrderInfo
from robot_processor.form.symbol_table import TypeSpec
from robot_processor.form.symbol_table import Value
from robot_processor.function.base import FnArg
from robot_processor.function.base import FnArgs
from robot_processor.function.base import FnBase
from robot_processor.function.base import FnSignature
from robot_processor.refund.models import TaobaoRefund
from robot_processor.shop.models import Shop


class GetPlatformOrderInfo(FnBase):
    """获取平台子订单信息

    fn get_platform_order_info()
    """

    class Args(FnArgs):
        # 平台订单号
        PLATFORM_TRADE_ID = FnArg(name="platform_trade_id", type_spec=TypeSpec(type="string"))
        SID = FnArg(name="sid", type_spec=TypeSpec(type="string"))

    Signature = FnSignature(
        name="get_platform_order_info",
        args=Args,
        rtype=TypeSpec(type="array", spec=[NamedTypeSpec.PLATFORM_ORDER_INFO.type_spec]),
    )

    @as_result(TypeError, ValueError)
    def call(self) -> Value:
        # 先支持淘宝
        platform_trade_id = self.get_arg_value(self.Args.PLATFORM_TRADE_ID, rtype=str).expect("缺少平台订单号")
        sid = self.get_arg_value(self.Args.SID, rtype=str).expect("缺少店铺SID信息")
        shop: Shop | None = Shop.query.filter_by(sid=sid).first()
        if not shop:
            raise ValueError("未找到店铺")
        order_infos = []
        if shop.is_taobao():
            trade_info = trade_client.get_trade_by_tid(sid=sid, tid=platform_trade_id)
            if trade_info:
                for order in trade_info.orders:
                    return_logistics_no = ""
                    return_logistics_company = ""
                    refund_reason = ""
                    refund_amount = None
                    readable_refund_type = ""
                    readable_refund_status = ""
                    if order.refund_id:
                        int_refund_id = int(order.refund_id)
                        refund = db.session.get(TaobaoRefund, int_refund_id)
                        if refund:
                            refund_info = refund.get_refund_info()
                            if refund_info.refund_fee:
                                refund_amount = refund_info.refund_fee  # 单位元
                            if refund_info.reason:
                                refund_reason = refund_info.reason
                            if refund_info.sid:
                                return_logistics_no = refund_info.sid
                            if refund_info.company_name:
                                return_logistics_company = refund_info.company_name
                            if refund_info.dispute_type:
                                readable_refund_type = refund_info.dispute_type.label
                            if refund_info.status:
                                readable_refund_status = refund_info.status.label
                    order_info = PlatformOrderInfo(
                        order_id=order.oid,
                        spu_title=order.title,
                        spu_id=order.spu_id,
                        quantity=order.quantity,
                        price=order.price,
                        sku_description=order.sku_description,
                        sku_id=order.sku_id,
                        outer_sku_id=order.outer_sku_id,
                        pay_amount=type_checkers.ToShortestFloat(order.divide_order_fee),  # type: ignore[attr-defined]
                        refund_id=order.refund_id,
                        logistics_no=order.shipping.tracking_num,
                        logistics_company=order.shipping.contractor,
                        return_logistics_no=return_logistics_no,
                        return_logistics_company=return_logistics_company,
                        refund_reason=refund_reason,
                        refund_amount=refund_amount,
                        readable_refund_type=readable_refund_type,
                        readable_refund_status=readable_refund_status,
                    )
                    order_infos.append(order_info)
        elif shop.is_pdd():
            resp = trade_client.get_trade_by_tid_and_channel([platform_trade_id], "PDD", sid=sid)
            if resp.trade_info_list:
                pdd_trade = resp.trade_info_list[0].pdd_trade_info

                pdd_refund_info: PddRefundDetailResponse | None = None
                try:
                    pdd_refund_info = pdd_bridge_client.refund_detail(
                        store_id=sid,
                        payload=PddRefundDetailPayload(order_sn=platform_trade_id),
                    )
                except PddServiceError as e:
                    if "该售后单不存在于授权店铺中" not in str(e):
                        raise ValueError(str(e))
                for item in pdd_trade.item_list:
                    order_info = PlatformOrderInfo(
                        order_id=None,
                        spu_title=item.goods_name,
                        spu_id=item.goods_id,
                        quantity=item.goods_count,
                        price=type_checkers.ToShortestFloat(item.goods_price),  # type: ignore[attr-defined]
                        sku_description="",
                        sku_id=item.sku_id,
                        outer_sku_id=item.outer_id,
                        pay_amount=None,
                        refund_id=str(pdd_refund_info.id) if pdd_refund_info else None,
                        logistics_no=pdd_trade.logistics_info.tracking_number,
                        logistics_company=pdd_trade.logistics_info.logistics_name,
                        return_logistics_no=pdd_refund_info.express_no if pdd_refund_info else None,
                        return_logistics_company=pdd_refund_info.shipping_name if pdd_refund_info else None,
                        refund_reason=pdd_refund_info.after_sales_reason if pdd_refund_info else None,
                        refund_amount=round(pdd_refund_info.refund_amount / 100.0, 2) if pdd_refund_info else None,
                        readable_refund_type=(
                            GetPlatformOrderInfo.convert_pdd_after_sales_type(pdd_refund_info.after_sales_type)
                            if pdd_refund_info
                            else None
                        ),
                        readable_refund_status=(
                            GetPlatformOrderInfo.convert_pdd_after_sales_status(pdd_refund_info.after_sales_status)
                            if pdd_refund_info
                            else None
                        ),
                    )
                    order_infos.append(order_info)
        elif shop.is_doudian():
            resp = trade_client.get_trade_by_tid_and_channel([platform_trade_id], "DOUDIAN", sid=sid)
            if resp.trade_info_list:
                doudian_trade = resp.trade_info_list[0].dy_trade_info
                from robot_processor.client import doudian_cloud

                aftersale_list_resp = doudian_cloud.get_aftersale_list(store_id=shop.sid, order_id=platform_trade_id)
                for sku_order in doudian_trade.sku_order_list:
                    aftersales = [
                        aftersale
                        for aftersale in aftersale_list_resp.items
                        if (aftersale.order_info.related_order_info[0].sku_order_id == sku_order.order_id)
                        and (aftersale.aftersale_info.aftersale_status != 8)
                    ]
                    order_info = PlatformOrderInfo(
                        order_id=doudian_trade.order_id,
                        spu_title=sku_order.product_name,
                        spu_id=sku_order.product_id,
                        quantity=sku_order.item_num,
                        price=round(sku_order.goods_price / 100.0, 2),
                        sku_description="",
                        sku_id=sku_order.sku_id,
                        outer_sku_id=sku_order.out_sku_id,
                        pay_amount=round(sku_order.pay_amount / 100.0, 2),
                        refund_id=aftersales[0].aftersale_info.aftersale_id if aftersales else None,
                        logistics_no=(
                            doudian_trade.logistics_info[0].tracking_no if doudian_trade.logistics_info else None
                        ),
                        logistics_company=(
                            doudian_trade.logistics_info[0].company_name if doudian_trade.logistics_info else None
                        ),
                        return_logistics_no=aftersales[0].aftersale_info.return_logistics_code if aftersales else None,
                        return_logistics_company=(
                            aftersales[0].aftersale_info.return_logistics_company_name if aftersales else None
                        ),
                        refund_reason=aftersales[0].text_part.reason_text if aftersales else None,
                        refund_amount=(
                            round(
                                aftersales[0].aftersale_info.refund_amount / 100.0,
                                2,
                            )
                            if aftersales
                            else None
                        ),
                        readable_refund_type=(
                            GetPlatformOrderInfo.convert_doudian_after_sales_type(
                                aftersales[0].aftersale_info.aftersale_type
                            )
                            if aftersales
                            else None
                        ),
                        readable_refund_status=(
                            GetPlatformOrderInfo.convert_doudian_after_sales_status(
                                aftersales[0].aftersale_info.aftersale_status
                            )
                            if aftersales
                            else None
                        ),
                    )
                    order_infos.append(order_info)
        elif shop.is_jd():
            resp = trade_client.get_trade_by_tid_and_channel([platform_trade_id], "JD", sid=sid)
            if resp.trade_info_list:
                jd_trade = resp.trade_info_list[0].jd_trade_info
                # 京东有退款单和售后单，暂时先不做售后信息
                for item in jd_trade.jd_items:
                    order_info = PlatformOrderInfo(
                        order_id=None,
                        spu_title=item.ware_title,
                        spu_id=item.ware_id,
                        quantity=item.qty,
                        price=item.jd_price,
                        sku_description=item.property,
                        sku_id=item.sku_id,
                        outer_sku_id=item.outer_sku_id,
                        pay_amount=None,
                    )
                    order_infos.append(order_info)
        ret = [NamedTypeSpec.PLATFORM_ORDER_INFO.model.validate(order).dict() for order in order_infos]
        return self.wrap_return(ret)

    @classmethod
    def convert_pdd_after_sales_status(cls, after_sales_status: int):
        return {
            0: "无售后",
            2: "买家申请退款，待商家处理",
            3: "退货退款，待商家处理",
            4: "商家同意退款，退款中",
            5: "平台同意退款，退款中",
            6: "驳回退款，待买家处理",
            7: "已同意退货退款,待用户发货",
            8: "平台处理中",
            9: "平台拒绝退款，退款关闭",
            10: "退款成功",
            11: "买家撤销",
            12: "买家逾期未处理，退款失败",
            13: "买家逾期，超过有效期",
            14: "换货补寄待商家处理",
            15: "换货补寄待用户处理",
            16: "换货补寄成功",
            17: "换货补寄失败",
            18: "换货补寄待用户确认完成",
            21: "待商家同意维修",
            22: "待用户确认发货",
            24: "维修关闭",
            25: "维修成功",
            27: "待用户确认收货",
            31: "已同意拒收退款，待用户拒收",
            32: "补寄待商家发货",
            33: "待商家召回",
        }.get(after_sales_status)

    @classmethod
    def convert_pdd_after_sales_type(cls, after_sales_type: int):
        return {
            1: "全部",
            2: "仅退款",
            3: "退货退款",
            4: "换货",
            5: "缺货补寄",
            6: "维修",
        }.get(after_sales_type)

    @classmethod
    def convert_doudian_after_sales_type(cls, after_sales_type: int) -> str:
        after_sales_type_map = {
            0: "售后退货退款",
            1: "售后仅退款",
            2: "发货前退款",
            3: "换货",
            4: "系统取消",
            5: "用户取消",
            6: "价保",
            7: "补寄",
            8: "维修",
        }
        return after_sales_type_map.get(after_sales_type, str(after_sales_type))

    @classmethod
    def convert_doudian_after_sales_status(cls, after_sales_status: int) -> str:
        after_sales_status_map = {
            6: "售后申请",
            7: "售后退货中",
            8: "售后待商家发货",
            11: "售后已发货",
            12: "售后成功",
            13: "售后商家已发货，待用户收货",
            14: "售后用户已收货",
            27: "拒绝售后申请",
            # 发货前商家拒绝售后 or 买家/客服取消售后 or 系统关单都会到达28
            # 但是发货后商家拒绝不会到达28，都是买家/客服取消 or 系统关单
            28: "售后失败",
            29: "售后退货拒绝",
            51: "订单取消成功",
            53: "逆向交易已完成",
        }
        return after_sales_status_map.get(after_sales_status, str(after_sales_status))
