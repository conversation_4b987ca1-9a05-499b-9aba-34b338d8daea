from __future__ import annotations

from dataclasses import dataclass
from dataclasses import field
from datetime import date
from decimal import Decimal
from typing import Literal
from typing import TypedDict

import arrow
from loguru import logger
from robot_types.func import conversion


class NumberCalculator(conversion.render.NumberCalculatorABC):
    def _validate_parameters(self):
        super()._validate_parameters()
        if not self.is_context_set():
            raise ValueError("未提供上下文信息")
        if not self.is_expression_set():
            raise ValueError("未提供表达式")

    def _execute(self):
        expression = Parser(self.expression).parse_expression()
        return expression.evaluate(self.context)


@dataclass
class ASTNode:
    def evaluate(self, context):
        raise NotImplementedError

    def repr(self, context):
        raise NotImplementedError


@dataclass
class SchemaNode(ASTNode):
    step_uuid: str
    target: list[SchemaNodeTokenTargetListItem]

    def evaluate(self, context):
        from robot_types.core.type_spec import TypeSpec
        from robot_types.core.value import PathIndicator
        from robot_types.core.value import Value
        from robot_types.core.value import Var
        from robot_types.helper import ValueResolver

        from robot_processor.utils import raise_exception

        value_resolver = ValueResolver(context)
        path_indicators = []
        for item in self.target[:-1]:
            array_flag = False if item["type"] == "collection" else True
            path_indicators.append(PathIndicator(item["key"], array_flag, False))
        path_indicators.append(PathIndicator(self.target[-1]["key"], False, False))
        var = Var.init_by_indicators(path_indicators)
        is_array = any([indicator.array_flag for indicator in path_indicators]) or self.target[-1]["type"] in (
            "table",
            "array",
        )
        if is_array:
            type_spec = TypeSpec("array")
        else:
            try:
                if self.target[-1]["type"] in ("datetime", "date"):
                    type_spec = TypeSpec(self.target[-1]["type"])
                else:
                    type_spec = TypeSpec("number")
            except Exception:  # noqa
                type_spec = TypeSpec("number")
        value = Value(type_spec, var=var)
        raw_value = value.with_resolver(value_resolver).resolve().unwrap_or_else(raise_exception)
        if is_array:
            raw_value = raw_value or []
        else:
            raw_value = raw_value or Decimal(0)
            if isinstance(raw_value, date):
                raw_value = arrow.get(raw_value).int_timestamp
        logger.info(f"[SchemaNode] value={raw_value}, path={var.path}")
        return raw_value

    def repr(self, context):
        return "{}: {}".format("/".join(item["label"] for item in self.target), self.evaluate(context))


@dataclass
class ConstantNode(ASTNode):
    value: float | int

    def evaluate(self, context):
        logger.info(f"[ConstantNode] value={self.value}")
        return self.value

    def repr(self, context):
        return "const: {}".format(self.value)


@dataclass
class BinaryOpNode(ASTNode):
    op: Literal["+", "-", "*", "/"]
    left: ASTNode
    right: ASTNode

    def evaluate(self, context):
        left_value = safe_decimal(self.left.evaluate(context), default=0)
        right_value = safe_decimal(self.right.evaluate(context), default=0)
        logger.info(f"[BinaryOpNode] {left_value} {self.op} {right_value}")
        if self.op == "+":
            return left_value + right_value
        elif self.op == "-":
            return left_value - right_value
        elif self.op == "*":
            return left_value * right_value
        elif self.op == "/":
            return left_value / right_value
        else:
            raise Exception(f"Unknown operator {self.op}")

    def repr(self, context):
        return "{} {} {}".format(self.left.repr(context), self.op, self.right.repr(context))


@dataclass
class FunctionCallNode(ASTNode):
    value: Literal["SUM", "COUNTS", "ROWS", "SECOND_DIFF"]
    args: list[ASTNode] = field(default_factory=list)

    def evaluate(self, context):
        from robot_processor.utils import flatten

        args = [arg.evaluate(context) for arg in self.args]
        logger.info(f"[FunctionCallNode] {self.value}({args})")
        if self.value == "SUM":
            return sum(filter(None, flatten(args[0])))
        elif self.value == "COUNTS":
            return len(args[0])
        elif self.value == "ROWS":
            return len(args[0])
        elif self.value == "SECOND_DIFF":
            return args[0] - args[1]
        else:
            raise Exception(f"Unknown function {self.value}")

    def repr(self, context):
        return "{}({})".format(self.value, [arg.repr(context) for arg in self.args])


class SchemaNodeTokenStep(TypedDict):
    uuid: str


class SchemaNodeTokenTargetListItem(TypedDict):
    type: str
    key: str
    label: str


class SchemaNodeToken(TypedDict):
    step: SchemaNodeTokenStep
    target: list[SchemaNodeTokenTargetListItem]
    operatorType: Literal["schema"]


class ConstantNodeToken(TypedDict):
    value: float | int
    operatorType: Literal["constant"]


class BinaryOpNodeToken(TypedDict):
    value: Literal["+", "-", "*", "/"]
    operatorType: Literal["symbol"]


class ParenthesesNodeToken(TypedDict):
    value: Literal["(", ")"]
    operatorType: Literal["parentheses"]


class FunctionCallNodeToken(TypedDict):
    value: str
    operatorType: Literal["function"]


class SeperatorNodeToken(TypedDict):
    value: Literal[","]
    operatorType: Literal["separator"]


class Parser:
    def __init__(
        self,
        tokens: list[SchemaNodeToken | BinaryOpNodeToken | ParenthesesNodeToken | FunctionCallNodeToken],
    ):
        self.tokens = tokens
        self.pos = 0

    def current_token(self):
        if self.pos < len(self.tokens):
            return self.tokens[self.pos]
        return None

    def eat(self, token_value=None, token_type=None):
        token = self.current_token()
        if token is None:
            raise Exception("Unexpected end of tokens")
        if token_value is not None and token["value"] != token_value:
            raise Exception(f"Expected token value {token_value} but got {token['value']}")
        if token_type is not None and token["operatorType"] != token_type:
            raise Exception(f"Expected token type {token_type} but got {token['type']}")
        self.pos += 1
        return token

    # 解析表达式：加减运算（最低优先级）
    def parse_expression(self):
        node = self.parse_term()
        while True:
            token = self.current_token()
            if token is not None and token["operatorType"] == "symbol" and token["value"] in ["+", "-"]:
                op = token["value"]
                self.eat(op, "symbol")
                right = self.parse_term()
                node = BinaryOpNode(op, node, right)
            else:
                break
        return node

    # 解析项：乘除运算
    def parse_term(self):
        node = self.parse_factor()
        while True:
            token = self.current_token()
            if token is not None and token["operatorType"] == "symbol" and token["value"] in ["*", "/"]:
                op = token["value"]
                self.eat(op, "symbol")
                right = self.parse_factor()
                node = BinaryOpNode(op, node, right)
            else:
                break
        return node

    # 解析因子：常量、括号、函数调用、列表字面量
    def parse_factor(self):
        token = self.current_token()
        if token is None:
            raise Exception("Unexpected end of tokens in factor")

        # 常量
        if token["operatorType"] == "schema":
            self.eat(None, "schema")
            return SchemaNode(step_uuid=token["step"]["uuid"], target=token["target"])

        elif token["operatorType"] == "constant":
            self.eat(None, "constant")
            return ConstantNode(token["value"])

        # 括号：优先解析括号内的表达式
        elif token["operatorType"] == "parentheses" and token["value"] == "(":
            self.eat("(", "parentheses")
            node = self.parse_expression()
            self.eat(")", "parentheses")
            return node

        # 函数调用
        elif token["operatorType"] == "function":
            func_name = token["value"]
            self.eat(func_name, "function")
            self.eat("(", "parentheses")
            args = self.parse_argument_list()
            self.eat(")", "parentheses")
            return FunctionCallNode(func_name, args)

        else:
            raise Exception(f"Unexpected token in factor: {token}")

    # 解析函数调用参数列表，参数之间用逗号分隔
    def parse_argument_list(self):
        args: list[ASTNode] = []
        token = self.current_token()
        # 如果遇到右括号，则说明参数列表为空
        if token is not None and token["operatorType"] == "parentheses" and token["value"] == ")":
            return args
        while True:
            arg = self.parse_expression()
            args.append(arg)
            token = self.current_token()
            if token is not None and token["operatorType"] == "parentheses" and token["value"] == ",":
                self.eat(",", "parentheses")
            else:
                break
        return args


def safe_decimal(val: int | float | Decimal | str | None, default=None):
    if default is not None:
        default = Decimal(default)
    if val is None:
        return default
    if isinstance(val, Decimal):
        return val
    if isinstance(val, (int, str)):
        try:
            return Decimal(val)
        except:  # noqa
            return default
    if isinstance(val, float):
        return Decimal(str(val))
    return default  # type: ignore[unreachable]
