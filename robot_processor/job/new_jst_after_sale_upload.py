from typing import cast

from loguru import logger
from pydantic import BaseModel

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.error.client_request import JstRequestError
from robot_processor.job import AutoJobControllerBase
from robot_processor.job.job_model_wrapper import SKU
from robot_processor.job.job_model_wrapper import ReissueSkus
from robot_processor.job.jst_trade import TradesFinder
from robot_processor.shop.auth_manager import get_jst_shop_id_by_shop
from robot_processor.utils import get_nonce
from rpa.erp.jst import AfterSaleUploadReq
from rpa.erp.jst import JstBaseOrder
from rpa.erp.jst import JstNewSDK
from rpa.erp.jst import JstQmSDK
from rpa.erp.jst import JstSDK
from rpa.erp.jst import OpenOrdersOutSimpleQuery


class AfterSaleOutput(BaseModel):
    as_id: str


# fixme： 所有售后合成一个executor https://git.leyantech.com/digismart/robot-processor/-/issues/274
class NewJstAfterSaleUploadExecutor(AutoJobControllerBase):
    """
    只做创建补发单操作，可重入
    """

    job_type = JobType.NEW_JST_AFTER_SALE_UPLOAD
    output_model = AfterSaleOutput

    # 下面几个属性，不同的售后单是不一样的,传错状态会导致创建失败
    default_shop_status = AfterSaleUploadReq.ShopStatus.WAIT_SELLER_AGREE
    default_good_status = AfterSaleUploadReq.GoodStatus.BUYER_NOT_RECEIVED
    default_type = "补发"

    def process(self):
        try:
            data = self.gen_jst_data()
        except Exception as e:
            logger.exception("get jst_data error.")
            return JobStatus.FAILED, str(e)
        try:
            resp = JstSDK(self.shop.sid).aftersale_upload([data])
        except JstRequestError as e:
            logger.exception("jst request error.")
            return JobStatus.FAILED, e.message
        if resp.datas and resp.datas[0].as_id:
            self.states.write_job_widget_collection(data={"as_id": resp.datas[0].as_id, "o_id": resp.datas[0].o_id})
            # 兼容老的版本
            self.states.write_job_output({"as_id": resp.datas[0].as_id, "o_id": resp.datas[0].o_id})
            return JobStatus.SUCCEED, None
        else:
            return JobStatus.FAILED, "创建售后单失败"

    def gen_jst_data(self):
        """这个函数中的输出项，为None的最后会被过滤掉"""
        tid = self.args.get_trades()[0].tid

        question_type = self.args.get_arg_as_str_by_task_arg_name("question_type") or "其他"
        good_status = self.args.get_arg_as_str_by_task_arg_name("good_status") or self.default_good_status
        shop_status = self.args.get_arg_as_str_by_task_arg_name("shop_status") or self.default_shop_status

        if self.args.is_argument_configured("wms_co_name"):
            wms_co_name = self.args.get_arg_as_str_by_task_arg_name("wms_co_name")
            wms_co_id = JstSDK(self.shop.sid or "").get_wms_co_id_by_name(wms_co_name, self.shop.org_id)
        else:
            wms_co_id = None

        return AfterSaleUploadReq(
            shop_id=self.jst_shop_id(tid, self.shop),
            outer_as_id=get_nonce(),
            so_id=tid,
            type=self.args.get_arg_as_str_by_task_arg_name("type") or self.default_type,
            logistics_company=self.args.get_arg_as_str_by_task_arg_name("logistics_company") or None,
            l_id=self.args.get_arg_by_task_arg_name("l_id"),
            remark=self.args.get_arg_as_str_by_task_arg_name("remark"),
            send_lc_id=self.args.get_arg_as_str_by_task_arg_name("send_lc_id") or None,
            send_lc_name=self.args.get_arg_as_str_by_task_arg_name("send_lc_name") or None,
            question_type=question_type,
            items=AfterSaleUploadReq.convert_items(self.get_items(), ""),
            # 下面这些都是随便填的，后续调试有需求的话通过参数暴露出来
            total_amount=float(self.args.get_arg_by_task_arg_name("total_amount") or 0.0),
            refund=float(self.args.get_arg_by_task_arg_name("refund") or 0.0),
            payment=float(self.args.get_arg_by_task_arg_name("payment") or 0.0),
            shop_status=shop_status,
            good_status=good_status,
            wms_co_id=wms_co_id,
            new_address=self.args.get_arg_by_task_arg_name("new_address"),
        ).fill_address()

    def jst_shop_id(self, tid, shop):
        from robot_processor.plugin.trade_utils import ErpTradeManager

        try:
            order = self.get_origin_order(tid, shop)
            return order.shop_id
        except:  # noqa
            params = OpenOrdersOutSimpleQuery(so_ids=[tid])
            out_order_resp = ErpTradeManager.query_out_orders_jst(
                JstQmSDK(self.shop.sid), JstNewSDK(self.shop.sid), params
            )
            if out_order_resp.datas:
                return out_order_resp.datas[0].shop_id
            shop_id = get_jst_shop_id_by_shop(self.shop)
            if shop_id is None:
                raise ValueError(f"店铺 {self.shop.title} 未关联聚水潭店铺")
            return cast(int, shop_id)

    @staticmethod
    def get_origin_order(tid, shop) -> JstBaseOrder:
        orders = TradesFinder(
            tid=tid,
            # 和产品沟通，原单补发只补发普通订单
            is_reissue="0",
            shop=shop,
        ).try_get_orders()
        if not orders:
            logger.error(f"找不到原订单, tid: {tid}")
            raise Exception("找不到原订单")
        order = sorted(orders, key=lambda x: x.o_id)[0]
        return order

    def get_items(self) -> ReissueSkus:
        """不同的售后单类型对于上传商品的要求不一样，这里需要两组商品，有些可能只要一组，他们的arg_name也不一样"""
        ret: ReissueSkus = self.get_reissue_skus_for_jst()
        assert ret.sku_list, "商品列表不能为空"
        assert ret.sku_list[0].type, "商品类型不能为空"
        return ret

    def get_reissue_skus_for_jst(self, arg_name="sku_id", outer_type="补发", inner_type="补发"):
        """支持从补发商品组件中获取 原单/自定义 补发商品信息"""
        reissue_skus: ReissueSkus = self.args.get_reissue_skus(arg_name)
        if reissue_skus.after_sales_type == "original":
            order = self.get_origin_order(self.args.get_trades()[0].tid, self.shop)
            skus_list = []
            for item in order.items:
                skus_list.append(
                    SKU(
                        sku_id=item.shop_sku_id,
                        outer_sku_id=item.sku_id,
                        spu_id=item.shop_i_id,
                        outer_spu_id=item.i_id,
                        qty=item.qty,
                        price=item.item_pay_amount,
                        type=inner_type,
                    )
                )

            ret = ReissueSkus(
                sku_list=skus_list,
                after_sales_type=reissue_skus.after_sales_type,
                type=reissue_skus.type or outer_type,
            )
        else:
            ret = reissue_skus
            for sku in ret.sku_list:
                sku.type = inner_type
        return ret
