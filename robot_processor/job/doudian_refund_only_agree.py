from pydantic import BaseModel

from robot_processor.client.schema import DoudianAftersaleOperateResp, \
    DoudianAfterSaleOperateResult
from robot_processor.enums import JobStatus, JobType
from robot_processor.job import AutoJobControllerBase
from robot_processor.client import doudian_cloud


class DoudianRefundOnlyAgreeOutput(BaseModel):
    execution_result: str


class DoudianRefundOnlyAgree(AutoJobControllerBase):
    job_type = JobType.DOUDIAN_REFUND_ONLY_AGREE
    output_model = DoudianRefundOnlyAgreeOutput

    def process(self):
        aftersale_id = self.args.get_arg_by_task_arg_name("aftersale_id")
        if aftersale_id is None:
            return JobStatus.FAILED, "缺失售后单号"
        ignore_repetitive_operation_error = self.args.get_arg_by_task_arg_name(
            "ignore_repetitive_operation_error")
        resp: DoudianAftersaleOperateResp = doudian_cloud.doudian_refund_agree(
            store_id=self.shop.sid, aftersale_id=aftersale_id,
            operate_type=201)
        result: DoudianAfterSaleOperateResult = resp.items.pop()
        if result.status_code == 0:
            self.states.write_job_widget_collection({"execution_result": "成功"})
            return JobStatus.SUCCEED, ""
        if ignore_repetitive_operation_error == "true" and \
                "所审核售后单均已完结" in result.status_msg:
            self.states.write_job_widget_collection(
                {"execution_result": "售后单已被处理"})
            return JobStatus.SUCCEED, ""
        return JobStatus.FAILED, result.status_msg
