from dataclasses import asdict

from robot_types.model.resource import Shop

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.function.trade.seller_memo import QueryTradeLatestSellerMemo
from robot_processor.job.auto_job import AutoJobControllerBase


class QueryLatestSellerMemoExecutor(AutoJobControllerBase):
    job_type = JobType.QUERY_LATEST_SELLER_MEMO

    def process(self):
        trades = self.args.get_trades()
        if not trades:
            return JobStatus.FAILED, "未配置订单"

        fn = QueryTradeLatestSellerMemo(tid=trades[0].tid, shop=Shop(sid=self.shop.sid, platform=self.shop.platform))
        result = fn.call()
        if result.is_err():
            return JobStatus.FAILED, result.unwrap_err()
        else:
            memo = result.unwrap()
        self.states.write_job_output(data={
            "memo": asdict(memo),
            "memo_content": memo.content,
            "memo_flag": memo.flag
        })
        return JobStatus.SUCCEED, None
