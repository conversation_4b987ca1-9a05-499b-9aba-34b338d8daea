from typing import <PERSON><PERSON>

from flask import current_app
from pydantic import BaseModel

from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.client.logistics_clients.jdl_sdk import JdlSD<PERSON>, \
    GenericOrderRequest, CustomerAddress, GenericGoodsInfo
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.form.models import FormSymbol
from robot_processor.form.models import Step
from robot_processor.job import AutoJobControllerBase
from robot_processor.job.job_model_wrapper import Address
from robot_processor.symbol_table.data_converter import convert_to_label_map
from robot_processor.symbol_table.models import Namespace


class Output(BaseModel):
    order_sn: str


class JdServiceCreateOrderExecutor(AutoJobControllerBase):
    job_type = JobType.JDL_SERVICE_CREATE_ORDER
    output_model = Output

    def process(self) -> Tuple[JobStatus, str]:
        trades = self.args.get_trades()
        skus = self.args.get_reissue_skus()
        simple_skus = self.args.get_arg_by_task_arg_name("simple_skus")
        address = self.args.get_address()
        remark = self.args.get_arg_by_task_arg_name("remark") or ""
        if not trades:
            return JobStatus.FAILED, "缺少订单"
        if not skus and not simple_skus:
            return JobStatus.FAILED, "缺少商品"
        if not address:
            return JobStatus.FAILED, "缺少地址"
        if self.shop.org_id == "3043":
            access_token = "e90989d14c8f42bfa32a738779277ece"
        else:
            credentials = self.shop.get_logistic_grant_records(LogisticsType.JDL)
            if not credentials:
                return JobStatus.FAILED, "无京东物流服务授权"
            credential = credentials[0]
            access_token = credential.get("access_token", "")
        jdl_services_skus = current_app.config.get("JDL_SERVICES_SKUS",
                                                   {})

        item_map = jdl_services_skus[self.shop.org_id]
        sdk = JdlSDK(access_token)
        goods_list = []
        if simple_skus:
            symbol_table = FormSymbol.query_symbol_table(self.form_id(),
                                                         self.order.form_version.version_no).unwrap()
            step_id = self.order.form_version.get_first_step()
            step = Step.query.get(step_id)
            if not step:
                return JobStatus.FAILED, "元信息错误"
            namespaces = [namespace for namespace in symbol_table.namespaces if
                          namespace.name == step.step_uuid]
            if not namespaces:
                return JobStatus.FAILED, "元信息错误"
            label_map = convert_to_label_map(self.order.data, Namespace(
                symbols=namespaces[0].symbols))
            for simple_sku in label_map["商品列表"]:
                goodsmodel = item_map[simple_sku["SKU ID"]]
                for item in goodsmodel.split(","):
                    goods = GenericGoodsInfo(
                        goodsCode=item,
                        goodsQty=int(simple_sku["数量"]),
                    )
                    goods_list.append(goods)
            if self.shop.org_id == "3043":
                remark = ",".join(
                    [simple_sku["SKU ID"] + "*" + str(int(simple_sku["数量"]))
                     for simple_sku in label_map["商品列表"]]
                )
        else:
            for sku in skus.sku_list:
                goodsmodel = item_map[sku.sku_id]
                for item in goodsmodel.split(","):
                    goods = GenericGoodsInfo(
                        goodsCode=item,
                        goodsQty=sku.qty)
                    goods_list.append(goods)
            if self.shop.org_id == "3043":
                remark = ",".join(
                    [sku.sku_id or "" + "*" + str(sku.qty) for sku in
                     skus.sku_list])
        req = GenericOrderRequest(
            orderNumber=trades[0].tid,
            goodsInfoList=goods_list,
            customerAddressList=[CustomerAddress(
                addressType=1,
                customerContactName=address.name,
                customerContactPhone=address.mobile,
                customerFullAddress=self.get_customer_address(address),
            )],
            remark=remark,
        )
        resp = sdk.generic_order_create(req)
        if resp["success"] and resp["code"] == "1":
            self.states.write_job_widget_collection(
                Output(order_sn=resp["content"]).dict())
            return JobStatus.SUCCEED, ""
        return JobStatus.FAILED, resp.msg

    def get_customer_address(self, address: Address):
        customer_address = " ".join(
            [address.state or "", address.city or "", address.district or "",
             address.town or "", address.address or ""]
        )
        return customer_address
