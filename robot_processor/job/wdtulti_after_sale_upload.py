from typing import Tuple, Optional

import emoji
from dramatiq import Retry

from robot_processor.enums import JobType, JobStatus
from robot_processor.error.client_request import WdtRequestError
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.job.utils import change_wdt_address
from robot_processor.utils import flatten, get_district_or_zone
from rpa.erp.wdtulti import GoodsDetail, TradeInfo, ReissueOrder, WdtUltiOpenAPIClient, \
    get_skus_from_wdtulti_trades, WdtUltiQM, WdtQmRateLimitError


class WdtUltiAfterSaleUploadExecutor(AutoJobControllerBase):
    job_type = JobType.WDTULTI_AFTER_SALE_UPLOAD
    """
    旺店通旗舰版补发
    """

    def get_rate_limit_setting(self, conf: dict) -> Optional[Tuple[str, str]]:
        limit_key = f'JOB_LIMITER_{self.job_type}_ORG_{self.job_wrapper.shop.org_id}'  # 在租户维度进行限流
        for conf_key in [limit_key, f'JOB_LIMITER_{self.job_type}']:
            if limit_value := conf.get(conf_key):
                return limit_key, limit_value
        return None

    def get_remark(self):
        remark = self.args.get_arg_by_task_arg_name("remark")
        if not remark:
            return None
        elif isinstance(remark, list):
            return ",".join(flatten(remark))
        return remark

    def get_warehouse_name(self):
        warehouse_name = self.args.get_arg_by_task_arg_name("warehouse_name")
        if not warehouse_name:
            return None
        elif isinstance(warehouse_name, list):
            return warehouse_name[0]
        return warehouse_name

    def get_trade_label(self):
        trade_label = self.args.get_arg_by_task_arg_name("trade_label")
        if not trade_label:
            return None
        elif isinstance(trade_label, list):
            return trade_label[0]
        return trade_label

    def process(self):
        receiver = self.args.get_address()
        remark = self.get_remark()
        warehouse_name = self.get_warehouse_name()
        tid = self.args.get_trades()[0].tid
        direct_paid = self.args.get_arg_by_task_arg_name("direct_paid")
        try:
            trades = WdtUltiQM(self.order.sid).get_orders(src_tid=tid).order
        except WdtQmRateLimitError as e:
            raise Retry(str(e), delay=1000)
        if not trades:
            return JobStatus.FAILED, f"找不到 {tid} 相关的普通订单"
        origin_trade = sorted(trades, key=lambda x: x.trade_no)[0]
        goods_detail_list = []
        reissue_skus = self.args.get_reissue_skus()
        if reissue_skus.after_sales_type == "original":
            # 原单补发从当前订单获取商品信息
            skus = get_skus_from_wdtulti_trades(origin_trade, tid)
        else:
            skus = reissue_skus.sku_list
        for index, sku in enumerate(skus):
            erp_spec_no = sku.outer_sku_id or sku.sku_id
            goods_detail = GoodsDetail(merchant_no=erp_spec_no, num=sku.qty, type=sku.type)
            goods_detail_list.append(goods_detail)

        trade_info = TradeInfo(
            trade_no=origin_trade.trade_no
        )
        if receiver:
            address = change_wdt_address(receiver.dict())
            trade_info.receiver_name = address["name"]
            if emoji.emoji_count(address["name"]) > 0:
                return JobStatus.FAILED, "收货人姓名不能包含表情"
            trade_info.mobile = address["mobile"]
            trade_info.telno = address["mobile"]
            trade_info.receiver_address = "{state} {city} {district} {address}".format(
                state=address["state"],
                city=address["city"],
                district=get_district_or_zone(address),
                address=address["address"]
            )
        if remark:
            trade_info.print_remark = remark
        if warehouse_name:
            try:
                warehouses = WdtUltiQM(self.order.sid).all_warehouse_query()
            except WdtQmRateLimitError as e:
                raise Retry(str(e), delay=1000)
            warehouses = [warehouse for warehouse in warehouses if
                          warehouse.name == warehouse_name]
            if warehouses:
                trade_info.warehouse_no = warehouses[0].warehouse_no
            else:
                return JobStatus.FAILED, f"找不到仓库 {warehouse_name} 请检查当前应用在商家的旺店通系统内有没有所有仓库的查询权限"
        try:
            if logistics_code := self._get_logistics_no(self.args.get_arg_by_task_arg_name("logistics_name"),
                                                        self.shop.sid):
                trade_info.logistics_code = logistics_code
        except Exception as e:  # noqa
            return JobStatus.FAILED, f"找不到物流公司 {self.args.get_arg_by_task_arg_name('logistics_name')} "
        # 最终的应收款 = post_amount + sum([good.price for good in goods_detail_list])
        # 客户需要金额的话，我们暂且把金额填到post_amount中来实现
        if direct_paid:
            trade_info.post_amount = direct_paid  # 旺店通客户端内展示的应收金额
            trade_info.direct_paid = direct_paid  # 传了direct_paid之后“待付款”字段为否，这样不需要财务再审核
        if trade_type := self.args.get_arg_as_str_by_task_arg_name("trade_type"):
            trade_info.trade_type = trade_type
        if trade_label := self.get_trade_label():
            trade_info.trade_label = trade_label
        try:
            order = ReissueOrder(trade_info=trade_info, goods_detail_list=goods_detail_list)
            resp = WdtUltiOpenAPIClient(self.order.sid).reissue_order(order)  # type: ignore[arg-type]
            self.states.write_job_output({"trade_no": resp.data.trade_no})
            return JobStatus.SUCCEED, None
        except WdtRequestError as e:
            return JobStatus.FAILED, e.message

    @staticmethod
    def _get_logistics_no(logistics_name, sid):
        if logistics_name:
            if isinstance(logistics_name, list):
                logistics_name = logistics_name[0]
            details = WdtUltiOpenAPIClient(sid).get_logistic_by_name(logistics_name).data.details
            if not details:
                raise Exception(f"找不到物流公司 {logistics_name}")
            return details[0].logistics_no
        return None
