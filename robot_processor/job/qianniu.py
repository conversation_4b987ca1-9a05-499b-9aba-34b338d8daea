import time
from enum import StrEnum
from typing import List
from typing import Optional
from typing import Union
from typing import cast

import arrow
from loguru import logger
from pydantic import BaseModel
from result import Err
from result import Ok

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.client import buyer_client
from robot_processor.client import chat_client
from robot_processor.client import kiosk_client
from robot_processor.enums import Creator
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.error.job_process import ShopNotFound
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.users.models import PlatformUser
from robot_processor.utils import unwrap_optional
from rpa.conf import rpa_config
from rpa.mola import MolaClient


class SenderEnum(StrEnum):
    UPDATER = "UPDATER"  # 更新人
    CREATOR = "CREATOR"  # 创建人
    FIXED = "FIXED"  # 指定发送人
    RANDOM_ONLINE = "RANDOM_ONLINE"  # 随机在线
    LAST_RECEPTION = "LAST_RECEPTION"  # 最后接待客服
    MANUAL_INPUT = "MANUAL_INPUT"  # 输入的任意字符串列表
    INPUT_TEXT = "INPUT_TEXT"  # 输入的任意字符串


class UserGroup(BaseModel):
    group_uuid: str
    enable: int = 1


class SenderConfig(BaseModel):
    details: Optional[List[AccountDetailV2]] = []
    assignee_groups: Optional[List[UserGroup]] = []


class SenderSchema(BaseModel):
    value: SenderEnum = SenderEnum.RANDOM_ONLINE
    extra: Union[SenderConfig, str, List[str]] = SenderConfig()

    def get_senders(self, job, contact):
        if self.value == SenderEnum.MANUAL_INPUT:
            return [AccountDetailV2(user_type=Creator.ASSISTANT, user_nick=user) for user in self.extra]
        elif self.value == SenderEnum.INPUT_TEXT:
            return [AccountDetailV2(user_type=Creator.ASSISTANT, user_nick=self.extra)]
        if self.value == SenderEnum.UPDATER:
            # 发送人为最近更新人
            # 最近更新人指的是  上一个人工步骤的提交人
            for prev in job.get_prev_jobs():
                if prev is None:
                    break
                if prev.is_human():
                    updater = prev.get_assignee_assistant()
                    if updater:
                        return updater.get_bound_platform_users(job.shop.channel_id)
            return cast(list[AccountDetailV2], [])
        elif self.value == SenderEnum.CREATOR:
            # 创建人为发送人
            creator = AccountDetailV2(
                user_id=job.business_order.creator_user_id, user_type=job.business_order.creator_type
            )
            return creator.get_bound_platform_users(job.shop.channel_id)
        elif self.value == SenderEnum.FIXED:
            # 指定的发送人
            sender_config = cast(SenderConfig, self.extra)
            users = sender_config.details or []
            group_uuids = set(group.group_uuid for group in sender_config.assignee_groups or [])
            return kiosk_client.get_platform_users_by_users_and_groups(users, group_uuids, job.shop.channel_id)
        elif self.value == SenderEnum.RANDOM_ONLINE:
            return cast(list[AccountDetailV2], [])
        elif self.value == SenderEnum.LAST_RECEPTION:
            # 上一次接待客服
            buyer_info = buyer_client.get_buyer_info_by_buyer_nick(buyer_nick=contact)
            leyan_buyer_id = buyer_info.leyan_buyer_id if buyer_info else None
            res = chat_client.get_last_assistant_by_store_buyer(job.business_order.sid, contact, leyan_buyer_id)
            match res:
                case Ok(assistant_nick):
                    match PlatformUser.find_or_create_taobao_user(shop=unwrap_optional(job.shop), nick=assistant_nick):
                        case Ok(user):
                            senders: list[AccountDetailV2] = [user.to_account_detail_v2()]
                            return senders
                        case Err(error):
                            logger.opt(exception=error).warning(
                                f"Last reception assistant {assistant_nick} not found in db."
                            )
                            return cast(list[AccountDetailV2], [])
                case Err(error):
                    logger.opt(exception=error).warning("get_last_assistant_by_store_buyer failed.")
                    return cast(list[AccountDetailV2], [])


def chose_online_user_from_chat(sid, senders):
    from random import choice

    # 通过乐语助人获取所有在线客服
    res = chat_client.get_online_assistants_by_sid(sid)
    logger.info(f"online_assistant_nicks:{res}, senders:{senders}")
    match res:
        case Err():
            return ""
        case Ok(online_assistants):
            if not senders:
                # 发送人为空, 随机挑选一个在线客服
                return choice(online_assistants)
            expected_assistants = [sender.user_nick for sender in senders]  # 从kiosk获取的客服账号
            candidates = []
            for expected in expected_assistants:
                for valid in online_assistants:
                    if expected.lower() == valid.lower():
                        # kiosk的客服账号有可能和在线客服账号大小写不一致，原因未知，暂时兼容
                        candidates.append(valid)
            if candidates:
                return choice(candidates)
            # 发送人都不在线，随机挑选一个在线客服
            logger.info("Expected senders offline, fallback to random.")
            return choice(online_assistants)


class QianniuExecutor(AutoJobControllerBase):
    job_type = JobType.SEND_QIANNIU

    def process(self):
        msg_arguments = self.args.get_send_message_arguments()
        if msg_arguments.skip:
            return JobStatus.SUCCEED, None

        text = msg_arguments.content
        if text and len(text) > rpa_config.QIANNIU_TEXT_MAX_LENGTH:
            return JobStatus.FAILED, f"内容超过长度限制, 当前长度: {len(text)}, 限制长度: {rpa_config.QIANNIU_TEXT_MAX_LENGTH}"

        if not msg_arguments.usernick and not msg_arguments.tid:
            return JobStatus.FAILED, "买家信息和订单号都为空"
        sender_schema = SenderSchema(**(msg_arguments.sender or {}))
        senders = sender_schema.get_senders(self.job, msg_arguments.usernick)

        match msg_arguments.tid:
            case str() as tid:
                tid = tid
            case {"tid": tid}:
                tid = tid
            case [{"tid": tid}, *_]:
                tid = tid
            case _:
                tid = None

        send_channel = msg_arguments.send_channel
        logger.info(f"Send QianNiu message with sender_schema={sender_schema.value} {senders=} {send_channel=}")
        if msg_arguments.send_channel == "CHAT_CLIENT":
            return self.send_message_by_chat_client(
                text, msg_arguments.image_urls, senders, contact=msg_arguments.usernick, tid=tid
            )
        else:
            final_consign_time: str | None = None
            if msg_arguments.is_send_delivery_consult_order == "true":
                if not msg_arguments.delivery_delay_days:
                    return JobStatus.FAILED, "请填写协商发货卡片延迟时间"
                try:
                    delivery_delay_days = int(
                        msg_arguments.delivery_delay_days)  # type: ignore[arg-type]
                    if delivery_delay_days < 3:
                        return JobStatus.FAILED, "协商发货卡片延迟时间不能小于3天"
                    final_consign_time = arrow.now().shift(
                        days=delivery_delay_days).format("YYYY-MM-DD")
                except ValueError:
                    return JobStatus.FAILED, "协商发货卡片延迟时间"
            return self.send_message_by_rpa_client(
                text, msg_arguments.image_urls, senders,
                contact=msg_arguments.usernick, tid=tid,
                final_consign_time=final_consign_time
            )

    def send_message_by_chat_client(
        self, text: str, image: List[str], senders: List[AccountDetailV2], contact: str = None, tid: str = None
    ):
        # 使用乐语助人发消息 💡若您选择[最近更新人][任务创建人][指定账号][最近接待客服]都无人在线时，系统将默认为[随机在线客服]
        assistant_nick = chose_online_user_from_chat(self.job.business_order.sid, senders)
        if not assistant_nick:
            return JobStatus.FAILED, "无客服发送失败"
        final_contact = contact
        if tid:
            get_nick_res = chat_client.get_nick_by_tid(assistant_nick, tid)
            match get_nick_res:
                case Ok(resp_json):
                    if (
                        resp_json["data"]["ret"][0] == "SUCCESS::调用成功" and resp_json["data"]["data"]["code"] == "0"
                    ):  # noqa: E501
                        if not resp_json["data"]["data"]["data"]:
                            return JobStatus.FAILED, "获取昵称失败"
                        final_contact = resp_json["data"]["data"]["data"][0]["nick"]
                    else:
                        return JobStatus.FAILED, resp_json
                case Err(e):
                    return JobStatus.FAILED, e
        res = chat_client.send_message(assistant_nick, final_contact, text, image)
        match res:
            case Ok():
                if tid:
                    ccode_res = chat_client.get_ccode_by_nick(assistant_nick, final_contact)
                    match ccode_res:
                        case Ok(ccode_resp):
                            if ccode_resp.get("data") and ccode_resp["data"]["code"] == 0:
                                ccode = ccode_resp["data"]["result"]["ccode"]
                                time.sleep(2)
                                timeline_res = chat_client.get_timeline_by_ccode(assistant_nick, ccode)
                                match timeline_res:
                                    case Ok(timeline_resp):
                                        if timeline_resp.get("data") and timeline_resp["data"]["code"] == 0:
                                            for msg in timeline_resp["data"]["result"]["msgs"]:
                                                if msg["fromid"]["nick"] == assistant_nick:
                                                    if msg_text := msg["originalData"].get("text"):
                                                        if msg_text == text:
                                                            if msg["mcode"].get("messageId"):
                                                                return JobStatus.SUCCEED, None
                                            return JobStatus.FAILED, "ACK失败"
                                        else:
                                            return JobStatus.FAILED, f"获取timeline失败 {timeline_resp}"
                            else:
                                return JobStatus.FAILED, f"获取ccode失败 {ccode_resp}"
                        case Err(e):
                            return JobStatus.FAILED, e
                return JobStatus.SUCCEED, None
            case Err(e):
                return JobStatus.FAILED, e

    def send_message_by_rpa_client(
        self, text: str, image: List[str], senders: List[AccountDetailV2], contact: str = None, tid: str = None,
        final_consign_time: str | None = None
    ):
        shop = self.job_wrapper.shop
        if not shop:
            return JobStatus.FAILED, ShopNotFound()

        mola_client = MolaClient(shop.sid)
        final_contact = contact
        if final_consign_time and not tid:
            return JobStatus.FAILED, "发送协商发货卡片需要订单号"
        if tid:
            if final_consign_time:
                consult_trade_res = mola_client.create_qianniu_delivery_consult_trade(tid, final_consign_time)
                if consult_trade_res.is_err():
                    return JobStatus.FAILED, consult_trade_res
            contact_res = mola_client.call_namespace_method("qianniu", "search-contact-by-trade", {"tid": tid})
            match contact_res:
                case Ok(contact_info):
                    final_contact = contact_info["result"]["nick"]
                case Err(e):
                    return JobStatus.FAILED, e
        sub_user_ids = ",".join([sender.sub_id for sender in senders or [] if sender.sub_id])
        res = mola_client.send_qianniu_message(sub_user_ids, final_contact, text, image[0] if image else "")
        match res:
            case Ok():
                return JobStatus.SUCCEED, None
            case Err(e):
                # TODO: 支持失败重试
                return JobStatus.FAILED, e
