import json
from typing import List

import arrow
from dramatiq import Retry
from flask import current_app
from loguru import logger
from pydantic import BaseModel
from pydantic import parse_obj_as
from requests import ConnectTimeout

from robot_processor.client import sf_client
from robot_processor.client import sto_client
from robot_processor.client.aliyun_logistics_client import AliyunLogisticsClient
from robot_processor.client.aliyun_logistics_client import Logistics
from robot_processor.client.aliyun_logistics_client import QueryLogisticsResp
from robot_processor.client.logistics_clients.enum import GetTraceRecordsMethod
from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.client.logistics_clients.jt_domain import JTExpressClient
from robot_processor.client.logistics_clients.schema import JTQueryLogisticOutput
from robot_processor.client.logistics_clients.schema import SFQueryLogisticResponse
from robot_processor.client.logistics_clients.schema import StoQueryTraceData
from robot_processor.client.logistics_clients.schema import StoWaybillItem
from robot_processor.client.logistics_clients.schema import ZTOQuery<PERSON>ogistic<PERSON>utput
from robot_processor.client.logistics_clients.yd_domain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from robot_processor.client.logistics_clients.yto_domain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from robot_processor.client.logistics_clients.zto_domain import ZTOExpressClient
from robot_processor.db import db
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.shop.auth_manager import Credentials


class Output(BaseModel):
    logistics: List[Logistics]


class AliyunLogisticTraceExecutor(AutoJobControllerBase):
    output_model = Output
    job_type = JobType.ALIYUN_LOGISTICS_TRACE

    def process(self):
        org_ids = current_app.config.get("ALIYUN_LOGISITCS_TRACE_ORG_IDS", [])
        if self.shop.org_id not in org_ids:
            return JobStatus.FAILED, "没有开通该能力，请联系运营顾问开通"
        app_code = None
        if self.shop.org_id == "1904":
            app_code = "3df4d8549d714845b4b62179c7714b2a"
        elif self.shop.org_id == "3168":
            app_code = "a5f6b49517464ea5aa1e2b346dc27a88"
        logistics_no = self.args.get_arg_by_task_arg_name("logistics_no")
        if not logistics_no:
            return JobStatus.FAILED, "物流单号为空"
        logistics_no = logistics_no.replace("@", "")
        # 收寄人的手机号后四位来验证。
        phone_number = self.args.get_arg_by_task_arg_name("phone_number")
        traces = []
        if logistics_no.startswith("SF"):  # 顺丰
            if not phone_number:
                return JobStatus.FAILED, "查询顺丰快递必须传入收寄人的手机号后四位"
            phone_numbers = set(phone_number.split(","))
            for ph in phone_numbers:
                success, sf_resp = sf_client.query_logistic(logistics_no, ph)
                if not success:
                    return JobStatus.FAILED, sf_resp
                sf_query_logistic_response = SFQueryLogisticResponse(**json.loads(sf_resp))
                if not sf_query_logistic_response.success:
                    return JobStatus.FAILED, sf_query_logistic_response.error_msg
                for route_data in sf_query_logistic_response.msg_data.route_resps:
                    if route_data.mail_no == logistics_no:
                        for route in route_data.routes:
                            traces.append(Logistics(time=route.accept_time, status=route.remark))
        elif (
            logistics_no.startswith("73")
            or logistics_no.startswith("74")
            or logistics_no.startswith("75")
            or logistics_no.startswith("78")
            or logistics_no.startswith("76")
        ) and len(logistics_no) == 14:
            # 中通
            if not phone_number:
                return JobStatus.FAILED, "查询中通快递必须传入收寄人的手机号后四位"
            zto_client = ZTOExpressClient(logistics_type=LogisticsType.ZTO)
            # 中通的快递查询需要任意 key + 手机后四位
            credential = db.session.get_one(Credentials, 14765)
            zto_client.init({"keys": [credential.auth_extra_data]})
            phone_numbers = set(phone_number.split(","))
            for pn in phone_numbers:
                zto_resp = zto_client.query_trace({"bill_code": logistics_no,
                                                   "mobile_phone": pn})
                query_logistic_trace = ZTOQueryLogisticOutput(**zto_resp)
                if not query_logistic_trace.status:
                    return JobStatus.FAILED, query_logistic_trace.message
                zto_traces = [detail for detail in query_logistic_trace.result if detail.bill_code == logistics_no]
                for trace_item in zto_traces:
                    traces.append(
                        Logistics(
                            time=arrow.get(trace_item.scan_date, tzinfo="Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss"),
                            status=trace_item.desc,
                        )
                    )
        elif (
            logistics_no.startswith("4341")
            or logistics_no.startswith("3125")
            or logistics_no.startswith("4642")
            or logistics_no.startswith("4344")
            or logistics_no.startswith("3126")
            or logistics_no.startswith("4344")
            or logistics_no.startswith("4643")
            or logistics_no.startswith("4345")
            or logistics_no.startswith("3127")
        ) and len(
            logistics_no
        ) == 15:  # 韵达
            yd_client = YdLogisticsClient(LogisticsType.YD)
            success, yd_traces = yd_client.query_logistic(logistics_no)
            if not success:
                return JobStatus.FAILED, "获取韵达物流轨迹失败"
            for yd_trace_item in yd_traces:
                traces.append(Logistics(time=yd_trace_item.update_time, status=yd_trace_item.desc))
        elif logistics_no.startswith("YT"):
            yto_client = YtoLogisticsClient(LogisticsType.YTO)
            yto_client.init_query_trace_sdk()
            try:
                succeed, error_message, raw_response = yto_client.query_trace({"waybill_no": logistics_no})
            except ConnectTimeout:
                raise Retry("圆通服务请求超时", delay=3000)
            if not succeed:
                if error_message == "查询结果为空。":
                    traces = []
                else:
                    return JobStatus.FAILED, error_message
            else:
                for yto_trace_item in raw_response.__root__:  # type: ignore[union-attr]
                    traces.append(
                        Logistics(
                            time=yto_trace_item.upload_Time,
                            status=yto_trace_item.processInfo,
                        )
                    )
        elif logistics_no.startswith("JT"):
            jt_client = JTExpressClient(LogisticsType.JT)
            jt_resp = jt_client.query_trace(logistics_no)
            jt_query_logistic_trace = JTQueryLogisticOutput(**jt_resp)
            if jt_query_logistic_trace.code != "1":
                return JobStatus.FAILED, jt_query_logistic_trace.msg
            jt_traces = [detail for detail in jt_query_logistic_trace.data if detail.bill_code == logistics_no]
            if jt_traces:
                for jt_trace_item in jt_traces[0].details:
                    traces.append(Logistics(time=jt_trace_item.scan_time, status=jt_trace_item.desc))
        elif (
            logistics_no.startswith("772")
            or logistics_no.startswith("773")
            or (logistics_no.startswith("454") and len(logistics_no) == 12)
            or (logistics_no.startswith("457") and len(logistics_no) == 12)
        ):
            sto_resp = sto_client.query_trace({"waybillNoList": [logistics_no]})
            if not sto_resp:
                return JobStatus.FAILED, "请求申通失败"
            if sto_resp.get("success") == "false":
                return JobStatus.FAILED, f"申通请求报错:{sto_resp.get('expInfo', '')}"
            # 提取data中的数组部分
            data_array = next(iter(sto_resp.get("data", {}).values()), [])  # type: ignore[var-annotated]
            # 实例化为StoQueryTraceData模型
            sto_trace_data = StoQueryTraceData(response_items=parse_obj_as(List[StoWaybillItem], data_array))
            for sto_trace_item in sto_trace_data.response_items:
                traces.append(Logistics(time=sto_trace_item.opTime, status=sto_trace_item.memo))
        else:
            logger.info(f"兜底物流工单号: {logistics_no}")
            resp: QueryLogisticsResp = AliyunLogisticsClient().query_logistics(logistics_no, app_code)
            traces.extend(resp.result.list)
            if resp.status != "0":
                return JobStatus.FAILED, resp.msg
        logger.info(f"物流轨迹: {traces}")

        get_trace_records_method = self.args.get_arg_by_task_arg_name("get_trace_records_method")

        filtered_traces, err = filter_traces(traces=traces, get_trace_records_method=get_trace_records_method)
        if err:
            return JobStatus.FAILED, err
        output = Output(logistics=filtered_traces)
        self.states.write_job_widget_collection(output.dict())
        return JobStatus.SUCCEED, ""


def filter_traces(
    traces: list[Logistics], get_trace_records_method: GetTraceRecordsMethod | None
) -> tuple[list[Logistics], str | None]:
    if len(traces) == 0 or get_trace_records_method not in [
        GetTraceRecordsMethod.GET_LATEST_RECORD,
        GetTraceRecordsMethod.GET_FIRST_RECORD,
    ]:
        return traces, None

    try:
        sorted_traces = sorted(traces, key=lambda record: record.convert_time_to_datetime(), reverse=True)
    except Exception as e:
        logger.exception(f"操作时间格式化时发生异常 {e}")
        return [], "操作时间格式化时发生异常"

    match get_trace_records_method:
        case GetTraceRecordsMethod.GET_LATEST_RECORD:
            return sorted_traces[:1], None
        case GetTraceRecordsMethod.GET_FIRST_RECORD:
            return sorted_traces[-1:], None
        case _:
            return sorted_traces, None
