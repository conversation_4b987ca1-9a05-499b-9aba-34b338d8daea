"""
遍历网关会配置绑定一个复合组件, 遍历网关在 workflow 执行中，会对这个复合组件进行遍历.
遍历网关的信息存储在 business_order.data 下, 因为当前版本的飞梭系统还没有 scope 概念,
遍历的信息(复合组件的长度 length, 当前遍历的索引 index) 都是存储在 business_order.data 下的.
namespace 之间不会有层级嵌套, 会在 workflow 执行中计算层级嵌套关系，完成 scope 的功能实现

找到步骤包裹在 begin 和 end 之间，把这些 step_uuid 记录下来，从 global namespace 找他们
"""

from json import dumps as json_dumps

from flask.globals import app_ctx
from loguru import logger
from pydantic import BaseModel
from pydantic import ValidationError
from sqlalchemy.orm import scoped_session
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.business_order.models import Job
from robot_processor.db import in_transaction
from robot_processor.enums import JobStatus
from robot_processor.error.base import JobProcessError
from robot_processor.job.base import JobExecuteResult
from robot_processor.job.gateway.base_gateway import BaseGateway


class SessionTracker:
    def __init__(self, session: scoped_session):
        self.processed = False
        self.logger = logger.bind(k="SessionTracker")
        self.session = session
        self.initial_scope_id = session.registry.scopefunc()
        self.initial_session_id = id(session.registry())
        self.initial_app_context_stack = len(app_ctx._cv_tokens)

    @property
    def log_context(self):
        return {
            "session_id": self.initial_session_id,
            "scope_id": self.initial_scope_id,
            "app_context_stack": self.initial_app_context_stack,
        }

    def check(self, checkpoint):
        from robot_processor.logging import to_log

        if self.processed is True:
            return None
        try:
            log_context = self.log_context.copy()
            log_context["checkpoint"] = checkpoint
            if not self.session.registry.has():
                self.processed = True
                self.logger.error(f"session 未注册 {to_log(log_context)}")
            if self.session.registry.scopefunc() != self.initial_scope_id:
                self.processed = True
                log_context["new_scope_id"] = self.session.registry.scopefunc()
                self.logger.error(f"session scope 发生变化 {to_log(log_context)}")
            if id(self.session.registry()) != self.initial_session_id:
                self.processed = True
                log_context["new_session_id"] = id(self.session.registry())
                self.logger.error(f"session id 发生变化 {to_log(log_context)}")
            if len(app_ctx._cv_tokens) != self.initial_app_context_stack:
                self.processed = True
                log_context["new_app_context_stack"] = len(app_ctx._cv_tokens)
                self.logger.error(f"app context stack 发生变化 {to_log(log_context)}")
        except Exception as e:
            logger.exception(f"SessionTracker check failed {e}")
        return None


class Iterator(BaseModel):
    length: int
    index: int

    @property
    def is_empty(self):
        """需要迭代的列表为空列表"""
        return self.length == 0

    @property
    def is_finished(self):
        """判断的时机为 next() 之前，所以这的 index 要和 length - 1 比较"""
        return self.index >= self.length - 1

    def next(self):
        self.index += 1


class IterateGatewayBegin(BaseGateway):
    """遍历网关-拆封"""

    def _log(self):
        iterator = self.get_iterator()
        logger.info(
            "遍历开始网关执行记录"
            f"\n\t当前索引: {iterator.index}"
            f"\n\t遍历长度: {iterator.length}"
            f"\n\t当前数据: {json_dumps(self.get_namespace()['item'])}"
        )

    def if_iterator_inited(self):
        try:
            self.get_iterator()
        except (KeyError, ValidationError):
            return False
        return True

    def get_iterator(self):
        namespace = self.get_namespace()
        iterator = Iterator.validate(namespace["iterator"])
        return iterator

    def get_namespace(self):
        name = self.job.step_uuid
        if name not in self.order.data:
            return dict()
        return self.order.data[name]

    def set_namespace(self, new_data):
        name = self.job.step_uuid
        self.order.data[name] = new_data

    @in_transaction()
    def init_iterator(self):
        array_value = self.args.get_widget_ref_value(self.get_widget_ref())
        if array_value is None:
            length = 0
        elif isinstance(array_value, list):
            length = len(array_value)
        else:
            raise Exception(f"遍历网关数据异常: {array_value}")
        iterator = Iterator(length=length, index=0)

        namespace = self.get_namespace()
        namespace["iterator"] = iterator.dict()
        self.set_namespace(namespace)
        flag_modified(self.order, "data")

    @in_transaction()
    def refresh_scope_data(self):
        iterator = self.get_iterator()
        widget_ref = self.get_widget_ref()

        if widget_ref.depth == 1:
            scope_data = context = {}  # type: ignore[var-annotated]
            key = widget_ref.key
        else:
            scope_data = context = {}
            for level in range(widget_ref.depth - 1):
                key = widget_ref.get_key_by_level(level)
                context[key] = {}
                context = context[key]
            key = widget_ref.get_key_by_level(widget_ref.depth - 1)

        value_list = self.args.get_widget_ref_value(widget_ref, without_self=self.job.step_uuid)
        logger.info(f"refresh scope data index: {iterator} value: {value_list}")
        value = value_list[iterator.index]
        context[key] = value

        namespace = self.get_namespace()
        namespace["item"] = scope_data
        self.set_namespace(namespace)
        flag_modified(self.order, "data")

    def process(self):
        from robot_processor.db import db

        session_tracker = SessionTracker(db.session)
        if not self.if_iterator_inited():
            self.init_iterator()
        session_tracker.check("after init_iterator")

        if self.get_iterator().is_empty:
            # 遍历列表为空，直接结束
            return JobExecuteResult(status=JobStatus.RUNNING, next_step=self.get_coupled_step_uuid())

        self.refresh_scope_data()
        session_tracker.check("after refresh_scope_data")
        self.args.bo_data = self.order.data
        self._log()
        session_tracker.check("after _log")
        for branch in self.get_branch_list():
            if branch.accept():
                return JobExecuteResult(status=JobStatus.RUNNING, next_step=branch.next_step_uuid)

        raise JobProcessError("没有满足条件的分支")

    def get_widget_ref(self):
        if not (widget_ref := self.job.widget_ref):
            raise JobProcessError("遍历网关未绑定组件")
        if not widget_ref.is_iterable:
            raise JobProcessError("遍历拆封组件异常，不是复合组件")

        return widget_ref

    def get_coupled_step_uuid(self):
        if coupled_step_uuid := self.job.raw_step_v2.get("data", {}).get("coupled_step_uuid"):
            return coupled_step_uuid
        raise JobProcessError("遍历拆封步骤异常，未指定对应的遍历合并节点")


class IterateGatewayEnd(BaseGateway):
    """遍历网关-合并"""

    def _log(self):
        iterator = self.get_iterator()
        logger.info(
            "遍历结束网关执行记录"
            f"\n\t当前索引: {iterator.index}"
            f"\n\t执行计划: {'清理' if iterator.is_finished else '继续下一次遍历'}"
        )

    def get_namespace(self):
        name = self.get_coupled_step_uuid()
        return self.order.data[name]

    def set_namespace(self, new_data):
        name = self.get_coupled_step_uuid()
        self.order.data[name] = new_data

    def get_iterator(self):
        iterator = Iterator.validate(self.get_namespace()["iterator"])
        return iterator

    @in_transaction()
    def clear_iterator(self):
        namespace = self.get_namespace()
        namespace.pop("iterator", None)
        namespace.pop("item", None)
        self.set_namespace(namespace)
        flag_modified(self.order, "data")

    @in_transaction()
    def move_iterator(self):
        iterator = self.get_iterator()
        iterator.next()
        namespace = self.get_namespace()
        namespace["iterator"] = iterator.dict()
        self.set_namespace(namespace)
        flag_modified(self.order, "data")

    def process(self):
        self._log()
        iterator = self.get_iterator()
        iterate_begin_job = self.order.get_job_by_step_uuid(self.get_coupled_step_uuid())

        if iterator.is_finished:
            self.clear_iterator()
            iterate_begin_job.set_status(JobStatus.SUCCEED)  # type: ignore[union-attr]
            return JobExecuteResult(status=JobStatus.SUCCEED)
        else:
            self.move_iterator()
            # 跳回到遍历拆封步骤
            reversed_job_history = self.order.get_reversed_job_history()
            idx = reversed_job_history.index(iterate_begin_job.id)  # type: ignore[union-attr]
            rollback_job_ids = reversed_job_history[: (idx + 1)]
            self.order.set_current_execute_job(iterate_begin_job)  # type: ignore[arg-type]
            self.reset_job_status(rollback_job_ids)
            # 遍历中是 running
            return JobExecuteResult(
                status=JobStatus.RUNNING, next_step=iterate_begin_job.step_uuid  # type: ignore[union-attr]
            )

    def reset_job_status(self, rollback_job_ids: list):
        """
        重置状态=init
        """
        if self.job.id in rollback_job_ids:
            rollback_job_ids.remove(self.job.id)
        if rollback_job_ids:
            jobs = self.order.jobs.filter(Job.id.in_(rollback_job_ids)).all()
            for job in jobs:
                job.set_status(JobStatus.INIT)

    def get_coupled_step_uuid(self):
        if coupled_step_uuid := self.job.raw_step_v2.get("data", {}).get("coupled_step_uuid"):
            return coupled_step_uuid
        raise JobProcessError("遍历合并步骤异常，未指定对应的遍历拆封节点")
