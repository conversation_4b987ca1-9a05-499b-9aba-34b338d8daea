import typing as t
from dataclasses import MISSING
from dataclasses import dataclass
from dataclasses import field
from decimal import Decimal
from functools import cached_property

import chevron
from flask import current_app
from loguru import logger
from more_itertools.more import first
from pydantic import BaseModel
from pydantic import Field
from robot_extension.widget import value_handler
from robot_types.helper.form_composer import FormComposer
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.business_order import handlers
from robot_processor.business_order.condition.condition import Branch
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.db import in_transaction
from robot_processor.enums import DataType
from robot_processor.enums import JobStatus
from robot_processor.ext import db
from robot_processor.form.models import FormVersion
from robot_processor.form.models import StepScope
from robot_processor.form.models import Widget
from robot_processor.form.models import WidgetInfo
from robot_processor.form.models import WidgetRef
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.models import Shop
from robot_processor.utils import convert_product_widget_data_as_array
from robot_processor.utils import convert_select_widget_data_to_old
from robot_processor.utils import jmespath_search
from robot_processor.utils import raise_exception
from robot_processor.utils import unwrap_optional
from robot_processor.utils import url_encode

T = t.TypeVar("T")
WIDGET_VALUE_RENDER_MODE = t.Literal["raw", "brief", "detail", "customize_589976", None]
AFTER_SALE_TYPE = t.Literal["non_original", "original", "custom"]
MULTI_ROW_MERGE_METHOD = t.Literal["join", "keep"]


class FixedConcepts(t.TypedDict):
    updator: str  # 工单最新修改人
    update_time: str  # 工单最新修改时间
    receipt_url: str  # 打款凭证, 支付宝提供的证明打款已经成功的 pdf 文件链接
    creator: str  # 工单创建人
    shop: str  # 店铺名称


@dataclass
class Trade:
    tid: str
    oid: t.Optional[str] = None


@dataclass
class MultiOidTrade:
    """一个订单下可能有多笔子订单"""

    tid: str
    oids: t.List[str]


class Address(BaseModel):
    name: str | None = Field(description="姓名")
    mobile: str | None = Field(description="手机号")
    state: str | None = Field(description="省")
    city: str | None = Field(description="市")
    zone: str | None = Field(description="区")
    district: str | None = Field(description="县")
    town: str | None = Field(description="镇")
    address: str | None = Field(description="详细地址")


@dataclass
class SKU:
    """飞梭层面的商品抽象"""

    spu_id: t.Optional[str]
    sku_id: t.Optional[str]
    outer_sku_id: t.Optional[str]
    outer_spu_id: t.Optional[str]  # 单品只会有outer_spu_id、spu_id，不会有sku的信息
    qty: t.Optional[int]
    pic: t.Optional[str] = None
    price: t.Optional[float] = 0
    type: t.Optional[t.Any] = 1  # 旺店通补发业务用，判断组合商品还是普通商品  # 在聚水潭业务中也用于表示售后商品类型
    source: t.Optional[str] = None  # sku数据来源(erp or platform), 当source_type = erp时，spu_id和sku_id不可信

    def is_single(self):
        """平台侧只有spu没有sku就是单品"""
        return self.source == "platform" and bool(not self.sku_id and self.spu_id)


@dataclass
class ReissueSkus:
    """飞梭层面订单补发商品的抽象"""

    after_sales_type: AFTER_SALE_TYPE = "non_original"
    sku_list: t.List[SKU] = field(default_factory=list)
    type: t.Optional[str] = "其他"

    def __add__(self, other):
        return ReissueSkus(
            after_sales_type=self.after_sales_type,
            sku_list=self.sku_list + other.sku_list,
            type=self.type,
        )

    def merge_same_outer_sku(self):
        outer_sku_to_sku = {}
        for sku in self.sku_list:
            if sku.outer_sku_id not in outer_sku_to_sku:
                outer_sku_to_sku[sku.outer_sku_id] = sku
            else:
                outer_sku_to_sku[sku.outer_sku_id].qty = (outer_sku_to_sku[sku.outer_sku_id].qty or 0) + (sku.qty or 0)
        return list(outer_sku_to_sku.values())


@dataclass
class SendMessageArguments:
    """发送消息类 job 的参数."""

    # 以下参数是必须项, 对于所有类型的消息发送都适用
    content: str  # 消息内容
    send_channel: str  # 通过什么渠道发送消息，RPA_CLIENT(RPA 客户端) 或者 CHAT_CLIENT(乐语助人客户端)
    image_urls: t.List[str]  # 消息中需要包含哪些图片
    skip: bool  # 此次消息发送是否可以跳过

    # 以下参数是可选项, 仅对部分类型的消息发送适用
    # 未来如果需要新增其他消息类型，需要将它的参数以可选形式添加到这里，并在对应的 job controller 中对参数值进行校验
    wechat_id: t.Optional[str] = None
    wechat_nick: t.Optional[str] = None
    wechat_group: t.Optional[str] = None

    qq_id: t.Optional[str] = None
    qq_nick: t.Optional[str] = None
    qq_group: t.Optional[str] = None

    url: t.Optional[str] = None  # 钉钉消息和企业微信群消息适用，用于构造  webhook
    signature: t.Optional[str] = None  # 钉钉消息和企业微信群消息适用, 用于构造  webhook
    text_picture_merge: t.Optional[dict] = None  # 企业微信适用，图片和文本分开发送或合并发送

    usernick: t.Optional[str] = None  # 千牛消息发送时适用

    assistant: t.Optional[str] = None  # 抖店，拼多多消息发送适用, 用于指定发送者的账号信息
    sender: t.Optional[dict] = None  # 抖店，拼多多，千牛, 钉钉消息发送适用
    trace_status_excluded: t.Optional[list] = None  # 抖店和拼多多消息发送试用, 用于排除某些订单状态不发送消息
    refund_status_excluded: t.Optional[list] = None  # 抖店和拼多多消息发送试用, 用于排除某些退款状态不发送消息

    dingtalk_group: t.Optional[str] = None

    feishu_webhook: t.Optional[str] = None  # 飞书机器人的webhook
    feishu_secret: t.Optional[str] = None  # 飞书机器人的webhook对应的secret

    tid: t.Optional[str | dict | list] = None  # 跨平台需要通过订单号发消息

    is_send_delivery_consult_order: t.Optional[str] = "false"  # 是否发送协商发货卡片
    delivery_delay_days: t.Optional[str] = None  # 协商发货卡片的延迟时间(天)

    def __post_init__(self):
        self.image_urls = [url_encode(img_url) for img_url in self.image_urls]

    @classmethod
    def optional_arguments(cls) -> t.List[str]:
        """获取可选参数的列表."""
        return [field.name for field in cls.__dataclass_fields__.values() if field.default is not MISSING]


class JobArguments:
    """用于简化 job 参数读取的辅助类.

    robot-processor 的一个工单通常由多个 job 构成，一种 job 完成一件事情，比如 支付宝打款(alipay.py)，发送千牛消息(qianniu.py)，修改订单备注(memo.py)等.

    job 要完成自己的工作，需要从工单中获取一些参数，比如: 支付宝打款金额，千牛消息的接收人，消息内容，订单备注时的订单号，备注内容等.

    每种 job 需要的参数声明在 job 模板(step) 关联的 rpa task (init_rpa.json) 中, 例如，init_rpa.json 中名为 ALIPAY 的 task，
    它的运行时逻辑实现在 alipay.py 内, 它的参数声明在 init_rpa.json 中，这些参数有：amount(打款金额), receive_info(收款人信息), comment(备注) 等.

    job 参数的来源有多种，比如：

    - 工单元信息，比如工单创建人，工单创建时间，工单最新修改人，工单最新修改时间等
    - 用户创建工单时，通过 web 表单填写的信息
    - 前置 job 执行过程中自动生成, 如某些 job 中会根据用户输入的淘宝订单的订单通过 erp 的接口获取 erp 内部订单号，然后将内部单号作为参数传递给后续的 job
    - job 模板(step) 创建时就已经定义好的信息，比如千牛消息的发送人，发送渠道等

    这些参数的格式也有多种，比如：

    - 有些参数是字符串，比如千牛消息的接收人.
    - 有些参数是字符串模板，比如千牛消息的内容，可能是一个字符串模板 ("@buyer 您的订单 @tid 已安排发送，物流单号是 @logistics_no ")，需要先用其他参数填充这个模板，才能得到最终的消息内容.
    - 有些参数是结构体，比如订单号通常用 `{"tid": xxx, "oid":xxx}` 的结构体表示

    这个类的作用就是将这些参数的来源和格式进行封装，使得 job 可以更方便的获取这些参数. 例如，alipay.py 中，
    通过 `self.args.get_arg_by_task_arg_name('amount')` 即可获取到打款金额， 不用关心这个参数是从工单元信息，用户填写的表单，
    还是前置 job 传递过来, 如果是从 web 表单中获得，也不需要关心来自表单中的哪个 widget.

    `get_arg_by_task_arg_name` 封装的逻辑是：

    - 根据 `arg_name`(例如 `argument`) 在 `task_arguments` 中查找对应的 rpa task 参数声明, 明确是否声明了该参数，这个参数的来源类型是表单，还是工单元信息，还是固定值.
    - 如果是表单，根据 `key_map` 找到对应的 widget key, 然后用这个 widget key 从 `bo_data` 中获取用户输入的 widget 值.
    - 如果是固定值，则从 `key_map` 中找到对应的值.
    - 找到参数值后，根据参数类型进行转换，特别地，如果参数类型是字符串模板，则需要按照参数查找逻辑找出模板内引用到的各类参数，把他们渲染成字符串后，再返回.

    除了最常用的 `get_arg_by_task_arg_name` 方法外，这个类还提供了一些其他方便读取 job 输入的方法，比如:

    - `get_trades`: 获取当前工单关联的所有订单号，因为飞梭工单业务的特性，几乎每个工单都关联了一个或多个订单，所以这里为此提供了一个专门的方法.
    - `get_send_message_arguments`: 发送消息类job（qianniu, qq, wechat,dingding) 等, 通常需要获取消息接收人，消息内容，消息渠道等参数，这里提供了一个专门的方法.

    使用这个类时，需要注意以下几点：

    - 尽量只使用 `get_arg_by_task_arg_name` 这个 API 来读取 job 参数.
    - 尽量不要添加新的参数读取规则，目前的规则已经较多较复杂了，添加更多新的规则会让代码更难理解.
    - 永远不要直接依赖这个类的成员变量，比如 `self.bo_data`, `self.widgets` 等.
    """

    def __init__(
        self,
        bo_data: dict,
        scopes: list[StepScope],
        widgets: list,
        task_arguments: list,
        key_map: dict,
        shop_nick: str = "",
        form_composer: FormComposer | None = None,
    ):
        """
        这个类用于封装对工单和job参数的操作.

        :param bo_data: business_order.data, 用户实际创建工单时在网页表单上填写的数据
        :param scopes: 当前步骤的 scope 信息
        :param widgets: 对应网页表单所有控件的定义
        :param task_arguments: 工单内一个 job 的参数定义
        :param key_map:  工单内一个 job 的参数和 widget 之间的映射关系
        """
        self.bo_data = bo_data  # TODO 检查 get 方法
        self.scopes = scopes
        self.widgets = widgets
        self.task_arguments = task_arguments
        self.key_map = key_map
        self.shop_nick = shop_nick
        self.form_composer = form_composer

    def _widget_ref_and_key(self, key: str | dict | WidgetRef):
        if isinstance(key, dict):
            widget_ref = WidgetRef.validate(key)
            key = widget_ref.key
        elif isinstance(key, WidgetRef):
            widget_ref = key
            key = widget_ref.key
        elif isinstance(key, str):
            widget_ref = None
            key = key
        else:
            raise TypeError(f"不支持的 key 类型 {key}")

        return widget_ref, key

    @t.overload
    def get_raw_value_by_widget_label(self, label: str) -> list | dict | int | float | str: ...

    @t.overload
    def get_raw_value_by_widget_label(self, label: str, *, rtype: type[T]) -> T: ...

    def get_raw_value_by_widget_label(self, label, **kwargs):
        """根据表单组件的 label 查找对应组件的原始数据

        注意：如果是table组件，则返回的是整个table data, 因为label无法定位到column上

        Args:
            label: 表单组件的 label，例如 "买家昵称", "订单ID" 等
        Returns:
            存在的话，返回 label 对应的输入参数；
            如果没有对应的输入参数，则返回 None; 找不到 label 对应的表单控件，则抛出 KeyError 异常
        """
        widget = self._get_widget_by_label(label)
        # 在尝试获取一个组件的值时，可能会有两种情况导致 KeyError
        # 场景一: 表单未配置；场景二：表单配置了组件，但是表单并未提交组件的值
        # fixme 应该区分不同场景的 KeyError ，由使用方决定如何处理
        if not widget:
            raise KeyError(f"找不到 {label=} 的 widget")
        return self.get_widget_key_value(widget["key"])

    @t.overload
    def get_and_render_value_by_widget_label(self, label: str) -> list | dict | int | float | str: ...

    @t.overload
    def get_and_render_value_by_widget_label(self, label: str, *, rtype: type[T]) -> T: ...

    def get_and_render_value_by_widget_label(self, label, **kwargs):
        """根据 widget label 获取值，并使用 value_handler 进行渲染

        Args:
            label: 表单组件的 label，例如 "买家昵称", "订单ID" 等
        Returns:
            存在的话，返回 label 对应的输入参数；
            如果没有对应的输入参数，则返回 None; 找不到 label 对应的表单控件，则抛出 KeyError 异常
        """
        raw_value = self.get_raw_value_by_widget_label(label)
        widget = self._get_widget_by_label(label)
        return value_handler({"type": widget["type"], "value": raw_value})

    def get_and_render_brief_value_by_widget_label(self, label):
        raw_value = self.get_raw_value_by_widget_label(label)
        widget = self._get_widget_by_label(label)
        return value_handler({"type": widget["type"], "value": raw_value}, brief=True)

    def get_and_render_detail_value_by_widget_label(self, label):
        raw_value = self.get_raw_value_by_widget_label(label)
        widget = self._get_widget_by_label(label)
        return value_handler({"type": widget["type"], "value": raw_value}, brief=False)

    def get_raw_value_by_widget_key(self, widget_ref_or_key: str | dict | WidgetRef):
        widget_ref, key = self._widget_ref_and_key(widget_ref_or_key)
        if widget_ref:
            return self.get_widget_ref_value(widget_ref)
        else:
            return self.get_widget_key_value(key)

    def _get_value_handler_widget_type(self, widget_ref_or_key: str | dict | WidgetRef):
        """获取组件的类型，用于 value_handler 渲染"""
        widget_ref, key = self._widget_ref_and_key(widget_ref_or_key)
        widget = self._get_widget_by_key(key)
        # 不是嵌套组件，返回组件本身
        if not (widget_ref and widget_ref.depth > 1):
            return widget["type"]
        # 尝试在 option_value.fields 中找到引用列的组件定义

        def find_column_widget(column_widget_key):
            return next((w for w in widgets if w.get("key") == column_widget_key), None)

        widgets = self.widgets
        current = widget_ref
        flag_find_column_widget_type = False
        column_widget_type = None
        while not flag_find_column_widget_type:
            if not (column_widget := find_column_widget(current.key)):
                break
            if not (column_widget_type := column_widget.get("type")):
                break
            widgets = (column_widget.get("option_value") or {}).get("fields", [])
            if isinstance(current.field, WidgetRef):
                current = current.field
            elif isinstance(current.field, str):
                current = WidgetRef(key=current.field, type="string")
            else:
                flag_find_column_widget_type = True
        if flag_find_column_widget_type:
            return column_widget_type
        else:
            if not self.form_composer:
                return None
            try:
                if value := widget_ref.to_value(self.form_composer):
                    symtable = self.form_composer.symbol_table_wrapper
                    return value.var.resolve_symbol(symtable, symtable.current_scope).type_spec.type
                return None
            except Exception as e:
                logger.error(f"form_composer {e}")
            return None

    def get_and_render_value_by_widget_key(self, widget_ref_or_key: str | dict | WidgetRef):
        widget_ref, key = self._widget_ref_and_key(widget_ref_or_key)
        if widget_ref:
            raw_value = self.get_widget_ref_value(widget_ref, flatten=True)
        else:
            raw_value = self.get_widget_key_value(key)
        if not raw_value:
            return raw_value

        widget_type = self._get_value_handler_widget_type(widget_ref_or_key)
        if isinstance(raw_value, list) and widget_ref and widget_ref.depth > 1:
            return ",".join(
                map(
                    str,
                    filter(
                        None,
                        map(
                            lambda x: value_handler({"type": widget_type or "string", "value": x}),
                            raw_value,
                        ),
                    ),
                )
            )
        elif not widget_type:
            return raw_value
        else:
            return value_handler({"type": widget_type, "value": raw_value})

    def get_and_render_brief_value_by_widget_key(self, widget_ref_or_key: str | dict | WidgetRef):
        widget_ref, key = self._widget_ref_and_key(widget_ref_or_key)
        # FIXME(<EMAIL>): 更好的做法是只拿输入组件？
        try:
            if widget_ref:
                raw_value = self.get_widget_ref_value(widget_ref, flatten=True)
            else:
                raw_value = self.get_widget_key_value(key)
        except KeyError:
            raw_value = None
        if not raw_value:
            return raw_value

        widget_type = self._get_value_handler_widget_type(widget_ref_or_key)
        if not widget_type:
            return raw_value
        if isinstance(raw_value, list) and widget_ref and widget_ref.depth > 1:
            return ",".join(
                map(
                    str,
                    filter(
                        None,
                        map(
                            lambda x: value_handler({"type": widget_type, "value": x}, brief=True),
                            raw_value,
                        ),
                    ),
                )
            )
        else:
            return value_handler({"type": widget_type, "value": raw_value}, brief=True)

    def get_and_render_detail_value_by_widget_key(self, widget_ref_or_key: str | dict | WidgetRef):
        widget_ref, key = self._widget_ref_and_key(widget_ref_or_key)
        if widget_ref:
            raw_value = self.get_widget_ref_value(widget_ref, flatten=True)
        else:
            raw_value = self.get_widget_key_value(key)
        if not raw_value:
            return raw_value

        widget_type = self._get_value_handler_widget_type(widget_ref_or_key)
        if not widget_type:
            return raw_value
        if isinstance(raw_value, list) and widget_ref and widget_ref.depth > 1:
            return ",".join(
                map(
                    str,
                    filter(
                        None,
                        map(
                            lambda x: value_handler({"type": widget_type, "value": x}, brief=False),
                            raw_value,
                        ),
                    ),
                )
            )
        else:
            return value_handler({"type": widget_type, "value": raw_value}, brief=False)

    def render_customize_589976_by_widget_key(self, widget_ref_or_key: str | dict | WidgetRef):
        widget_ref, key = self._widget_ref_and_key(widget_ref_or_key)
        if widget_ref:
            raw_value = self.get_widget_ref_value(widget_ref, flatten=True)
        else:
            raw_value = self.get_widget_key_value(key)
        if not raw_value:
            return raw_value
        widget_type = self._get_value_handler_widget_type(widget_ref_or_key)
        if not widget_type:
            return raw_value
        return value_handler(
            {"type": widget_type, "value": raw_value},
            brief=False,
            customize_589976=True,
        )

    def get_arg_as_str_by_task_arg_name(self, name: str) -> str:
        raw_value = self.get_arg_by_task_arg_name(name)
        if isinstance(raw_value, str):
            return raw_value
        if isinstance(raw_value, t.List):
            return ",".join([str(v) for v in raw_value])
        if raw_value is None:
            return ""
        raise ValueError(f"参数 {name} 的值 {raw_value} 无法转换为字符串")

    def get_widget_value_by_key(self, widget_key: str):
        # select 类型的参数，arg_meta 保存的是 widget key
        widget_ref, key = self._widget_ref_and_key(widget_key)
        if widget_ref:
            widget_value = self.get_widget_ref_value(widget_ref)
        else:
            try:
                widget_value = self.get_widget_key_value(key)
            except KeyError:
                widget_value = None
        if widget_value is None:
            return
        widget = self._get_widget_by_key(key)
        if Widget.Utils.check_if_widget_type_select(widget["type"]):
            return convert_select_widget_data_to_old(widget_value)
        elif widget["type"] == "product":
            return convert_product_widget_data_as_array(widget_value)
        else:
            return widget_value

    def get_arg_by_task_arg_name(self, name: str):
        """根据 task_arguments(job.step.raw_step['task']['arguments']) 中声明的参数名称，从 bo_data 中找到对应的值."""
        arg_def = self._get_arg_def_by_name(name)
        if not arg_def:
            return None
        arg_type = arg_def["data_type"]
        if name not in self.key_map and arg_type != DataType.AUTO.value:
            return None
        # 根据 arg_type 不同，widget_ref_or_value 中保存的可能是 arg value，也可能是 widget ref
        # 如果是 arg value，直接返回; 如果是 widget key，需要从 bo_data 中获取然后返回
        # 如果key_map中value可能是一个引用了table组件下的field的 WidgetRef 对象
        if arg_type == DataType.INPUT_TEXTAREA.value:
            # 此时 arg_meta 是一个如下形式的 list：
            # [{'text': '', 'type': 'text'},
            #  {'key': 'c2020349c3e74dc09481aed50bbddd9e','label': '通知事项', 'type': 'concept'}]
            return self._render_text_template(template=self.key_map[name])
        elif arg_type == DataType.MSG_RULES.value:
            return self._render_send_message_rules(self.key_map[name])
        elif arg_type in (
            DataType.INPUT_SELECT.value,
            DataType.INPUT.value,
            DataType.INPUT_SELECT_EXTRA.value,
            DataType.INPUT_MULTI_SELECT_DROPDOWN.value,
            DataType.RULES.value,
            DataType.CRON.value,
        ):
            # 参数值在创建 step 时就已经决定，此时 self.key_map[name] 保存的就是 arg value
            # 不需要从 bo_data 中获取
            return self.key_map[name]
        elif arg_type in [DataType.SELECT.value, DataType.SELECT_WITH_FIXED.value]:
            if self.key_map[name] is None:
                return
            # select 类型的参数，arg_meta 保存的是 widget key
            widget_ref, key = self._widget_ref_and_key(self.key_map[name])
            if widget_ref:
                widget_value = self.get_widget_ref_value(widget_ref)
            else:
                try:
                    widget_value = self.get_widget_key_value(key)
                except KeyError:
                    widget_value = None
            if widget_value is None:
                return
            widget = self._get_widget_by_key(key)
            if Widget.Utils.check_if_widget_type_select(widget["type"]):
                return convert_select_widget_data_to_old(widget_value)
            elif widget["type"] == "product":
                return convert_product_widget_data_as_array(widget_value)
            else:
                return widget_value
        elif arg_type == DataType.AUTO.value:
            # auto 类型的参数
            # 如果 widget key 在 bo_data 中不存在，则尝试获取 arg_def 中的 default_value
            return self.key_map.get(name) or arg_def.get("default_value")
        elif arg_type == DataType.TEXT_TEMPLATE.value:
            from robot_processor.function.conversion.text_template_render import TextTemplateRender

            form_composer: FormComposer = unwrap_optional(self.form_composer)
            current_step = first(
                filter(lambda x: x.current, form_composer.steps),
                form_composer.begin_step,
            )
            return (
                TextTemplateRender(
                    form_version_id=Decimal(form_composer.meta.version_id),
                    current_step_id=current_step.id,
                    context=self.bo_data.copy(),
                    template=self.key_map.get(name, []),
                )
                .call()
                .unwrap_or_else(raise_exception)
            )
        else:
            return None

    @staticmethod
    def merge_oid_in_trade(trades: t.List[Trade]) -> t.List[MultiOidTrade]:
        tid_to_oids: t.Dict[str, list] = {}
        for trade in trades:
            if trade.tid not in tid_to_oids:
                tid_to_oids[trade.tid] = []
            if trade.oid:
                tid_to_oids[trade.tid].append(trade.oid)
        return [MultiOidTrade(tid=tid, oids=oids) for tid, oids in tid_to_oids.items()]

    def get_trades(self, arg_name="tid") -> t.List[Trade]:
        """获取订单列表.

        注意, 一个工单相关的订单信息比较特殊:

        1. 它有可能是客服创建工单时，通过表单参数显示提供的，需要通过 bo_data[arguments['tid']['key']] 方式读取到
        2. 还有可能是千牛侧边栏在创建工单时, 客户端代码自动从当前会话上下文中提取到的，作为工单默认背景参数包含在工单创建请求中, 可以通过 bo_data['tid'] 读取到

        这里优先尝试读取第一类工单信息，如果没有，再尝试读取第二类工单信息.
        需要读取工单 id 的 job，如果没有特殊理由，应该总是通过 get_trades() 方法来读取，而不是通过 get_arg_by_task_arg_name('tid') 来读取,
        更不是通过 bo_data['tid'] 来读取.
        """
        tid_arg_def = self._get_arg_def_by_name(arg_name)
        if tid_arg_def:
            trades_in_task_args = self.get_arg_by_task_arg_name(arg_name)
        else:
            trades_in_task_args = None
        if trades_in_task_args and isinstance(trades_in_task_args, list):
            trades = [Trade(tid=trade["tid"], oid=trade.get("oid")) for trade in trades_in_task_args if "tid" in trade]
            logger.bind(trade_source="task_args_list").info("订单信息={}, 原始数据={}", trades, trades_in_task_args)
            return trades
        elif trades_in_task_args and isinstance(trades_in_task_args, dict) and "tid" in trades_in_task_args:
            trades = [Trade(tid=trades_in_task_args["tid"], oid=trades_in_task_args.get("oid"))]
            logger.bind(trade_source="task_args_dict").info("订单信息={}, 原始数据={}", trades, trades_in_task_args)
            return trades
        elif trades_in_task_args and isinstance(trades_in_task_args, str):
            return [Trade(tid=trades_in_task_args, oid="")]
        elif "tid" in self.bo_data:
            trades = [Trade(tid=self.bo_data.get("tid"), oid=self.bo_data.get("oid"))]  # type: ignore[arg-type]
            logger.bind(trade_source="bo_data").info("订单信息={}", trades)
            return trades
        else:
            logger.warning("订单信息不存在")
            return []

    def get_address(self, arg_name: str = "address") -> Address | None:
        """
        获取地址组件的信息。
        :param arg_name:
        :return:
        """
        address_info = self.get_arg_by_task_arg_name(arg_name)
        if address_info is None:
            return None
        try:
            address = Address(**address_info)
            # 如果所有字段都为无效字符（None 或 "" 或 "    "）则判断为空。
            if all(
                [
                    (address.name is None or address.name.strip() == ""),
                    (address.mobile is None or address.mobile.strip() == ""),
                    (address.state is None or address.state.strip() == ""),
                    (address.city is None or address.city.strip() == ""),
                    (address.zone is None or address.zone.strip() == ""),
                    (address.district is None or address.district.strip() == ""),
                    (address.town is None or address.town.strip() == ""),
                    (address.address is None or address.address.strip() == ""),
                ]
            ):
                return None
            else:
                return address
        except Exception as e:
            logger.exception(f"地址解析失败：{e}")
            return None

    def get_reissue_skus(self, arg_name="sku_id") -> ReissueSkus:
        """
        获取补发商品(包括商品组件，商品组件会兼容为非原单)

        该函数内部并不会实现获取原单补发的商品。
        需要做原单补发的功能，需要调用该方法后，检测 after_sales_type 是否为 origin，然后自己去查订单获取商品。
        """
        reissue_product = self.get_arg_by_task_arg_name(arg_name)
        if not reissue_product:
            return ReissueSkus()

        # ref: https://git.leyantech.com/fed/mini-sidebar/-/blob/afdad81fcfbd2e3eab5f9661b205bd83a55da85a/src/plugin/components/reissue-product-picker/index.js#L64 # noqa:
        if isinstance(reissue_product, dict):
            after_sales_type = reissue_product["after_sales_type"]
            reissue_skus = reissue_product["sku_list"]
        elif isinstance(reissue_product, list):
            # 新补发商品是 list[dict] 的数据格式，并且是否为原单补发是存在 bo.data 的 context 里的。
            if self.key_map.get(arg_name) is None:
                return ReissueSkus()
            _, reissue_product_key = self._widget_ref_and_key(self.key_map[arg_name])
            bo_context: dict = self.bo_data.get("context") or {}
            reissue_product_context = bo_context.get(reissue_product_key)
            if not isinstance(reissue_product_context, dict):
                raise Exception("获取新补发商品的上下文信息失败")
            after_sales_type = reissue_product_context.get("reissue_type")
            reissue_skus = reissue_product
        else:
            # Note: 兼容旧格式以及 product 组件
            after_sales_type = "non_original"
            reissue_skus = reissue_product

        if after_sales_type in ["non_original", "custom", None]:
            sku_list = []
            for sku in reissue_skus:
                if self.is_new_product_widget(sku):
                    logger.info(f"new build sku-info, sku@{sku}")
                    sku_list.append(
                        SKU(
                            spu_id=sku.get("SPU", ""),
                            sku_id=sku.get("SKU", ""),
                            outer_sku_id=sku.get("SKU_OUTER") or sku.get("SKU"),
                            # 补发数量
                            qty=sku.get("REISSUE_QUANTITY") or sku.get("COUNT"),
                            pic=sku.get("PICTURE"),
                            price=sku.get("PRICE", 0),
                            type=1 if sku.get("COMBINE") in ["SINGLE", "CHILD"] else 2,
                            outer_spu_id=sku.get("SPU_OUTER", ""),
                            source=sku.get("SOURCE"),
                        )
                    )
                else:
                    sku_list.append(
                        SKU(
                            spu_id=sku.get("spu", ""),
                            sku_id=sku.get("sku", ""),
                            outer_sku_id=sku.get("outer_sku") or sku.get("sku"),
                            qty=sku.get("quantity") or sku.get("qty"),
                            pic=sku.get("pic_url"),
                            price=0,
                            type=sku.get("type", 1),
                            outer_spu_id=sku.get("outer_spu_id", "") or sku.get("outer_spu", ""),
                            source=sku.get("source"),
                        )
                    )

            return ReissueSkus(after_sales_type=after_sales_type, sku_list=sku_list)
        else:
            # 暂不支持原单补发，需要做原单补发的功能，需要调用该方法后，检测 after_sales_type 是否为 origin，然后自己去查订单获取商品。
            return ReissueSkus(after_sales_type=after_sales_type, sku_list=[])

    @staticmethod
    def is_new_product_widget(item: dict):
        return "SPU" in item.keys()

    def get_send_message_arguments(self, arg_name="branch") -> SendMessageArguments:
        """获取发送消息的相关参数, 仅在 SEND_QIANNIU, SEND_WECHAT_GROUP, SEND_QQ 等 job 中有用."""
        result_by_arg_name = self.get_arg_by_task_arg_name(arg_name)
        if result_by_arg_name:
            return result_by_arg_name
        else:
            # NOTE: 一些历史遗留数据中，消息参数直接保存在 key_map 下，而不是 key_map['branch'] 下
            return self._render_send_message_rules(self.key_map)

    def get_text_concept(self, concept_key: str | WidgetRef | dict) -> t.Optional[str]:
        """获取指定 concept.

        concept 是字符串模板中的一个变量，例如，工单备注中，用户可以通过模板 '@通知事项 @快递单号' 来配置备注内容，
        其中的 '@通知事项' 和 '@快递单号' 就是 concept.

        但要注意，运行时的 concept_key 并不是 `@通知事项` 这样的人肉易读的文本，而是：

        - uuid 形式的 widget key，例如 'c2020349c3e74dc09481aed50bbddd9e'


        :param concept_key: uuid 形式的字符串，或者 FixedConcepts 中的某个 key, 或者 RefWidgetObj
        :return: key 对应的 concept 存在，则返回对应的字符串；否则返回 ''
        """

        _, key = self._widget_ref_and_key(concept_key)

        def get_dynamic_concept():
            widget: dict = self._get_widget_by_key(key)
            if not widget:
                return None
            if widget["type"] == "address" and self.shop_nick in current_app.config.get(
                "CUSTOMIZE_589976_SELLER_NICKS", []
            ):
                value = self.render_customize_589976_by_widget_key(concept_key)
            else:
                value = self.get_and_render_value_by_widget_key(concept_key)

            return value

        try:
            concept_value = get_dynamic_concept()
        except KeyError:
            concept_value = ""
        if concept_value:
            return str(concept_value)
        else:
            return ""

    def _render_text_template(self, template: list) -> str:
        """使用工单数据将一个结构化的文本模板渲染成字符串.

        :param template: 一个结构化的文本模板，例如：
            [
                {'text': '', 'type': 'text'},
                {'key': 'c2020349c3e74dc09481aed50bbddd9e','label': '通知事项', 'type': 'concept'}
                {'key': {'key':'<widget_uuid>', 'field':'<uuid>','multi_row':False}, 'label':'时间', 'type': 'concept'}
            ]
        :return: 渲染后的字符串
        """
        text_segments = []
        mustache_mapper = {}
        for schema in template:
            if schema["type"] == "text":
                text_segments.append(schema["text"])
            elif schema.get("mustache"):
                text_segments.append(schema["label"])
                mustache_mapper[schema["label"]] = self.get_raw_value_by_widget_key(schema["key"])
            elif concept_value := self.get_text_concept(schema["key"]):
                text_segments.append(concept_value)
        text = "".join(text_segments)
        if mustache_mapper:
            text = chevron.render(text, mustache_mapper)
        return text

    def _render_send_message_rules(self, raw_conf: dict) -> SendMessageArguments:
        """
        消息发送规则数据的结构比较复杂，按照 branch_status 可分成两类： single 和 multiple
        """

        def get_settings_for_multiple_mode():
            send_method = raw_conf.get("send_method", "")
            overall_settings = raw_conf.get(send_method, {})
            branch_settings = sorted(overall_settings.get("multiple", []), key=lambda x: x["order"])
            for branch_setting in branch_settings:
                branch = Branch.build_branch(self, branch_setting)
                if branch.accept():
                    return overall_settings, branch_setting
            return overall_settings, {}

        def get_settings_for_single_mode():
            overall_settings = raw_conf["single"]
            branch_setting = raw_conf["single"]
            return overall_settings, branch_setting

        mode = raw_conf.get("branch_status")  # mode 应该是 'multiple' 或 'single'
        if mode == "multiple":
            overall_settings, branch = get_settings_for_multiple_mode()
        elif mode == "single":
            overall_settings, branch = get_settings_for_single_mode()
        else:
            overall_settings, branch = raw_conf, raw_conf

        def get_content() -> str:
            """获取消息内容"""
            return self._render_text_template(branch.get("content", []))

        def get_images() -> t.List[str]:
            """获取消息中需要包含的图片链接"""
            image_key = branch.get("image", "")
            image_widget_ref = WidgetRef.parse(image_key, force_parse_str=True)
            image_urls = self.bo_data.get(image_widget_ref.key)
            if not image_urls:
                return []
            if isinstance(image_urls, str):
                return [image_urls]
            elif isinstance(image_urls, list):
                return [img.get("url") for img in image_urls if "url" in img]
            elif isinstance(image_urls, dict) and "url" in image_urls:
                return [t.cast(str, image_urls.get("url"))]
            else:
                return []

        def get_send_channel() -> str:
            """获取消息发送渠道"""
            return overall_settings.get("send_channel", {}).get("value", "RPA_CLIENT")

        def should_skip() -> bool:
            """是否跳过发送"""
            return bool(branch.get("skip"))

        def get_arg_def_by_name(arg_name: str):
            from itertools import chain

            try:
                branch_def = self._get_arg_def_by_name("branch")
                for arg in chain(
                    branch_def.get("commons", []),
                    branch_def.get("arguments", []),
                    branch_def.get("optionals", []),
                ):
                    if arg.get("name") == arg_name:
                        return arg
            except:  # noqa
                return None

        message_arguments = SendMessageArguments(
            content=get_content(),
            send_channel=get_send_channel(),
            image_urls=get_images(),
            skip=should_skip(),
        )
        # 解析可选参数
        for name in SendMessageArguments.optional_arguments():
            # HACK: 对于千牛消息，usernick 可能直接存储在 bo_data 中，{'usernick': 'nick_name'}
            # 也可能通过 key 间接存储在 bo_data 中,
            # 如 bo_data={'xxxxxxx': 'nick_name'}, key_map={'branch': {'single': {'usernick': 'xxxxxxx'}}}
            if name == "usernick":
                if directly_set_usernick := self.bo_data.get("usernick"):
                    message_arguments.usernick = directly_set_usernick.strip()
                elif usernick_widget_ref := branch.get(name, overall_settings.get(name)):
                    try:
                        usernick: str | None = self.get_raw_value_by_widget_key(usernick_widget_ref)
                        if usernick:
                            message_arguments.usernick = usernick.strip()
                    except KeyError:
                        pass
            elif name == "tid" and (tid_widget_ref := branch.get(name, overall_settings.get(name))):
                try:
                    message_arguments.tid = self.get_raw_value_by_widget_key(tid_widget_ref)
                except KeyError:
                    pass

            elif value := branch.get(name):
                match get_arg_def_by_name(name):
                    case {"data_type": DataType.SELECT}:
                        widget_ref, key = self._widget_ref_and_key(value)
                        if widget_ref:
                            setattr(
                                message_arguments,
                                name,
                                self.get_widget_ref_value(widget_ref),
                            )
                        else:
                            try:
                                setattr(
                                    message_arguments,
                                    name,
                                    self.get_widget_key_value(key),
                                )
                            except KeyError:
                                pass
                    case _:
                        value = value.strip() if isinstance(value, str) else value
                        setattr(message_arguments, name, value)

            elif value := overall_settings.get(name):
                match get_arg_def_by_name(name):
                    case {"data_type": DataType.SELECT}:
                        widget_ref, key = self._widget_ref_and_key(value)
                        if widget_ref:
                            setattr(
                                message_arguments,
                                name,
                                self.get_widget_ref_value(widget_ref),
                            )
                        else:
                            try:
                                setattr(
                                    message_arguments,
                                    name,
                                    self.get_widget_key_value(key),
                                )
                            except KeyError:
                                pass
                    case _:
                        value = value.strip() if isinstance(value, str) else value
                        setattr(message_arguments, name, value)

        return message_arguments

    def _get_widget_by_key(self, key: t.Union[str, WidgetRef]):
        return next((w for w in self.widgets if w["key"] == key), None)

    def _get_widget_by_label(self, label: str):
        return next((w for w in self.widgets if w["label"] == label), None)

    def get_widget_key_value(self, key: str):
        """从 data 中直接返回原始信息"""
        return self.bo_data[key]

    def get_widget_ref_value(
        self,
        widget_ref: WidgetRef,
        flatten: bool = False,
        without_self: str | None = None,
    ):
        """从 data 中获取当前 scope 的信息"""

        flag_is_parent_scope = False
        raw_widget_ref_value: t.Any = None
        current_level = 0
        if without_self and self.scopes and self.scopes[0].step_uuid == without_self:
            scopes = self.scopes[1:]
        else:
            scopes = self.scopes
        # scopes 是当前步骤由近到远的作用域信息，要从内到外找和 widget_ref 有关的 scope
        for scope in scopes:
            scoped_data = self.bo_data.get(scope.step_uuid, {}).get("item", {}).copy()

            if widget_ref.key not in scoped_data:  # 和当前 widget_ref 无关的 scope
                continue
            # widget_ref.key 在当前 scope 中，说明当前 scope 是最近的 scope
            for level in range(widget_ref.depth):
                # 在父级 scope 中查找到了未遍历的结果
                if isinstance(scoped_data, list):
                    flag_is_parent_scope = True
                    raw_widget_ref_value = scoped_data
                    current_level = level
                    break
                key = widget_ref.get_key_by_level(level)

                # 要跳过 array item, array item 的 key 固定为 {array.key}_ITEM，但是在数据结构中是不存在这一层的
                if widget_ref.is_array_item_by_level(level):
                    continue

                # 已经查找到了叶子节点，说明 scope 是对的，只是该节点有可能值为 null
                # 参考 business_order.job_wrapper.test_realworld.TestCase629199
                if level == widget_ref.depth - 1:
                    scoped_data = scoped_data.get(key)

                # 查询的是 sibling 的遍历结果，退出当前 scope 的查找，去父级 scope 中查找
                elif key not in scoped_data:
                    break

                else:
                    scoped_data = scoped_data[key]

            else:
                # 当前的查询是被遍历的，直接查询到了结果，返回一个标量
                widget_ref_value = scoped_data
                logger.info(f"找到了 widget_ref={widget_ref} 的值 {widget_ref_value}")
                return widget_ref_value

            if flag_is_parent_scope:  # 找到了父级 scope ，对 table 进行处理
                break
            else:  # key 不在 context 中，继续循环，到父级 scope 去获取
                pass

        else:
            # 没有找到 widget_ref.key，返回 None
            raw_widget_ref_value = self.bo_data.get(widget_ref.key)
            # 取出了 widget_ref.key 的值，等于将 level 推进了一层
            current_level = current_level + 1

        # 不是 table 组件，直接返回
        if not isinstance(raw_widget_ref_value, list):
            logger.info(f"找到了 widget_ref={widget_ref} 的值 {raw_widget_ref_value}")
            return raw_widget_ref_value
        if not (widget_ref.widget_type == "table" or widget_ref.type == "table"):
            return raw_widget_ref_value

        # 构造 jmespath 语法来对剩余的层级进行查询
        raw_path_list = []
        for level in range(current_level, widget_ref.depth):
            path = '"' + widget_ref.get_key_by_level(level) + '"'
            raw_path_list.append(path)
        path_list = ["[]"]
        for path in raw_path_list[:-1]:
            if flatten:  # [] 语法为对结果进行 flatten
                path = path + "[]"
            else:  # [*] 语法为对结果保留层级信息
                path = path + "[*]"
            path_list.append(path)
        path_list.extend(raw_path_list[-1:])
        # 构建 jmespath 查询路径
        expression = ".".join(path_list)
        value = jmespath_search(expression, raw_widget_ref_value)
        if flatten and isinstance(value, list) and all(isinstance(item, list) for item in value):
            value = jmespath_search("[]", value)

        logger.info(f"get_widget_ref_value: {widget_ref=} {value=}")
        if widget_ref.type == "table" and widget_ref.multi_row is False and len(value):
            return value[0]
        return value

    def _get_arg_def_by_name(self, name: str):
        return next((arg for arg in self.task_arguments if arg.get("name") == name), None)

    def is_argument_configured(self, arg_name: str):
        """检查用户是否配置了相关 rpa argument"""
        return arg_name in self.key_map

    def is_argument_configured_in_task_arguments(self, arg_name: str):
        def extract_data_binding(json_data):
            data_binding_values = []

            def traverse_json(obj):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        if key == "data_binding":
                            data_binding_values.append(value)
                        elif isinstance(value, (dict, list)):
                            traverse_json(value)
                elif isinstance(obj, list):
                    for item in obj:
                        traverse_json(item)

            traverse_json(json_data)
            return data_binding_values

        if not self.task_arguments:
            return False
        all_data_binding = extract_data_binding(self.task_arguments)
        for binding in all_data_binding:
            if binding.get("expression", "") == arg_name:
                return True
        return False

    def get_configured_arguments(self):
        # todo fixme 需要一个输出全部 配置了组件并且为输出类型的函数
        return list(self.key_map.keys())

    @classmethod
    def extract_from_job_orm(cls, job: Job) -> "JobArguments":
        from robot_processor.business_order.models import get_form_composer

        order: BusinessOrder = job.business_order
        shop: Shop | None = job.shop
        if not (form_version := order.form_version):
            raise ReferenceError(f"工单实例没有版本信息 business_order_id={order.id}")
        if not shop:
            raise ReferenceError(f"工单实例没有店铺信息 business_order_id={order.id}")
        form_version = t.cast(FormVersion, form_version)
        try:
            scopes = form_version.get_step_scope(job.step_uuid)  # type: ignore[arg-type]
        except StopIteration:
            scopes = []
        # get_form_composer 的 performance 见 https://git.leyantech.com/digismart/robot-processor/-/merge_requests/4739
        try:
            form_composer = get_form_composer(order.form_version_id)
            form_composer.set_current(job.step_id)
        except Exception as e:
            logger.opt(exception=e).error("init form composer error")
            form_composer = None

        return cls(
            bo_data=order.data,
            scopes=scopes,
            widgets=handlers.bo_widget_wrapper(order),
            task_arguments=job.raw_step_v2.get("task", {}).get("arguments", []),
            key_map=job.raw_step_v2.get("key_map"),
            shop_nick=shop.nick,
            form_composer=form_composer,
        )


class JobStates:
    def __init__(
        self,
        bo: BusinessOrder,
        job: Job,
        task_output_arguments: list,
        key_map: dict,
        widgets: list,
    ):
        self.bo = bo
        self.job = job
        self.task_output_arguments = task_output_arguments
        self.key_map = key_map
        self.widgets = widgets
        self.stop_on_error = (key_map or {}).get("stop_on_error", True)
        self.continue_on_error_whitelist: list[int] = (key_map or {}).get("continue_on_error_whitelist", [])

    @classmethod
    def of(cls, job: Job):
        def get_task_arguments() -> dict:
            task_arguments = job.raw_step_v2.get("task", {}).get("argument")
            if task_arguments is not None:
                return task_arguments
            else:
                # Note: 兼容 raw_step_v1
                return {"mixed": job.raw_step_v2.get("task", {}).get("arguments", [])}

        def get_output_arguments():
            task_arguments = get_task_arguments()
            task_output_arguments = []
            for argument in task_arguments.get("mixed", []):
                if argument["data_type"] == DataType.SELECT and "输出" in argument["label"]:
                    task_output_arguments.append(argument)
            task_output_arguments.extend(task_arguments.get("output", []))
            return task_output_arguments

        key_map = job.raw_step_v2.get("key_map")
        widgets = handlers.bo_widget_wrapper(job.business_order)
        return cls(job.business_order, job, get_output_arguments(), key_map, widgets)

    @staticmethod
    def _is_empty_value(val):
        return val is None or val == ""

    def _update_bo_data(self, updated_data: dict):
        logger.info(f"update bo data with {updated_data}")
        self.bo.data.update(updated_data)
        flag_modified(self.bo, "data")
        db.session.add(self.bo)

    def _is_upload_image(self, widget_key: str) -> bool:
        widget = next(filter(lambda x: x["key"] == widget_key, self.widgets), None)
        if not widget:
            logger.error("找不到key {}对应的widget", widget_key)
            return False
        return widget.get("uploadType") == "image"

    def _is_reissue_product(self, widget_key: str) -> bool:
        widget = next(filter(lambda x: x["key"] == widget_key, self.widgets), None)
        if not widget:
            logger.error("找不到key {}对应的widget", widget_key)
            return False
        return widget.get("type") == "reissue-product"

    def write_widget_data(self, data: dict):
        logger.info("writing widget data: {}", data)
        new_bo_data = {}
        for widget in self.widgets:
            widget_label, widget_key = widget["label"], widget["key"]
            if widget_label in data:
                new_bo_data[widget_key] = data[widget_label]
        self._update_bo_data(new_bo_data)

    def write_job_output(self, data: dict, allow_empty_value: bool = True) -> t.List[str]:
        """回写 job 的 output 到 bo.data 中, 一般推荐在 job 结束时一次性写入, 应用场景:
        - 作为后续 job 输入参数
        - 给人工步骤提供依据, 用于 UI 展示

        注：data 是单行数据
        注: 需要先配置 rpa 的 output arguments

        @param data job 的 output
        @param allow_empty_value 业务写入方是否允许 output 里的值为空值（None和空字符串）
        @returns 工单配置中应该写入 bo.data , 但是本次 data 没有提供数据的 rpa output arguments 列表
        """
        logger.info("writing output: {}", data)

        missing_args = []
        bo_data = self.bo.data_wrapper
        modified = False
        for argument in self.task_output_arguments:
            arg_name = argument["name"]
            if self.check_widget_is_table_by_arg_name(arg_name):
                # 一些任务会同时使用 write_job_output 和 write_job_output_multiple 方法
                # 需要在这里过滤掉 table 类型的参数
                # FIXME: https://git.leyantech.com/digismart/robot-processor/-/issues/341
                continue
            if not self.key_map.get(arg_name):
                # 工单模版没有配置, 或者配置的是空值
                continue

            if arg_name not in data:
                # TODO: 检查 argument['required']
                missing_args.append(argument["label"])
                continue

            # TODO: 根据 widget 类型转换数据格式
            value = data[arg_name]
            # argument.get('required')表示该组件是必填的，如果必填的组件将写入空值，并且业务方不允许空值，添加到missing_args中
            if not allow_empty_value and JobStates._is_empty_value(value):
                missing_args.append(argument["label"])
                continue

            modified = True
            widget_key = self.key_map[arg_name]
            if isinstance(widget_key, dict):
                key = widget_key["key"]
            elif isinstance(widget_key, WidgetRef):
                key = widget_key.key
            elif isinstance(widget_key, str):
                key = widget_key
            else:
                raise ValueError(f"不支持的 widget_key 类型: {widget_key} {type(widget_key)}")

            bo_data[key] = value

        if modified:
            self._update_bo_data(bo_data.raw_data)

        return missing_args

    @in_transaction()
    def write_job_widget_collection(self, data: dict, ui_schema: list[dict] | None = None):
        """如果自动化应用绑定了表单，根据表单配置和自动化应用执行结果提交表单

        @param data: 自动化应用执行结果
        @param ui_schema: 表单配置，默认无需填写，会从 job 中获取

        相关设计见测试用例 tests.job.job_states.test_write_job_widget_collection
        处理方法：
            1. 根据表单配置，获取表单中所有的组件
            2. 遍历组件，获取组件的data_binding配置
            3. 根据data_binding配置，获取组件的值
        Todo:
            交接给 BusinessOrderDataWrapper 处理
        """
        if ui_schema is None:
            ui_schema = self.job.raw_ui_schema
        ui_schema_view = [
            t.cast(WidgetInfo.View.RawStep, WidgetInfo.View.RawStep.parse_obj(widget_info)) for widget_info in ui_schema
        ]

        # 当前表单定义的组件
        output_data: dict[str, t.Any] = {}
        # 记录一下缺失信息的组件
        missing: list[WidgetInfo.View.LeafFromRootPath] = []
        widget_ref_pair = WidgetInfo.Schema.WidgetRefAndLeafFromRootPathPair.build_from_ui_schema(
            ui_schema_view, self.bo.widget_form_wrapper.all_widget_info_list
        )

        # 1. 根据表单配置，获取表单中所有的组件
        # 找到所有引用了前序组件的组件，把它维护到 `refer_data` 中
        for widget_info in ui_schema_view:
            leaf_from_root_path_list = WidgetInfo.Utils.flatten_widget_info_tree_paths(widget_info)
            for leaf_from_root_path in leaf_from_root_path_list:
                leaf_widget_info = leaf_from_root_path.leaf
                leaf_widget_ref = WidgetInfo.Utils.get_widget_info_reference(leaf_widget_info)
                if leaf_widget_ref is not None:
                    output_data[leaf_widget_ref.key] = self.bo.data.get(leaf_widget_ref.key)

        for widget_info in ui_schema_view:
            missing.extend(
                WidgetInfo.Utils.fill_widget_info_by_data_binding(widget_info, output_data, data, widget_ref_pair)
            )
        self._update_bo_data(output_data)

        @dataclass
        class WriteResult:
            missing: list[WidgetInfo.View.LeafFromRootPath]

        # 可能存在同一个字段在多行都 missing 的情况，这个时候重复是没有意义的，需要去重
        write_result = WriteResult([])
        marked_basket = set()
        for each_missing in missing:
            if each_missing.readable in marked_basket:
                continue
            marked_basket.add(each_missing.readable)
            write_result.missing.append(each_missing)

        return write_result

    def check_widget_is_table_by_arg_name(self, arg_name: str) -> bool:
        """
        通过 arg_name 检测 widget 是否为复合组件。
        """
        if arg_name not in self.key_map:
            # 没有配置，也不算复合组件
            return False
        widget_ref = WidgetRef.parse(self.key_map[arg_name], force_parse_str=True)
        if widget_ref and widget_ref.type == "upload":
            # 要过滤图片上传组件
            return False
        if widget_ref and widget_ref.type == "reissue-product":
            # 过滤补发商品组件
            return False
        elif widget_ref and widget_ref.is_table:
            # 如果是复合组件，则直接返回 True
            return True
        else:
            return False

    def write_job_output_with_multi_row(self, datas: t.List[t.Dict]) -> t.List[str]:
        """
        回写多行数据(全量覆盖)
        TODO 临时方案，目标是先跑通， rpa输出到bo.data的api需要更好的设计
         正式方案需要产品参与规划，比如在页面配置输出单行还是多行

        写入分以下三种情况:
            rpa输出单条数据到旧组件
            rpa输出单条数据到单行table组件
            rpa输出单条数据到多行table组件

        但 rpa 本身不应该关注这些
        """

        # [
        #   (arg_name, arg_label, widget_ref)
        # ]
        output_args = []
        for argument in self.task_output_arguments:
            arg_name = argument["name"]
            arg_label = argument["label"]
            if not self.key_map.get(arg_name):
                # 工单模版没有配置, 或者配置的是空值
                continue

            widget_ref = WidgetRef.parse(self.key_map[arg_name])
            output_args.append((arg_name, arg_label, widget_ref))

        # 构造组件数据：
        # {
        #    "widget1_key_uuid":[<rows>],
        #    "widget2_key_uuid":[<rows>],
        # }
        missing_args = []
        new_widgets_value: t.Dict[str, t.List[t.Any]] = {}
        for data in datas:

            # 构造一行的数据, 注意可能输出到多个组件中
            # {
            #   "widget1_key_uuid":{<row_data>},
            #   "widget2_key_uuid":{<row_data>},
            # }
            widgets_row_data: t.Dict[str, t.Dict[str, t.Any]] = {}
            for arg_name, arg_label, widget_ref in output_args:
                if isinstance(widget_ref.field, str):
                    arg_widget_field_key = widget_ref.field
                elif isinstance(widget_ref.field, WidgetRef):
                    arg_widget_field_key = widget_ref.field.key
                else:
                    logger.warning(f"组件[{arg_name}@{arg_label}]的field配置不正确 {widget_ref}")
                    continue
                # 不写入的字段不关心
                if arg_name not in data:
                    continue
                # 对写入空值的情况做处理
                if JobStates._is_empty_value(data.get(arg_name)):
                    widget_info = [i for i in self.widgets if i.get("key") == widget_ref.key][0]
                    this_field_data_schema = [
                        i for i in widget_info["data_schema"]["fields"] if i["name"] == arg_widget_field_key
                    ][0]
                    if this_field_data_schema.get("constraints", {}).get("required") is True:
                        missing_args.append(arg_label)
                    else:
                        continue
                if not widget_ref.is_table:
                    raise ValueError(f"当前组件[{arg_label}]非复合组件")
                if widget_ref.key not in widgets_row_data:
                    widgets_row_data[widget_ref.key] = {}
                widgets_row_data[widget_ref.key][arg_widget_field_key] = data.get(arg_name)

            # 把每个组件的一行数据 append 到最终结果
            for widget_key, one_row in widgets_row_data.items():
                if widget_key not in new_widgets_value:
                    new_widgets_value[widget_key] = []
                new_widgets_value[widget_key].append(one_row)

        bo_data = self.bo.data_wrapper
        for widget_key, rows in new_widgets_value.items():
            for idx, row in enumerate(rows):
                bo_data.set(
                    WidgetRef(
                        key=widget_key,
                        type="table",
                        widget_type="table",
                        multi_row=True,
                    ),
                    row,
                    index=idx,
                    strategy="update",
                )
        self._update_bo_data(bo_data.raw_data)
        return missing_args

    def write_job_states(self, state: dict, replace=False):
        """更新 job 的状态. 一般用于保存 job 内部的信息:
        - 分步骤执行时, 保存已经完成的步骤
        - 批量处理数据时, 保存已经完成处理的数据

        @param state job 的运行状态
        @param replace 是否完全替换原来的运行状态
        """
        old_extra_data = self.job.extra_data or {}
        if replace:
            new_extra_data = state
        else:
            new_extra_data = {**old_extra_data, **state}
        logger.info(
            "writing state: old_state={} new_state={} replace={}",
            self.job.extra_data,
            new_extra_data,
            replace,
        )
        self.job.extra_data = new_extra_data
        flag_modified(self.job, "_extra_data")
        db.session.add(self.job)

    @in_transaction()
    def write_job_result_on_success(self, data: dict):
        job_result = {f"system_job_{self.job.step_uuid}_success": True, **data}
        self._update_bo_data(job_result)
        return JobStatus.SUCCEED, None

    @in_transaction()
    def write_job_result_on_fail(self, error: Exception | str, data: dict | None = None):
        from robot_processor.business_order.binlog.job_processor import exception_ruler_keeper

        ruler = exception_ruler_keeper.exception_ruler
        raw_exc = str(error)
        step_type = self.job.raw_step_type
        task_type = self.job.raw_task_info.get("task_type", None)
        process_result = ruler.process(raw_exc, step_type, task_type)

        job_result = {
            f"system_job_{self.job.step_uuid}_success": False,
            f"system_job_{self.job.step_uuid}_reason": process_result.render_result.reason,
            f"system_job_{self.job.step_uuid}_suggestion": process_result.render_result.suggestion,
            f"system_job{self.job.step_uuid}_exception_info": process_result.to_dict(),
        }
        if data:
            job_result.update(data)
        self._update_bo_data(job_result)
        if self.stop_on_error:
            return JobStatus.FAILED, error
        elif process_result.match_result.match_rule.id not in self.continue_on_error_whitelist:
            return JobStatus.FAILED, error
        else:
            return JobStatus.SUCCEED, None

    def get_job_state(self, state_name, default_value=None):
        return (self.job.extra_data or {}).get(state_name, default_value)


class JobModelWrapper:
    def __init__(self, job):
        self.job = job
        self.order = job.business_order

    @cached_property
    def erp_info(self) -> t.Optional[ErpInfo]:
        """
        获取店铺的 Erp 绑定信息, 目前一家店铺只会绑定一个 Erp
        """
        if shop := self.shop:
            return ErpInfo.query.filter_by(shop_id=shop.id).order_by(ErpInfo.id.desc()).first()
        return None

    @cached_property
    def shop(self):
        return Shop.Queries.optimal_shop_by_sid(self.job.business_order.sid)

    @property
    def step_uuid(self):
        return self.job.step_uuid

    def form_id(self):
        return self.job.business_order.form_id
