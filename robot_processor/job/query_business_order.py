from robot_types.model import resource

from robot_processor.enums import JobType
from robot_processor.function.business_order import QueryBusinessOrder
from robot_processor.job.auto_job import AutoJobControllerBase


class QueryBusinessOrderExecutor(AutoJobControllerBase):
    job_type = JobType.QUERY_BUSINESS_ORDER

    def process(self):
        func_impl = QueryBusinessOrder()
        func_impl.form_id = self.args.key_map["form"]
        if self.args.key_map.get("use_current_shop") is True:
            func_impl.shop = resource.Shop(sid=self.shop.sid, platform=self.shop.platform)
        else:
            func_impl.shop = resource.Shop(
                sid=self.args.key_map["shop"]["sid"],
                platform=self.args.key_map["shop"]["platform"],
            )
        func_impl.filters = self.args.key_map["filters"].copy()
        func_impl.context = self.order.data.copy()
        func_impl.context["system_business_order_id"] = self.order.id
        func_impl.field_mask = self.args.key_map["field_mask"]
        data_name = self.args.key_map["data_name"]
        result = func_impl.call()
        data = {data_name: result.unwrap_or([])}
        if result.is_err():
            return self.states.write_job_result_on_fail(error=result.unwrap_err(), data=data)
        else:
            return self.states.write_job_result_on_success(data=data)
