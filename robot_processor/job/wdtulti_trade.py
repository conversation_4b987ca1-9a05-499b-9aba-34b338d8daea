"""
旺店通旗舰版订单信息回执
"""
import math
from typing import List, cast

from loguru import logger
from dramatiq.errors import Retry
from result import Ok, Err

from robot_processor.client import app_config
from robot_processor.enums import JobType, JobStatus
from robot_processor.job.erp_trade_handler.exceptions import ErpFilteredError
from robot_processor.job.job_model_wrapper import JobArguments, JobStates
from robot_processor.job.trade import TradeExecutor
from robot_processor.shop.models import Shop
from rpa.erp.wdtulti import WdtultiOrderModel, WdtUltiQM, WdtultiOrderResp, WdtultiOrderModelOutput, WdtQmRateLimitError
from robot_processor.job.utils import get_valid_tracking_numbers, split_string_to_list


class WDTUltiTradeExecutor(TradeExecutor):
    job_type = JobType.WDT_ULTIMATE_TRADE

    def process(self):
        if self.shop.org_id in app_config.wdtulti_trade_query_v2_whitelist:
            return self.process_v2()

        try:
            if bo_trades := self.args.get_trades("wdt_tid"):
                all_trades = []
                for bo_trade in bo_trades:
                    tid = bo_trade.tid
                    oid = bo_trade.oid
                    trades: List[WdtultiOrderModel] = TradeFinder(
                        src_tid=tid,
                        l_id=self.args.get_arg_by_task_arg_name("orginal_logistics_no"),
                        shop=self.job_wrapper.shop,
                        trade_no=self.args.get_arg_by_task_arg_name("trade_no"),
                        is_reissue=self.args.get_arg_by_task_arg_name("is_reissue"),
                        src_oid=oid if self.args.is_argument_configured("src_oid") else None,
                        ignore_not_found_error=self.args.get_arg_by_task_arg_name("ignore_not_found_error") or False
                    ).process()
                    all_trades.extend(trades)
                is_need_merge = self.args.get_arg_by_task_arg_name("is_need_merge") == "是"
                ExtraInfoAdder.process(all_trades, self.job_wrapper.shop, self.args)
                self._save(self.states, all_trades, is_need_merge)
        except WdtQmRateLimitError as e:
            raise Retry(str(e), delay=1000)
        except Exception as e:
            logger.opt(exception=e).info(f"处理旺店通旗舰版回执失败 {e}")
            return JobStatus.FAILED, str(e)
        return JobStatus.SUCCEED, ""

    def process_v2(self):
        """原 process 处理逻辑将订单的查询和筛选耦合在一起，且无法支持多订单号的场景"""
        from rpa.erp.wdtulti.util.trade_finder import TradeFinder, TradeFromType

        def is_logistics_no_set():
            arg_name = "orginal_logistics_no"
            if not self.args.is_argument_configured(arg_name):
                return False
            if self.shop.org_id not in app_config.allow_invalid_tracking_number:
                valid_logistics_no_list = get_valid_tracking_numbers(self.args.get_arg_by_task_arg_name(arg_name))
                return len(valid_logistics_no_list) > 0
            else:
                return True

        def get_logistics_no_list():
            arg_name = "orginal_logistics_no"
            original_logistics_no = self.args.get_arg_by_task_arg_name(arg_name)
            if self.shop.org_id in app_config.allow_invalid_tracking_number:
                return split_string_to_list(original_logistics_no)
            else:
                return get_valid_tracking_numbers(original_logistics_no)

        is_src_tid_set = self.args.is_argument_configured("wdt_tid")
        is_trade_no_set = self.args.is_argument_configured("trade_no")
        qm_sdk = WdtUltiQM(self.shop.sid)
        query = TradeFinder.Query()
        filter_ = TradeFinder.Filter()
        ignore_not_found_error = self.args.get_arg_by_task_arg_name(
            "ignore_not_found_error") or False
        trade_finder = TradeFinder(qm_sdk=qm_sdk, query=query, filter_=filter_,
                                   ignore_not_found_error=ignore_not_found_error)
        if self.args.is_argument_configured("is_reissue"):
            filter_.trade_from_type = TradeFromType(self.args.get_arg_by_task_arg_name("is_reissue"))
        if self.args.is_argument_configured("src_oid"):
            match self.args.get_arg_by_task_arg_name("src_oid"):
                case str() as oid_str:
                    filter_.src_oid_list = [oid_str]
                case list() as oid_list if all(isinstance(item, dict) and "oid" in item for item in oid_list):
                    filter_.src_oid_list = [item["oid"] for item in oid_list]
                case _ as unregistered:
                    return JobStatus.FAILED, ValueError(f"工单模板配置了子订单参数，但是子订单是非法的结构 {unregistered!r}")

        if is_logistics_no_set():
            logistics_no_list = get_logistics_no_list()
            query.logistics_no_list = logistics_no_list
            query_fields = ["物流单号"]
            # 平台单号和系统单号可以配合物流单号一起搜索
            if is_src_tid_set:
                query_fields.append("平台订单号")
                bo_trades = self.args.get_trades("wdt_tid")
                query.src_tid_list = [bo_trade.tid for bo_trade in bo_trades]
                if not is_trade_no_set:
                    filter_.src_tid_list = query.src_tid_list
            if is_trade_no_set:
                query_fields.append("旺店通系统订单号")
                query.trade_no = self.args.get_arg_by_task_arg_name("trade_no")
            do_fetch = trade_finder.fetch_with_logistics_no

        elif is_trade_no_set:
            query.trade_no = self.args.get_arg_by_task_arg_name("trade_no")
            query_fields = ["旺店通系统单号"]
            do_fetch = trade_finder.fetch_with_trade_no

        elif is_src_tid_set:
            bo_trades = self.args.get_trades("wdt_tid")
            query_fields = ["平台订单号"]
            query.src_tid_list = [bo_trade.tid for bo_trade in bo_trades]
            filter_.src_tid_list = query.src_tid_list
            do_fetch = trade_finder.fetch_with_src_tid

        else:
            return JobStatus.FAILED, "订单号和旺店通订单号不能同时为空"

        match do_fetch():
            case Ok(orders):
                # 只在API返回后立即判断
                if len(orders) == 0:
                    if not ignore_not_found_error:
                        return JobStatus.FAILED, ErpFilteredError("订单查询结果为空 筛选条件:{}".format("/".join(query_fields)))
                    # 如果ignore_not_found_error为True，直接返回空orders，后续do_filter不再抛错
                orders = cast(list[WdtultiOrderModel], orders)
            case Err(WdtQmRateLimitError() as e):
                raise Retry(str(e), delay=1000)
            case Err(e):
                return JobStatus.FAILED, str(e)
            case _ as unregistered:
                return JobStatus.FAILED, f"未知错误 {unregistered!r}"

        match trade_finder.do_filter(orders):
            case Ok(orders):
                orders = cast(list[WdtultiOrderModel], orders)
            case Err(e):
                return JobStatus.FAILED, e
            case _ as unregistered:
                return JobStatus.FAILED, f"未知错误 {unregistered!r}"

        # 和原 process 逻辑一致
        try:
            is_need_merge = self.args.get_arg_by_task_arg_name("is_need_merge") == "是"
            ExtraInfoAdder.process(orders, self.job_wrapper.shop, self.args)
            self._save(self.states, orders, is_need_merge)
            return JobStatus.SUCCEED, ""
        except WdtQmRateLimitError as e:
            raise Retry(str(e), delay=1000)
        except Exception as e:
            logger.opt(exception=e).info(f"处理旺店通旗舰版回执失败 {e}")
            return JobStatus.FAILED, str(e)

    @staticmethod
    def _save(states, trades, is_need_merge):
        DataSaver.process(trades, is_need_merge, states)


class WDTUltiTradeInfoExecutor(WDTUltiTradeExecutor):
    job_type = JobType.WDTULTI_TRADE_INFO
    output_model = WdtultiOrderModelOutput

    @staticmethod
    def _save(states: JobStates, trades, _):
        res = states.write_job_widget_collection({"orders": [i.dict() for i
                                                             in trades],
                                                  "count": len(trades)})
        if res.missing:
            status_str = " ".join([t.chinese_status for t in trades])
            missing_str = ' / '.join(arg.readable for arg in res.missing)
            raise Exception(f"当前订单状态: {status_str} 获取输出字段失败: {missing_str}")


class TradeFinder:
    def __init__(self, src_tid, l_id, shop, trade_no=None, is_reissue="2", src_oid=None, ignore_not_found_error=False):
        self.src_tid = src_tid
        self.l_id = l_id
        self.shop = shop
        self.trade_no = trade_no
        self.is_reissue = is_reissue
        if shop is not None and shop.org_id in app_config.allow_invalid_tracking_number:
            self.l_ids = split_string_to_list(l_id)
        else:
            self.l_ids = get_valid_tracking_numbers(l_id)
        self.src_oid = src_oid
        self.ignore_not_found_error = ignore_not_found_error

    def process(self):
        orders: List[WdtultiOrderModel] = self.try_find_orders(
            self.shop, self.src_tid, self.trade_no, self.l_ids[0] if len(
                self.l_ids) == 1 else None)
        order = self._filter_orders(orders, self.l_ids, self.is_reissue, self.src_oid)
        return order

    def try_find_orders(self, shop, src_tid, trade_no, logistics_no) -> List[WdtultiOrderModel]:
        try:
            res: WdtultiOrderResp = WdtUltiQM(shop.sid).get_orders(src_tid, trade_no, logistics_no)
        except WdtQmRateLimitError as e:
            raise Retry(str(e), delay=1000)
        if not res.order:
            if not self.ignore_not_found_error:
                raise Exception(f"未找到订单 {src_tid} {trade_no}")
            else:
                return []
        return res.order

    @staticmethod
    def _filter_orders(orders: List[WdtultiOrderModel], l_ids, is_reissue, src_oid) -> \
            List[WdtultiOrderModel]:
        if l_ids and len(l_ids) > 1:
            # TODO 旺店通订单可以绑定多物流单号，但是接口只返回一个主物流单号
            # TODO 目前还没有申请到旺店通订单多物流单号查询的接口，申请后需要用订单的多物流单号和入参的 l_ids 进行匹配
            orders = [order for order in orders if order.logistics_no in l_ids]
            if not orders:
                raise ErpFilteredError("没有符合筛选条件的订单 筛选条件:物流单号")
        if str(is_reissue) == "1":  # 补发订单
            orders = [order for order in orders if order.trade_from == 6]
        elif str(is_reissue) == "0":  # 普通订单
            orders = [order for order in orders if order.trade_from not in [6]]
        if not orders:
            raise ErpFilteredError("没有符合筛选条件的订单 筛选条件:订单类型")
        if src_oid:
            for order in orders:
                if order and order.detail_list:
                    order.detail_list = [detail for detail in order.detail_list if detail.src_oid == src_oid]
                    order.paid = math.fsum([float(detail.paid) or 0.0 for detail in order.detail_list])  # type: ignore
            orders = [order for order in orders if order.detail_list]
            if not orders:
                raise ErpFilteredError("没有符合筛选条件的订单 筛选条件:子订单号")
        # 旺店通的不存在复杂的拆分合并场景
        # 拆分即有多笔WDTUltraOrderModel
        # 合并只有一笔WDTUltraOrderModel， 但是detail_list中合并包含了两笔原订单的信息
        # 补发与拆分类似，有多个WDTUltraOrderModel 注意文档中注明的销售类型（trade_type字段）中有一个售后换货，
        # 但是实际情况是补发的订单类型也是“网店销售”。订单来源（trade_from）也不是补发，所以目前无法区分补发与拆分
        return orders


class ExtraInfoAdder:
    join_detail_fields = [
        "goods_no",
        "goods_name",
        "spec_no",
        "spec_name",
    ]

    @staticmethod
    def process(orders: List[WdtultiOrderModel], shop: Shop, args: JobArguments):
        wdt_ulti_qm = WdtUltiQM(shop.sid)
        warehouses = wdt_ulti_qm.all_warehouse_query()
        warehouse_no_2_name = {
            x.warehouse_no: x.name
            for x in warehouses
            if x.warehouse_no is not None and x.name is not None}
        for order in orders:
            for field in ExtraInfoAdder.join_detail_fields:
                setattr(order, field,
                        ",".join([getattr(detail, field) for detail in order.detail_list]))
            if order.warehouse_no:
                # 可能是多笔订单的合并结果
                warehouse_no_list = order.warehouse_no.split(",")
                warehouses_per_order: List[str] = []
                # 单笔订单，是否存在未知发货仓编号的情况
                has_unknown_warehouse_no = False
                for no in warehouse_no_list:
                    if no not in warehouse_no_2_name:
                        has_unknown_warehouse_no = True
                        break
                    warehouses_per_order.append(warehouse_no_2_name[no])
                if has_unknown_warehouse_no:
                    order.warehouse_name = ""
                else:
                    order.warehouse_name = ",".join(warehouses_per_order)

            if args.is_argument_configured("provider_name"):
                # todo 目前的需求客户一个订单只有一个商品，后续用复合组件进行多商品多供应商支持
                order.provider_name = wdt_ulti_qm.get_provider_name(order.detail_list[0].spec_no)
            order.paid = order.detail_paid
            order.all_goods_info = order.all_goods_info
            order.is_split = True if order.trade_mask and order.trade_mask >= 2048 else False
            stockouts = wdt_ulti_qm.get_stockout_order_by_trade_no(order.trade_no)
            stockouts = [stockout for stockout in stockouts if stockout.status != 5]
            packages = [detail for stockout in stockouts for detail in
                        stockout.logistics_list]
            order.is_multi_packages = True if len(packages) > 1 else False
        return orders


class DataSaver:
    @staticmethod
    def process(orders: List[WdtultiOrderModel], need_merge: bool, states: JobStates):
        single_trade = WdtultiOrderModel.merge_orders(orders) if need_merge else orders[0]
        field_names = [
            "logistics_name", "warehouse_no", "paid", "logistics_no", "warehouse_name", "goods_no", "spec_no",
            "spec_name", "goods_name", "chinese_status",
            "provider_name", "all_goods_info"
        ]
        to_multi_fields = [i for i in field_names if states.check_widget_is_table_by_arg_name(i)]
        to_single_fields = [i for i in field_names if i not in to_multi_fields]

        to_single_data = {}
        for field in to_single_fields:
            to_single_data[field] = getattr(single_trade, field)

        # 对于单行输出，直接用write_job_output回写
        missing_args = states.write_job_output(to_single_data, allow_empty_value=False)
        if missing_args:
            raise Exception(f"当前订单状态: {to_single_data.get('chinese_status')} 获取输出字段失败: {missing_args}")

        to_multi_rows = []
        for trade in orders:
            row = {}
            for field in to_multi_fields:
                row[field] = getattr(trade, field)
            if row:
                # to_multi_fields不为空（代表存在复合组件），才能添加数据
                to_multi_rows.append(row)
        trades_status = " ".join([t.chinese_status for t in orders])
        missing_args = states.write_job_output_with_multi_row(to_multi_rows)
        if missing_args:
            raise Exception(f"当前订单状态: {trades_status} 获取输出字段失败: {missing_args}")
