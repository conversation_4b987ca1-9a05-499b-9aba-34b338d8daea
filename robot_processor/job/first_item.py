from typing import Any

from robot_types.helper import ValueResolver
from robot_types.helper import serialize
from robot_types.helper.data_projection import label_based
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.db import in_transaction
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.logging import to_log


class FirstItemExecutor(AutoJobControllerBase):
    job_type = JobType.FIRST_ITEM

    def process(self):
        try:
            array_widget_ref, _ = self.args._widget_ref_and_key(self.args.key_map["array"])
            item_widget_ref, item_key = self.args._widget_ref_and_key(self.args.key_map["item"])
        except Exception as e:
            return JobStatus.FAILED, str(e)
        if self.args.form_composer is None:
            return JobStatus.FAILED, "未找到表单配置"
        form_composer = self.args.form_composer
        array_value = array_widget_ref.to_value(form_composer)
        item_value = item_widget_ref.to_value(form_composer)
        if array_value.type_spec.items.type != item_value.type_spec.type:
            return (
                JobStatus.FAILED,
                "组件类型不匹配，期望类型: {}, 得到类型: {}".format(
                    to_log(array_value.type_spec.items), to_log(item_value.type_spec)
                ),
            )
        array_symbol = array_value.var.resolve_symbol(
            symbol_table=form_composer.symbol_table_wrapper,
            scope=form_composer.symbol_table_wrapper.current_scope,
        )
        item_symbol = item_value.var.resolve_symbol(
            symbol_table=form_composer.symbol_table_wrapper,
            scope=form_composer.symbol_table_wrapper.current_scope,
        )

        value_resolver = ValueResolver(self.order.data.copy())
        array_data_result = array_value.with_resolver(value_resolver).resolve()
        if array_data_result.is_err():
            return JobStatus.FAILED, str(array_data_result.unwrap_err())
        array_data: list | Any = array_data_result.unwrap()
        match array_data:
            case None | []:
                return JobStatus.SUCCEED, None
            case [item, *_]:
                item_data = item
            case _:
                return JobStatus.FAILED, f"不是数组 {array_data}"

        if (
            item_value.type_spec.type == "collection"
            and isinstance(item_data, dict)
            and array_symbol is not None
            and len(array_symbol.children) > 0
            and len(array_symbol.children[0].children) > 0
            and len(item_symbol.children) > 0
        ):
            # 新版输出依赖数据绑定，旧版输出无法兼容复杂类型，这里使用智能建单的 label 匹配方式输出
            item_data = label_based.apply_projection(item_data, array_symbol.children[0].children, item_symbol.children)
        with in_transaction():
            self.order.data[item_key] = serialize(item_data)
            flag_modified(self.order, "data")
        return JobStatus.SUCCEED, None
