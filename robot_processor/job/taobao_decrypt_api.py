import json

from pydantic import BaseModel
from result import Ok, Err

from robot_processor.client import app_config
from robot_processor.client import hyws_taobao_client, \
    trade_client
from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.types.client.top.top_response import Receiver


class TaobaoDecryptOutput(BaseModel):
    receiver_name: str
    receiver_phone: str
    receiver_address: str


class TaobaoDecryptAPIExecutor(AutoJobControllerBase):
    job_type = JobType.TAOBAO_DECRYPT_API
    output_model = TaobaoDecryptOutput

    def process(self):
        def build_address(receiver: Receiver):
            address = " ".join(
                [
                    receiver.get("state") or "",
                    receiver.get("city") or "",
                    receiver.get("district") or "",
                    receiver.get("town") or "",
                    receiver.get("address_detail") or ""
                ]
            )
            return address
        trades = self.args.get_trades()
        if not trades:
            return JobStatus.FAILED, "订单号为空，无法解密订单信息"
        tid = trades[0].tid
        tokens: dict = json.loads(app_config.HYWS_TOKENS)
        if not tokens.get(self.shop.sid):
            return JobStatus.FAILED, "店铺没有授权"
        token = tokens[self.shop.sid]
        trade = trade_client.get_trade_by_tid(self.shop.sid, tid)

        res = hyws_taobao_client.oaid_decrypt(token, trade.oaid, tid)

        match res:
            case Ok(resp):
                if not resp["receiver_list"]:
                    return JobStatus.FAILED, "解密失败"
                receiver = resp["receiver_list"][0]
                address = build_address(receiver)
                output = self.output_model(
                    receiver_name=receiver["name"],
                    receiver_phone=receiver["mobile"],
                    receiver_address=address
                )
                self.states.write_job_widget_collection(output.dict())
                return JobStatus.SUCCEED, None
            case Err(exception):
                return JobStatus.FAILED, str(exception)
