from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.client.errors import DoudianCloudServiceError
from loguru import logger
from robot_processor.client import doudian_cloud
from robot_processor.client.schema import DoudianOrderDetailSchema, \
    DoudianOrderAddrSchema
from robot_processor.error.job_process import (
    ShopNotFound,
    PlatformNotSupported
)
from pydantic import BaseModel
from decimal import Decimal
import arrow
import typing as t


class DoudianSubOrder(BaseModel):
    product_name: t.Optional[str] = None  # 商品名称, 从子订单里获取
    product_id: t.Optional[int] = None  # 商品ID, 从子订单里获取
    out_product_id: t.Optional[str] = None  # 外部商品ID, 从子订单里获取
    sku_id: t.Optional[int] = None  # sku id, 从子订单里获取
    out_sku_id: t.Optional[str] = None  # 外部sku id, 商家不可见，从子订单里获取
    # 外部sku编码，商家可见，从子订单里获取（注意，抖店有两个外部sku编码，各自含义参考https://op.jinritemai.com/docs/question-docs/37/194）
    code: t.Optional[str] = None
    goods_price: t.Optional[float] = None  # 商品价格, 从子订单里获取
    spec_str: t.Optional[str] = None  # 规格描述
    num: t.Optional[int] = None  # 商品数量, 从子订单里获取


class DoudianLogistics(BaseModel):
    company_name: t.Optional[str] = None  # 快递公司名称, 从主订单物流信息里获取
    tracking_no: t.Optional[str] = None  # 物流单号, 从主订单物流信息里获取


class DoudianTradeInfoOutput(BaseModel):
    logistics_info: t.List[DoudianLogistics] = []  # 抖店物流信息
    pay_time: t.Optional[str] = None  # 支付时间, 从主订单里获取
    create_time: t.Optional[str] = None  # 下单时间, 从主订单里获取
    sub_orders: t.List[DoudianSubOrder] = []  # 子订单数据
    pay_amount: t.Optional[float] = None  # 支付金额, 从主订单里获取
    order_status_str: t.Optional[str] = None  # 订单状态文字说明, 从主订单内获取
    seller_words: str | None = None
    seller_remark_stars_str: t.Optional[str] = None
    desensitization_address: str | None = None  # 脱敏收件人信息

    # 物流公司、物流单号、商品名称、商品ID、商品规格在抖店平台是列表结构
    # 但是老版抖店订单回执把它们当做单条数据处理
    # 所以单独开这几个字段，兼容老的工单模板
    first_company_name: t.Optional[str] = None
    first_tracking_no: t.Optional[str] = None
    first_product_name: t.Optional[str] = None
    first_product_id: t.Optional[int] = None
    first_sku_id: t.Optional[int] = None
    first_code: t.Optional[str] = None
    first_spec_str: t.Optional[str] = None


class DoudianTradeInfoExecutor(AutoJobControllerBase):
    job_type = JobType.DOUDIAN_TRADE_INFO
    output_model = DoudianTradeInfoOutput

    def process(self):
        if not (shop := self.job_wrapper.shop):
            return JobStatus.FAILED, ShopNotFound()
        if not shop.is_doudian():
            return JobStatus.FAILED, PlatformNotSupported()
        tid = self.args.get_trades()[0].tid
        try:
            resp = doudian_cloud.get_order_detail(store_id=shop.sid, order_id=tid)
        except DoudianCloudServiceError as ex:
            return JobStatus.FAILED, str(ex)
        logger.info("抖店订单回执返回: {}", resp)
        output = self._construct_trade_info(resp)
        self.states.write_job_widget_collection(output.dict())
        return JobStatus.SUCCEED, None

    def _construct_trade_info(self, trade: DoudianOrderDetailSchema):
        logistics = [DoudianLogistics(
            company_name=x.company_name,
            tracking_no=x.tracking_no
        ) for x in trade.logistics_info]
        sub_orders = [DoudianSubOrder(
            product_name=x.product_name,
            product_id=x.product_id,
            out_product_id=x.out_product_id,
            sku_id=x.sku_id,
            out_sku_id=x.out_sku_id,
            goods_price=(Decimal(x.goods_price) / 100).quantize(Decimal(".01")),  # 分转成元
            code=x.code,
            spec_str=x.spec_str,
            num=x.item_num
        ) for x in trade.sku_order_list]
        if logistics:
            first_company_name = logistics[0].company_name
            first_tracking_no = logistics[0].tracking_no
        else:
            first_company_name = None
            first_tracking_no = None
        if sub_orders:
            first_product_name = sub_orders[0].product_name
            first_product_id = sub_orders[0].product_id
            first_sku_id = sub_orders[0].sku_id
            first_code = sub_orders[0].code
            first_spec_str = sub_orders[0].spec_str
        else:
            first_product_name = None
            first_product_id = None
            first_sku_id = None
            first_code = None
            first_spec_str = None
        return DoudianTradeInfoOutput(
            logistics_info=logistics,
            pay_time=self._convert_dt(trade.pay_time),
            create_time=self._convert_dt(trade.create_time),
            sub_orders=sub_orders,
            pay_amount=(Decimal(trade.pay_amount) / 100).quantize(Decimal(".01")),  # 分转成元
            order_status_str=self._format_order_status(trade),
            seller_remark_stars_str=self._format_seller_remark_stars(trade),
            first_company_name=first_company_name,
            first_tracking_no=first_tracking_no,
            first_product_name=first_product_name,
            first_product_id=first_product_id,
            first_sku_id=first_sku_id,
            first_code=first_code,
            first_spec_str=first_spec_str,
            seller_words=trade.seller_words,
            desensitization_address=build_desensitization_address(trade.post_addr)
        )

    def _format_order_status(self, trade: DoudianOrderDetailSchema) -> str:
        mapper = {
            1: "待确认/待支付",
            105: "已支付",
            2: "备货中",
            101: "部分发货",
            3: "已发货",
            4: "已取消",
            5: "已完成"
        }
        if trade.order_status not in mapper:
            raise ValueError(f"未知抖店订单状态: {trade.order_status}")
        return mapper[trade.order_status]

    def _format_seller_remark_stars(self, trade: DoudianOrderDetailSchema) -> str:
        mapper = {
            0: "灰",
            1: "紫",
            2: "青",
            3: "绿",
            4: "橙",
            5: "红"
        }
        if trade.seller_remark_stars not in mapper:
            raise ValueError(f"未知抖店旗帜颜色: {trade.seller_remark_stars}")
        return mapper[trade.seller_remark_stars]

    def _convert_dt(self, ts: t.Optional[int]) -> t.Optional[str]:
        if ts is None:
            return None
        return arrow.get(ts, tzinfo="Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S")


def build_desensitization_address(post_address: DoudianOrderAddrSchema):
    if not post_address:
        return ""
    desensitization_address = " ".join(
        [
            post_address.province.name or "",
            post_address.city.name or "",
            post_address.town.name or "",
            post_address.street.name or ""
        ]
    )
    return desensitization_address
