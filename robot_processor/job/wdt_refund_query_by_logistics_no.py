from typing import List

from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.wdt import WdtClient, RefundQueryResp, Refund


class RefundOutput(BaseModel):
    orders: List[Refund]
    count: int


class WdtRefundQueryByLogisticsNoExecutor(AutoJobControllerBase):
    job_type = JobType.WDT_REFUND_QUERY_BY_LOGISTICS_NO
    output_model = RefundOutput
    """
    旺店通企业版根据物流单号获取退换单
    """

    def process(self):
        logistics_no = self.args.get_arg_by_task_arg_name("logistics_no")
        if not logistics_no:
            return JobStatus.FAILED, "没有物流单号"
        resp: RefundQueryResp = WdtClient(
            self.shop.sid).refund_query_by_logistics_no(logistics_no)
        if len(resp.response.refunds) == 0:
            return JobStatus.FAILED, "没有获取到退换单"
        self.states.write_job_widget_collection(RefundOutput(
            orders=resp.response.refunds,
            count=len(resp.response.refunds)
        ).dict())
        return JobStatus.SUCCEED, ""
