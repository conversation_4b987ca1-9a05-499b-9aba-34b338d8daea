import json

from pydantic import BaseModel
from pydantic import Field

from robot_processor.client import sf_client
from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.client.logistics_clients.schema import SFQueryLogisticResponse
from robot_processor.client.logistics_clients.schema import SFRouteData
from robot_processor.client.logistics_clients.utils import get_filtered_traces
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.shop.models import Shop


class SFQueryLogisticOutput(BaseModel):
    routes: list[SFRouteData.Route] = Field(default_factory=list)


class SFQueryLogisticExecutor(AutoJobControllerBase):
    """
    顺丰查询物流轨迹。
    """

    job_type = JobType.SF_QUERY_LOGISTIC
    output_model = SFQueryLogisticOutput

    def process(self) -> tuple[JobStatus, str | None]:
        # 检测 job 的 shop。
        shop: Shop | None = self.job.shop
        if shop is None:
            return JobStatus.FAILED, f"{self.job.id} 缺少 shop 信息"

        mail_no = self.args.get_arg_by_task_arg_name("mail_no")
        if mail_no is None:
            return JobStatus.FAILED, "缺少运单号"

        phone_number = self.args.get_arg_by_task_arg_name("phone_number")
        monthly_cards: list[str] = [
            credential_info["monthly_card"] for credential_info in shop.get_org_logistic_grant_records(LogisticsType.SF)
        ]
        match monthly_cards, phone_number:
            case [_, *_], _:  # 如果是我们已经绑定了的月结账号，则不需要穿收寄人的手机号后四位来验证。
                phone_number = None
            case _, str():  # 如果不是我们已经绑定了的月结账号，则必须要传收寄人的手机号后四位来获取快递单号的物流轨迹。
                pass
            case _:
                return JobStatus.FAILED, "商家未绑定月结账号，且未输入收寄人中的任意一位的手机号后 4 位"

        # 获取物流轨迹的方式，是全部获取还是仅获取最后一条。
        get_trace_records_method = self.args.get_arg_by_task_arg_name("get_trace_records_method")

        # 使用 phone number 时，认为是退货退款的物流轨迹查询，此时快递单号和月结卡号无关，不应该做校验
        if not phone_number:
            success, sfwaybill_resp = sf_client.query_sfwaybill(mail_no, phone_number)
            if not success:
                return JobStatus.FAILED, sfwaybill_resp
            sfwaybill = json.loads(sfwaybill_resp)
            # 8148用来区分 没有运单信息! 和 月结卡号没有配置正确
            if not sfwaybill["success"] and sfwaybill["errorCode"] != "8148":
                return JobStatus.FAILED, sfwaybill["errorMsg"]
        # 请求顺丰接口，查询物流轨迹。
        success, response = sf_client.query_logistic(mail_no, phone_number)
        if not success:
            return JobStatus.FAILED, response

        sf_query_logistic_response = SFQueryLogisticResponse(**json.loads(response))
        if not sf_query_logistic_response.success:
            return JobStatus.FAILED, sf_query_logistic_response.error_msg

        logistic_trace_records: list[SFRouteData.Route] = []
        for route_data in sf_query_logistic_response.msg_data.route_resps:
            if route_data.mail_no == mail_no:
                logistic_trace_records = route_data.routes

        # 如果没有获取到物流轨迹则直接返回成功
        if len(logistic_trace_records) == 0:
            self.states.write_job_widget_collection({})
            return JobStatus.SUCCEED, ""

        filtered_traces, err = get_filtered_traces(
            logistic_trace_records, logistic_type=LogisticsType.SF, get_trace_records_method=get_trace_records_method
        )
        if err:
            return JobStatus.FAILED, err
        logistic_trace_records = filtered_traces

        output_content = SFQueryLogisticOutput(routes=logistic_trace_records)
        self.states.write_job_widget_collection(output_content.dict())
        return JobStatus.SUCCEED, None
