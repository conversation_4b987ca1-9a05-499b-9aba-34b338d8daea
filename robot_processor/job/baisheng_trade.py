#  Copyright 2023 Leyantech Ltd. All Rights Reserved.
from typing import List

from pydantic import BaseModel
from pydantic import root_validator
from pydantic import validator

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.utils import make_fields_optional
from rpa.erp.baisheng import BaiShengClient


@make_fields_optional
class BaishengOrderDetail(BaseModel):
    goods_sn: str
    sku: str
    goods_id: int
    sku_id: int
    goods_number: int
    goods_price: float
    shop_price: float
    share_price: float
    share_payment: float
    original_order_sn: str
    original_deal_code: str
    sub_deal_code: str
    num_iid: str
    pic_path: str
    goods_name: str
    is_gift: int
    goods_sname: str
    barcode: str
    cbj: float
    brand_name: str

    @validator("cbj", pre=True)
    def set_null(cls, v):
        if v == "":
            return None
        return v


@make_fields_optional
class BaishengOrder(BaseModel):
    order_sn: str
    deal_code: str
    sd_id: int
    order_status: int
    shipping_code: str
    shipping_name: str
    shipping_sn: str
    is_copy: int
    is_split: int
    is_split_new: int
    is_combine: int
    is_combine_new: int
    order_msg: str
    seller_msg: str
    seller_flag: int
    fhck: str
    fhckmc: str
    payment: float
    shipping_status: int
    orderDetailGets: List[BaishengOrderDetail]

    readable_shipping_status: str
    readable_order_status: str
    output_is_combine: bool

    @root_validator(pre=True)
    def generate_all_readable_str(cls, values):
        shipping_status = values.get("shipping_status")
        shipping_status_map = {
            "0": "初始",
            "1": "预分配缺货处理中",
            "2": "已完成预分配",
            "3": "已通知配货",
            "4": "拣货中(已分配拣货任务)",
            "5": "已完成拣货",
            "6": "已发货",
            "7": "已出库",
            "9": "取消",
        }
        values["readable_shipping_status"] = shipping_status_map.get(str(shipping_status))

        order_status = values.get("order_status")
        order_status_map = {"0": "未确认", "1": "已确认", "3": "已作废", "5": "已完成"}
        values["readable_order_status"] = order_status_map.get(str(order_status))
        return values


class Output(BaseModel):
    orders: List[BaishengOrder]


class BaishengTradeExecutor(AutoJobControllerBase):
    job_type = JobType.BAISHENG_TRADE

    output_model = Output

    def process(self):
        if trades := self.args.get_trades():
            tid = trades[0].tid
        else:
            tid = None

        order_sn = self.args.get_arg_by_task_arg_name("order_sn")
        shipping_sn = self.args.get_arg_by_task_arg_name("shipping_sn")
        if not tid and not order_sn and not shipping_sn:
            return JobStatus.FAILED, "订单号和系统单号和快递单号至少需要一项"
        if order_sn:
            resp = BaiShengClient(sid=self.job_wrapper.shop.sid).get_order_list_by_order_sn(order_sn)
        elif shipping_sn:
            resp = BaiShengClient(sid=self.job_wrapper.shop.sid).get_order_list_by_shipping_sn(shipping_sn)
        else:
            resp = BaiShengClient(sid=self.job_wrapper.shop.sid).get_order_list(tid)

        if not resp.ok:
            return JobStatus.FAILED, f"网络问题 {resp.status_code}"

        resp_obj = resp.json()
        if resp_obj.get("status") == "api-success" and resp_obj.get("message") == "success":
            order_list = resp_obj.get("data").get("orderListGets")
            baisheng_orders = []
            if resp_obj.get("data").get("orderListGets"):
                for order in order_list:
                    baisheng_order = BaishengOrder.parse_obj(order)
                    baisheng_order.output_is_combine = True if baisheng_order.is_combine else False
                    baisheng_orders.append(baisheng_order)
            self.states.write_job_widget_collection(Output(orders=baisheng_orders).dict())
            return JobStatus.SUCCEED, None
        else:
            return JobStatus.FAILED, resp_obj.get("message")
