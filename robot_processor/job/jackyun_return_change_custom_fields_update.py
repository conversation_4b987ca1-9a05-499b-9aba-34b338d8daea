import json

from pydantic import BaseModel

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.jackyun.schemas import (ReturnChangeUpdateCustomFieldsDetail,
                                     ReturnChangeUpdateCustomFieldsReq)
from rpa.erp.jackyun.sdk import JackyunSDK


class OutputModel(BaseModel):
    success: bool


class JackyunReturnChangeCustomFieldsUpdateExecutor(AutoJobControllerBase):
    job_type = JobType.JACKYUN_RETURN_CHANGE_CUSTOM_FIELDS_UPDATE
    output_model = OutputModel

    def get_column_n(self, n: int):
        column_n = self.args.get_arg_by_task_arg_name(f"column_{n}")
        if not column_n:
            return None
        elif isinstance(column_n, list):
            return column_n[0]
        return column_n

    def process(self):
        column1 = self.get_column_n(1)
        column2 = self.get_column_n(2)
        column3 = self.get_column_n(3)
        column4 = self.get_column_n(4)
        column5 = self.get_column_n(5)
        column6 = self.get_column_n(6)
        column7 = self.get_column_n(7)
        column8 = self.get_column_n(8)
        column9 = self.get_column_n(9)
        column10 = self.get_column_n(10)
        return_change_no = self.args.get_arg_by_task_arg_name("return_change_no")

        detail = ReturnChangeUpdateCustomFieldsDetail(
            exchangeNo=return_change_no,
            column1=column1,
            column2=column2,
            column3=column3,
            column4=column4,
            column5=column5,
            column6=column6,
            column7=column7,
            column8=column8,
            column9=column9,
            column10=column10)
        req = ReturnChangeUpdateCustomFieldsReq(
           items=[detail]
        )
        update_resp = JackyunSDK(self.shop.sid).returnchange_update_customfields(req)
        if update_resp.code == 200:
            data = json.loads(update_resp.result.data)
            if data.get("failedResultMap"):
                self.states.write_job_widget_collection({"success": False})
                return JobStatus.FAILED, data.get("failedResultMap").popitem()[1]
            else:
                self.states.write_job_widget_collection({"success": True})
                return JobStatus.SUCCEED, None
        self.states.write_job_widget_collection({"success": False})
        return JobStatus.FAILED, update_resp.msg
