from typing import List
from typing import <PERSON><PERSON>
from typing import Union

from pydantic import BaseModel

from robot_processor.client import trade_client
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.enums import ShopPlatform
from robot_processor.job import AutoJobControllerBase
from robot_processor.plugin.erp_utils import jst_query_combine_items
from rpa.erp.jst import JstNewSDK


class Output(BaseModel):
    class Item(BaseModel):
        outer_sku_id: str
        supplier_name: str
        outer_spu_id: str
        sku_name: str

    items: List[Item]


class JstSupplierExecutor(AutoJobControllerBase):
    job_type = JobType.JST_SUPPLIER
    output_model = Output

    def process(self) -> Tuple[JobStatus, Union[str, Exception, None]]:
        tid = None
        oid = None
        if trades := self.args.get_trades():
            tid = trades[0].tid
            oid = trades[0].oid

        outer_sku_ids = []
        match self.shop.platform:
            case ShopPlatform.TMALL | ShopPlatform.TAOBAO:
                trade = trade_client.get_trade_by_tid(self.shop.sid, tid)  # type: ignore[arg-type]
                if not trade.trade_id:
                    return JobStatus.FAILED, "找不到订单"
                if oid:
                    orders = [order for order in trade.orders if order.oid == oid]
                else:
                    orders = trade.orders
                if len(orders) == 0:
                    return JobStatus.FAILED, "找不到订单"
                outer_sku_ids.extend([order.outer_sku_id for order in orders])
            case ShopPlatform.PDD:
                orders_resp = trade_client.get_trade_by_tid_and_channel([tid])  # type: ignore[list-item]
                orders = [trade_info.pdd_trade_info for trade_info in orders_resp.trade_info_list]
                if len(orders) == 0:
                    return JobStatus.FAILED, "找不到订单"
                # 拼多多没有oid
                outer_sku_ids.extend([item.outer_id for order in orders for item in order.item_list])
            case ShopPlatform.DOUDIAN:
                orders_resp = trade_client.get_trade_by_tid_and_channel(
                    [tid], ShopPlatform.DOUDIAN.value  # type: ignore[list-item]
                )
                orders = [trade_info.dy_trade_info for trade_info in orders_resp.trade_info_list]
                if len(orders) == 0:
                    return JobStatus.FAILED, "找不到订单"
                if oid:
                    outer_sku_ids.extend(
                        [sku.out_sku_id for order in orders for sku in order.sku_order_list if sku.sku_order_id == oid]
                    )
                else:
                    outer_sku_ids.extend([sku.out_sku_id for order in orders for sku in order.sku_order_list])
            case ShopPlatform.ALIBABA:
                orders_resp = trade_client.get_trade_by_tid_and_channel(
                    [tid], ShopPlatform.ALIBABA.value, org_id=self.shop.org_id  # type: ignore[list-item]
                )
                current_orders = [trade_info.alibaba_trade_info for trade_info in orders_resp.trade_info_list]
                main_order = current_orders[0]
                if oid:
                    outer_sku_ids.extend(
                        [item.cargo_number for item in main_order.product_items if item.sub_item_id == oid]
                    )
                else:
                    outer_sku_ids.extend([item.cargo_number for item in main_order.product_items])
            case ShopPlatform.JD:
                orders_resp = trade_client.get_trade_by_tid_and_channel(
                    [tid], ShopPlatform.JD.value, sid=self.shop.sid  # type: ignore[list-item]
                )
                orders = [trade_info.jd_trade_info for trade_info in orders_resp.trade_info_list]
                if len(orders) == 0:
                    return JobStatus.FAILED, "找不到订单"
                # 京东没有oid
                outer_sku_ids.extend([item.outer_sku_id for order in orders for item in order.jd_items])
            case _:
                return JobStatus.FAILED, "不支持的平台"
        sdk = JstNewSDK(self.shop.sid)
        combine_item_id_to_item_map, sub_item_ids_set = jst_query_combine_items(sdk, set(outer_sku_ids))
        single_sku_ids = set(outer_sku_ids) | sub_item_ids_set
        single_skus_resp = sdk.query_skus_by_sku_ids(list(single_sku_ids))
        if len(single_skus_resp.data.datas) != len(single_sku_ids):
            return JobStatus.FAILED, "商品信息获取不全"

        items = [
            Output.Item(
                outer_sku_id=sku.sku_id, supplier_name=sku.supplier_name, outer_spu_id=sku.i_id, sku_name=sku.name
            )
            for sku in single_skus_resp.data.datas
        ]
        self.states.write_job_widget_collection(Output(items=items).dict())
        return JobStatus.SUCCEED, None
