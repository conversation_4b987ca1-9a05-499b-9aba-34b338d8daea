from typing import List

from pydantic import BaseModel

from robot_processor.enums import Job<PERSON>tatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.jackyun.schemas import AftersaleUploadReq
from rpa.erp.jackyun.schemas import ReturnChange
from rpa.erp.jackyun.schemas import ReturnChangeDetail
from rpa.erp.jackyun.sdk import JackyunQmSDK
from rpa.erp.jackyun.sdk import JackyunSDK
from rpa.erp.jackyun.utils import filter_orders


class OutputModel(BaseModel):
    return_change_no: str


class JackyunAftersaleUploadExecutor(AutoJobControllerBase):
    job_type = JobType.JACKYUN_AFTERSALE_RETURN_UPLOAD
    output_model = OutputModel

    def get_warehouse_name(self):
        warehouse_name = self.args.get_arg_by_task_arg_name("warehouse_name")
        if not warehouse_name:
            return None
        elif isinstance(warehouse_name, list):
            return warehouse_name[0]
        return warehouse_name

    def process(self):
        remark = self.args.get_arg_by_task_arg_name("remark")
        reason = self.args.get_arg_by_task_arg_name("reason")
        warehouse_name = self.get_warehouse_name()
        tid = self.args.get_trades()[0].tid
        jackyun_orders = JackyunQmSDK(self.shop.sid).order_list(tid).response.jackyunData.trades
        jackyun_orders = filter_orders(jackyun_orders)
        if not jackyun_orders:
            return JobStatus.FAILED, "找不到订单"
        jackyun_order = jackyun_orders[0]

        # 通过订单返回的 shopCode 来查询渠道名称
        jackyun_channels = JackyunSDK(self.shop.sid).query_channel_by_code(jackyun_order.shopCode)
        if (
            not jackyun_channels.result
            or not jackyun_channels.result.data
            or not jackyun_channels.result.data.salesChannelInfo
        ):
            return JobStatus.FAILED, "查不到渠道号"
        shop_name = jackyun_channels.result.data.salesChannelInfo[0].channelName

        reissue_skus = self.args.get_reissue_skus()
        skus: List[ReturnChangeDetail] = []
        if reissue_skus.after_sales_type == "original":
            # 原单补发
            for detail in jackyun_order.goodsDetail:
                skus.append(
                    ReturnChangeDetail(
                        goodsNo=detail.goodsNo,
                        price=float(detail.sellPrice) if self.shop.org_id not in ["2591"] else 0,
                        reasonDesc=reason if reason else "",
                        returnCount=float(detail.sellCount),
                        specName=detail.specName,
                        isFit=detail.isFit,
                        isGift=detail.isGift,
                    )
                )
        else:
            for sku in reissue_skus.sku_list:
                if sku.outer_spu_id is None:
                    return JobStatus.FAILED, "货品编码不存在"
                goods_resp = JackyunSDK(self.shop.sid).goods_list_by_goods_no(sku.outer_spu_id)
                goods = goods_resp.result.data.goods.pop(0)
                if goods.goodsNo != sku.outer_spu_id:
                    return JobStatus.FAILED, f"货品编码 {sku.outer_spu_id} 不存在"
                skus.append(
                    ReturnChangeDetail(
                        goodsNo=sku.outer_spu_id,
                        price=sku.price if self.shop.org_id not in ["2591"] else 0,
                        reasonDesc=reason if reason else "",
                        returnCount=sku.qty,
                        specName=sku.outer_sku_id,
                        isFit=str(goods.isPackageGood),
                    )
                )
        request = AftersaleUploadReq(
            returnChange=ReturnChange(
                returnChangeDetails=skus,
                onlineTradeNo=tid,
                resendType="2",
                shopName=shop_name,
                refundTypeCode="1",
                settlementType="1",
                reasonDesc="退货",
                tradeNo=jackyun_order.tradeNo,
            )
        )
        if warehouse_name:
            request.returnChange.warehouseName = warehouse_name
        if reason:
            request.returnChange.reasonDesc = reason
        if remark:
            request.returnChange.sellerMemo = remark
        resp = JackyunSDK(self.shop.sid).aftersale_upload(request)
        if resp.code == 200:
            output = OutputModel(return_change_no=resp.result.data.returnChange.returnChangeNo)
            self.states.write_job_widget_collection(output.dict())
            return JobStatus.SUCCEED, ""
        return JobStatus.FAILED, resp.msg
