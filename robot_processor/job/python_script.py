import requests
from loguru import logger
from more_itertools import first
from robot_types.core import Symbol
from robot_types.helper import deserialize
from robot_types.helper import serialize
from robot_types.helper.data_projection import FieldMask
from robot_types.helper.data_projection import label_based

from robot_processor.enums import JobType
from robot_processor.job import AutoJobControllerBase
from robot_processor.logging import to_log


class PythonScriptExecutor(AutoJobControllerBase):
    job_type = JobType.PYTHON_SCRIPT

    def process(self):
        from sandbox.code import generate_fn_signature

        code = self.prepare_code()
        fn_signature = generate_fn_signature(self.param_symbols, self.return_symbols)
        params = self.prepare_params()
        result = self.invoke(code, serialize(fn_signature), params)
        log_message = f"params: {to_log(params)}, code: {to_log(code)}, response: {to_log(result)}"

        if result["success"] is False:
            logger.error(log_message)
            return self.states.write_job_result_on_fail(error=result["error"])
        else:
            logger.info(log_message)
            output = self.prepare_output(result["output"])
            return self.states.write_job_result_on_success(data=output)

    def invoke(self, code, fn_signature, params):
        return requests.post(
            "http://pyscript-runner.robot-processor.lain:8000/invoke",
            json=dict(code=code, fn_signature=fn_signature, params=params),
        ).json()

    @property
    def param_symbols(self) -> list[Symbol]:
        return deserialize(self.args.key_map["param_symbols"], list[Symbol])

    @property
    def return_symbols(self) -> list[Symbol]:
        current_step = first(filter(lambda step: step.current, self.args.form_composer.steps))  # type: ignore
        return current_step.symbols

    def prepare_params(self):
        field_mask = FieldMask({symbol.name: True for symbol in self.param_symbols})
        bo_data = field_mask.apply(self.order.data.copy())
        params = label_based.convert_to_label_map(bo_data, self.param_symbols)
        return params

    def prepare_code(self):
        return self.args.key_map["code"]

    def prepare_output(self, result: dict):
        output = label_based.convert_to_name_map(result, self.return_symbols)
        return output
