from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from leyan_proto.digismart.trade.dgt_trade_pb2 import GetTradesByTidListAndChannelResp
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import LogisticsInfo
from google.protobuf.json_format import MessageToDict
from loguru import logger
from robot_processor.client import trade_client, TradeClient
from robot_processor.error.job_process import (
    ShopNotFound,
    PlatformNotSupported
)
from pydantic import BaseModel
import typing as t


class PddItemOutput(BaseModel):
    goods_name: t.Optional[str]  # 商品名称
    outer_goods_id: t.Optional[str]  # 外部商品编码（商品级别）
    outer_id: t.Optional[str]  # 外部商品编码（sku级别）
    goods_spec: t.Optional[str]  # sku商品规格
    goods_price: t.Optional[float]  # 商品价格
    goods_count: t.Optional[int]  # 商品数量
    goods_id: t.Optional[int]  # 商品编码（商品级别）
    sku_id: t.Optional[int]  # 商品编码（sku级别）


class PddTradeInfoOutput(BaseModel):
    logistics_name: t.Optional[str]  # 快递公司名称
    tracking_number: t.Optional[str]  # 物流单号
    pay_time: t.Optional[str]  # 支付时间
    items: t.List[PddItemOutput]  # 订单商品相关
    discount_amount: t.Optional[float]  # 折扣金额
    pay_amount: t.Optional[float]  # 支付金额
    order_status_str: t.Optional[str]  # 订单状态文字说明
    buyer_memo: t.Optional[str]  # 备注
    remark: str | None  # 商家订单备注
    desensitization_address: str  # 脱敏收件人信息


class PddTradeInfoExecutor(AutoJobControllerBase):
    job_type = JobType.PDD_TRADE_INFO
    output_model = PddTradeInfoOutput

    def process(self):
        if not (shop := self.job_wrapper.shop):
            return JobStatus.FAILED, ShopNotFound()
        if not shop.is_pdd():
            return JobStatus.FAILED, PlatformNotSupported()
        tid = self.args.get_trades()[0].tid
        resp: GetTradesByTidListAndChannelResp = trade_client.get_trade_by_tid_and_channel([tid], "PDD")
        if not resp.trade_info_list:
            return JobStatus.FAILED, "未查询到拼多多订单"
        raw = MessageToDict(resp)
        logger.info("拼多多订单回执返回: {}", raw)
        trade = resp.trade_info_list[0].pdd_trade_info
        items = [PddItemOutput(
            goods_name=x.goods_name,
            outer_goods_id=x.outer_goods_id,
            outer_id=x.outer_id,
            goods_spec=x.goods_spec,
            goods_price=x.goods_price,
            goods_count=x.goods_count,
            goods_id=x.goods_id,
            sku_id=x.sku_id
        ) for x in trade.item_list]
        output = PddTradeInfoOutput(
            logistics_name=trade.logistics_info.logistics_name,
            tracking_number=trade.logistics_info.tracking_number,
            pay_time=trade.pay_time,
            discount_amount=trade.discount_amount,
            pay_amount=trade.pay_amount,
            order_status_str=TradeClient.to_pdd_order_status_zh(trade.order_status),
            buyer_memo=trade.buyer_memo,
            items=items,
            remark=trade.remark,
            desensitization_address=build_desensitization_address(
                trade.logistics_info)
        )
        self.states.write_job_widget_collection(output.dict())
        return JobStatus.SUCCEED, None

def build_desensitization_address(logistics_info: LogisticsInfo):
    if not logistics_info:
        return ""
    desensitization_address = " ".join(
        [
            logistics_info.country or "",
            logistics_info.province or "",
            logistics_info.city or "",
            logistics_info.town or ""
        ]
    )
    return desensitization_address
