from robot_processor.client import pdd_bridge_client
from robot_processor.client.schema import PddOrderUploadExtraLogisticsPayload
from robot_processor.enums import JobType, JobStatus
from robot_processor.job import AutoJobControllerBase


class PddAddMultiPackageExecutor(AutoJobControllerBase):
    job_type = JobType.PDD_ADD_MULTI_PACKAGE
    output_model = None

    def process(self) -> tuple[JobStatus, str]:
        # 获取请求参数。
        trades = self.args.get_trades()
        if len(trades) == 0:
            return JobStatus.FAILED, "未提供订单号"
        tid = trades[0].tid
        resp = pdd_bridge_client.logistics_companies_get(store_id=self.shop.sid)
        packages = []
        if self.args.is_argument_configured("logistics_info"):
            logistics_infos = self.args.get_arg_by_task_arg_name(
                "logistics_info")
            for logistics_info in logistics_infos:
                data = list(logistics_info.values())
                logistics_company = data[1][0]['label']
                logistics_no = data[0]
                companies = [company for company in
                             resp.logistics_companies_get_response.logistics_companies
                     if company.logistics_company == logistics_company]
                if not companies:
                    return JobStatus.FAILED, "提供的快递公司在拼多多不存在"
                company = companies[0]
                packages.append(PddOrderUploadExtraLogisticsPayload.Package(
                    shipping_id=company.id, tracking_number=logistics_no))
        else:
            logistics_company = self.args.get_arg_by_task_arg_name(
                "logistics_company")
            logistics_no = self.args.get_arg_by_task_arg_name("logistics_no")
            if isinstance(logistics_company, list):
                logistics_company = logistics_company[0]
            companies = [company for company in
                         resp.logistics_companies_get_response.logistics_companies
                         if company.logistics_company == logistics_company]
            if not companies:
                return JobStatus.FAILED, "提供的快递公司在拼多多不存在"
            company = companies[0]
            packages.append(PddOrderUploadExtraLogisticsPayload.Package(
                shipping_id=company.id, tracking_number=logistics_no))
        # 1=分包发货，2=补发商品，3=发放赠品
        extra_track_type = self.args.get_arg_by_task_arg_name("extra_track_type")
        payload = PddOrderUploadExtraLogisticsPayload(
            order_sn=tid,
            packages=packages,
        )
        if extra_track_type:
            payload.extra_track_type = int(extra_track_type)
        upload_resp = pdd_bridge_client.order_upload_extra_logistics(
            store_id=self.shop.sid, payload=payload)
        if upload_resp.upload_extra_logistics_response.success:
            return JobStatus.SUCCEED, ""

        return JobStatus.FAILED, "添加多包裹信息失败"
