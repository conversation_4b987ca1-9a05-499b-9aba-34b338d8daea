import time
from decimal import Decimal

import arrow
from pydantic import BaseModel
from result import Ok, Err

from robot_processor.db import db
from robot_processor.enums import JobStatus, JobType
from robot_processor.job import AutoJobControllerBase
from robot_processor.refund.models import TaobaoRefund
from robot_processor.shop.models import App, TaobaoSubuserGrantRecord


class TaobaoRefundsAgreeOutput(BaseModel):
    execution_result: str


class TaobaoRefundsAgree(AutoJobControllerBase):
    job_type = JobType.TAOBAO_REFUNDS_AGREE
    output_model = TaobaoRefundsAgreeOutput

    def process(self):
        refund_id = self.args.get_arg_by_task_arg_name("refund_id")
        ignore_repetitive_operation_error = self.args.get_arg_by_task_arg_name(
            "ignore_repetitive_operation_error")
        if refund_id is None:
            return JobStatus.FAILED, "缺失 Refund ID 信息"
        refund_record = db.session.get(TaobaoRefund, refund_id)
        if refund_record is None:
            return JobStatus.FAILED, "未找到退款信息"
        sub_nicks = self.shop.get_refund_agree_accounts()
        if not sub_nicks:
            return JobStatus.FAILED, "没有配置退款子账号"
        grant: TaobaoSubuserGrantRecord | None = None
        sub_nick: str | None = None
        for _sub_nick in sub_nicks:
            grant = self.shop.get_recent_subuser_record(_sub_nick,
                                                        App.QN_SIDEBAR)
            if (grant and arrow.get(
                    int(grant.extra_data.get(  # type: ignore[arg-type]
                        "refresh_token_valid_time"))) < arrow.now()):
                sub_nick = _sub_nick
                break
        if not sub_nick:
            for _sub_nick in sub_nicks:
                grant = self.shop.get_recent_subuser_record(_sub_nick,
                                                            App.FEISUO)
                if grant:
                    sub_nick = _sub_nick
                    break
        if not sub_nick or not grant:
            return JobStatus.FAILED, "没找到有效的子账号授权"
        main_grant = self.shop.get_recent_record(App.FEISUO)
        if not main_grant:
            return JobStatus.FAILED, "没有找到有效的主账号授权"
        refund_info = refund_record.get_refund_info()
        refund_fee = int(Decimal(refund_info.refund_fee) * 100)
        match self.shop.platform:
            case "TAOBAO":
                result = grant.taobao_client.taobao_refunds_agree(
                    grant.access_token,
                    refund_id,
                    refund_fee,
                    refund_info.refund_version)
            case "TMALL":
                # 只需要做审核操作，审核如果不通过退款也会失败的，所以不需要检查结果.
                # 审核可以用主账号的token.
                # FIXME 后续迁移至店铺配置，在飞梭后台配置退款审核的账号
                if self.shop.org_id == "2137":
                    review_token = grant.access_token
                    taobao_client = grant.app.taobao_client
                else:
                    review_token = main_grant.access_token
                    taobao_client = main_grant.app.taobao_client
                review_result = taobao_client.tmall_refunds_review(
                    review_token,
                    sub_nick,
                    refund_id,
                    refund_info.refund_version,
                    refund_info.refund_phase,
                    message="同意退款"
                )
                if review_result.is_err():
                    if ignore_repetitive_operation_error == "true" \
                            and "退款单已完结" in str(review_result.unwrap_err()):
                        self.states.write_job_widget_collection(
                            {"execution_result": "售后单已被处理"})
                        return JobStatus.SUCCEED, ""
                    return JobStatus.FAILED, str(review_result.unwrap_err())
                time.sleep(1)
                result = grant.taobao_client.tmall_refunds_agree(
                    grant.access_token,
                    refund_id,
                    refund_fee,
                    refund_info.refund_version,
                    refund_info.refund_phase)
            case _:
                return JobStatus.FAILED, "非淘宝、天猫店铺"
        match result:
            case Ok(resp):
                if resp["succ"]:
                    if resp["results"][0]["succ"]:
                        self.states.write_job_widget_collection(
                            {"execution_result": "退款单已被处理"})
                        return JobStatus.SUCCEED, ""
                    else:
                        return JobStatus.FAILED, resp["results"][0]["message"]
                else:
                    return JobStatus.FAILED, resp["message"]
            case Err(exception):
                return JobStatus.FAILED, str(exception)
