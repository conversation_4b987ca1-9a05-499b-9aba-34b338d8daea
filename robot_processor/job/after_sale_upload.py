import dataclasses
import time
import uuid
from typing import Any, List

from dramatiq import Retry
from flask import current_app
from loguru import logger
from result import Ok, Err, Result

from robot_processor.client import buyer_client
from robot_processor.constants import (
    DEFAULT_WDT_TRADE_STATUS,
    DEFAULT_WDT_PAY_STATUS,
    DEFAULT_WDT_DELIVERY_TERM,
    DEFAULT_WDT_REFUND_STATUS, DEFAULT_WDT_PROCESS_STATUS)
from robot_processor.enums import JobStatus, JobType, ErpType
from robot_processor.error.job_process import (
    ShopNotFound,
    ErpNotConfigured,
    ErpNotSupported
)
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.logging import vars as log_vars
from robot_processor.utils import combine_address_with_town, flatten, \
    get_district_or_zone, ts2date
from robot_processor.utils import get_nonce
from rpa.erp.baisheng import BaiShengClient
from rpa.erp.duohong import DuohongClient
from rpa.erp.guanyiyun import GuanyiyunClient
from rpa.erp.guanyiyunapi.schemas import AftersaleUploadReq
from rpa.erp.guanyiyunapi.sdk import GyyQmSDK, GyySDK
from rpa.erp.kuaimai import KuaimaiClient
from rpa.erp.wanliniu import WanliniuClient
from rpa.erp.wdt import PushTrade, PushOrder, get_skus_from_wdt_trades, Trade
from rpa.erp.wdt import WdtClient
from rpa.erp.wdtulti import UltiPushDiscount, UltiPushTrade, UltiPushOrder, \
    get_skus_from_wdtulti_trades, WdtUltiQM, WdtUltiOpenAPIClient
from rpa.erp.wdtulti import WdtUltiClient, WdtQmRateLimitError

ProcessResult = Result[Any, str]


class AfterSaleUploadExecutor(AutoJobControllerBase):
    job_type = JobType.AFTER_SALE_UPLOAD

    def get_remark(self):
        remark = self.args.get_arg_by_task_arg_name("remark")
        if not remark:
            return ""
        elif isinstance(remark, list):
            return ",".join(flatten(remark))
        return remark

    def get_logistics_type(self):
        # 旺店通的物流类型是数字，但是用户的输入是中文，
        # 具体映射关系参考https://open.wangdian.cn/qyb/open/apidoc/doc?path=trade_push.php的相关字段
        logistics_type = self.args.get_arg_as_str_by_task_arg_name(
            "logistics_type")
        if not logistics_type:
            return -1
        if logistics_type in current_app.config.get("wdt_logistic_mapping", {}):
            return current_app.config["wdt_logistic_mapping"][logistics_type]
        logger.warning("未找到物流类型对应的编号")
        return -1

    def get_shop_name(self):
        shop_name = self.args.get_arg_by_task_arg_name("shop_name")
        if not shop_name:
            return ""
        elif isinstance(shop_name, list):
            return shop_name[0]
        return shop_name


    def gen_wdt_data(self):
        receiver = self.args.get_arg_by_task_arg_name("address")
        warehouse_no = None
        if warehouse_name := self.args.get_arg_by_task_arg_name(
                "warehouse_name"):
            warehouse_query_resp = WdtClient(self.order.sid).warehouse_query()
            warehouses = [warehouse for warehouse in
                          warehouse_query_resp.response.warehouses
                          if warehouse.name == warehouse_name]
            if warehouses:
                warehouse_no = warehouses[0].warehouse_no
        tid = self.args.get_trades()[0].tid
        if seller_id := self.job_wrapper.shop.seller_id:
            buyer_nick = buyer_client.get_buyer_nick_by_tid(tid, seller_id)
        else:
            buyer_nick = None
        push_order_list = list()
        if self.args.get_arg_as_str_by_task_arg_name("use_org_tid") == "是":
            wdt_tid = tid
        else:
            wdt_tid = f"{tid}BF{get_nonce()[0:4]}"
        reissue_skus = self.args.get_reissue_skus()
        if reissue_skus.after_sales_type == "original":
            # 原单补发从当前订单获取商品信息
            trades = WdtClient(self.shop.sid).trade_query(
                wdt_tid=tid).response.trades
            origin_trade: Trade = sorted(trades, key=lambda x: x.trade_no)[0]
            skus = get_skus_from_wdt_trades(origin_trade, tid)
        else:
            skus = reissue_skus.sku_list

        for index, sku in enumerate(skus):
            erp_spec_no = sku.outer_sku_id or sku.sku_id or sku.outer_spu_id
            order = PushOrder(
                tid=wdt_tid,
                oid=f"{wdt_tid}{index}",
                num=sku.qty,
                price=sku.price,
                status=DEFAULT_WDT_TRADE_STATUS,
                refund_status=DEFAULT_WDT_REFUND_STATUS,
                goods_id=sku.spu_id,
                spec_id=erp_spec_no,
                spec_no=erp_spec_no,
            )
            push_order_list.append(order)

        push_trade = PushTrade(
            tid=wdt_tid,
            trade_status=DEFAULT_WDT_TRADE_STATUS,
            process_status=DEFAULT_WDT_PROCESS_STATUS,
            refund_status=DEFAULT_WDT_REFUND_STATUS,
            pay_status=DEFAULT_WDT_PAY_STATUS,
            delivery_term=DEFAULT_WDT_DELIVERY_TERM,
            buyer_nick=wdt_tid if not buyer_nick else buyer_nick,
            receiver_name=receiver["name"],
            receiver_province=receiver["state"],
            receiver_city=receiver["city"],
            receiver_district=f"{get_district_or_zone(receiver)}",
            receiver_address=combine_address_with_town(
                f"{receiver.get('town') or ''}",
                f"{receiver['address']}"),
            receiver_mobile=receiver["mobile"],
            order_list=push_order_list,
            seller_memo=self.get_remark(),
            logistics_type=self.get_logistics_type(),
            warehouse_no=warehouse_no
        )
        if warehouse_no:
            push_trade.warehouse_no = warehouse_no
        """
        企业版 trade_push 接口 推送的 paid 值计算不正确会导致报错：未付款订单不可发货

        delivery_term = 1 trade_status = 30 pay_status = 2 ，需要根据公式计算出正确的 paid 值。
        paid计算公式：paid = Σ(price * num + adjust_amount -discount – share_discount）+ post_amount + other_amount。
        """
        sigma_result: float = 0.0
        for push_order in push_order_list:
            sigma_result += (
                    push_order.price * push_order.num
                    + push_order.adjust_amount - push_order.discount - push_order.share_discount
            )
        push_trade.paid = sigma_result + push_trade.post_amount + push_trade.other_amount
        return push_trade

    @property
    def wdtulti_data(self):
        receiver = self.args.get_arg_by_task_arg_name("address")
        tid = self.args.get_trades()[0].tid
        if seller_id := self.job_wrapper.shop.seller_id:
            buyer_nick = buyer_client.get_buyer_nick_by_tid(tid, seller_id)
        else:
            buyer_nick = None
        goods_count = 0
        push_order_list = list()
        push_distinct_list = list()
        wdt_tid = f"{tid}BF{get_nonce()[0:4]}"
        reissue_skus = self.args.get_reissue_skus()
        shop_no = ""
        shop_name = self.get_shop_name()
        if shop_name:
            shop_name = self.get_shop_name()
            if shop_name:
                wdt_shops = WdtUltiOpenAPIClient(
                    self.order.sid).all_shops()  # type: ignore[arg-type]
                wdt_shops = [wdt_shop for wdt_shop in wdt_shops if
                             wdt_shop.shop_name == shop_name]
                if wdt_shops:
                    shop_no = wdt_shops[0].shop_no
        else:
            try:
                trades = WdtUltiQM(self.order.sid).get_orders(src_tid=tid).order
            except WdtQmRateLimitError as e:
                raise Retry(str(e), delay=1000)
            origin_trade = sorted(trades, key=lambda x: x.trade_no)[0]
            shop_no = origin_trade.shop_no

        if reissue_skus.after_sales_type == "original":
            skus = get_skus_from_wdtulti_trades(origin_trade, tid)
        else:
            skus = reissue_skus.sku_list
        for index, sku in enumerate(skus):
            wdt_oid = f"{wdt_tid}-{index}"
            goods_count += int(sku.qty)  # type: ignore[arg-type]
            erp_spec_no = sku.outer_sku_id or sku.sku_id or sku.outer_spu_id
            order = UltiPushOrder(
                tid=wdt_tid,
                oid=wdt_oid,
                num=sku.qty,
                price=sku.price,
                status=DEFAULT_WDT_TRADE_STATUS,
                refund_status=DEFAULT_WDT_REFUND_STATUS,
                goods_name='',
                goods_id=sku.spu_id,
                goods_no=sku.spu_id,
                spec_id=erp_spec_no,
                spec_no=erp_spec_no
            )
            push_order_list.append(order)
            distinct = UltiPushDiscount(
                tid=wdt_tid,
                oid=wdt_oid,
                amount=0.0,
                detail=""
            )
            push_distinct_list.append(distinct)
        push_trade = UltiPushTrade(
            tid=wdt_tid,
            trade_status=DEFAULT_WDT_TRADE_STATUS,
            process_status=DEFAULT_WDT_PROCESS_STATUS,
            refund_status=DEFAULT_WDT_REFUND_STATUS,
            pay_status=DEFAULT_WDT_PAY_STATUS,
            order_count=len(push_order_list),
            goods_count=goods_count,
            delivery_term=DEFAULT_WDT_DELIVERY_TERM,
            buyer_nick=wdt_tid if not buyer_nick else buyer_nick,
            receiver_name=receiver["name"],
            receiver_address=combine_address_with_town(
                f"{receiver.get('town') or ''}",
                f"{receiver['address']}"),
            receiver_area=f"{receiver['state']} {receiver['city']} "
                          f"{get_district_or_zone(receiver)}",
            receiver_mobile=receiver["mobile"],
            pay_time=ts2date(time.time()),
            trade_time=ts2date(time.time()),
            end_time=ts2date(time.time()),
            remark=self.get_remark(),
            logistics_type=self.get_logistics_type()
        )
        return push_distinct_list, push_order_list, push_trade, shop_no

    def gen_guanyiyun_data(self):
        def normalize_province(province):
            if province in ("上海市", "北京市", "天津市", "重庆市", "台北市"):
                province = province.replace("市", "")
            return province

        receiver_info_ = self.args.get_arg_by_task_arg_name("receiverInfo")
        receiver_info = dict(
            receiverName=receiver_info_['name'],
            receiverZip="",
            receiverMobile=receiver_info_.get("mobile"),
            receiverAddress=f'{receiver_info_.get("state")}{receiver_info_.get("city")}'
                            f'{receiver_info_.get("zone")}{receiver_info_.get("address")}',
            provinceId=normalize_province(receiver_info_.get("state")),
            cityId=receiver_info_.get("city").replace("市辖区", ""),
            # 前端提交的数据中会存在 zone=null; key 存在，无法走dict 默认值
            areaId=receiver_info_.get("zone") or "",
            # guanyiyun 只有三级地址，与飞梭对不上，zone/town 都会传给mola
            # mola 先使用zone 调用api，如返回'区域信息不正确'，在使用town 去尝试
            town=receiver_info_.get("town") or "",
        )

        # ref: https://git.leyantech.com/digismart/mola-service/-/blob/master/src/sites/guanyiyun/README.md#%E5%9B%9B%E5%88%9B%E5%BB%BA%E8%A1%A5%E5%8F%91%E5%8D%95 # noqa:
        business_data = dict()
        business_data["tid"] = self.args.get_trades()[0].tid
        business_data[
            "receiverInfo"] = receiver_info  # type: ignore[assignment]
        business_data["skuList"] = [
            {"itemCode": sku.outer_sku_id, "qty": sku.qty} for sku in
            # type: ignore[assignment] # noqa: E501
            self.args.get_reissue_skus().sku_list]
        is_benefit = self.ensure_plain_value(
            self.args.get_arg_by_task_arg_name("is_benefit"))
        business_data[
            "isBenefit"] = 1 if is_benefit == "是" else 0  # type: ignore[assignment]
        for arg_name in ("extendMemo", "warehouseName", "orderTypeName", "logisticsName"):
            business_data[arg_name] = self.ensure_plain_value(
                self.args.get_arg_by_task_arg_name(arg_name))
        return business_data

    def get_receiver_address(self):
        """
        "receiverAddress": {
        "receiverState": "上海",
        "receiverCity": "上海市",
        "receiverDistrict": "长宁区",
        "detailAddress": "联通大厦18f",
        "receiverMobile": "***********",
        "receiverName": "测试-leyan-收货人"
    }
        """
        receiver = self.args.get_arg_by_task_arg_name("address")
        receiver_address = {
            "receiverState": receiver['state'],
            "receiverCity": receiver['city'],
            "receiverDistrict": f"{get_district_or_zone(receiver)}",
            "detailAddress": combine_address_with_town(
                f"{receiver.get('town', '')}", f"{receiver['address']}"),
            "receiverMobile": receiver["mobile"],
            "receiverName": receiver["name"]
        }
        return receiver_address

    @staticmethod
    def extract_value(business_data, key: str):
        if key not in business_data:
            return None
        if isinstance(business_data[key], list):
            if len(business_data[key]) == 0:
                return None
            return business_data[key][0]
        return business_data[key]

    @staticmethod
    def ensure_plain_value(val):
        if isinstance(val, list):
            return val[0] if val else ''
        return val if val else ''

    def _jst_process(self) -> ProcessResult:
        from robot_processor.job.new_jst_after_sale_upload import \
            NewJstAfterSaleUploadExecutor
        exe = NewJstAfterSaleUploadExecutor(self.job)
        try:
            succeed, msg = exe.process()
        except Exception as e:
            logger.exception("jst upload error.")
            return Err(str(e))
        if succeed == JobStatus.SUCCEED:
            return Ok("")
        else:
            return Err(msg)

    def _wdt_process(self) -> ProcessResult:
        created_wdt_tid = self.states.get_job_state("wdt_tid", "")
        wdt_client = WdtClient(self.order.sid)
        if not created_wdt_tid:
            shop_name = self.get_shop_name()
            if shop_name:
                wdt_shops = wdt_client.all_shops()
                wdt_shops = [wdt_shop for wdt_shop in wdt_shops if
                             wdt_shop.shop_name == shop_name]
                if wdt_shops:
                    wdt_client.after_sale_shop_no = wdt_shops[0].shop_no
                else:
                    return Err(f"旺店通中找不到店铺 {shop_name}")
            wdt_data = self.gen_wdt_data()
            logger.info(f"processing wdt_tid {wdt_data.tid}")
            res = wdt_client.after_sale_upload(wdt_data)
            if res.is_err():
                return res
            created_wdt_tid = wdt_data.tid
            self.states.write_job_states({"wdt_tid": created_wdt_tid})
            raise Retry("", delay=60*1000)
        resp = wdt_client.trade_query(created_wdt_tid)
        if not resp.response.trades:
            return Err(f"找不到订单 {created_wdt_tid}")
        self.states.write_job_output({'wdt_tid': created_wdt_tid,
                                      "trade_no": resp.response.trades[
                                          0].trade_no})
        return Ok("")


    def _wdtulti_process(self) -> ProcessResult:
        created_wdt_tid = self.states.get_job_state("wdt_tid", "")
        if not created_wdt_tid:
            distinct_list, order_list, trade, shop_no = self.wdtulti_data
            if not shop_no:
                return Err("找不到店铺")
            wdtulti_client = WdtUltiClient(self.order.sid)  # type: ignore[arg-type]
            if self.shop and self.shop.org_id == "2257":
                if self.shop.sid in [
                    "206231395",
                    "174033374",
                    "205724914",
                    "4459119942",
                    "1696197640",
                    "235483973",
                ] or shop_no in ["2024090401", "QT20240701"]:
                    wdtulti_client.after_sale_shop_no = "QT20240701"
                else:
                    wdtulti_client.after_sale_shop_no = "QT16070901"
            res = wdtulti_client.after_sale_upload(distinct_list, order_list,
                                                   trade)
            if res.is_err():
                return res
            created_wdt_tid = trade.tid
            self.states.write_job_states({"wdt_tid": created_wdt_tid})
            raise Retry("", delay=60*1000)
        resp = WdtUltiClient(self.order.sid).trade_query(created_wdt_tid)  # type: ignore[arg-type]
        resp_json = resp.get("response")
        if resp_json and resp_json.get("status") == 0 and resp_json.get(
                "data"):
            data = resp_json["data"]
            if data["total_count"] == 0:
                return Err(f"找不到订单 {created_wdt_tid}")
            ret_trade = data["order"][0]
            self.states.write_job_output({'wdt_tid': created_wdt_tid,
                                          "trade_no": ret_trade["trade_no"]})
        return Ok("")


    def _kuaimai_process(self) -> ProcessResult:
        if trade := self.args.get_trades():
            tid = trade[0].tid
        else:
            return Err("缺失 tid 字段")

        warehouse = self.ensure_plain_value(
            self.args.get_arg_by_task_arg_name("warehouse"))
        exp_tpl_id = self.ensure_plain_value(
            self.args.get_arg_by_task_arg_name('exp_tpl_id'))
        match res := KuaimaiClient(self.order.sid).after_sale_upload(  # type: ignore[arg-type]
            warehouse=warehouse,
            express_template=exp_tpl_id,
            tid=tid,
            sku_list=dataclasses.asdict(self.args.get_reissue_skus())[
                "sku_list"]
        ):
            case Ok(after_sale_tid):
                self.states.write_job_output({'after_sale_tid': after_sale_tid})
                return res
            case _:
                return res

    def _baisheng_process(self) -> ProcessResult:
        skus = dataclasses.asdict(self.args.get_reissue_skus())["sku_list"]
        tid = self.args.get_trades()[0].tid
        business_data = {
            "tid": tid,
        }
        match res := BaiShengClient(sid=self.job_wrapper.shop.sid).after_sale_upload(business_data, skus):
            case Ok(order_sn):
                self.states.write_job_output({"order_sn": order_sn})
                return res
            case _:
                return res

    def _wanliniu_process(self) -> ProcessResult:
        skus = dataclasses.asdict(self.args.get_reissue_skus())["sku_list"]
        receiver_address = self.get_receiver_address()
        res = WanliniuClient(self.order.sid).after_sale_upload(  # type: ignore[arg-type]
            shop_name=self.args.get_arg_by_task_arg_name('store'),
            buyer_name=self.args.get_arg_by_task_arg_name('buyer'),
            address=receiver_address,
            sku_list=skus,
            tid=self.args.get_trades()[0].tid,
            remark=self.get_remark(),
            warehouse=self.args.get_arg_by_task_arg_name('warehouse'),
            logistics_corp=self.args.get_arg_by_task_arg_name('logistics_corp'),
            sales_man=self.args.get_arg_by_task_arg_name('sales_man')
        )
        match res:
            case Ok(tid):
                self.states.write_job_output({'after_sale_tid': tid})
        return res

    def _duohong_process(self) -> ProcessResult:
        reissue_skus = self.args.get_reissue_skus()
        receiver_address = self.get_receiver_address()
        tid = self.args.get_trades()[0].tid
        if self.job_wrapper.shop.platform == "DOUDIAN" and "A" not in tid:
            tid = tid + "A"
        if reissue_skus.after_sales_type == "original":
            res = DuohongClient(self.order.sid).after_sale_upload_replicate(  # type: ignore[arg-type]
                tid, receiver_address)  # noqa: E501
        else:
            sku_list = dataclasses.asdict(reissue_skus)["sku_list"]
            res = DuohongClient(self.order.sid).after_sale_upload(  # type: ignore[arg-type]
                tid, sku_list, receiver_address)  # noqa: E501
        match res:
            case Ok(tid_new):
                self.states.write_job_output({'tid_new': tid_new})
        return res

    def _guanyiyun_process(self) -> ProcessResult:
        guanyiyun_client = GuanyiyunClient(shop=self.job_wrapper.shop)
        match res := guanyiyun_client.after_sale_upload(self.gen_guanyiyun_data()):
            case Ok(erp_tid):
                self.states.write_job_output(dict(erp_tid=erp_tid))
                return res
            case _:
                return res

    def _guanyiyun_process_v2(self) -> ProcessResult:
        tid = self.args.get_trades()[0].tid
        gyy_orders = GyyQmSDK(
            self.job_wrapper.shop.sid).query_trade_by_tid(tid)
        oldest_gyy_order = sorted(gyy_orders, key=lambda x: x.code)[0]
        reissue_skus = self.args.get_reissue_skus()
        skus: List[AftersaleUploadReq.Item] = []
        if reissue_skus.after_sales_type == "original":
            # 原单补发
            for gyy_order in gyy_orders:
                if gyy_order.order_type_name == "销售订单":
                    for detail in gyy_order.details:
                        skus.append(AftersaleUploadReq.Item(
                            item_code=detail.item_code,
                            price=detail.price,
                            qty=int(float(detail.qty))
                        ))
        else:
            for sku in reissue_skus.sku_list:
                skus.append(AftersaleUploadReq.Item(
                    item_code=sku.outer_sku_id,
                    price=str(sku.price),
                    qty=sku.qty
                ))
        receiver_info = self.args.get_arg_by_task_arg_name("receiverInfo")
        req = AftersaleUploadReq(
            shop_code=oldest_gyy_order.shop_code,
            vip_code=oldest_gyy_order.vip_code,
            platform_code=oldest_gyy_order.platform_code,
            deal_datetime=ts2date(time.time()),
            order_type_code="Delivery",
            details=skus,
            receiver_name=receiver_info["name"],
            receiver_mobile=receiver_info["mobile"],
            receiver_province=receiver_info["state"],
            receiver_city=receiver_info["city"],
            receiver_district=receiver_info.get('zone') or '',
            receiver_address=receiver_info["address"],
            receiver_town=receiver_info.get("town"),
            unique_tid=uuid.uuid4().hex,
            seller_memo=self.get_remark()
        )
        resp = GyySDK(self.job_wrapper.shop.sid).aftersale_upload(req)
        if resp.success:
            self.states.write_job_output({'code': resp.code})
            return Ok("")
        else:
            return Err(resp.errorDesc)

    def process(self):
        if not self.job_wrapper.shop:
            return JobStatus.FAILED, ShopNotFound()
        if not self.job_wrapper.erp_info:
            return JobStatus.FAILED, ErpNotConfigured()

        erp_type = self.job_wrapper.erp_info.erp_type
        log_vars.ErpType.set(erp_type.name)  # type: ignore[union-attr]

        if ErpType.JST == erp_type:
            result = self._jst_process()
        elif ErpType.WDT == erp_type:
            result = self._wdt_process()
        elif ErpType.WDTULTI == erp_type:
            result = self._wdtulti_process()
        elif ErpType.KUAIMAI == erp_type:
            result = self._kuaimai_process()
        elif ErpType.BAISHENG == erp_type:
            result = self._baisheng_process()
        elif ErpType.WANLINIU == erp_type:
            result = self._wanliniu_process()
        elif ErpType.DUOHONG == erp_type:
            result = self._duohong_process()
        elif ErpType.GUANYIYUN == erp_type:
            if self.job_wrapper.erp_info.meta.get("app_secret"):
                result = self._guanyiyun_process_v2()
            else:
                result = self._guanyiyun_process()
        else:
            return JobStatus.FAILED, ErpNotSupported(erp_type)

        self.update_receiver_address()

        match result:
            case Ok():
                return JobStatus.SUCCEED, None
            case Err(error_message):
                return JobStatus.FAILED, error_message

    def update_receiver_address(self):
        """
        更新买家收货地址
        """
        tid = self.args.get_trades()[0].tid
        receiver_info = self.args.get_arg_by_task_arg_name("address")
        if not receiver_info:
            # note: 创建ERP补发单&修改收货地址
            receiver_info = self.args.get_arg_by_task_arg_name("new_address")
        if not receiver_info:
            # note: 创建ERP补发单[金蝶管易云]
            receiver_info = self.args.get_arg_by_task_arg_name("receiverInfo")
        self.job_wrapper.shop.update_receiver_info(tid, receiver_info)
