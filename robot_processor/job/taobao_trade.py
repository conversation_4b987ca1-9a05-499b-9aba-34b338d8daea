import math
from typing import List, TypedDict, Optional

from robot_processor.client import taobao_client
from robot_processor.enums import JobType, JobStatus, TradeStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.plugin.trade_api import get_taobao_trade
from robot_processor.utils import unwrap_optional


class OrderDict(TypedDict):
    # SKU的值。如：机身颜色:黑色;手机套餐:官方标配
    # $.trade_fullinfo_get_response.trade.orders.order[].sku_properties_name
    sku_description: str
    # 商家外部编码
    # $.trade_fullinfo_get_response.trade.orders.order[].outer_iid
    outer_spu_id: str
    # 外部网店自己定义的Sku编号
    # $.trade_fullinfo_get_response.trade.orders.order[].outer_sku_id
    outer_sku_id: str
    divide_order_fee: float
    sku_id: str
    spu_id: str
    oid: str
    title: str


class CombineLogisticsDetail(TypedDict):
    invoice_no: str
    logistics_company: str
    sub_order_id: str


class Participant(TypedDict):
    name: Optional[str]
    country: Optional[str]
    state: Optional[str]
    city: Optional[str]
    district: Optional[str]
    town: Optional[str]
    address: Optional[str]
    zip: Optional[str]
    mobile: Optional[str]
    phone: Optional[str]


class TradeDict(TypedDict):
    memo: str
    oaid: str
    orders: List[OrderDict]
    payment: float  # 实付金额，单位：元
    tmall_coupon_fee: float  # 天猫平台的红包信息，单位：分
    combine_logistics_details: Optional[List[CombineLogisticsDetail]]
    receiver: Optional[Participant]
    status: int


def get_trade_status_zh(status: int):
    if status == TradeStatus.WAIT_BUYER_PAY:
        return "等待买家付款"
    elif status == TradeStatus.WAIT_SELLER_SEND_GOODS:
        return "等待卖家发货"
    elif status == TradeStatus.SELLER_CONSIGNED_PART:
        return "卖家部分发货"
    elif status == TradeStatus.WAIT_BUYER_CONFIRM_GOODS:
        return "等待买家确认收货"
    elif status == TradeStatus.TRADE_BUYER_SIGNED:
        return "买家已签收（货到付款专用）"
    elif status == TradeStatus.TRADE_FINISHED:
        return "交易成功"
    elif status == TradeStatus.TRADE_CLOSED:
        return "交易关闭"
    elif status == TradeStatus.TRADE_CLOSED_BY_TAOBAO:
        return "交易被淘宝关闭"
    elif status == TradeStatus.TRADE_NO_CREATE_PAY:
        return "没有创建支付宝交易"
    elif status == TradeStatus.WAIT_PRE_AUTH_CONFIRM:
        return "余额宝0元购合约中"
    elif status == TradeStatus.PAY_PENDING:
        return "外卡支付付款确认中"
    elif status == TradeStatus.ALL_WAIT_PAY:
        return "所有买家未付款的交易"
    elif status == TradeStatus.ALL_CLOSED:
        return "所有关闭的交易"
    elif status == TradeStatus.PAID_FORBID_CONSIGN:
        return "该状态代表订单已付款但是处于禁止发货状态"
    return "未知"


class OutputDict(TypedDict, total=False):
    # 订单的备注信息
    memo: str
    spu_id: str
    sku_id: str
    # 商家外部编码
    outer_spu_id: str
    # SKU 信息
    sku_description: str
    # 外部网店自己定义的Sku编号
    outer_sku_id: str
    # 实付金额
    total_fee: float
    # 开票金额
    invoiced_amount: float
    logistics_count: int
    # 商品名称
    title: str
    desensitization_address: str  # 脱敏收件人信息
    trade_status_zh: str  # 订单状态


class TaobaoTradeExecutor(AutoJobControllerBase):
    job_type = JobType.TAOBAO_TRADE

    def process(self):
        tid = self.args.get_trades()[0].tid
        oid = self.args.get_trades()[0].oid
        trades: List[TradeDict] = get_taobao_trade(self.job_wrapper.shop.sid,
                                                   [tid])
        if not trades:
            return JobStatus.FAILED, "找不到订单信息"

        trade = trades[0]
        output = OutputDict()
        self._output_memo(trade, output)
        self._output_total_fee(trade, output)
        self._output_invoiced_amount(trade, output)
        self._output_item(trade, output, oid)
        self._output_logistics_count(tid, output)
        self._output_desensitization_address(trade, output)
        self.states.write_job_output(output)  # type: ignore[arg-type]
        return JobStatus.SUCCEED, None

    def _output_desensitization_address(
            self, trade: TradeDict, output: OutputDict):
        if self.args.is_argument_configured(
                "desensitization_address") and trade.get("receiver"):
            desensitization_address = " ".join(
                [
                    trade["receiver"].get("state") or "",  # type: ignore[union-attr]
                    trade["receiver"].get("city") or "",  # type: ignore[union-attr]
                    trade["receiver"].get("district") or "",  # type: ignore[union-attr]
                    trade["receiver"].get("town") or "",  # type: ignore[union-attr]
                    trade["receiver"].get("address") or "",  # type: ignore[union-attr]
                    trade["receiver"].get("name") or "",  # type: ignore[union-attr]
                    trade["receiver"].get("phone") or "",  # type: ignore[union-attr]
                ]
            )
            output["desensitization_address"] = desensitization_address

    def _output_logistics_count(self, tid: str, output: OutputDict):
        if self.args.is_argument_configured("logistics_count"):
            access_token = unwrap_optional(
                self.shop.get_recent_record()).access_token
            traces_response = taobao_client.taobao_logisitcs_trace_get(
                access_token, tid).unwrap()
            output["logistics_count"] = len(
                {trace["out_sid"] for trace in traces_response["result"]})

    def _output_total_fee(self, trade: TradeDict, output: OutputDict):
        if self.args.is_argument_configured("total_fee"):
            output["total_fee"] = math.fsum(
                [order["divide_order_fee"] for order in trade["orders"]])

    def _output_invoiced_amount(self, trade: TradeDict, output: OutputDict):
        if self.args.is_argument_configured("invoiced_amount"):
            output["invoiced_amount"] = round(
                trade["payment"] - (trade["tmall_coupon_fee"] / 100), 2)

    def _output_memo(self, trade: TradeDict, output: OutputDict):
        if self.args.is_argument_configured("memo"):
            output["memo"] = trade["memo"]

    def _output_item(self, trade: TradeDict, output: OutputDict,
                     oid: None | str):
        """为了替代 robot_processor.job.n8n_workflows.taobao.TaobaoOrderWorkflow"""
        orders = trade["orders"]
        # 允许使用商品信息做筛选
        if self.args.is_argument_configured("item"):
            item = self.args.get_arg_by_task_arg_name("item")
            filter_key = None
            if isinstance(item, str):
                filter_key = item
            elif isinstance(item, list) and len(item) > 0 and all(
                    isinstance(each, dict) for each in item):
                if outer_sku := item[0].get("outer_sku"):
                    filter_key = outer_sku

            if filter_key:
                orders = filter(  # type: ignore[assignment]
                    lambda each_order: any([
                        each_order["outer_sku_id"] == filter_key,
                        each_order["outer_spu_id"] == filter_key
                    ]),
                    orders
                )

        if not (orders := list(orders)):
            return
        # 在改造为输出当前表单之前，仅支持输出第一个商品的信息
        order = orders[0]
        if oid:
            order = [order for order in orders if order["oid"] == oid][0]
        if self.args.is_argument_configured("outer_spu_id"):
            output["outer_spu_id"] = order["outer_spu_id"]
        if self.args.is_argument_configured("sku_description"):
            output["sku_description"] = order["sku_description"]
        if self.args.is_argument_configured("outer_sku_id"):
            output["outer_sku_id"] = order["outer_sku_id"]
        if self.args.is_argument_configured("sku_id"):
            output["sku_id"] = order["sku_id"]
        if self.args.is_argument_configured("spu_id"):
            output["spu_id"] = order["spu_id"]
        if self.args.is_argument_configured("title"):
            output["title"] = order["title"]

    def _output_trade_status_zh(self, trade: TradeDict, output: OutputDict):
        if self.args.is_argument_configured("trade_status_zh"):
            output["trade_status_zh"] = get_trade_status_zh(trade["status"])
