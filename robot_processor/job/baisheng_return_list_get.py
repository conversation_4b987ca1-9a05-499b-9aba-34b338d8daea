from typing import List

from pydantic import BaseModel
from pydantic import root_validator

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job import AutoJobControllerBase
from robot_processor.utils import make_fields_optional
from rpa.erp.baisheng import BaiShengClient


@make_fields_optional
class ReturnDetail(BaseModel):
    line_no: int
    goods_sn: str
    goods_name: str
    sku_id: int
    sku: str
    goods_id: int
    goods_number: int
    goods_number_return_sj: int
    goods_price: float
    shop_price: float
    share_price: float
    share_payment: float
    barcode: str
    cbj: str
    brand_name: str
    taocan_link_sign: str
    payment: float
    outer_goods_id: str


@make_fields_optional
class ReturnOrder(BaseModel):
    return_order_status: int
    return_shipping_status: int
    return_pay_status: int
    return_order_sn: str
    refund_deal_code: str
    refund_code: str
    receiver_name: str
    receiver_addr: str
    receiver_mobile: str
    return_total_amount: float
    return_order_amount: float
    return_payment: float
    return_other_discount_fee: float
    return_shipping_code: str
    return_shipping_name: str
    return_shipping_sn: str
    relating_order_sn: str
    sync_status: str
    return_shipping_time_rk: str
    receiver_province_name: str
    receiver_city_name: str
    receiver_district_name: str
    fhck: str
    relating_fhck: str
    return_ck_dm: str
    return_ck_name: str
    fxs_return_order_amount: str
    add_time: str
    last_update: str
    orderDetailGets: List[ReturnDetail]
    readable_return_order_status: str
    readable_return_shipping_status: str
    readable_return_pay_status: str

    @root_validator(pre=True)
    def generate_all_readable_str(cls, values):
        return_order_status = values.get("return_order_status")
        return_order_status_map = {"0": "未确认", "1": "已确认", "3": "作废", "10": "完成"}
        values["readable_return_order_status"] = return_order_status_map.get(str(return_order_status))

        return_shipping_status = values.get("return_shipping_status")
        return_shipping_status_map = {
            "0": "未收货",
            "1": "已收货,未入库",
            "2": "已入库",
            "3": "可入库",
            "4": "已退回给客户",
        }
        values["readable_return_shipping_status"] = return_shipping_status_map.get(str(return_shipping_status))

        return_pay_status = values.get("return_pay_status")
        return_pay_status_map = {"0": "未结算", "1": "已结算", "2": "待结算"}
        values["readable_return_pay_status"] = return_pay_status_map.get(str(return_pay_status))
        return values


class Output(BaseModel):
    return_orders: List[ReturnOrder]
    count: int


class BaishengReturnListGetExecutor(AutoJobControllerBase):
    job_type = JobType.BAISHENG_RETURN_LIST_GET

    output_model = Output

    def process(self):
        refund_code = self.args.get_arg_by_task_arg_name("refund_code")
        return_order_sn = self.args.get_arg_by_task_arg_name("return_order_sn")
        trades = self.args.get_trades()
        relating_order_sn = None

        return_shipping_sn = self.args.get_arg_by_task_arg_name("return_shipping_sn")
        if not refund_code and not return_order_sn and not return_shipping_sn and not trades:
            return JobStatus.FAILED, "平台退款单号和E3退款单号和退回物流单号和平台订单号至少需要一项"
        if trades:
            relating_order_sn = trades[0].tid
        return_orders = []
        try:
            if return_shipping_sn:
                return_orders.extend(self.fetch_return_orders_by_shipping_sn(return_shipping_sn))
            else:
                return_orders.extend(self.fetch_return_orders(refund_code, return_order_sn, relating_order_sn))
        except Exception as e:
            return JobStatus.FAILED, str(e)
        return_orders = [order for order in return_orders if order.return_order_status != 3]
        self.states.write_job_widget_collection(Output(return_orders=return_orders, count=len(return_orders)).dict())
        return JobStatus.SUCCEED, None

    def fetch_return_orders_by_shipping_sn(self, return_shipping_sn):
        detail_resp = BaiShengClient(sid=self.job_wrapper.shop.sid).get_return_detail(return_shipping_sn)
        if not detail_resp.ok:
            raise Exception(f"网络问题 {detail_resp.status_code}")
        detail_resp_obj = detail_resp.json()
        if detail_resp_obj.get("status") != "api-success" or detail_resp_obj.get("message") != "success":
            raise Exception(detail_resp_obj.get("message"))

        return_orders = []
        for return_order_brief_json in detail_resp_obj.get("data"):
            e3_return_order_sn = return_order_brief_json["e3_return_order_sn"]
            if e3_return_order_sn:
                return_orders.extend(self.fetch_return_orders(None, e3_return_order_sn, None))
        return return_orders

    def fetch_return_orders(self, refund_code, return_order_sn, relating_order_sn):
        resp = BaiShengClient(sid=self.job_wrapper.shop.sid).get_return_list(
            refund_code, return_order_sn, relating_order_sn
        )
        if not resp.ok:
            raise Exception(f"网络问题 {resp.status_code}")
        resp_obj = resp.json()
        if resp_obj.get("status") != "api-success" or resp_obj.get("message") != "success":
            raise Exception(resp_obj.get("message"))

        return_orders = []
        for return_order_json in resp_obj["data"].get("orderListGets"):
            return_order = ReturnOrder.parse_obj(return_order_json)
            return_orders.append(return_order)
        return return_orders
