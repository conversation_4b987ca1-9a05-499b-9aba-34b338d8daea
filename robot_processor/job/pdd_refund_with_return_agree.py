from pydantic import BaseModel

from robot_processor.client import pdd_bridge_client
from robot_processor.client.errors import PddServiceError
from robot_processor.client.schema import PddRefundAgreePayload
from robot_processor.enums import JobStatus, JobType
from robot_processor.job import AutoJobControllerBase


class PddRefundsAgreeOutput(BaseModel):
    execution_result: str


class TaobaoRefundWithReturnAgree(AutoJobControllerBase):
    job_type = JobType.PDD_REFUND_WITH_RETURN_AGREE
    output_model = PddRefundsAgreeOutput

    def process(self):
        tid = self.args.get_trades()[0].tid
        if not tid:
            return JobStatus.FAILED, "缺失订单号"
        aftersale_id = self.args.get_arg_by_task_arg_name("aftersale_id")
        if not aftersale_id:
            return JobStatus.FAILED, "缺失售后单号"
        ignore_repetitive_operation_error = self.args.get_arg_by_task_arg_name(
            "ignore_repetitive_operation_error")
        payload = PddRefundAgreePayload(
            after_sale_id=aftersale_id,
            order_sn=tid
        )
        try:
            resp = pdd_bridge_client.refund_agree(store_id=self.shop.sid,
                                                  payload=payload)
            if resp.response.result.succ:
                self.states.write_job_widget_collection(
                    {"execution_result": "成功"})
                return JobStatus.SUCCEED, None
            else:
                self.states.write_job_widget_collection(
                    {"execution_result": "失败"})
                return JobStatus.FAILED, resp.response.result.message
        except PddServiceError as e:
            if ignore_repetitive_operation_error == "true" and \
                    "已处理，禁止操作" in str(e):
                self.states.write_job_widget_collection(
                    {"execution_result": "售后单已被处理"})
                return JobStatus.SUCCEED, None
            self.states.write_job_widget_collection(
                {"execution_result": "失败"})
            return JobStatus.FAILED, str(e)
