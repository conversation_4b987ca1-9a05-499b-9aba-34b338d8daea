from typing import Op<PERSON>
from typing import <PERSON><PERSON>

import emoji
from dramatiq import Retry
from result import Err
from result import Ok

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.error.client_request import WdtRequestError
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.job.utils import change_wdt_address
from robot_processor.job.utils import normalize_address
from robot_processor.utils import combine_address_with_town
from robot_processor.utils import flatten
from robot_processor.utils import get_district_or_zone
from rpa.erp.wdt import SalesSpec
from rpa.erp.wdt import SalesTrade
from rpa.erp.wdt import Trade
from rpa.erp.wdt import WdtClient
from rpa.erp.wdt import WdtOpenAPIClient
from rpa.erp.wdt import get_skus_from_wdt_trades


class WdtAfterSaleUploadExecutor(AutoJobControllerBase):
    job_type = JobType.WDT_AFTER_SALE_UPLOAD
    """
    旺店通补发
    """

    def get_rate_limit_setting(self, conf: dict) -> Optional[Tuple[str, str]]:
        limit_key = f"JOB_LIMITER_{self.job_type}_ORG_{self.job_wrapper.shop.org_id}"  # 在租户维度进行限流
        for conf_key in [limit_key, f"JOB_LIMITER_{self.job_type}"]:
            if limit_value := conf.get(conf_key):
                return limit_key, limit_value
        return None

    def get_remark(self):
        remark = self.args.get_arg_by_task_arg_name("remark")
        if not remark:
            return None
        elif isinstance(remark, list):
            return ",".join(flatten(remark))
        return remark

    def get_warehouse_name(self):
        warehouse_name = self.args.get_arg_by_task_arg_name("warehouse_name")
        if not warehouse_name:
            return None
        elif isinstance(warehouse_name, list):
            return warehouse_name[0]
        return warehouse_name

    def process(self):
        receiver = self.args.get_arg_by_task_arg_name("address")
        if isinstance(receiver, dict):
            receiver = change_wdt_address(receiver)
        remark = self.get_remark()
        warehouse_name = self.get_warehouse_name()
        is_cover = self.args.get_arg_by_task_arg_name("is_cover")
        tid = self.args.get_trades()[0].tid
        trade_query_resp = WdtClient(self.order.sid).trade_query(tid)
        trades = [trade for trade in trade_query_resp.response.trades]
        if not trades:
            return JobStatus.FAILED, f"找不到 {tid} 相关的订单"
        oldest_trade: Trade = sorted(trades, key=lambda x: x.trade_no)[0]
        # 最老的原始单加上拆分出去的普通单
        # https://leyan.yuque.com/digismart/lqmmke/ifsygboduzg8eht0/edit?toc_node_uuid=ew-OLWmK4XFn90xd
        origin_trades = [
            t
            for t in trades
            if (
                t.trade_no == oldest_trade.trade_no
                or (t.split_from_trade_id and int(t.split_from_trade_id) == oldest_trade.trade_id)
            )
        ]
        origin_trade = Trade.get_origin_trade_with_all_goods(origin_trades)
        spec_list = []
        reissue_skus = self.args.get_reissue_skus()
        if reissue_skus.after_sales_type == "original":
            # 原单补发从当前订单获取商品信息
            skus = get_skus_from_wdt_trades(origin_trade, tid)
        else:
            # 可能存在 outer_sku 重复，在补发前需要进行合并。（重复的原因为 outer sku 在不同的组合装商品内被选择）
            skus = reissue_skus.merge_same_outer_sku()
        for index, sku in enumerate(skus):
            if sku.is_single():
                erp_spec_no = sku.outer_spu_id
            else:
                erp_spec_no = sku.outer_sku_id or sku.sku_id
            if self.shop.org_id == "1904":
                if reissue_skus.after_sales_type != "original":
                    # https://redmine.leyantech.com/issues/624843
                    # 原单补发sku从erp获取，不会存在组合商品的判断逻辑问题
                    # 非原单有可能从erp的商品过来，也可能从平台商品过来，但后端感知不到，
                    # 所以非原单的所有sku判断一下是否是组合商品
                    suite_resp = WdtClient(sid=self.order.sid).suites_query(erp_spec_no)  # type: ignore[arg-type]
                    if suite_resp.response.total_count > 0:
                        sku.type = 2
            # sku.type=2表明是组合商品
            is_suite = "1" if sku.type == 2 else "0"
            spec = SalesSpec(spec_no=erp_spec_no, num=str(sku.qty), is_suite=is_suite)
            if sku.price is not None and sku.price > 0.0:
                spec.original_price = sku.price
            spec_list.append(spec)
        if trades[0].is_archived:
            try:
                WdtOpenAPIClient(self.order.sid).trade_unfile(trades[0].trade_no)
                # 旺店通的说法是不需要等待
                raise Retry("反归档等待10秒", 1000 * 10)
            except WdtRequestError as e:
                return JobStatus.FAILED, f"反归档失败，{e.message}"

        sales_trade = SalesTrade(
            trade_no=trades[0].trade_no,
            spec_list=spec_list,
            is_check=int(self.args.get_arg_by_task_arg_name("is_check") or 1),
        )
        if receiver:
            normalize_address(receiver)
            sales_trade.receiver_name = receiver["name"]
            if emoji.emoji_count(receiver["name"]) > 0:
                return JobStatus.FAILED, "收货人姓名不能包含表情"
            sales_trade.receiver_mobile = receiver["mobile"]
            sales_trade.receiver_address = combine_address_with_town(
                f"{receiver.get('town') or ''}", f"{receiver['address']}"
            )
            sales_trade.receiver_province = receiver["state"]
            # 如果是县级市，信息在zone里，否则一般在city里
            sales_trade.receiver_city = receiver.get("city") or receiver.get("zone")
            sales_trade.receiver_district = f"{get_district_or_zone(receiver)}"
        if remark:
            sales_trade.cs_remark = remark
        if is_cover:
            sales_trade.is_cover = int(is_cover)
        if warehouse_name:
            warehouse_query_resp = WdtClient(self.order.sid).warehouse_query()
            warehouses = [
                warehouse for warehouse in warehouse_query_resp.response.warehouses if warehouse.name == warehouse_name
            ]
            if warehouses:
                sales_trade.warehouse_no = warehouses[0].warehouse_no
        logistics_code_result = self._get_logistics_no(
            self.args.get_arg_by_task_arg_name("logistics_name"), self.shop.sid
        )
        if logistics_code_result.is_err():
            return JobStatus.FAILED, str(logistics_code_result.unwrap_err())
        if (logistics_code := logistics_code_result.unwrap()) is not None:
            sales_trade.logistics_code = logistics_code

        if buyer_message := self.args.get_arg_as_str_by_task_arg_name("buyer_message"):
            sales_trade.buyer_message = buyer_message
        if operator_no := self.args.get_arg_as_str_by_task_arg_name("operator_no"):
            sales_trade.operator_no = operator_no
        if post_amount := self.args.get_arg_as_str_by_task_arg_name("post_amount"):
            sales_trade.post_amount = post_amount
        if print_remark := self.args.get_arg_as_str_by_task_arg_name("print_remark"):
            sales_trade.print_remark = print_remark
        if custom_trade_type_name := self.args.get_arg_as_str_by_task_arg_name("custom_trade_type_name"):
            sales_trade.custom_trade_type_name = custom_trade_type_name
        try:
            resp = WdtOpenAPIClient(sid=self.order.sid).sales_trade_replenish(sales_trade)
            self.states.write_job_output({"trade_no": resp.trade_no})
            return JobStatus.SUCCEED, None
        except Exception as e:
            return JobStatus.FAILED, str(e)

    @staticmethod
    def _get_logistics_no(logistics_name, sid) -> Ok[str | None] | Err[Exception]:
        if not logistics_name:
            return Ok(None)

        if isinstance(logistics_name, str):
            final_logistics_name = logistics_name
        elif isinstance(logistics_name, list) and all(isinstance(item, str) for item in logistics_name):
            final_logistics_name = logistics_name[0]
        elif isinstance(logistics_name, list) and all(isinstance(item, dict) for item in logistics_name):
            final_logistics_name = logistics_name[0].get("value", "")
        else:
            return Err(Exception(f"补发物流公司数据格式错误 {logistics_name=}"))
        all_logistics = {
            logistics.logistics_name: logistics for logistics in WdtOpenAPIClient(sid).get_logistic().logistics_list
        }
        if final_logistics_name not in all_logistics:
            return Err(Exception(f"没有匹配的物流公司 {final_logistics_name=}"))
        else:
            logistics = all_logistics[final_logistics_name]
            return Ok(logistics.logistics_no)
