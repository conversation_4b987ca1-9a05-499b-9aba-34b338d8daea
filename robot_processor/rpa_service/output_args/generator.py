import typing as t
from dataclasses import is_dataclass
from datetime import date
from datetime import datetime
from datetime import time
from decimal import Decimal
from functools import cached_property

from pydantic import BaseModel
from pydantic.dataclasses import create_pydantic_model_from_dataclass
from pydantic.fields import SHAPE_ITERABLE
from pydantic.fields import SHAPE_LIST
from pydantic.fields import SHAPE_SEQUENCE
from pydantic.fields import SHAPE_SET
from pydantic.fields import SHAPE_SINGLETON
from pydantic.fields import <PERSON><PERSON>ield
from pydantic.typing import all_literal_values
from pydantic.typing import is_literal_type

from robot_processor.form.schemas import RpaArgOutputSchema
from robot_processor.form.schemas import RpaDataBinding
from robot_processor.form.schemas import WidgetDataType
from robot_processor.job import AutoJobControllerBase
from robot_processor.rpa_service.output_args.exceptions import GenerateSchemaError


class JobOutputGenerator:
    def __init__(self, job_class: t.Type[AutoJobControllerBase]) -> None:
        self.job_class = job_class
        self._python_to_schema_type: t.Dict[t.Type, WidgetDataType] = {
            str: WidgetDataType.STRING,
            Decimal: WidgetDataType.NUMBER,
            int: WidgetDataType.NUMBER,
            float: WidgetDataType.NUMBER,
            datetime: WidgetDataType.DATETIME,
            date: WidgetDataType.DATE,
            time: WidgetDataType.TIME,
            bool: WidgetDataType.BOOLEAN,
        }
        self._allowed_field_shapes = {SHAPE_SINGLETON, SHAPE_LIST, SHAPE_SET, SHAPE_SEQUENCE, SHAPE_ITERABLE}

    def is_table(self, field: ModelField) -> bool:
        python_type = self._check_field_type(field)
        if issubclass(python_type, BaseModel):
            return True
        if is_dataclass(python_type):
            return True
        return False

    @cached_property
    def job_output_schema(self) -> t.List[RpaArgOutputSchema]:
        output_model = getattr(self.job_class, "output_model")
        if output_model is None:
            # rpa应用没有输出
            return []
        return self.generate_output_schema(output_model, None)

    def generate_output_schema(
        self, model_cls, parent_schema: RpaArgOutputSchema | None = None
    ) -> list[RpaArgOutputSchema]:
        ret: t.List[RpaArgOutputSchema] = []
        if is_dataclass(model_cls):
            model_cls = create_pydantic_model_from_dataclass(model_cls)
        for field in model_cls.__fields__.values():
            try:
                ret.append(self._generate_field_schema(field, parent_schema))
            except ValueError as ex:
                raise GenerateSchemaError(model_cls=model_cls, field=field, msg=str(ex))
        return ret

    def _generate_field_schema(
        self, field: ModelField, parent_schema: t.Optional[RpaArgOutputSchema] = None
    ) -> RpaArgOutputSchema:
        python_type = self._check_field_type(field)
        if self.is_table(field):
            default_type = t.cast(WidgetDataType, "table")
            current_field_expression = f"{field.name}[]"
        else:
            default_type = self._python_to_schema_type[python_type]
            current_field_expression = f"{field.name}"
        if not parent_schema:
            level = "$"
            expression = current_field_expression
        else:
            level = parent_schema.data_binding.level + f".{parent_schema.name}"
            expression = parent_schema.data_binding.expression + "." + current_field_expression
        data_binding = RpaDataBinding(level=level, expression=expression)
        ret = RpaArgOutputSchema(
            label=None,  # 代码中不对label做定义
            name=field.name,
            desc=None,  # 代码中不对desc做定义
            default_type=default_type,
            allow_types=[default_type],
            children=None,
            data_binding=data_binding,
        )
        if self.is_table(field):
            ret.children = self.generate_output_schema(python_type, ret)
        return ret

    def _get_python_type(self, field: ModelField) -> t.Type[t.Any]:
        if not is_literal_type(field.type_):
            # 非Literal类型，直接返回pydantic.type_
            return field.type_
        choices = all_literal_values(field.type_)
        for choice in choices:
            if type(choice) != "str":
                raise ValueError("类型非法，Literal类型只能是字符串")
        return str

    def _check_field_type(self, field: ModelField) -> t.Type[t.Any]:
        if field.shape not in self._allowed_field_shapes:
            raise ValueError(f"类型非法，无法转换成【WidgetDataType】{field.shape}")
        elif field.shape == SHAPE_SINGLETON:
            # 单元素定义, 只支持: Literal[str], str, Decimal, int, float, datetime, date, time
            python_type = self._get_python_type(field)
            if (python_type not in self._python_to_schema_type) and not is_dataclass(python_type):
                raise ValueError("类型非法，无法转换成【WidgetDataType】")
        else:
            # 除了SHAPE_SINGLETON外，其他类型都是table类型
            python_type = self._get_python_type(field)
            if not issubclass(python_type, BaseModel):
                # table类型只支持 Array Of BaseModel的定义，eg. List[BaseModel]
                raise ValueError("类型非法，不是pydantic.BaseModel")
        return python_type
