"""外部系统授权管理"""

import datetime
import json
import time
from functools import partialmethod
from typing import Optional
from typing import TypeVar

import sqlalchemy as sa
from loguru import logger
from pydantic import BaseModel
from result import Err
from result import Ok
from result import as_result
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column

from robot_processor.db import DbBaseModel
from robot_processor.db import db
from robot_processor.db import in_transaction
from robot_processor.enums import AuthType
from robot_processor.ext import cache
from robot_processor.ext import org_erp_info_producer
from robot_processor.ext import shop_erp_info_producer
from robot_processor.shop.kiosk_models import KioskShop
from robot_processor.shop.models import Shop
from robot_processor.shop.schema import OrgErpInfoEventMsg
from robot_processor.shop.schema import ShopErpInfoEventMsg
from robot_processor.utils import filter_none

T = TypeVar("T")


def get_shop_yto_credential(channel_id: int, auth_account: str | None = None) -> Optional["YtoAuthExtraData"]:
    """查询店铺的圆通授权信息

    Args:
        channel_id (int): 店铺ID
        auth_account (str | None): 授权账号名称
    Returns:
        YtoAuthExtraData | None: 圆通授权信息
    """

    stmt = (
        sa.select(Credentials.auth_extra_data)
        .select_from(CredentialShopMapping)
        .join(Credentials, CredentialShopMapping.auth_id == Credentials.id)
        .where(CredentialShopMapping.channel_id == channel_id)
        .where(Credentials.auth_type == AuthType.YTO)
    )

    if auth_account is not None:
        stmt = stmt.where(Credentials.auth_account == auth_account)

    raw_extra_data = db.session.execute(stmt).scalars().all()
    if not raw_extra_data:
        raise Exception("未找到圆通授权信息")
    if len(raw_extra_data) > 1:
        raise Exception("存在多个圆通授权信息，需要指定使用哪一项")
    return YtoAuthExtraData.validate(raw_extra_data[0])


def get_kiosk_shop_by_erp_platform_sid(erp_id: int, erp_platform_sid: int) -> KioskShop | None:
    shop_mapping = CredentialShopMapping.query.filter_by(erp_id=erp_id, erp_platform_sid=erp_platform_sid).first()
    if shop_mapping:
        return KioskShop.query.get(shop_mapping.channel_id)
    return None


def get_jst_shop_id_by_shop(shop):
    shop_mapping = (
        CredentialShopMapping.query.join(Credentials, Credentials.id == CredentialShopMapping.auth_id)
        .filter(
            Credentials.org_id == shop.org_id,
            Credentials.auth_type == AuthType.JST,
            CredentialShopMapping.channel_id == shop.channel_id,
        )
        .first()
    )
    if shop_mapping is None:
        return
    return shop_mapping.erp_platform_sid


def get_wdt_shop_no_by_shop(shop):
    shop_mapping = (
        CredentialShopMapping.query.join(Credentials,
                                         Credentials.id == CredentialShopMapping.auth_id)
        .filter(
            Credentials.org_id == shop.org_id,
            Credentials.auth_type == AuthType.WDT,
            CredentialShopMapping.channel_id == shop.channel_id,
        )
        .first()
    )
    if shop_mapping is None:
        return
    return shop_mapping.erp_platform_sid


def get_shop_by_erp_platform_sid(org_id: int, erp_type: str, erp_platform_sid: int):
    from robot_processor.shop.models import Shop

    record = (
        db.session.query(CredentialShopMapping)
        .join(Credentials, Credentials.id == CredentialShopMapping.auth_id)
        .filter(
            Credentials.org_id == org_id,
            Credentials.auth_type == erp_type.upper(),
            CredentialShopMapping.erp_platform_sid == erp_platform_sid,
        )
        .first()
    )
    if not record:
        return Err(PermissionError(f"租户 {org_id} 无 {erp_platform_sid}@{erp_type} 绑定店铺信息"))
    if shop := Shop.Queries.by_channel_id(record.channel_id):
        return Ok(shop)
    else:
        return Err(ValueError("店铺不存在"))


class Credentials(DbBaseModel):
    __bind_key__ = "kiosk"
    __tablename__ = "org_auth_manager"
    __table_args__ = (
        sa.PrimaryKeyConstraint("id"),
        sa.Index("idx_org_auth_manager_org_id_and_auth_account", "org_id", "auth_account"),
    )

    id: Mapped[int]
    org_id: Mapped[int]
    auth_type: Mapped[str] = mapped_column(sa.String(30), comment="授权类型")
    auth_account: Mapped[str] = mapped_column(sa.String(128), comment="授权账号")
    auth_extra_data: Mapped[dict] = mapped_column(sa.JSON, comment="额外的信息")
    auth_nick: Mapped[str] = mapped_column(sa.String(128), default="")
    auth_status: Mapped[str] = mapped_column(sa.String(64), nullable=True, default="INIT")
    available_status: Mapped[str] = mapped_column(sa.String(64), default="INIT")
    expire_timestamp: Mapped[int] = mapped_column(sa.BigInteger, nullable=True)
    created_at: Mapped[datetime.datetime] = mapped_column(sa.DateTime, nullable=True, default=datetime.datetime.now)

    ref_uuid: Mapped[str] = mapped_column(sa.String(64), default="")

    def query_available_status(self):
        try:
            return {True: "AVAILABLE", False: "UNAVAILABLE"}[self.get_authorized_erp_client().check_grant_is_valid()]
        except NotImplementedError:
            return "AVAILABLE"
        except:  # NOQA
            return self.available_status

    def get_authorized_erp_client(self):
        from rpa.erp.baisheng import BaiShengOpenPlatformAPIClient
        from rpa.erp.guanyiyunapi import GyyOpenPlatformAPIClient
        from rpa.erp.jackyun import JackyunOpenPlatformAPIClient
        from rpa.erp.kuaimai import KuaimaiOpenPlatformAPIClient
        from rpa.erp.wdgj import WDGJOpenPlatformAPIClient
        from rpa.erp.wdt import WDTOpenPlatformAPIClient
        from rpa.erp.wdtulti import WdtUltiOpenPlatformAPIClient
        from rpa.erp.wln import WlnClient

        # FIXME 一个 erp 应该只提供一个 client
        match self.auth_type:
            case AuthType.WDT:
                return WDTOpenPlatformAPIClient(self.auth_extra_data)
            case AuthType.WDTULTI:
                return WdtUltiOpenPlatformAPIClient(self.auth_extra_data)
            case AuthType.KUAIMAI:
                return KuaimaiOpenPlatformAPIClient(self.auth_extra_data)
            case AuthType.WANLINIU:
                return WlnClient.init_by_credentials(self)
            case AuthType.BAISHENG:
                return BaiShengOpenPlatformAPIClient(self.auth_extra_data)
            case AuthType.WDGJ:
                return WDGJOpenPlatformAPIClient(self.auth_extra_data)
            case AuthType.JACKYUN:
                return JackyunOpenPlatformAPIClient(self.auth_extra_data)
            case AuthType.GUANYIYUN:
                return GyyOpenPlatformAPIClient(self.auth_extra_data)
            case _:
                raise NotImplementedError(f"{self.auth_type} support")

    @classmethod
    def create(
        cls,
        org_id: int,
        auth_type: AuthType,
        auth_nick: str,
        auth_extra_data: dict,
        dry_run: bool = True,
    ):
        self = cls(
            org_id=org_id,
            auth_type=auth_type,
            auth_nick=auth_nick,
            auth_extra_data=auth_extra_data,
        )
        if (result := self.update_auth_extra_data(auth_extra_data)).is_err():
            error = result.unwrap_err()
            logger.opt(exception=error).error(f"授权信息格式不合法: {auth_extra_data} {error}")
            return Err(ValueError("授权信息格式不合法"))
        if self.query_available_status() != "AVAILABLE":
            return Err(ValueError("授权信息不可用"))
        match self.auth_type:
            case AuthType.KUAIMAI:
                try:
                    km_refresh_session_response = self.get_authorized_erp_client().refresh_session(
                        self.auth_extra_data["refreshToken"]
                    )
                    self.expire_timestamp = int(time.time()) + km_refresh_session_response.session.expires_in
                except Exception as e:
                    logger.opt(exception=e).error(f"快麦接口错误: {e}")
                    return Err(ValueError("快麦信息不可用"))

        self.available_status = "AVAILABLE"
        if not dry_run:
            with in_transaction() as session:
                session.add(self)
        self.after_create()
        return Ok(self)

    @as_result(Exception)
    def update_auth_extra_data(self, auth_extra_data: dict):
        from robot_processor.client import super_book_client
        from robot_processor.shop.schema import AUTH_TYPE_TO_SUPER_BOOK_APP_NAME_MAP
        from robot_processor.shop.schema import BaiShengGrantMeta
        from robot_processor.shop.schema import DouYinGrantMeta
        from robot_processor.shop.schema import EMSGrantMeta
        from robot_processor.shop.schema import GuanYiYunGrantMeta
        from robot_processor.shop.schema import JackyunGrantMeta
        from robot_processor.shop.schema import JDLGrantMeta
        from robot_processor.shop.schema import KuaiMaiGrantMeta
        from robot_processor.shop.schema import SFGrantMeta
        from robot_processor.shop.schema import SuperBookGrantMeta
        from robot_processor.shop.schema import WanLiNiuGrantMeta
        from robot_processor.shop.schema import WDGJGrantMeta
        from robot_processor.shop.schema import WDTGrantMeta
        from robot_processor.shop.schema import WDTUltiGrantMeta
        from robot_processor.shop.schema import YTOGrantMeta
        from robot_processor.shop.schema import ZTOGrantMeta

        match self.auth_type:
            case AuthType.WDT:
                wdt_meta = WDTGrantMeta.validate(auth_extra_data)
                self.auth_account = wdt_meta.sid
            case AuthType.WDTULTI:
                wdtulti_meta = WDTUltiGrantMeta.validate(auth_extra_data)
                self.auth_account = wdtulti_meta.sid
            case AuthType.WANLINIU:
                wln_meta = WanLiNiuGrantMeta.validate(auth_extra_data)
                self.auth_account = wln_meta.app_key
            case AuthType.BAISHENG:
                bs_meta = BaiShengGrantMeta.validate(auth_extra_data)
                self.auth_account = bs_meta.sid
            case AuthType.KUAIMAI:
                kuaimai_meta = KuaiMaiGrantMeta.validate(auth_extra_data)
                self.auth_account = kuaimai_meta.co_id
            case AuthType.GUANYIYUN:
                gyy_meta = GuanYiYunGrantMeta.validate(auth_extra_data)
                self.auth_account = gyy_meta.co_id
                gyy_meta.erp_account = gyy_meta.co_id
                auth_extra_data = gyy_meta.dict()
            case AuthType.WDGJ:
                wdgj_meta: WDGJGrantMeta = WDGJGrantMeta.validate(auth_extra_data)
                self.auth_account = wdgj_meta.app_key
                wdgj_meta.erp_account = wdgj_meta.app_key
                auth_extra_data = wdgj_meta.dict()
            case AuthType.JACKYUN:
                jackyun_meta: JackyunGrantMeta = JackyunGrantMeta.validate(auth_extra_data)
                self.auth_account = jackyun_meta.app_key
                jackyun_meta.erp_account = jackyun_meta.app_key
                auth_extra_data = jackyun_meta.dict()
            case AuthType.YTO:
                yto_meta: YTOGrantMeta = YTOGrantMeta.validate(auth_extra_data)
                self.auth_account = yto_meta.partner_key
            case AuthType.ZTO:
                zto_meta: ZTOGrantMeta = ZTOGrantMeta.validate(auth_extra_data)
                self.auth_account = zto_meta.app_key
            case AuthType.EMS:
                ems_meta: EMSGrantMeta = EMSGrantMeta.validate(auth_extra_data)
                self.auth_account = ems_meta.sender_no
            case AuthType.SF:
                sf_meta: SFGrantMeta = SFGrantMeta.validate(auth_extra_data)
                self.auth_account = sf_meta.monthly_card
            case AuthType.JDL:
                jdl_meta: JDLGrantMeta = JDLGrantMeta.validate(auth_extra_data)
                self.auth_account = jdl_meta.customer_code
            case AuthType.KS_LFX | AuthType.JD_YZ | AuthType.JD_FS | AuthType.XHS_ERP:
                super_book_app_name = AUTH_TYPE_TO_SUPER_BOOK_APP_NAME_MAP[self.auth_type]
                if "sid" in auth_extra_data:
                    # 快手、言准店铺 token 从超级本来同步，并先检测是否订阅
                    # 小红书直接走access_token接口，小红书没有订购，客户授权一次就是永久的
                    if self.auth_type != AuthType.XHS_ERP:
                        order_res = super_book_client.query_isv_orders(super_book_app_name, auth_extra_data["sid"])
                        if not order_res.is_ok() or not order_res.unwrap().success:
                            raise Exception("检测订购授权失败")
                    access_token_res = super_book_client.query_access_token(super_book_app_name, auth_extra_data["sid"])
                    if access_token_res.is_ok():
                        access_token_response = access_token_res.unwrap()
                        if access_token_response.success:
                            auth_extra_data["accessToken"] = access_token_response.access_token
                            auth_extra_data["expiresAt"] = access_token_response.expires_at
                super_book_meta = SuperBookGrantMeta.validate(auth_extra_data)
                self.auth_account = super_book_meta.sid
                self.expire_timestamp = super_book_meta.expiresAt
            case AuthType.PIGEON | AuthType.DOUYIN_XYZ:
                douyin_meta = DouYinGrantMeta.validate(auth_extra_data)
                self.auth_account = douyin_meta.sid
                self.expire_timestamp = int(douyin_meta.expires_at_ms / 1000)
            case _:
                self.auth_account = ""
        self.auth_extra_data = filter_none(auth_extra_data)

    def get_bind_erp_info(self):
        org_erp_info: OrgErpInfo | None = (
            OrgErpInfo.query.join(CredentialShopMapping, CredentialShopMapping.erp_id == OrgErpInfo.id)
            .join(
                # CredentialShopMapping 没有 auth_id 的索引，所以要通过 Credentials 来查询
                Credentials,
                Credentials.id == CredentialShopMapping.auth_id,
            )
            .filter(Credentials.id == self.id)
            .first()
        )
        return org_erp_info

    @classmethod
    def list_by_shop_auth_type(cls, shop: Shop, auth_type: AuthType) -> list["Credentials"]:
        return (
            Credentials.query.join(CredentialShopMapping, CredentialShopMapping.auth_id == Credentials.id)
            .filter(
                CredentialShopMapping.channel_id == shop.channel_id,
                Credentials.auth_type == auth_type,
            )
            .all()
        )

    @classmethod
    def get_by_shop_auth_type(
        cls, shop: Shop, auth_type: AuthType, auth_account: str | None = None
    ) -> Optional["Credentials"]:
        query = (
            Credentials.query.join(CredentialShopMapping, CredentialShopMapping.auth_id == Credentials.id)
            .filter(CredentialShopMapping.channel_id == shop.channel_id)
            .filter(Credentials.auth_type == auth_type)
        )
        if auth_account is not None:
            query = query.filter(Credentials.auth_account == auth_account)
        return query.first()

    @classmethod
    def get_by_org_auth_type(cls, org_id, auth_type: AuthType, auth_account: str | None = None) -> list["Credentials"]:
        query = Credentials.query.filter(
            Credentials.auth_type == auth_type,
            Credentials.org_id == org_id,
        )
        if auth_account:
            query = query.filter(Credentials.auth_account == auth_account)
        return query.all()

    def after_create(self):
        match self.auth_type:
            case AuthType.KS_LFX | AuthType.DOUYIN_XYZ | AuthType.PIGEON:
                shop: KioskShop | None = KioskShop.query.filter_by(org_id=self.org_id, sid=self.auth_account).first()
                if shop is not None:
                    # 如果该店铺还没有绑定过该授权，再新建。
                    with in_transaction() as session:
                        csm: CredentialShopMapping | None = CredentialShopMapping.query.filter(
                            CredentialShopMapping.auth_id == self.id,
                            CredentialShopMapping.channel_id == shop.id,
                        ).first()
                        if csm is None:
                            session.add(CredentialShopMapping(channel_id=shop.id, auth_id=self.id))


class YtoAuthExtraData(BaseModel):
    partner_key: str
    partner_id: str


class CredentialShopMapping(DbBaseModel):
    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_shop_erp_mapping"
    __table_args__ = (sa.PrimaryKeyConstraint("id"),)

    id: Mapped[int]
    channel_id: Mapped[int]
    auth_id: Mapped[int]
    erp_id: Mapped[int] = mapped_column(sa.BigInteger, nullable=True)
    erp_platform_sid: Mapped[int] = mapped_column(sa.Integer, nullable=True)


class OrgErpInfo(DbBaseModel):
    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_org_erp_info"
    __table_args__ = (sa.PrimaryKeyConstraint("id"),)

    id: Mapped[int]
    org_id: Mapped[int] = mapped_column(sa.BigInteger)
    erp_type: Mapped[str] = mapped_column(sa.String(32))
    erp_account: Mapped[str] = mapped_column(sa.String(64))
    meta: Mapped[str] = mapped_column(sa.String(2000))
    init_done: Mapped[int] = mapped_column(sa.SmallInteger, default=0)
    status: Mapped[int] = mapped_column(sa.SmallInteger, default=1)
    deleted: Mapped[int] = mapped_column(sa.SmallInteger, default=0)
    expire_timestamp: Mapped[int | None] = mapped_column(sa.BigInteger, default=None)
    refresh_token: Mapped[str | None] = mapped_column(sa.String(128), default=None)
    access_token: Mapped[str | None] = mapped_column(sa.String(128), default=None)

    @classmethod
    @in_transaction()
    def create_by_credential(cls, credential: Credentials):
        self = cls(
            org_id=credential.org_id,
            erp_type=credential.auth_type,
            erp_account=credential.auth_account,
            meta=json.dumps(credential.auth_extra_data),
        )
        db.session.add(self)
        return self

    @in_transaction()
    def modify_by_credential(self, credential: Credentials):
        self.erp_account = credential.auth_account
        self.meta = json.dumps(credential.auth_extra_data)

    @logger.catch
    def produce_kafka_shop_message(self, action: str, shop: KioskShop):
        shop_erp_info_producer(
            ShopErpInfoEventMsg(
                action=action,
                orgId=shop.org_id,
                channelType=shop.platform,
                channelNo=shop.sid,
                sellerNick=shop.nick,
                erpId=self.id,
                erpType=self.erp_type,
                erpAccount=self.erp_account,
                meta=self.meta,
                status=self.status,
            ).dict()
        )

    produce_kafka_shop_erp_create = partialmethod(produce_kafka_shop_message, "create")
    produce_kafka_shop_erp_modify = partialmethod(produce_kafka_shop_message, "modify")
    produce_kafka_shop_erp_delete = partialmethod(produce_kafka_shop_message, "delete")

    @logger.catch
    def produce_kafka_org_message(self, action: str):
        org_erp_info_producer(
            OrgErpInfoEventMsg(
                id=self.id,
                action=action,
                orgId=self.org_id,
                erpType=self.erp_type,
                erpAccount=self.erp_account,
                meta=self.meta,
            ).dict()
        )

    produce_kafka_org_erp_create = partialmethod(produce_kafka_org_message, "create")
    produce_kafka_org_erp_modify = partialmethod(produce_kafka_org_message, "modify")
    produce_kafka_org_erp_delete = partialmethod(produce_kafka_org_message, "delete")


class MolaShops(DbBaseModel):
    __bind_key__ = "mola_api"
    __tablename__ = "shops"
    __table_args__ = (sa.PrimaryKeyConstraint("id"),)

    id: Mapped[int]
    title: Mapped[str] = mapped_column(sa.String(255))
    nickname: Mapped[str] = mapped_column(sa.String(255))
    sid: Mapped[str] = mapped_column(sa.String(255))
    site: Mapped[str] = mapped_column(sa.String(255))

    power_group: Mapped[int | None] = mapped_column(sa.Integer, nullable=True)


class PowerGroups(DbBaseModel):
    __bind_key__ = "mola_api"
    __tablename__ = "power_groups"
    __table_args__ = (sa.PrimaryKeyConstraint("id"),)

    id: Mapped[int]
    name: Mapped[str] = mapped_column(sa.String(255))
    org_id: Mapped[int | None] = mapped_column(sa.Integer, nullable=True)
    org_name: Mapped[str] = mapped_column(sa.Text, nullable=True)


class PowerGroupRelatedShops(DbBaseModel):
    __bind_key__ = "mola_api"
    __tablename__ = "power_group_related_shops"
    __table_args__ = (sa.PrimaryKeyConstraint("id"),)

    id: Mapped[int]
    power_group_id: Mapped[int] = mapped_column("power-group_id", sa.Integer)
    shop_id: Mapped[int] = mapped_column(sa.Integer)


@cache.memoize(timeout=3600)
def if_shop_pdd_auth_valid(sid: str):
    from robot_processor.client import super_book_client

    for app_name in (
        super_book_client.AppName.PDD_ATX,
        super_book_client.AppName.PDD_207,
    ):
        res = super_book_client.query_access_token(app_name, sid)
        if res.is_err():
            continue
        token_res = res.unwrap()
        if token_res.success and token_res.expires_at > int(time.time()):
            return True
    return False
