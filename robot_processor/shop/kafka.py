import json

import arrow
from loguru import logger
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.constants import FORMAT_DATE_TIME
from robot_processor.enums import ProductCode, ErpType, ShopStatus
from robot_processor.ext import db, kafka_consumer
from robot_processor.logging import vars as log_vars
from robot_processor.shop.models import ContractInfo, ErpInfo, Shop

org_event = kafka_consumer.subscribe("kiosk-org-product-info")
shop_erp_event = kafka_consumer.subscribe("kiosk-shop-erp-info")
shop_update_event = kafka_consumer.subscribe("kiosk-shop-change-event")

jst_shop_site_dict = {"TAOBAO": "淘宝天猫",
                      "TMALL": "淘宝天猫",
                      "PDD": "拼多多",
                      "DOUDIAN": "头条放心购",
                      "XIAOHONGSHU": "小红书",
                      "KUAISHOU": "快手电商",
                      "JD": "京东商城"}


@logger.catch
@org_event.connect
def update_contract(_, record):
    log_vars.OrgId.set(record.get('org_id'))
    c = ContractInfo.find_or_create(org_id=(record["org_id"]))
    c.end_ts = arrow.get(record["end_at"], FORMAT_DATE_TIME).int_timestamp
    c.product_code = ProductCode(record["product_code"])
    c.master_cid = record.get('master_cid') or ''
    db.session.commit()
    logger.info(f"Contract update or create: {record}")


@logger.catch
@shop_erp_event.connect
def update_shop_erp(_, record):
    """
    接收来自kiosk项目的店铺绑定erp源的消息.
    {'action': 'CREATE', 'id': 1, 'orgId': 13,
    'channelType': 'taobao', 'channelNo': '41234',
    'erpId': 12, 'erpType': 'JST', 'erpAccount': '1234',
    'meta': '{"token":"abdesc"}', 'status': 1,
    'sellerNick': '234'}
    """
    log_vars.Sid.set(record.get('channelNo'))
    logger.info(f"update_shop_erp: {record}")
    try:
        platform = record['channelType'].upper()
        sid = record['channelNo']
        erp_type = eval(f"ErpType.{record['erpType']}")
        erp_type = ErpType(erp_type)
        erp_meta = json.loads(record['meta'])
        shop = Shop.query.filter_by(platform=platform, sid=sid).first()
        if not shop:
            logger.error(f"update_shop_erp from kafka:<{platform}>,<{sid}>店铺不存在!")
            return
        erp_info = ErpInfo.query.filter_by(shop_id=shop.id).order_by(ErpInfo.id.desc()).first()
        if erp_info is None:
            erp_info = ErpInfo(shop_id=shop.id)
        erp_info.erp_type = erp_type
        if not erp_info.nick:
            erp_info.nick = shop.nick
        erp_info.meta = erp_meta
        erp_info.token = erp_meta.get('token', '')
        erp_info.shop_site = jst_shop_site_dict.get(shop.platform, '')
        erp_info.erp_id = record['erpId']
        flag_modified(erp_info, "meta")
        db.session.add(erp_info)
        db.session.commit()
    except BaseException as e:
        logger.exception(f"update_shop_erp, exception: {repr(e)}")


@logger.catch
@shop_update_event.connect
def update_shop(_, record):
    from robot_processor.shop.tasks import init_miniapp
    from flask import current_app

    """
    {
        "action_type": "INSERT",
        "operate_ts": 1661160193005,
        "shop_info": {
            "org_id": 1,
            "channel_id": 1,
            "channel_no": "xxx",
            "channel_name": "测试-接口绑定店铺",
            "seller_nick": "测试-接口绑定店铺",
            "channel_icon": "",
            "channel_type": "taobao",
            "order_status": 0,
            "status": 1,
            "deleted": 0
        }
    }
    """

    shop = None
    record = record['shop_info']
    log_vars.Sid.set(record.get('channel_no'))
    logger.info(f"update shop with {record}")
    channel_id = record['channel_id']
    shop_platform = record['channel_type'].upper()
    # 如果是淘宝/天猫店铺，先找一下有没有预授权信息
    if shop_platform in ['TAOBAO', 'TMALL', "DOUDIAN"]:
        shop = (Shop.query
                .filter(Shop.Filters.status_authorized)
                .filter(Shop.sid == record["channel_no"])
                .first())
        if shop:
            template_version = current_app.config.get("MINI_VERSION_TEMPLATE_VERSION")
            init_miniapp.send_with_options(args=(shop.sid, template_version))
    shop = shop or Shop.find_or_create(channel_id=channel_id)
    shop.platform = shop_platform
    shop.sid = record['channel_no']
    shop.org_id = record['org_id']
    shop.title = record['channel_name'] or record['seller_nick'] or shop.nick
    shop.status = ShopStatus.ENABLE if record['status'] == 1 else ShopStatus.DISABLE
    shop.deleted = record['deleted']
    shop.nick = record.get('seller_nick', shop.nick)
    shop.channel_id = channel_id
    db.session.commit()
