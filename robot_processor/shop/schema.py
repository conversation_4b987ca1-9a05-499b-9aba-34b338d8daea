from typing import Optional
from urllib.parse import unquote_plus

from pydantic import BaseModel, Field, validator

from robot_processor.enums import AuthType, ErpType
from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.client.superbook import SuperBook


AUTH_TYPE_TO_MOLA_SITE_MAP: dict[str, str] = {
    AuthType.WANLINIU: "wanliniu"
}


AUTH_TYPE_TO_SUPER_BOOK_APP_NAME_MAP: dict[AuthType, SuperBook.AppName] = {
    AuthType.KS_LFX: SuperBook.AppName.KS_LFX,
    AuthType.JD_YZ: SuperBook.AppName.JD_YZ,
    AuthType.JD_FS: SuperBook.AppName.JD_FS,
    AuthType.XHS_ERP: SuperBook.AppName.XHS_ERP,
}


AUTH_TYPE_TO_ERP_TYPE_MAP: dict[AuthType | str, ErpType] = {
    AuthType.JST: ErpType.JST,
    AuthType.WDT: ErpType.WDT,
    AuthType.WDTULTI: ErpType.WDTULTI,
    AuthType.KUAIMAI: ErpType.KUAIMAI,
    AuthType.WANLINIU: ErpType.WANLINIU,
    AuthType.GUANYIYUN: ErpType.GUANYIYUN,
    AuthType.WDGJ: ErpType.WDGJ,
    AuthType.JACKYUN: ErpType.JACKYUN,
    AuthType.BAISHENG: ErpType.BAISHENG,
}

LOGISTIC_TYPE_TO_AUTH_TYPE_MAP: dict[LogisticsType, AuthType] = {
    LogisticsType.EMS: AuthType.EMS,
    LogisticsType.ZTO: AuthType.ZTO,
    LogisticsType.SF: AuthType.SF,
    LogisticsType.YTO: AuthType.YTO,
    LogisticsType.JDL: AuthType.JDL,
}


class QianniuAuthentication(BaseModel):
    """千牛的侧边栏或者服务市场通过千牛网关调用飞梭服务时必定会携带的，和用户认证有关的几组信息.

    这些信息可以认为已经被千牛验证过，也就是说，当我们收到请求时，可以确定触发这些请求的用户的 open_id 和 user_nick 就是 query 中的 open_id 和 user_nick.

    相关文档：https://open.taobao.com/v2/doc?spm=a219a.7629140.0.0.6d0d75fekIUu2I#/abilityToOpen?docId=118538&docType=1
    """
    access_token: str
    user_nick: str = Field(..., description="用户的淘宝账号, 可能是主账号或者子账号，主账号也就是店铺名，如 '飞梭旗舰店',"
                                            "子账号格式是 <店铺名>:<子账号>， 如 '飞梭旗舰店:飞梭子账号'")
    open_id: str = Field(..., description="用户的淘宝 open_id, 用于标识用户的唯一身份")


class TaobaoShopGetOpenUid(BaseModel):
    mix_nick: str


class TaobaoShopAuthentication(BaseModel):
    """
    淘宝新版授权将只会传递一个 code，接着需要用这个 code 去换 access_token。
    """
    code: str


class TaobaoCreateOrRefreshAuthTokenResponse(BaseModel):
    token_result: str


class TaobaoCreateOrRefreshAuthTokenResult(BaseModel):
    """
    {
        "refresh_token_valid_time": 1722500886827,
        "parent_open_uid": "AAGNQKeQAOVCGjiEV8vA2dWJ",
        "expire_time": 1725092886827,
        "open_uid": "AAFGQKeQAOVCGjqEV8szakFQ",
        "locale": "zh_CN",
        "user_nick": "%E6%9C%89%E9%A1%B6%E5%A4%A9%E7%9A%84%E4%BB%B0%E6%9C%9B%3Atest",
        "access_token": "500020016412OTCtqarricdjv3oT2ifHJHahwXCG2nQudJt2QtXBk15a9f1a0ddALPr",
        "refresh_token": "50003000441zBN9mqLdtCnBw1mtBh4fXcIUdxTfAhFR9nryjGl4wk1a86f544CfRER8",
        "w1_valid": 1725092886827,
        "w2_valid": 1722502686827,
        "parent_nick": "%E6%9C%89%E9%A1%B6%E5%A4%A9%E7%9A%84%E4%BB%B0%E6%9C%9B",
        "r2_valid": 1722760086827,
        "r1_valid": 1725092886827,
        "sp": "tbUIC"
    }
    """
    access_token: str = Field(description="Access token")
    open_uid: str = Field(description="Open UID")
    user_nick: str = Field(description="用户昵称")
    expire_time: int | None = Field(
        description="授权过期时间，单位为 毫秒，表示会在该 unix 时间戳（毫秒）的时刻过期"
    )
    refresh_token: str | None = Field(description="Refresh token，可用来刷新 access_token")
    refresh_token_valid_time: int | None = Field(
        description="Refresh token 可用时间，单位为 毫秒，Refresh token 可在该 unix 时间戳（毫秒）的时刻后可用"
    )
    parent_nick: str | None = Field(description="主账号昵称")
    parent_open_uid: str | None = Field(description="主账号 Open UID")

    w1_valid: int | None = Field(
        description="w1 级别 API 或字段的访问过期时间，单位为 毫秒，表示会在该 unix 时间戳（毫秒）的时刻过期"
    )
    w2_valid: int | None = Field(
        description="w2 级别 API 或字段的访问过期时间，单位为 毫秒，表示会在该 unix 时间戳（毫秒）的时刻过期"
    )
    r1_valid: int | None = Field(
        description="r1 级别 API 或字段的访问过期时间，单位为 毫秒，表示会在该 unix 时间戳（毫秒）的时刻过期"
    )
    r2_valid: int | None = Field(
        description="r2 级别 API 或字段的访问过期时间，单位为 毫秒，表示会在该 unix 时间戳（毫秒）的时刻过期"
    )
    sp: str | None
    locale: str | None

    def urldecode_nick(self):
        self.user_nick = unquote_plus(self.user_nick)
        if self.parent_nick:
            self.parent_nick = unquote_plus(self.parent_nick)


class PlatformGrantSchema(BaseModel):
    access_token: str
    nick: str
    open_id: Optional[str]
    sid: str
    seller_id: str
    platform: str
    org_id: str
    channel_id: int
    title: Optional[str]


class GrandRecordSchema(BaseModel):
    sid: str
    seller_nick: Optional[str]
    platform: Optional[str]
    access_token: Optional[str]
    app: Optional[str]


class ShopTokenSchema(BaseModel):
    token: str


class OrgErpInfoEventMsg(BaseModel):
    id: int
    orgId: int
    erpType: str
    action: str = Field(description="create/modify/delete")
    erpAccount: str
    meta: str


class ShopErpInfoEventMsg(BaseModel):
    action: str = Field(description="create/modify/delete")
    orgId: int
    channelType: str
    channelNo: str
    erpId: int
    erpType: str
    erpAccount: str
    meta: str
    status: int
    sellerNick: str


class ErpGrantMeta(BaseModel):
    erp_account: str
    meta: dict
    meta_string: str


class WDTGrantMeta(BaseModel):
    sid: str
    app_key: str
    app_secret: str
    after_sale_shop_no: str | None


class WDTUltiGrantMeta(BaseModel):
    sid: str
    wdt_salt: str
    wdt_appkey: str
    wdt_secret: str
    after_sale_shop_no: str | None


class KuaiMaiGrantMeta(BaseModel):
    co_id: str
    appKey: str
    appSecret: str
    accessToken: str
    refreshToken: str


class WanLiNiuGrantMeta(BaseModel):
    app_key: str
    app_secret: str


class GuanYiYunGrantMeta(BaseModel):
    co_id: str
    app_secret: str
    session_key: str
    erp_account: str | None


class BaiShengGrantMeta(BaseModel):
    sid: str
    baseUrl: str
    appKey: str
    appSecret: str


class WDGJGrantMeta(BaseModel):
    app_key: str
    app_secret: str
    erp_account: str | None
    access_token: str


class JackyunGrantMeta(BaseModel):
    app_key: str
    app_token: str | None
    app_secret: str
    customer_id: str
    erp_account: str | None


class YTOGrantMeta(BaseModel):
    partner_id: str
    partner_key: str


class ZTOGrantMeta(BaseModel):
    app_key: str
    app_secret: str


class EMSGrantMeta(BaseModel):
    authorization: str
    sm4_key: str
    sender_no: str


class SFGrantMeta(BaseModel):
    monthly_card: str


class JDLGrantMeta(BaseModel):
    access_token: str
    customer_code: str


class SuperBookGrantMeta(BaseModel):
    sid: str
    accessToken: str
    expiresAt: int


class DouYinGrantMeta(BaseModel):
    sid: str
    access_token: str
    refresh_token: str
    expires_at_ms: int


class GrantChannelInfo(BaseModel):
    channel_id: int
    channel_no: str
    channel_type: str
    channel_name: str
    erp_platform_sid: int | None


class CreateGrantRecordRequest(BaseModel):
    auth_type: AuthType
    auth_nick: str = Field("")

    meta: dict

    @validator("auth_type")
    def validate_auth_type_editable(cls, auth_type):
        if auth_type not in AuthType.get_editable_auth_type_list():
            raise ValueError(f"{auth_type} 暂时不支持")
        return auth_type


class UpdateGrantRecordRequest(BaseModel):
    channel: list[GrantChannelInfo] | None


class CheckGrantRecordRequest(BaseModel):
    auth_type: AuthType
    meta: dict


class QueryGrantRecordParameters(BaseModel):
    bind_shops: list[str] | None
    page_num: int | None
    page_size: int | None
    auth_type: AuthType | None
    auth_nick: str | None
    sync_data_status: str | None
    available_status: str | None
    order_field: str | None
    order_rule: str | None
