"""在店铺授权时，无需进行 jwt 验证"""

from base64 import b64encode

from flask import Blueprint
from flask import render_template
from flask import request
from loguru import logger
from result import Err
from result import Ok
from result import Result

from robot_processor.base_schemas import Success
from robot_processor.client.conf import app_config
from robot_processor.client.taobao_1688 import Taobao1688Client
from robot_processor.enums import ChannelType
from robot_processor.ext import db
from robot_processor.shop.models import App
from robot_processor.shop.models import GrantRecord
from robot_processor.shop.models import Shop
from robot_processor.shop.utils import bind_kiosk_auth
from robot_processor.utils import arrow_now
from robot_processor.utils import current_trace_id
from robot_processor.utils import unwrap_optional
from rpa.doudian import schemas as doudian_schemas

api = Blueprint("shop-grant-api", __name__)


def doudian_grant(code: str, app_str: str) -> Result[Shop, str]:
    """抖店授权

    Args:
        code: 抖店授权码，为抖店授权请求携带的参数
        app_str: 区分抖店不用 app 的标识，由 api route 来区分

    References:
        https://op.jinritemai.com/docs/guide-docs/9/22
    """
    app = App(app_str)
    doudian_client = app.doudian_openapi_client
    token_request = doudian_schemas.TokenCreateParam(code=code)
    token_res = doudian_client.token_create(token_request)
    if token_res.is_err():
        logger.opt(exception=token_res.err()).warning("token create failed.")
        return Err(f"授权失败，请联系销售同学处理。追踪码: {current_trace_id()}")
    token_response: doudian_schemas.TokenCreateRes = unwrap_optional(token_res.ok())
    logger.info(f"token create success: {app_str} {token_response.dict()}")

    code_info_tracer = b64encode(str(token_response.dict()).encode()).decode()
    shop: Shop | None = (
        Shop.query.filter(Shop.platform == ChannelType.DOUDIAN.name).filter(Shop.sid == token_response.shop_id).first()
    )
    if not shop:
        if app == App.DOUDIAN_XYZ:
            # 智能外呼需要抖店小柚子授权。未开店铺也要记录
            auth_only_shop = Shop.Queries.insert_shop_grant_record_for_auth_only(
                nick=token_response.shop_name,
                open_id=None,
                title=token_response.shop_name,
                platform=ChannelType.DOUDIAN.name,
                sid=str(token_response.shop_id),
                app=app,
                access_token=token_response.access_token,
            )
            xyz_grant_record = auth_only_shop.records.filter_by(app=app).first()
            xyz_grant_record.refresh_token = token_response.refresh_token
            expires_at_ms = (arrow_now().int_timestamp + token_response.expires_in) * 1000
            xyz_grant_record.expires_at_ms = expires_at_ms
            return Ok(auth_only_shop)
        else:
            return Err(f"后台未添加店铺，请联系顾问先添加店铺。追踪码: {code_info_tracer}")

    if not GrantRecord.query_if_exists(shop.id, token_response.access_token):
        # fixme 暂时使用 grant_record 来维护授权信息
        # 需要将 kiosk 的授权管理迁回 robot 后，接入授权管理
        record = GrantRecord()
        record.app = app
        record.access_token = token_response.access_token
        record.refresh_token = token_response.refresh_token
        expires_at_ms = (arrow_now().int_timestamp + token_response.expires_in) * 1000
        record.expires_at_ms = expires_at_ms
        shop.records.append(record)
        db.session.commit()

        # 飞鸽和小柚子授权则在 Kiosk DB 中创建数据，并与店铺绑定.
        bind_kiosk_auth(grant_record=record)

    return Ok(shop)


@api.get("/doudian/<string:app>/goto")
def doudian_shop_grant(app: str):
    code: str | None = request.args.get("code") or request.args.get("authCode")
    if not code:
        if app_config.DOUDIAN_GRANT_PASS_NON_CODE_REQUEST == "false":
            context = {"error": "缺少授权码信息"}
        else:
            context = {"shop": Shop(nick="")}  # type: ignore[dict-item]
    else:
        code = unwrap_optional(code)
        grant_res = doudian_grant(code, app)
        if grant_res.is_err():
            err = grant_res.err()
            if app == App.DOUDIAN_XYZ.value and err and "后台未添加店铺" in err:
                # 没有店铺，对于小柚子来说不是错误，需要通过
                context = {"shop": Shop(nick="")}  # type: ignore[dict-item]
            else:
                context = {"error": unwrap_optional(grant_res.err())}
        else:
            context = {"shop": unwrap_optional(grant_res.ok())}  # type: ignore[dict-item]

    return render_template("doudian/pigeon_shop_grant.html", **context)


@api.get("/taobao_1688/goto")
def taobao_1688_shop_grant():
    logger.info(request.args)
    logger.info(request.data)

    token_resp = Taobao1688Client().get_token(
        code=request.args.get("code"),  # type: ignore[arg-type]
        # 无用?
        redirect_uri="www.baidu.com",
    )
    logger.info(token_resp)
    return Success(success=True)
