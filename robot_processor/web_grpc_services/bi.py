from collections import defaultdict
from collections.abc import Sequence
from operator import attrgetter
from typing import TYPE_CHECKING
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple
from typing import cast

import grpc
from flask import g
from google.protobuf import struct_pb2
from leyan_proto.digismart.robot.bi import report_pb2
from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimension
from leyan_proto.digismart.robot.bi.generate_pb2 import DataRange
from leyan_proto.digismart.robot.bi.generate_pb2 import DataRangeGroups
from leyan_proto.digismart.robot_web import bi_pb2
from leyan_proto.digismart.robot_web.bi_pb2_grpc import BiServicer
from more_itertools import first
from result import Err
from result import Ok
from sqlalchemy import select
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.bi.dashboard import ReportDashboard
from robot_processor.bi.dashboard import ReportPanel
from robot_processor.client import app_config
from robot_processor.db import db
from robot_processor.db import in_transaction
from robot_processor.utils import message_to_dict

if TYPE_CHECKING:
    from robot_processor.auth import KioskJwtPayload
    from robot_processor.form.models import Form
    from robot_processor.form.models import FormShop
    from robot_processor.shop.models import Shop


class BiService(BiServicer):
    def ListReportDataType(self, request, context: grpc.ServicerContext):
        """报表支持的数据类型及操作符

        Path:
            GET /digismart/v1/bi/data-type
        """
        from robot_processor.bi.generate_schema.data_type._base import ReportDataTypeMeta

        response = bi_pb2.ListReportDataTypeResponse()
        report_data_type_map = ReportDataTypeMeta.get_data_type_map()
        data_type_list = [
            response.DataTypeInfo(
                data_type=item.DATA_TYPE,
                data_range_operators=item.DATA_RANGE_AGENT.support_operators,
                data_statistics_operators=item.STATISTICS_AGENT.support_operators,
            )
            for item in report_data_type_map.values()
        ]

        response.data_type_list.extend(data_type_list)

        return response

    def BatchGetReportDataTypeEnumOptions(self, request, context: grpc.ServicerContext):
        """查询数据支持的候选项

        Path:
            POST /digismart/v1/bi/data-type/enum/options
        """
        from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimensionDisplay

        from robot_processor.bi.generate_schema.data_type import ReportDataType

        response = bi_pb2.BatchGetReportDataTypeEnumOptionsResponse()
        for dimension in request.dimension:
            agent = ReportDataType.auto_init_dimension_display(
                dimension_display=DataDimensionDisplay(dimension=dimension)
            )
            if hasattr(agent, "get_enum_options"):
                response.dimension_option_list.append(
                    response.DimensionEnumOptions(
                        dimension=dimension,
                        option_list=agent.get_enum_options(request.data_source),
                    )
                )

        return response

    def ListReportDataDimension(self, request, context: grpc.ServicerContext):
        """查询当前看板数据源可用的数据纬度

        Path:
            GET /digismart/v1/bi/data-dimension
        """
        from robot_processor.form.getter import is_form_has_transfer_info

        response = bi_pb2.ListReportDataDimensionResponse()
        if not request.HasField("panel_id"):
            context.abort(grpc.StatusCode.INVALID_ARGUMENT, "panel_id is required.")
        with db.session.no_autoflush as session:
            panel: ReportPanel | None = session.get(ReportPanel, request.panel_id.value)
        if not panel:
            context.abort(grpc.StatusCode.NOT_FOUND, "panel not found.")
        if not panel.data_source:
            context.abort(grpc.StatusCode.UNAVAILABLE, "panel datasource is empty.")

        from robot_processor.bi.generate_schema.dimension import custom
        from robot_processor.bi.generate_schema.dimension import system

        custom_field_dimension_list = list(
            map(
                custom.auto_init_dimension,
                custom.DAO.query_widget_info_list(panel.data_source),
            )
        )
        system_field_dimension_list = system.gather(
            with_transfer=is_form_has_transfer_info(first(panel.data_source.form_ids, None))
        )
        response.dimension_list.extend(system_field_dimension_list)
        response.dimension_list.extend(custom_field_dimension_list)

        return response

    def ListReportDashboard(self, request, context: grpc.ServicerContext):
        """获取报表 dashboard

        Path:
            GET /digismart/v1/bi/dashboard
        """
        from sqlalchemy import desc

        response = bi_pb2.ListReportDashboardResponse()
        with db.session.no_autoflush as session:
            dashboard_list = (
                session.query(ReportDashboard)
                .filter(ReportDashboard.org_id == str(g.auth.org_id))
                .filter(
                    ReportDashboard.status.in_(
                        [
                            ReportDashboard.DashboardStatus.ENABLED,
                            ReportDashboard.DashboardStatus.DISABLED,
                        ]
                    )
                )
                .order_by(desc(ReportDashboard.id))
                .all()
            )
        response.dashboard_list.extend(dashboard.to_pb2() for dashboard in dashboard_list)

        return response

    def CreateReportDashboard(self, request, context: grpc.ServicerContext):
        """创建报表 dashboard

        Path:
            POST /digismart/v1/bi/dashboard
        """
        with db.session.no_autoflush as session:
            check_dashboard_name = (
                select(ReportDashboard)
                .select_from(ReportDashboard)
                .where(ReportDashboard.org_id == str(g.auth.org_id))
                .where(ReportDashboard.name == request.dashboard.name.value)
                .where(ReportDashboard.status != ReportDashboard.DashboardStatus.DELETED)
            )
            if session.execute(check_dashboard_name).one_or_none() is not None:
                context.abort(grpc.StatusCode.ALREADY_EXISTS, "已有同名看板，建议您修改")

        response = bi_pb2.GetReportDashboardResponse()

        current_user = ReportAssembler.build_account_pb2_from_auth(g.auth)

        with in_transaction() as session:
            dashboard = ReportDashboard()
            session.add(dashboard)
            dashboard.mark_updated_by(current_user)
            dashboard.mark_created_by(current_user)
            dashboard.org_id = str(g.auth.org_id)
            dashboard.name = request.dashboard.name.value
            dashboard.description = request.dashboard.description.value
            dashboard.layout_schema = message_to_dict(request.dashboard.layout_schema)
            session.flush()
            response.dashboard.MergeFrom(dashboard.to_pb2())

        return response

    def UpdateReportDashboard(self, request, context: grpc.ServicerContext):
        """更新报表 dashboard

        Path:
            POST /digismart/v1/bi/dashboard/{dashboard.id}
        """
        with db.session.no_autoflush as session:
            check_dashboard_name = (
                select(ReportDashboard)
                .select_from(ReportDashboard)
                .where(ReportDashboard.org_id == str(g.auth.org_id))
                .where(ReportDashboard.name == request.dashboard.name.value)
                .where(ReportDashboard.id != request.dashboard.id.value)
                .where(ReportDashboard.status != ReportDashboard.DashboardStatus.DELETED)
            )
            if request.dashboard.HasField("name") and session.execute(check_dashboard_name).one_or_none() is not None:
                context.abort(grpc.StatusCode.ALREADY_EXISTS, "已有同名看板，建议您修改")

        response = bi_pb2.GetReportDashboardResponse()
        current_user = ReportAssembler.build_account_pb2_from_auth(g.auth)

        with in_transaction() as session:
            dashboard: ReportDashboard | None = session.get(ReportDashboard, request.dashboard.id.value)
            if not dashboard:
                context.abort(grpc.StatusCode.NOT_FOUND, "dashboard not found.")

            dashboard.patch_update(request.dashboard)
            dashboard.mark_updated_at()
            dashboard.mark_updated_by(current_user)
            session.flush()
            response.dashboard.MergeFrom(dashboard.to_pb2())

        return response

    def ListReportPanel(self, request, context: grpc.ServicerContext):
        """获取报表看板列表

        Path:
            GET /digismart/v1/bi/dashboard/{dashboard_id}/panel
        """
        response = bi_pb2.ListReportPanelResponse()
        with db.session.no_autoflush as session:
            dashboard = session.get(ReportDashboard, request.dashboard_id)
            panel_list = (
                session.query(ReportPanel)
                .filter(ReportPanel.dashboard_id == request.dashboard_id)
                .filter(
                    ReportPanel.status.in_(
                        [
                            ReportPanel.PanelStatus.ENABLED,
                            ReportPanel.PanelStatus.DISABLED,
                        ]
                    )
                )
                .all()
            )
        response.panel_list.extend(panel.to_pb2(fill_data_flag=False) for panel in panel_list)
        if dashboard and dashboard.layout_schema:
            response.layout_schema.update(dashboard.layout_schema)

        return response

    def CreateReportPanel(self, request, context: grpc.ServicerContext):
        """创建报表看版

        Path:
            POST /digismart/v1/bi/dashboard/{dashboard_id}/panel
        """
        with db.session.no_autoflush as session:
            check_panel_name = (
                select(ReportPanel)
                .select_from(ReportPanel)
                .where(ReportPanel.dashboard_id == request.dashboard_id.value)
                .where(ReportPanel.name == request.panel.name.value)
                .where(ReportPanel.status != ReportPanel.PanelStatus.DELETED)
            )
            if session.execute(check_panel_name).one_or_none() is not None:
                context.abort(grpc.StatusCode.ALREADY_EXISTS, "同个看板内已有同名报表，建议您修改")
        response = bi_pb2.GetReportPanelResponse()

        current_user = ReportAssembler.build_account_pb2_from_auth(g.auth)

        with in_transaction() as session:
            panel = ReportPanel()
            session.add(panel)
            panel.mark_created_by(current_user)
            panel.mark_updated_by(current_user)
            panel.dashboard_id = request.dashboard_id.value
            panel.name = request.panel.name.value
            panel.description = request.panel.description.value
            panel.ui_schema = message_to_dict(request.panel.ui_schema)
            panel.data_range_groups = self._default_data_range_groups()
            panel.dimensions = request.panel.dimensions
            panel.statistics = request.panel.statistics
            if request.panel.panel_type:
                panel.type = ReportPanel.PanelType(request.panel.panel_type)
            session.flush()

            response.panel.MergeFrom(panel.to_pb2())

        return response

    @staticmethod
    def _default_data_range_groups():
        range_groups = DataRangeGroups()
        dimension = DataDimension(
            title="工单创建时间",
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=True,
            data_type=DataDimension.DataType.DATA_TYPE_DATETIME,
            dimension_type=DataDimension.DataDimensionType.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
            system_field=DataDimension.DataDimensionSystemField.DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME,
        )
        operator = DataRange.Operator.OPERATOR_RELATIVE_DATE
        value = struct_pb2.Value()
        value.struct_value.update({"unit": "MONTH", "value": 1})
        range_groups.data_range_groups.extend(
            [
                DataRangeGroups.DataRangeGroup(
                    relation=DataRange.Relation.RELATION_AND,
                    data_ranges=[DataRange(dimension=dimension, operator=operator, value=value)],
                )
            ]
        )
        range_groups.relation = DataRange.Relation.RELATION_AND
        return range_groups

    def UpdateReportPanel(self, request, context: grpc.ServicerContext):
        """更新报表看板

        Path:
            PATCH /digismart/v1/bi/dashboard/{dashboard_id}/panel/{panel.id}
        """
        with db.session.no_autoflush as session:
            check_panel_name = (
                select(ReportPanel)
                .select_from(ReportPanel)
                .where(ReportPanel.dashboard_id == request.dashboard_id.value)
                .where(ReportPanel.name == request.panel.name.value)
                .where(ReportPanel.id != request.panel.id.value)
                .where(ReportPanel.status != ReportPanel.PanelStatus.DELETED)
            )
            if request.panel.HasField("name") and session.execute(check_panel_name).one_or_none() is not None:
                context.abort(grpc.StatusCode.ALREADY_EXISTS, "同个看板内已有同名报表，建议您修改")

        response = bi_pb2.GetReportPanelResponse()
        current_user = ReportAssembler.build_account_pb2_from_auth(g.auth)

        with in_transaction() as session:
            panel: ReportPanel | None = session.get(ReportPanel, request.panel.id.value)
            if not panel:
                context.abort(grpc.StatusCode.NOT_FOUND, "dashboard not found.}")

            panel.mark_updated_by(current_user)
            panel.mark_updated_at()
            panel.patch_update(request.panel)

            session.flush()
            response.panel.MergeFrom(panel.to_pb2())

        return response

    def GetReportPanel(self, request, context: grpc.ServicerContext):
        """查看看板详情

        Path:
            GET /digismart/v1/bi/dashboard/{dashboard_id}/panel/{panel_id}
        """
        response = bi_pb2.GetReportPanelResponse()
        if not request.HasField("panel_id"):
            context.abort(grpc.StatusCode.INVALID_ARGUMENT, "panel_id is required.")
        with db.session.no_autoflush as session:
            panel: ReportPanel | None = session.get(ReportPanel, request.panel_id.value)
        if not panel:
            context.abort(grpc.StatusCode.NOT_FOUND, "panel not found.")

        if request.HasField("drill_down_status"):
            panel.drill_down_status = request.drill_down_status
        response.panel.MergeFrom(panel.to_pb2())

        return response

    def DrillDownReportPanel(self, request, context):
        """查看看板详情，和 GetReportPanel 相同，只是更换为 post 方法

        Path:
            POST /digismart/v1/bi/dashboard/{dashboard_id}/panel/{panel_id}
        """
        response = bi_pb2.GetReportPanelResponse()
        if not request.HasField("panel_id"):
            context.abort(grpc.StatusCode.INVALID_ARGUMENT, "panel_id is required.")
        panel = db.session.get(ReportPanel, request.panel_id.value)
        if not panel:
            return context.abort(grpc.StatusCode.NOT_FOUND, "panel not found.")

        if request.HasField("drill_down_status"):
            panel.drill_down_status = request.drill_down_status
        response.panel.MergeFrom(panel.to_pb2())

        return response

    def BatchRefreshReportPanelDataset(self, request, context: grpc.ServicerContext):
        """批量刷新看板数据

        Path:
            POST /digismart/v1/bi/dashboard/{dashboard_id}/panel/-/dataset
        """
        response = bi_pb2.BatchRefreshReportPanelDatasetResponse()

        with in_transaction() as session:
            dashboard: ReportDashboard | None = session.get(ReportDashboard, request.dashboard_id.value)
            if not dashboard:
                context.abort(grpc.StatusCode.NOT_FOUND, "dashboard not found.")
            stmt = select(ReportPanel).select_from(ReportPanel).where(ReportPanel.dashboard_id == dashboard.id)
            if request.panel_id_list:
                panel_id_list = message_to_dict(request)["panel_id_list"]
                stmt = stmt.where(ReportPanel.id.in_(panel_id_list))
            panels: Sequence[ReportPanel] = session.execute(stmt).scalars().all()

            for panel in panels:
                panel_dataset_info = response.ReportPanelDataset()
                panel_dataset_info.panel_id.value = panel.id
                panel_dataset_info.dataset.MergeFrom(panel.get_dataset_as_struct())
                response.panel_dataset_list.append(panel_dataset_info)

        return response

    def ListReportDataSource(self, request, context: grpc.ServicerContext):
        """报表可用的数据源信息

        Path:
            GET /digismart/v1/bi/data-source
        """
        from robot_processor.form.models import Form
        from robot_processor.form.models import FormShop

        response = bi_pb2.ListReportDataSourceResponse()
        form_shops = (
            Form.Queries.form_shops_by_org_id(str(g.auth.org_id))
            .options(Form.Options.joined_load_form)
            .filter(Form.Filters.can_view_in_report)
            .all()
        )

        group_by_form: Dict[Form, List[FormShop]] = defaultdict(list)
        for form_shop in form_shops:
            group_by_form[form_shop.form].append(form_shop)
        for form, subscribed in group_by_form.items():
            form_id_list = Form.Utils.form_ids_by_form_shops(subscribed)
            form_view = bi_pb2.ListReportDataSourceResponse.DataSource.Form(
                name=form.name,  # type: ignore[arg-type]
                status=ReportAssembler.which_form_status_pb2(subscribed),
                data_source=ReportAssembler.build_datasource_pb2(form_id=form_id_list, form_sync_id=form.id),
            )
            shop_view = [
                bi_pb2.ListReportDataSourceResponse.DataSource.Shop(
                    sid=shop.sid, nick=shop.nick, title=shop.title, platform=shop.platform
                )
                for shop in map(attrgetter("shop"), subscribed)
            ]
            response.form_view.append(
                bi_pb2.ListReportDataSourceResponse.DataSource.FormView(
                    form=form_view,
                    shop=shop_view,
                )
            )

        response.shop_view.extend(ReportAssembler.convert_form_view_to_shop_view(list(response.form_view)))

        return response

    def GenerateReportDataset(self, request, context: grpc.ServicerContext):
        """生成查询的结果集

        Path:
            POST /digismart/v1/bi/dataset
        """
        from robot_processor.bi.dashboard.compatible import to_metadata
        from robot_processor.bi.database import Session
        from robot_processor.bi.database import mysql_compiler
        from robot_processor.bi.dataset.etl.pipeline import get_raw_sql
        from robot_processor.bi.dataset.etl.pipeline import load_to_echarts_source_from_database
        from robot_processor.bi.dataset.etl.pipeline import load_to_stat_from_database
        from robot_processor.bi.dataset.etl.pipeline import load_to_table_from_database
        from robot_processor.bi.dataset.sql_builder import analysis

        response = bi_pb2.GenerateReportDatasetResponse()
        temp_panel = ReportPanel()
        temp_panel.data_source = request.data_source
        temp_panel.type = ReportPanel.PanelType(request.panel_type)
        temp_panel.display_mode = ReportPanel.DisplayMode(request.display_mode)
        if request.WhichOneof("report_schema") == "sql_schema":
            temp_panel.mode = ReportPanel.PanelMode.SQL
            temp_panel.sql_schema = request.sql_schema
        else:
            temp_panel.mode = ReportPanel.PanelMode.GENERATE
            temp_panel.dimensions = request.generate_schema.dimensions
            temp_panel.statistics = request.generate_schema.statistics
            temp_panel.data_range_groups = request.generate_schema.data_range_groups
        if request.HasField("drill_down_status"):
            temp_panel.drill_down_status = request.drill_down_status

        source_res: Ok[list] | Err[Exception | str]
        raw_sql_res: Ok[str] | Err[Exception | str]
        # 更新 source，先设置一个默认值
        response.dataset["source"] = []
        if str(g.auth.org_id) in app_config.bi_mysql8_whitelist:
            try:
                metadata = to_metadata(temp_panel)
                sql_builder = analysis.SQLBuilder(metadata)
                title = sql_builder.get_title()
                with Session() as session:
                    data = sql_builder.get_data(session)
                result = [title]
                for item in data:
                    result.append([str(col) for col in item])
                source_res = Ok(result)
                raw_sql_res = Ok(mysql_compiler.compile(sql_builder.get_sql()))
            except Exception as e:
                source_res = Err(e)
                raw_sql_res = Err(e)
        elif request.panel_type == ReportPanel.PanelType.TABLE.pb2_value:
            source_res = load_to_table_from_database(temp_panel)
            raw_sql_res = get_raw_sql(temp_panel)
        elif request.panel_type == ReportPanel.PanelType.INDICATOR.pb2_value:
            source_res = load_to_stat_from_database(temp_panel)
            raw_sql_res = get_raw_sql(temp_panel)
        else:
            source_res = load_to_echarts_source_from_database(temp_panel)
            raw_sql_res = get_raw_sql(temp_panel)
        (
            source_res.map_err(lambda reason: response.dataset.update({"reason": str(reason)})).map(
                lambda source: response.dataset.update({"source": source})
            )
        )
        # debug 使用的 raw_sql
        raw_sql_res.map(lambda raw_sql: response.dataset.update({"raw_sql": raw_sql}))
        response.drill_down_status.CopyFrom(temp_panel.drill_down_status)

        return response

    def CloneDashboard(self, request, context: grpc.ServicerContext):
        dashboard = ReportDashboard.query.get(request.dashboard_id.value)
        new_dashboard = self.dashboard_clone(dashboard)
        db.session.add(new_dashboard)
        db.session.commit()

        # 保存新旧panel_id
        panel_ids = {}
        for panel in ReportPanel.query.filter_by(dashboard=dashboard):
            if not panel or panel.status == ReportPanel.PanelStatus.DELETED:
                continue
            new_panel = self.panel_clone(panel)
            new_panel.dashboard = new_dashboard
            db.session.add(new_panel)
            db.session.commit()
            panel_ids[f"panel_{str(panel.id)}"] = new_panel.id

        # 更新dashboard
        layout_schema = new_dashboard.layout_schema
        for schema_key in layout_schema.keys():
            for item in layout_schema[schema_key]:
                item["i"] = f"panel_{panel_ids[item.get('i')]}"
        flag_modified(new_dashboard, "layout_schema")
        db.session.add(new_dashboard)
        db.session.commit()
        response = bi_pb2.CloneDashboardResponse()
        response.dashboard_id.value = new_dashboard.id
        return response

    def ClonePanel(self, request, context: grpc.ServicerContext):
        from copy import deepcopy

        dashboard = ReportDashboard.query.get(request.dashboard_id.value)
        if not dashboard:
            return
        panel = ReportPanel.query.get(request.panel_id.value)
        if not panel or panel.status == ReportPanel.PanelStatus.DELETED:
            return
        new_panel = self.panel_clone(panel, True)
        new_panel.dashboard = dashboard
        db.session.add(new_panel)
        db.session.commit()
        # 更新dashboard
        layout_schema = dashboard.layout_schema
        for schema_key in layout_schema.keys():
            for item in layout_schema[schema_key]:
                if item.get("i") != f"panel_{request.panel_id.value}":
                    continue
                copy_schema = deepcopy(item)
                copy_schema.update({"i": f"panel_{new_panel.id}"})
                layout_schema[schema_key].append(copy_schema)
        flag_modified(dashboard, "layout_schema")
        db.session.add(dashboard)
        db.session.commit()
        response = bi_pb2.CloneReportPanelResponse()
        response.panel_id.value = new_panel.id
        return response

    @staticmethod
    def dashboard_clone(dashboard):
        from robot_processor.bi.dashboard.models import ReportDashboard

        def _check_name(org_id, name):
            return ReportDashboard.query.filter_by(org_id=org_id, name=name).first()

        dolly = ReportDashboard()
        dolly.org_id = dashboard.org_id
        dolly.name = dashboard.name + "(复制)"
        if _check_name(dashboard.org_id, dolly.name):
            dolly.name += "(复制)"
        dolly.description = dashboard.description
        dolly.layout_schema = dashboard.layout_schema
        dolly.created_by = dashboard.created_by

        return dolly

    @staticmethod
    def panel_clone(panel, edit_name_flag=False):

        def _check_name(dashboard_id, name):
            return ReportPanel.query.filter_by(dashboard_id=dashboard_id, name=name).first()

        dolly = ReportPanel()
        if edit_name_flag:
            dolly.name = panel.name + "(复制)"
            if _check_name(panel.dashboard_id, dolly.name):
                dolly.name += "(复制)"
        else:
            dolly.name = panel.name
        dolly.description = panel.description
        dolly.mode = panel.mode
        dolly.type = panel.type
        dolly.status = panel.status
        dolly.ui_schema = panel.ui_schema
        dolly.created_by = panel.created_by
        dolly._generate_schema = panel._generate_schema.copy()

        return dolly


class ReportAssembler:
    @classmethod
    def build_account_pb2_from_auth(cls, auth: "KioskJwtPayload"):
        from leyan_proto.digismart.common.account_pb2 import Account

        account_pb2 = Account.BasicView()
        account_pb2.id.value = auth.login_user_id
        account_pb2.name.value = auth.nick
        account_pb2.type = cast("Account.Type.ValueType", auth.login_user_type.value)

        return account_pb2

    @classmethod
    def which_form_status_pb2(cls, form_shops: List["FormShop"]):
        from robot_processor.form.models import FormShop

        status_set = set(map(attrgetter("status"), form_shops))

        if FormShop.Status.ENABLED in status_set:
            return bi_pb2.ListReportDataSourceResponse.DataSource.ENABLED
        elif FormShop.Status.DISABLED in status_set:
            return bi_pb2.ListReportDataSourceResponse.DataSource.DISABLED
        else:
            return bi_pb2.ListReportDataSourceResponse.DataSource.DELETED

    @classmethod
    def build_datasource_pb2(cls, form_id: List[int], form_sync_id: Optional[int]):
        datasource = report_pb2.ReportDataSource()
        datasource.form_ids.extend(form_id)
        if form_sync_id:
            datasource.sync_form_id.value = form_sync_id
        return datasource

    @classmethod
    def build_datasource_form_pb2(cls, form: "Form"):
        """构建无关联关系的工单"""
        return bi_pb2.ListReportDataSourceResponse.DataSource.Form(
            name=form.name,  # type: ignore[arg-type]
            status=cls.which_form_status_pb2(form),  # type: ignore[arg-type]
            data_source=cls.build_datasource_pb2(form_id=[form.id], form_sync_id=None),
        )

    @classmethod
    def build_aggregate_datasource_form_pb2(cls, form_list: List["Form"], form_sync_id: int):
        return bi_pb2.ListReportDataSourceResponse.DataSource.Form(
            name=first(form_list).name,  # type: ignore[arg-type]
            status=max(map(cls.which_form_status_pb2, form_list)),  # type: ignore[arg-type]
            data_source=cls.build_datasource_pb2(form_id=[form.id for form in form_list], form_sync_id=form_sync_id),
        )

    @classmethod
    def build_datasource_shop_pb2(cls, shop: "Shop"):
        return bi_pb2.ListReportDataSourceResponse.DataSource.Shop(
            sid=shop.sid, nick=shop.nick, title=shop.title, platform=shop.platform  # type: ignore[arg-type]
        )

    @classmethod
    def convert_form_view_to_shop_view(
        cls,
        form_view_list: list[bi_pb2.ListReportDataSourceResponse.DataSource.FormView],
    ):
        from collections import defaultdict

        def shop_ident(_shop):
            return _shop.sid, _shop.platform

        shop_map = dict()
        shop_dict: Dict[Tuple[str, str], list] = defaultdict(list)
        for form_view in form_view_list:
            for shop in form_view.shop:
                ident = shop_ident(shop)
                shop_map[ident] = shop
                shop_dict[ident].append(form_view)

        return [
            bi_pb2.ListReportDataSourceResponse.DataSource.ShopView(shop=shop_map[shop], form=form)
            for shop, form in shop_dict.items()
        ]
