from dataclasses import asdict
from typing import Any
from typing import List
from typing import cast

import arrow
import grpc
from leyan_proto.digismart.robot.symbol_table_pb2 import Filter as pb_Filter
from leyan_proto.digismart.robot.symbol_table_pb2 import Operand as pb_Operand
from leyan_proto.digismart.robot_web.exception_pool_pb2 import GetExceptionPoolFilterContextResponse
from leyan_proto.digismart.robot_web.exception_pool_pb2 import ListExceptionPoolV2Response
from leyan_proto.digismart.robot_web.exception_pool_pb2 import ListFilterContextOperatorsResponse
from leyan_proto.digismart.robot_web.exception_pool_pb2 import PageSort as pb_PageSort
from leyan_proto.digismart.robot_web.exception_pool_pb2_grpc import ExceptionPoolServiceServicer
from loguru import logger
from sqlalchemy import and_
from sqlalchemy.sql import func

from robot_processor.business_order.exception_rule import ExceptionBusinessOrder
from robot_processor.business_order.exception_rule import ExceptionRule
from robot_processor.business_order.models import Business<PERSON>rder
from robot_processor.constants import TIME_ZONE
from robot_processor.currents import g
from robot_processor.enums import AutoRetryRecordStatus
from robot_processor.ext import db
from robot_processor.form.models import JobAutoRetryRecord
from robot_processor.symbol_table.filters import query_filter_to_sql_criteria
from robot_processor.types.exception_pool import JobInfo
from robot_processor.utils import message_to_dict
from robot_processor.utils import unwrap_optional
from robot_processor.web_grpc_services.enums import AutoRetryStatus


class ExceptionPoolService(ExceptionPoolServiceServicer):
    @classmethod
    def build_job_brief(cls, job_brief_obj: JobInfo, rpa_mapper: dict | None = None):
        from leyan_proto.digismart.robot_web.exception_pool_pb2 import JobBrief

        job_brief = JobBrief()
        if job_id := job_brief_obj.get("job_id"):
            job_brief.job_id = str(job_id)
        if step_id := job_brief_obj.get("step_id"):
            job_brief.step_id = str(step_id)
        if uuid := job_brief_obj.get("uuid"):
            job_brief.uuid = str(uuid)
        if name := job_brief_obj.get("name"):
            job_brief.name = str(name)
        if rpa_name := job_brief_obj.get("rpa_name"):
            if rpa_mapper:
                job_brief.rpa_name = rpa_mapper.get(str(rpa_name), str(rpa_name))
            else:
                job_brief.rpa_name = str(rpa_name)
        if type_ := job_brief_obj.get("type"):
            try:
                job_brief.type = JobBrief.NodeType.Value(type_)
            except ValueError:
                job_brief.type = JobBrief.NodeType.NODE_TYPE_UNSPECIFIED

        return job_brief

    @classmethod
    def build_account_group(cls, account_group_obj: dict):
        from leyan_proto.digismart.common.account_pb2 import AccountGroup as AccountGroupMeta
        from leyan_proto.digismart.robot_web.exception_pool_pb2 import AccountGroup

        account_group = AccountGroup()
        if uuid_ := account_group_obj.get("uuid"):
            account_group.uuid = uuid_
        if name := account_group_obj.get("name"):
            account_group.name = name
        if type_ := account_group_obj.get("type"):
            try:
                type_ = AccountGroupMeta.Type.Value(type_)
            except ValueError:
                type_ = AccountGroupMeta.Type.ACCOUNT_GROUP_TYPE_UNSPECIFIED
            account_group.type = type_

        return account_group

    @classmethod
    def map_build_account_group(cls, account_group_obj_list: List[dict] | None):
        if account_group_obj_list is None:
            return None
        return map(cls.build_account_group, account_group_obj_list)

    @classmethod
    def build_account(cls, account_obj: dict | None):
        from leyan_proto.digismart.robot_web.exception_pool_pb2 import Account

        if account_obj is None:
            return None

        account = Account()
        if id_ := account_obj.get("id"):
            try:
                id_ = int(id_)
            except (TypeError, ValueError):
                id_ = 0
            account.id = id_
        if type_ := account_obj.get("type"):
            try:
                type_ = Account.AccountType.Value(type_)
            except ValueError:
                type_ = Account.AccountType.ACCOUNT_TYPE_UNSPECIFIED
            account.type = type_
        if nick := account_obj.get("nick"):
            account.nick = nick
        if phone := account_obj.get("phone"):
            account.phone = phone

        return account

    @classmethod
    def get_auto_retry_info_by_jobs(cls, job_ids):
        """
        输入job列表，返回各job的自动重试信息
        """
        sub_query_of_max_seq = (
            db.session.query(
                func.max(JobAutoRetryRecord.seq).label("max_seq"),
                JobAutoRetryRecord.job_id,
            )
            .filter(JobAutoRetryRecord.job_id.in_(job_ids))
            .group_by(JobAutoRetryRecord.job_id)
            .subquery()
        )

        max_seq_records = db.session.query(JobAutoRetryRecord).join(
            sub_query_of_max_seq,
            and_(
                JobAutoRetryRecord.job_id == sub_query_of_max_seq.c.job_id,
                JobAutoRetryRecord.seq == sub_query_of_max_seq.c.max_seq,
            ),
        )
        auto_retry_infos = {i.job_id: i.to_dict() for i in max_seq_records}
        return {job_id: auto_retry_infos.get(job_id, {}) for job_id in job_ids}

    @classmethod
    def build_exception_info(cls, exception_info_obj: dict):
        from leyan_proto.digismart.robot_web.exception_pool_pb2 import ExceptionInfo

        exception_info = ExceptionInfo()
        if id_ := exception_info_obj.get("id"):
            try:
                id_ = int(id_)
            except (TypeError, ValueError):
                id_ = 0
            exception_info.id = id_
        if type_ := exception_info_obj.get("type"):
            exception_info.type = str(type_)
        if reason := exception_info_obj.get("reason"):
            exception_info.reason = reason
        if suggestion := exception_info_obj.get("suggestion"):
            exception_info.suggestion = suggestion

        return exception_info

    def ListExceptionPool(self, request, context: grpc.ServicerContext):
        from leyan_proto.digismart.robot_web.exception_pool_pb2 import ExceptionPool
        from leyan_proto.digismart.robot_web.exception_pool_pb2 import ListExceptionPoolResponse
        from leyan_proto.digismart.robot_web.exception_pool_pb2 import PageInfo
        from leyan_proto.digismart.robot_web.exception_pool_pb2 import PageSort
        from sqlalchemy import and_
        from sqlalchemy import asc
        from sqlalchemy import desc

        from robot_processor.form.models import Form
        from robot_processor.form.models import FormShop

        with db.session.no_autoflush as session:
            query = session.query(ExceptionBusinessOrder)
            filters = []
            # 根据店铺筛选
            sid_list = request.sids if len(request.sids) else [g.auth.store_id]
            filters.extend(
                [
                    ExceptionBusinessOrder.org_id == g.auth.org_id,
                    ExceptionBusinessOrder.sid.in_(sid_list),
                ]
            )
            # 根据 bo 筛选
            if bo_id := request.business_order_id:
                filters.append(ExceptionBusinessOrder.business_order_id == bo_id)
            # 根据模板名筛选
            if request.form_name:
                filters.append(ExceptionBusinessOrder.name == request.form_name)
            if request.form_id:
                # 目前 ExceptionBusinessOrder 只有 form_name 这一属性可用，因此需要基于 id 来查询名称。
                form_and_form_shops: list[tuple[Form, FormShop]] = (
                    session.query(Form, FormShop)
                    .join(
                        Form,
                        Form.id == FormShop.form_id,
                    )
                    .filter(Form.id.in_([int(form_id) for form_id in request.form_id]))
                    .all()
                )
                form_names = set()
                sids = set()
                org_ids = set()
                for form, form_shop in form_and_form_shops:
                    form_names.add(form.name)
                    sids.add(form_shop.sid)
                    org_ids.add(form_shop.org_id)
                filters.append(
                    and_(
                        ExceptionBusinessOrder.org_id.in_(list(org_ids)),
                        ExceptionBusinessOrder.sid.in_(list(sids)),
                        ExceptionBusinessOrder.name.in_(list(form_names)),
                    )
                )
            # 根据异常类型筛选, 模糊搜索
            if request.reason:
                filters.append(ExceptionBusinessOrder.reason.like(f"%{request.reason}%"))
            # 根据当前步骤筛选
            if job := request.current_job_name:
                filters.append(ExceptionBusinessOrder.current_job_name == job)

            # 根据客服/客服组筛选
            # 根据创建人筛选
            if request.creator:
                filters.append(ExceptionBusinessOrder.creator_by(request.creator))  # type: ignore[arg-type]
            # 创建人所属用户组
            if request.creator_user_group_uuid:
                filters.append(
                    ExceptionBusinessOrder.creator_user_group_by(  # type: ignore[arg-type]
                        request.creator_user_group_uuid
                    )
                )
            # 创建人[平台账号]
            if request.HasField("plat_creator_user_id"):
                filters.append(
                    ExceptionBusinessOrder.platform_creator_by(  # type: ignore[arg-type]
                        request.plat_creator_user_id.value
                    )
                )
            # 创建人所属用户组[平台账号]
            if request.plat_creator_user_group_uuid:
                filters.append(
                    ExceptionBusinessOrder.platform_creator_user_group_by(  # type: ignore[arg-type]
                        request.plat_creator_user_group_uuid
                    )
                )
            # 创建人[乐言账号]
            if request.HasField("fs_creator_user_id"):
                filters.append(
                    ExceptionBusinessOrder.ly_creator_by(request.fs_creator_user_id.value)  # type: ignore[arg-type]
                )
            # 创建人所属用户组[乐言账号]
            if request.fs_creator_user_group_uuid:
                filters.append(
                    ExceptionBusinessOrder.ly_creator_user_group_by(  # type: ignore[arg-type]
                        request.fs_creator_user_group_uuid
                    )
                )

            # 根据当前步骤处理人筛选
            # 根据当前步骤处理人
            if assignee := request.assignee_name:
                filters.append(ExceptionBusinessOrder.assignee_by(assignee))  # type: ignore[arg-type]
            # 根据当前步骤处理人用户组
            if request.assignee_user_group_uuid:
                filters.append(
                    ExceptionBusinessOrder.assignee_user_group_by(  # type: ignore[arg-type]
                        request.assignee_user_group_uuid
                    )
                )
            # 根据当前步骤处理人[平台账号]
            if request.HasField("plat_assignee_user_id"):
                filters.append(
                    ExceptionBusinessOrder.platform_assignee_by(  # type: ignore[arg-type]
                        request.plat_assignee_user_id.value
                    )
                )
            # 根据当前步骤处理人所属用户组[平台账号]
            if request.plat_assignee_user_group_uuid:
                filters.append(
                    ExceptionBusinessOrder.platform_assignee_user_group_by(  # type: ignore[arg-type]
                        request.plat_assignee_user_group_uuid
                    )
                )
            # 根据当前步骤处理人[乐言账号]
            if request.HasField("fs_assignee_user_id"):
                filters.append(
                    ExceptionBusinessOrder.ly_assignee_by(request.fs_assignee_user_id.value)  # type: ignore[arg-type]
                )
            # 根据当前步骤处理人所属用户组[乐言账号]
            if request.fs_assignee_user_group_uuid:
                filters.append(
                    ExceptionBusinessOrder.ly_assignee_user_group_by(  # type: ignore[arg-type]
                        request.fs_assignee_user_group_uuid
                    )
                )

            # 根据创建时间
            if create_begin := request.create_begin_time:
                filters.append(ExceptionBusinessOrder.bo_created_at >= create_begin)
            if create_end := request.create_end_time:
                filters.append(ExceptionBusinessOrder.bo_created_at <= create_end)
            # 根据更新时间
            if update_begin := request.update_begin_time:
                update_begin = arrow.get(update_begin, tzinfo=TIME_ZONE).datetime
                filters.append(ExceptionBusinessOrder.updated_at >= update_begin)
            if update_end := request.update_end_time:
                update_end = arrow.get(update_end, tzinfo=TIME_ZONE).datetime
                filters.append(ExceptionBusinessOrder.updated_at <= update_end)
            # 根据 RPA
            if rpa_name := request.current_job_rpa_name:
                if rpa_name == "无":
                    rpa_name = ""
                filters.append(ExceptionBusinessOrder.current_job_rpa_name == rpa_name)
            # 根据自动重试状态
            if auto_retry_status := request.auto_retry_status:
                filters.append(ExceptionBusinessOrder.auto_retry_status == auto_retry_status)
            # 根据是否还会自动重试
            if request.HasField("will_retry"):
                if request.will_retry.value is True:
                    filters.append(ExceptionBusinessOrder.auto_retry_status == AutoRetryStatus.ENABLED_IN_CYCLE.value)
                else:
                    filters.append(ExceptionBusinessOrder.auto_retry_status != AutoRetryStatus.ENABLED_IN_CYCLE.value)
            # 排序
            order_by = {  # type: ignore[var-annotated]
                PageSort.UPDATE_ASC: asc(ExceptionBusinessOrder.updated_at),
                PageSort.UPDATE_DESC: desc(ExceptionBusinessOrder.updated_at),
                PageSort.CREATE_ASC: asc(ExceptionBusinessOrder.bo_created_at),
                PageSort.CREATE_DESC: desc(ExceptionBusinessOrder.bo_created_at),
                PageSort.PAGE_SORT_UNSPECIFIED: desc(ExceptionBusinessOrder.updated_at),
            }[request.page_sort]

            result = (
                query.filter(*filters)
                .order_by(order_by)
                .paginate(page=request.page.page, per_page=request.page.per_page)
            )
            page = PageInfo(
                page=result.page,
                per_page=result.per_page,
                pages=result.pages,
                total=result.total,
            )
            job_ids = [int(i.current_job_id) for i in result.items]
            auto_retry_info = ExceptionPoolService.get_auto_retry_info_by_jobs(job_ids)
            logger.info(f"auto_retry_info: {auto_retry_info}")
            data = [
                ExceptionPool(
                    template_id=item.bo_info.get("template_id", ""),
                    business_order_id=item.business_order_id,
                    business_order_name=item.name,
                    # 创建人信息
                    creator=self.build_account(item.platform_creator or item.ly_creator),
                    creator_user_group=self.map_build_account_group(
                        item.platform_creator_user_group or item.ly_creator_user_group
                    ),
                    plat_creator=(
                        self.build_account(item.platform_creator) if item.platform_creator is not None else None
                    ),
                    plat_creator_user_group=self.map_build_account_group(item.platform_creator_user_group),
                    fs_creator=self.build_account(item.ly_creator),
                    fs_creator_user_group=self.map_build_account_group(item.ly_creator_user_group),
                    current_job_brief=(
                        self.build_job_brief(item.current_job_info) if item.current_job_info is not None else None
                    ),
                    shop_name=item.shop_name,
                    shop_platform=item.shop_platform,
                    auto_retry_status=auto_retry_info.get(int(item.current_job_id)).get("description"),
                    task_type=item.task_type,  # type: ignore[arg-type]
                    # 当前步骤处理人信息
                    assignee=self.build_account(item.platform_assignee or item.ly_assignee),
                    assignee_user_group=self.map_build_account_group(
                        item.platform_assignee_user_group or item.ly_assignee_user_group
                    ),
                    plat_assignee=self.build_account(item.platform_assignee),
                    plat_assignee_user_group=self.map_build_account_group(item.platform_assignee_user_group),
                    fs_assignee=self.build_account(item.ly_assignee),
                    fs_assignee_user_group=self.map_build_account_group(item.ly_assignee_user_group),
                    updated_at=int(item.updated_at.timestamp()),
                    exception_info=(
                        self.build_exception_info(item.exception_info) if item.exception_info is not None else None
                    ),
                    is_processing=item.is_processing,
                    created_at=item.bo_created_at,  # type: ignore[arg-type]
                    will_retry=auto_retry_info.get(int(item.current_job_id)).get("status")
                    == AutoRetryRecordStatus.PENDING.value,
                    retry_timestamp=auto_retry_info.get(int(item.current_job_id)).get("eta"),
                )
                for item in cast(List[ExceptionBusinessOrder], result.items)
            ]

            return ListExceptionPoolResponse(
                success=True,
                code=0,
                msg="操作成功",
                data=ListExceptionPoolResponse.Data(page=page, page_data=data),
            )

    def ListExceptionPoolV2(self, request, context):
        from leyan_proto.digismart.robot_web.exception_pool_pb2 import ExceptionPool
        from robot_types.helper.sql import SQLCriteriaResolver
        from sqlalchemy import Integer
        from sqlalchemy import cast as sql_cast
        from sqlalchemy import select

        from robot_processor.symbol_table.registry.enum_options import rpa_resolver

        response = ListExceptionPoolV2Response(success=True)
        page_info = request.page
        page_info.page = page_info.page or 1
        page_info.per_page = page_info.per_page or 10
        query = (
            select(ExceptionBusinessOrder)
            .select_from(ExceptionBusinessOrder)
            .join(
                BusinessOrder,
                BusinessOrder.id == sql_cast(ExceptionBusinessOrder.business_order_id, Integer),
            )
        )
        sql_criteria_resolver = SQLCriteriaResolver(
            field_resolver_registry=ExceptionBusinessOrder.sql_field_resolver_registry,
            table_field_registry=ExceptionBusinessOrder.sql_table_field_registry,
        )
        where_clauses = query_filter_to_sql_criteria(request.filter, sql_criteria_resolver)
        query = query.where(ExceptionBusinessOrder.org_id == g.auth.org_id).where(where_clauses)
        logger.info("select with: {}".format(query.compile(compile_kwargs={"literal_binds": True})))
        count_query = query.with_only_columns(func.count())
        count: int = unwrap_optional(db.session.execute(count_query).scalar())
        page_info.total = count
        page_info.pages = count // page_info.per_page + 1
        response.data.page.CopyFrom(page_info)
        if count == 0:
            return response
        order_by = {
            pb_PageSort.PAGE_SORT_UNSPECIFIED: ExceptionBusinessOrder.id.desc(),
            pb_PageSort.UPDATE_ASC: ExceptionBusinessOrder.updated_at.asc(),
            pb_PageSort.UPDATE_DESC: ExceptionBusinessOrder.updated_at.desc(),
            pb_PageSort.CREATE_ASC: ExceptionBusinessOrder.created_at.asc(),
            pb_PageSort.CREATE_DESC: ExceptionBusinessOrder.created_at.desc(),
        }[request.page_sort]
        items = (
            db.session.execute(
                query.order_by(order_by)  # type: ignore[arg-type]
                .limit(page_info.per_page)
                .offset(page_info.per_page * (page_info.page - 1)),
            )
            .scalars()
            .all()
        )
        job_ids = [int(i.current_job_id) for i in items]
        auto_retry_info = ExceptionPoolService.get_auto_retry_info_by_jobs(job_ids)
        rpa_options = rpa_resolver.resolve_enum()
        rpa_mapper = dict((option.value, option.label) for option in rpa_options)
        data = [
            ExceptionPool(
                template_id=item.bo_info.get("template_id", ""),
                business_order_id=item.business_order_id,
                business_order_name=item.name,
                # 创建人信息
                creator=self.build_account(item.platform_creator or item.ly_creator),
                creator_user_group=self.map_build_account_group(
                    item.platform_creator_user_group or item.ly_creator_user_group
                ),
                plat_creator=(self.build_account(item.platform_creator) if item.platform_creator is not None else None),
                plat_creator_user_group=self.map_build_account_group(item.platform_creator_user_group),
                fs_creator=self.build_account(item.ly_creator),
                fs_creator_user_group=self.map_build_account_group(item.ly_creator_user_group),
                current_job_brief=(
                    self.build_job_brief(item.current_job_info, rpa_mapper)
                    if item.current_job_info is not None
                    else None
                ),
                shop_name=item.shop_name,
                shop_platform=item.shop_platform,
                auto_retry_status=auto_retry_info.get(int(item.current_job_id)).get("description"),
                task_type=rpa_mapper.get(item.task_type, item.task_type),
                # 当前步骤处理人信息
                assignee=self.build_account(item.platform_assignee or item.ly_assignee),
                assignee_user_group=self.map_build_account_group(
                    item.platform_assignee_user_group or item.ly_assignee_user_group
                ),
                plat_assignee=self.build_account(item.platform_assignee),
                plat_assignee_user_group=self.map_build_account_group(item.platform_assignee_user_group),
                fs_assignee=self.build_account(item.ly_assignee),
                fs_assignee_user_group=self.map_build_account_group(item.ly_assignee_user_group),
                updated_at=int(item.updated_at.timestamp()),
                exception_info=(
                    self.build_exception_info(item.exception_info) if item.exception_info is not None else None
                ),
                is_processing=item.is_processing,
                created_at=item.bo_created_at,  # type: ignore[arg-type]
                will_retry=auto_retry_info.get(int(item.current_job_id)).get("status")
                == AutoRetryRecordStatus.PENDING.value,
                retry_timestamp=auto_retry_info.get(int(item.current_job_id)).get("eta"),
            )
            for item in cast(List[ExceptionBusinessOrder], items)
        ]
        response.data.page_data.extend(data)
        return response

    def GetExceptionPoolFilterContext(self, request, context):
        from robot_types.helper.predefined import BizType

        from robot_processor.symbol_table.assembler import symbol_to_pb
        from robot_processor.symbol_table.services import FilterServicer

        symbol_context: dict[str, Any] = dict(org_id=g.auth.org_id)
        if g.login_user_detail is not None and (leyan_user := g.login_user_detail.get_bound_leyan_user()):
            symbol_context["leyan_user_id"] = unwrap_optional(leyan_user.user_id)
        response = GetExceptionPoolFilterContextResponse(success=True)
        if request.HasField("filter"):
            sids: list[str] = []
            shop_conditions = FilterServicer.filter_specified_condition(request.filter, "shop")
            for shop_condition in shop_conditions:
                match shop_condition.o:
                    case [pb_Filter.EQ]:
                        sids.append(message_to_dict(shop_condition.b.const.value)["sid"])  # type: ignore[unreachable]
                    case [pb_Filter.EQ_ANY]:
                        sids.extend(  # type: ignore[unreachable]
                            [shop_dict["sid"] for shop_dict in message_to_dict(shop_condition.b.const.value)]
                        )
            symbol_context["sids"] = sids

        symbol_table = BizType.EXCEPTION_FILTER_CONTEXT.provide_symbol_table(symbol_context)
        symbols = symbol_table.scope_symbols_mapper[symbol_table.root_scope.id].symbols
        for symbol in symbols:
            if symbol.name == "shop":
                symbol.extra = {"onchange": True}
            if symbol.name == "bo_created_at":
                symbol.extra = {"default": True}
            response.data.symbols.append(symbol_to_pb(symbol))
        return response

    def ListFilterContextOperators(self, request, context):
        from robot_types.helper.predefined import BizType

        from robot_processor.symbol_table.assembler import type_spec_from_pb
        from robot_processor.utils import get_dict_factory
        from robot_processor.utils import struct_wrapper

        response = ListFilterContextOperatorsResponse(success=True)
        operator_resolver = BizType.EXCEPTION_FILTER_CONTEXT.provide_operator_resolver()
        operators = operator_resolver.resolve(type_spec_from_pb(request.value.type_spec), request.value.var.path)
        for name, op in operators.items():
            if op.type_spec:
                expect = struct_wrapper(asdict(op.type_spec.to_deprecated(), dict_factory=get_dict_factory()))
            else:
                expect = None
            operand = pb_Operand(
                label=op.label,
                target=request.value.type_spec,
                operator=pb_Filter.Operator.Value(name.upper()),
                expect=expect,
            )
            if (op.extra or {}).get("default"):
                operand.default = True
            response.data.operators.append(operand)
        return response

    def OperateExceptionPool(self, request, context: grpc.ServicerContext):
        from leyan_proto.digismart.robot_web.exception_pool_pb2 import OperateExceptionPoolResponse

        from robot_processor.assistant.schema import AccountDetailV2
        from robot_processor.business_order.job_action import JobAction
        from robot_processor.business_order.utils.operate_debounce import JobOperateLock
        from robot_processor.enums import Action
        from robot_processor.error.base import BizError
        from robot_processor.logging import vars as log_vars

        operate_assistant = AccountDetailV2(
            user_type=g.auth.login_user_type.value,
            user_id=g.auth.login_user_id,
            user_nick=g.auth.nick,
        )
        log_vars.User = g.auth.nick
        action_mapping = {
            request.CLOSE: Action.close,
            request.COMPLETE: Action.complete,
            request.RETRY: Action.retry,
            request.ASSIGN: Action.assign,
        }

        errors = []
        for bo_info in request.bo_briefs:
            log_vars.JobId.set(bo_info.current_job_id)
            log_vars.BusinessOrderId.set(bo_info.bo_id)

            job_operate_lock = JobOperateLock(
                bo_id=bo_info.bo_id,
                current_job_id=bo_info.current_job_id,
                body=message_to_dict(bo_info),
                action=action_mapping.get(request.action),
            )
            job_action = JobAction(job_id=bo_info.current_job_id, operate_lock=job_operate_lock)
            try:
                if request.action == request.CLOSE:
                    job_action.close(
                        operate_assistant=operate_assistant,
                        operate_reason=request.reason,
                    )
                elif request.action == request.COMPLETE:
                    job_action.complete(
                        operate_assistant=operate_assistant,
                        operate_reason=request.reason,
                    )
                elif request.action == request.RETRY:
                    job_action.retry(
                        operate_assistant=operate_assistant,
                        operate_reason=request.reason,
                    )
                elif request.action == request.ASSIGN:
                    if not request.HasField("job_assistant"):
                        errors.append((bo_info.bo_id, "缺少指派客服信息"))
                        continue
                    job_action.assign(
                        operate_assistant=operate_assistant,
                        operate_reason=request.reason,
                        assignee_assistant=AccountDetailV2(
                            user_type=request.job_assistant.type,
                            user_id=request.job_assistant.id,
                            user_nick=request.job_assistant.nick,
                        ),
                    )
            except BizError as e:
                errors.append((bo_info.bo_id, e.biz_display))

        if errors:
            error_tmpl = "工单 {} 操作失败：{}"
            errors_str = [error_tmpl.format(*error) for error in errors]
            return OperateExceptionPoolResponse(
                success=False,
                code=400,
                msg="部分成功",
                data="\n".join(errors_str),
            )

        return OperateExceptionPoolResponse(
            success=True,
            code=0,
            msg="操作成功",
        )

    def ListExceptionRule(self, request, context: grpc.ServicerContext):
        from leyan_proto.digismart.robot_web.exception_pool_pb2 import ListExceptionRuleResponse
        from sqlalchemy import distinct
        from sqlalchemy import func
        from sqlalchemy import true

        with db.session.no_autoflush as session:
            exception_rule_query = (
                session.query(
                    distinct(
                        func.json_extract(ExceptionBusinessOrder.exception_info, "$.id").label("exception_rule_id")
                    )
                )
                .filter(ExceptionBusinessOrder.org_id == g.auth.org_id)
                .filter(ExceptionBusinessOrder.sid.in_(request.sids if len(request.sids) else [g.auth.store_id]))
                .filter(
                    ExceptionBusinessOrder.current_job_rpa_name == request.rpa.value
                    if request.HasField("rpa")
                    else true
                )
            )
            exception_rule_id_list = [exception_rule_id for exception_rule_id, in exception_rule_query]
            exception_rule_list = (
                session.query(ExceptionRule).filter(ExceptionRule.id.in_(exception_rule_id_list)).all()
            )

        return ListExceptionRuleResponse(
            success=True,
            msg="",
            data=ListExceptionRuleResponse.Data(
                exception_rules=[
                    ListExceptionRuleResponse.ExceptionRule(id=item.id, scopes=item.scopes, reason=item.reason)
                    for item in exception_rule_list
                ]
            ),
        )
