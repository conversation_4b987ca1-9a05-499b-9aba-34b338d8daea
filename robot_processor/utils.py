import json
import random
import re
import time
import uuid
import weakref
from collections.abc import Iterable
from collections.abc import Iterator
from collections.abc import MutableMapping
from dataclasses import asdict
from dataclasses import dataclass
from datetime import datetime
from decimal import ROUND_HALF_UP
from decimal import Decimal
from enum import Enum
from functools import wraps
from typing import Any
from typing import Callable
from typing import Dict
from typing import Generic
from typing import List
from typing import Literal
from typing import NoReturn
from typing import Optional
from typing import Protocol
from typing import Set
from typing import Tuple
from typing import TypedDict
from typing import TypeVar
from typing import Union
from typing import cast
from typing import overload
from uuid import UUID

import arrow
import jmespath
import result
from dacite import from_dict
from flask import current_app
from flask import jsonify
from google.protobuf.json_format import MessageToDict
from google.protobuf.message import Message
from leyan_cloud.babysitter import BabySitter
from leyan_tracing import get_tracer
from loguru import logger
from more_itertools import first
from pydantic import BaseModel
from pydantic import Field
from pydantic import root_validator
from pydantic import validator
from pydantic.class_validators import Validator
from pydantic.fields import <PERSON><PERSON>ield
from pydantic.generics import GenericModel
from requests import PreparedRequest
from requests import Request
from requests import Response as RequestsResponse
from sqlalchemy import func
from werkzeug import Response as WerkzeugResponse

from robot_processor.constants import TIME_ZONE

_NOT_FOUND = object()
T = TypeVar("T")
E = TypeVar("E")
F = TypeVar("F", bound=Callable)


def class_property(deco_func):
    if not isinstance(deco_func, (classmethod, staticmethod)):
        deco_func = classmethod(deco_func)

    class ClassPropertyDescriptor:
        def __init__(self, fn_get, fn_set=None):
            self.fn_get = fn_get
            self.fn_set = fn_set

        def __get__(self, instance, owner=None):
            if owner is None:
                owner = type(instance)
            return self.fn_get.__get__(instance, owner)()

        def __set__(self, instance, value):
            if not self.fn_set:
                raise AttributeError
            owner = type(instance)
            return self.fn_set.__get__(instance, owner)(value)

        def setter(self, func_):
            if not isinstance(func_, (classmethod, staticmethod)):
                func_ = classmethod(func_)
            self.fn_set = func_

            return self

    return ClassPropertyDescriptor(deco_func)


def json_length(*args, **kwargs):
    db_uri = current_app.config["SQLALCHEMY_DATABASE_URI"]
    if "sqlite" in db_uri:
        return func.json_array_length(*args, **kwargs)
    return func.json_length(*args, **kwargs)


def convert_product_widget_data_as_array(value):
    if isinstance(value, dict):
        return [value]
    return value


def convert_select_widget_data_to_old(value):
    """
    输入： select组件的值  为List[str] or List[List[str]] (old) 或者
    List[dict] or List[List[dict]]
    dict的格式为{"label": XXX, "value":"xxx"}
    """
    if not value:
        return value
    if NewSelectData.is_new_select_data_type(value):
        from robot_processor.form.widget_condition import ValueType

        if isinstance(first(value, []), list):
            return [ValueType.value_format(ValueType.ENUM, item) for item in value]
        else:
            return ValueType.value_format(ValueType.ENUM, value)
    else:
        return value


class SelectElement(BaseModel):
    label: str
    value: str


class NewSelectData(BaseModel):
    value: Union[List[SelectElement], List[List[SelectElement]]]

    @classmethod
    def is_new_select_data_type(cls, value):
        try:
            cls(value=value)
            return True
        except ValueError:
            return False


def convert_data(job):
    from robot_processor.enums import DataType

    business_order = job.business_order
    # FIXME 加入了我的工单(人工审批)后, data 要存在 job 身上
    data = business_order.data

    raw_step_v2 = job.raw_step_v2
    arguments: List[Dict[str, Any]] = raw_step_v2["task"].get("arguments", [])

    _product_key = None
    for _schema in job.raw_ui_schema:
        if not _schema.get("before", False) and _schema.get("type", "") == "product":
            _product_key = _schema.get("key")
            break

    kwargs = {}
    for arg in arguments:
        key = raw_step_v2["key_map"].get(arg["name"])
        if DataType.is_input(arg["data_type"]):
            # get from step
            value = key
        elif arg["data_type"] == DataType.SELECT.value:
            # get by uuid
            value = data.get(key)
        elif arg["data_type"] == DataType.SELECT_WITH_FIXED.value:
            value = data.get(key)
        else:
            value = None
        if value is not None:
            kwargs[arg["name"]] = value
    res = kwargs or data
    for key in res:
        value = convert_select_widget_data_to_old(res[key])
        res[key] = value

        if _product_key is not None and key == _product_key:  # 商品组件全部转换为数组形式
            value = convert_product_widget_data_as_array(res[key])
            res[key] = value
    return res


def convert_vip_data(job):
    _product_key = None
    for _schema in job.raw_ui_schema:
        if not _schema.get("before", False) and _schema.get("type", "") == "product":
            _product_key = _schema.get("key")
            break
    res = job.business_order.data
    for key in res:
        value = convert_select_widget_data_to_old(res[key])
        res[key] = value
        if _product_key is not None and key == _product_key:  # 商品组件全部转换为数组形式
            value = convert_product_widget_data_as_array(res[key])
            res[key] = value
    return res


class ResultUtil:
    @staticmethod
    def get_status_code(res: dict):
        return res["status_code"]

    @staticmethod
    def get_data(res: dict):
        return res["data"]

    @staticmethod
    def get_reason(res: dict):
        return res["reason"]

    @staticmethod
    def not_success(res: dict):
        return ResultUtil.get_status_code(res) != 200

    @staticmethod
    def build_dict(status_code: int, data, reason: str):
        return {"status_code": status_code, "data": data, "reason": reason}

    @staticmethod
    def build_cloud_rep(rep_dict: dict):
        if ResultUtil.not_success(rep_dict):
            return jsonify(success=False, errorMsg=ResultUtil.get_reason(rep_dict))
        return jsonify(success=True, data=ResultUtil.get_data(rep_dict))

    @staticmethod
    def build_error_rep(rep_dict: dict):
        return jsonify(reason=ResultUtil.get_reason(rep_dict)), ResultUtil.get_status_code(rep_dict)


def flatten(lst):
    result = []

    def flat(items):
        for item in items:
            if isinstance(item, Iterable) and not isinstance(item, (str, bytes, dict)):
                flat(item)
            else:
                result.append(item)

    flat(lst)
    return result


class StepProtocol(Protocol):
    name: str
    step_uuid: str
    next_step_ids: List[str]
    prev_step_ids: List[str]


StepT = TypeVar("StepT", bound=StepProtocol)


def topological_sorting(graph: Dict[str, Set[str]]) -> Tuple[List[str], Dict[str, int]]:
    """拓扑排序

    Arguments:
        graph: key 为当前 node, value 为依赖的 node
    Returns:
        拓扑排序列表 List[key]
    Examples:
        >>> graph_example = {"D": {"B", "C"}, "C": {"A"}, "B": {"A"}}
        >>> topological_sorting(graph_example)
        (['A', 'C', 'B', 'D'], {'D': 2, 'C': 1, 'B': 1, 'A': 0})
    """
    nodes = list(graph.keys())
    nodes.extend(set(flatten(graph.values())).difference(flatten(graph.keys())))
    in_degrees = dict((node, 0) for node in nodes)
    node_level = dict((node, 0) for node in nodes)
    for node, deps in graph.items():
        in_degrees[node] = len(deps)
    results = []

    current_level = 0

    def add_node_into_results(_node):
        nonlocal current_level

        in_degrees[_node] -= 1
        results.append(_node)
        node_level[_node] = current_level
        for _dep_node, _deps in graph.items():
            if _node in _deps:
                in_degrees[_dep_node] -= 1

    max_times = 1000
    current_times = 1
    last_round = False
    while current_times < max_times:
        for in_degree_0_node in list(filter(lambda node: in_degrees[node] == 0, in_degrees)):
            add_node_into_results(in_degree_0_node)
        if last_round:
            break

        current_times += 1
        current_level += 1

        if len(list(filter(lambda node: in_degrees[node] > 0, in_degrees))) == 0:
            last_round = True

    else:
        raise Exception("非有效拓扑图")

    return results, node_level


def sort_steps(steps: List[StepT]) -> List[StepT]:
    """输出步骤的拓扑排序后的结果

    增加了排序信息
    """
    from collections import defaultdict

    step_map = {step.step_uuid: step for step in steps}
    graph = defaultdict(set)
    if len(steps) == 1:
        return steps
    for step in steps:
        for next_step_uuid in step.next_step_ids:
            graph[next_step_uuid].add(step.step_uuid)
        for prev_step_uuid in step.prev_step_ids:
            graph[step.step_uuid].add(prev_step_uuid)

    unsorted_step_uuid, step_uuid_level_map = topological_sorting(graph)
    unsorted_steps = []
    for step_uuid in unsorted_step_uuid:
        if step_uuid in step_map:
            unsorted_steps.append(step_map[step_uuid])
    sorted_steps: List[StepT] = []
    same_level_steps: List[StepT] = []
    for _step in unsorted_steps:
        if len(same_level_steps) == 0:
            same_level_steps.append(_step)
        elif step_uuid_level_map[same_level_steps[0].step_uuid] == step_uuid_level_map[_step.step_uuid]:
            same_level_steps.append(_step)
        else:
            sorted_steps = [
                *sorted_steps,
                *sorted(same_level_steps, key=lambda _s: _s.name),
            ]
            same_level_steps = [_step]
    sorted_steps = [*sorted_steps, *sorted(same_level_steps, key=lambda _s: _s.name)]

    return sorted_steps


def health_check(skip_apollo=False):
    if skip_apollo:
        enable_health_check = True
    else:
        enable_health_check = current_app.config.get("ENABLE_HEALTH_CHECK", False)  # noqa
    if enable_health_check:
        baby_sitter = BabySitter()
        baby_sitter.start_async()


def timestamp_ms() -> int:
    return int(arrow.utcnow().float_timestamp * 1000)


def arrow_now() -> arrow.Arrow:
    return arrow.now(TIME_ZONE)


def datetime_now():
    return arrow_now().datetime


def current_trace_id():
    trace_id = ""
    if (tracer := get_tracer()) and (span := tracer.active_span):
        trace_id = "{:x}".format(span.trace_id)

    return trace_id


def check_uuid4(value) -> bool:
    try:
        return uuid.UUID(value).version == 4
    except ValueError:
        return False


def combine_address_with_town(town, address):
    town = town or ""
    address = address or " "
    if address.startswith(town):
        return address
    return town + address


def filter_none(data):
    if isinstance(data, dict):
        return dict((key, filter_none(value)) for key, value in data.items() if value is not None)
    elif isinstance(data, list):
        return [filter_none(item) if isinstance(item, dict) else item for item in data if item is not None]
    else:
        return data


def filter_dict(data: dict, keys: Iterable[str]):
    return {k: v for k, v in data.items() if k in keys}


def dict_diff(old: dict, new: dict):
    from json import dumps

    from robot_types.helper import serialize

    remove_key = set(old.keys()) - set(new.keys())
    add_key = set(new.keys()) - set(old.keys())
    update_key = set()
    for key in set(old.keys()) & set(new.keys()):
        if dumps(serialize(old[key])) != dumps(serialize(new[key])):
            update_key.add(key)
    return {
        "remove": {key: old[key] for key in remove_key},
        "add": {key: new[key] for key in add_key},
        "update": {key: {"old": old[key], "new": new[key]} for key in update_key},
    }


def is_uuid(string) -> bool:
    try:
        UUID(string)
        return True
    except ValueError:
        return False


class ApiDegrade:
    class DegradeConfig(TypedDict):
        type: Literal["org", "sid", "all"]
        target: list
        return_value: Any

    class DegradeError(Exception):
        def __init__(self, response):
            self.response = response

    def degrade(self):
        from flask import request

        from robot_processor.currents import g

        if not request.endpoint:
            return None
        api_degrade_config = current_app.config.get("API_DEGRADE", {})
        if request.endpoint not in api_degrade_config:
            return None

        config = cast(ApiDegrade.DegradeConfig, api_degrade_config[request.endpoint])
        strategy = config.get("type")
        targets = [str(t) for t in config.get("target", [])]
        degrade_error = self.DegradeError(config.get("return_value", {}))
        if strategy == "all":
            raise degrade_error
        elif strategy == "org" and g.auth and str(g.auth.org_id) in targets:
            raise degrade_error
        elif strategy == "sid" and g.auth and str(g.auth.store_id) in targets:
            raise degrade_error


api_degrade = ApiDegrade()


def import_this_module(path, prefix):
    from importlib import import_module
    from pkgutil import walk_packages

    for _path in walk_packages(path, prefix):
        import_module(_path.name)


def underline_to_camel(origin: str):
    return re.sub(r"(_\w)", lambda match: match.group(1)[1].upper(), origin)


def is_buyer_nick_encrypt(buyer_nick: str) -> bool:
    if match := re.match(r"^([^\*]+)(\**)$", buyer_nick):
        return bool(match.groups()[-1])
    return True


def readable_enum(enum) -> str:
    # 测试发现 '{}'.format(enum) 和 f'{enum}' 总是展示 enum.value 而不是 name, 在日志中可读性太差
    # https://git.leyantech.com/digismart/robot-processor/-/issues/104
    if isinstance(enum, Enum):
        return f"{enum.__class__.__name__}.{enum.name}"
    return str(enum)


AnyClass = TypeVar("AnyClass", bound=type)


def get_all_subclasses(cls: AnyClass) -> Iterator[AnyClass]:
    for subclass in cls.__subclasses__():
        yield from get_all_subclasses(subclass)
        yield subclass


def get_nearest_prev_human_job_id(job) -> Optional[int]:
    """
    获取最近的上一个可重复执行人工任务的 job id
    """
    for prev_job in job.get_prev_jobs():
        if prev_job is None:
            return None
        if prev_job.is_human() and prev_job.step.can_retry:
            return prev_job.id
        if not prev_job.step.can_retry:
            return None
    return None


def proto_to_json(proto: Message) -> str:
    """
    将 proto 转换为 json 字符串，该方法主要用于日志打印.

    :param proto: proto message 对象
    :return: string
    """
    try:
        return json.dumps(MessageToDict(proto), ensure_ascii=False)
    except Exception:
        return str(proto)


def message_to_dict(message: Message) -> dict:
    return MessageToDict(message, including_default_value_fields=True, preserving_proto_field_name=True)


def url_encode(url: str) -> str:
    """url 中含有中文时，需要进行 encode"""
    from functools import partial
    from urllib.parse import quote
    from urllib.parse import urlparse

    if not url:
        return url
    do_quote = partial(quote, safe=";/?:@&=+$,", encoding="utf-8")

    try:
        parsed = urlparse(url)
        parsed = parsed._replace(path=do_quote(parsed.path))
        parsed = parsed._replace(query=do_quote(parsed.query))

        return parsed.geturl()
    except:  # noqa pylint: disable=bare-except
        logger.exception("url encode error.")
        return url


def get_obj_hash(obj):
    import hashlib
    import json

    md = hashlib.md5(json.dumps(obj, sort_keys=True).encode())
    return md.hexdigest()


class Response(GenericModel, Generic[T]):
    succeed: bool = Field(default=True)
    code: int = Field(default=200, description="一般用于错误码")
    msg: Optional[str] = Field(default=None, description="一般用于错误信息的说明")
    data: Optional[T] = Field(default=None, description="业务返回")

    @classmethod
    def Success(cls, data, msg=None, **kwargs):
        return cls(succeed=True, msg=msg, data=data, **kwargs)

    @classmethod
    def Failed(cls, msg, data=None, **kwargs):
        return cls(succeed=False, msg=msg, data=data, **kwargs)


class GenericResponse(GenericModel, Generic[T]):
    succeed: bool = Field(default=True)
    code: int = Field(default=200, description="一般用于错误码")
    msg: Optional[str] = Field(default=None, description="一般用于错误信息的说明")
    data: Optional[T] = Field(default=None, description="业务返回")

    @classmethod
    def Success(cls, data, msg=None, **kwargs):
        return cls(succeed=True, msg=msg, data=data, **kwargs)

    @classmethod
    def Failed(cls, msg, data=None, **kwargs):
        return cls(succeed=False, msg=msg, data=data, **kwargs)

    success: bool = Field(default=False, description="deprecated")
    reason: Optional[str] = Field(default=None, description="deprecated")
    error_display: Optional[str] = Field(default=None, description="deprecated")

    @root_validator(pre=True)
    def set_deprecated(cls, values):
        values["success"] = values.get("succeed", True)
        values["error_display"] = values.get("msg", None)
        values["reason"] = values.get("msg", None)
        return values

    @validator("code", pre=True)
    def set_code(cls, code):
        return code if code is not None else cls.__fields__["code"].default


BaseResponse = GenericResponse[Any]


def wrap_status_code_in_response_body(response: WerkzeugResponse) -> WerkzeugResponse:
    """将异常响应转化为200 的json响应, 并将异常状态码放入响应体中.

    为什么做这种转换？
    淘宝小程序的接口要求返回的状态码必须是200，否则前端会报错，拿不到任何有效业务信息。
    """
    status_code = response.status_code

    jsonify_mimetype = "application/json"
    if response.mimetype == jsonify_mimetype or status_code != 200:
        # 异常响应转化为200 的json响应
        response.mimetype = jsonify_mimetype
        data = response.get_json(silent=True) or {}
        data["status_code"] = status_code
        serialized_response_body = json.dumps(data)
        response.data = serialized_response_body
        response.status_code = 200
    return response


def get_district_or_zone(receiver: dict) -> str:
    """
    Python 字典的 get(key, default) 方法.
    如果 key 在字典中，且其数值为 None，则还会返回 None。不在字典里才会返回默认值。
    example：
    v = {"zone": None}
    z = v.get("zone", "")                 assert z is None
    d = v.get("district", "")             assert d == ""
    """
    if (district := receiver.get("district")) is not None:
        return district
    elif (zone := receiver.get("zone")) is not None:
        return zone
    else:
        return ""


def no_throw(fn: Callable, *args, **kwargs):
    """检查函数是否抛出异常，不抛出异常则返回 True，否则返回 False"""
    try:
        fn(*args, **kwargs)
        return True
    except:  # noqa
        return False


def ts2date(ts: Union[float, int], date_format="YYYY-MM-DD HH:mm:ss") -> str:
    return arrow.get(ts).to("Asia/Shanghai").format(date_format)


def text_append_now_datetime(text: str, date_format: str = "YYYY-MM-DD HH:mm:ss") -> str:
    now_datetime = arrow.get(time.time()).to("Asia/Shanghai").format(date_format)
    return "{} {}".format(text, now_datetime)


def get_nonce() -> str:
    return uuid.uuid4().hex


def db_session_rollback():
    from robot_processor.ext import db

    try:
        db.session.rollback()
    except:  # noqa pylint: disable=bare-except
        db.session.remove()


@overload
def use_state() -> bool: ...


@overload
def use_state(default: T) -> Tuple[Callable[[], T], Callable[[T], None]]: ...


def use_state(default=False):
    value = default

    def getter():
        return value

    def setter(new_value):
        nonlocal value
        value = new_value

    return getter, setter


def copy_current_app_context(fn: F) -> F:
    # ref: https://github.com/dchevell/flask-executor/blob/master/flask_executor/executor.py
    import copy

    from flask import Flask
    from flask.globals import _cv_app
    from flask.globals import current_app
    from flask.globals import g
    from werkzeug.local import LocalProxy

    app = cast(LocalProxy[Flask], current_app)._get_current_object()
    _g = copy.copy(g)

    @wraps(fn)
    def wrapper(*args, **kwargs):
        with app.app_context():
            appctx = _cv_app.get(None)
            appctx.g = _g  # type: ignore[union-attr]
            return fn(*args, **kwargs)

    return wrapper  # type: ignore[return-value]


def unwrap_optional(value: T | None) -> T:
    """
    用于我们明显知道 Optional type 的值不为 None , 但又不想或者不好做显式地 check null 的特殊情况, 强制转成对应的非 Optional 的类型
    """
    return cast(T, value)


ModelClass = TypeVar("ModelClass", bound=type[BaseModel])


def make_fields_optional(model: ModelClass) -> ModelClass:
    """将 pydantic model 的所有字段都设置为运行时可选, 可以为 None.

    为什么这么做？
    对于 jst/wdt 等第三方 erp 的接口:

    1. 我们无法强制要求其返回的数据必须包含我们定义的字段
    2. 我们希望通过静态建模来确保业务逻辑代码没有明显问题
    3. 尽可能减少代码中的 if not None 判断

    所以通过这个 decorator 来达到既有静态类型安全，又尽可能包容运行时数据的目的。

    注意⚠️⚠️⚠️：除了为第三方接口的响应数据建模之外，不要在其他地方使用这个 decorator。

    ref: https://git.leyantech.com/digismart/robot-processor/-/issues/241
    """
    for field in model.__fields__.values():
        field.allow_none = True
        field.required = False
    return model


def omit_none_fields_before_validate(*fields: str) -> Callable[[ModelClass], ModelClass]:
    """滤掉 None 值的字段, 对应的字段最终会被转换成默认值"""

    def set_default_if_none(cls, value, field: ModelField):
        return value if value is not None else field.get_default()

    def decorator(model: ModelClass) -> ModelClass:
        for field in fields:
            if field_info := model.__fields__.get(field):
                field_info.class_validators["set_default_if_none"] = Validator(
                    set_default_if_none, pre=True, check_fields=True
                )
                field_info.populate_validators()
        return model

    return decorator


def request_to_log(request: Union[Request, PreparedRequest]) -> str:
    from json import dumps

    if isinstance(request, Request):
        request_info = dict(
            method=request.method,
            url=request.url,
            headers=request.headers,
            params=request.params,
            body=request.data,
            json=request.json,
        )
    elif isinstance(request, PreparedRequest):
        request_info = dict(
            method=request.method,
            url=request.url,
            headers=request.headers,
            body=request.body.decode() if isinstance(request.body, bytes) else request.body,
        )
    else:
        request_info = dict(raw=str(request))  # type: ignore[unreachable]

    return dumps(request_info, ensure_ascii=False, default=str)


def response_to_log(response: RequestsResponse) -> str:
    """将 requests.Response 对象转换为日志字符串"""
    import json

    from requests.exceptions import JSONDecodeError

    try:
        response_obj = response.json()
        response_str = json.dumps(response_obj, ensure_ascii=False, sort_keys=True)
    except JSONDecodeError:
        response_str = response.text

    # 接口请求失败
    if not response.ok:
        error_template = "status_code: {}, response: {}"
        return error_template.format(response.status_code, response_str)
    # 业务逻辑失败
    else:
        return response_str


def get_generic_types(tp):
    from typing import get_args
    from typing import get_origin

    from result import Err
    from result import Ok

    origin = get_origin(tp)
    if origin is None:
        return Err(None)
    else:
        return Ok(get_args(tp))


def raise_exception(exception: Exception) -> NoReturn:
    raise exception


def tap(fn: Callable[[T], Any]) -> Callable[[T], T]:
    def decorator(argument_to_return: T) -> T:
        fn(argument_to_return)
        return argument_to_return

    return decorator


def normalize_buyer_uid(uid: str | None) -> str | None:
    """去除买家昵称中的 cntaobao 前缀."""
    if uid and uid.startswith("cntaobao"):
        return uid[8:]
    return uid


def list_result_to_result_list(list_result: Iterable[result.Result[T, E]]) -> result.Result[list[T], E]:
    """将 list[Result] 转换为 Result[list]，如果有 Err 则返回 Err，否则返回 Ok(list)

    Examples:
        >>> list_result_to_result_list([result.Ok(1), result.Ok(2), result.Ok(3)])
        Ok([1, 2, 3])
        >>> list_result_to_result_list([result.Ok(1), result.Err("error"), result.Ok(3)])
        Err("error")
    """
    ok_list = []
    for item in list_result:
        if item.is_ok():
            ok_list.append(item.unwrap())
        else:
            return result.Err(item.unwrap_err())

    return result.Ok(ok_list)


def cached_instance_method(user_function: F, /) -> F:
    """Cache decorator that keeps a weak reference to `self`"""

    caches: MutableMapping[Any, Any] = weakref.WeakKeyDictionary()
    kwd_mark = object()

    def wrapper(self, *args, **kwargs):
        cache = caches.setdefault(self, {})
        # ref: https://stackoverflow.com/questions/10220599/how-to-hash-args-kwargs-for-function-cache
        key = args + (kwd_mark,) + tuple(sorted(kwargs.items()))
        if key not in cache:
            cache[key] = user_function(self, *args, **kwargs)
        return cache.get(key)

    return cast(F, wrapper)


def req_to_curl(response):
    req = response.request

    command = "curl -X {method} -H {headers}  '{uri}'"
    method = req.method
    uri = req.url
    # data = req.body
    _headers = ['"{0}: {1}"'.format(k, v) for k, v in req.headers.items()]
    headers = " -H ".join(_headers)
    return command.format(method=method, headers=headers, uri=uri)


@wraps(jmespath.search)
def jmespath_search(expression, data, options=None):
    """使用 jmespath 查询语法查询数据

    Why:
        在 3.11 版本发生变更: population 必须为一个序列。 不再支持将集合自动转换为列表。
        oss2 模块依赖 jmespath<1.0.0 and >=0.9.3, 在这些版本的 jmespath 中会存在 population 为集合的情况。

    References:
        https://docs.python.org/zh-cn/3/library/random.html#random.sample
    """

    class Parser(jmespath.parser.Parser):
        def _free_cache_entries(self):
            for key in random.sample(list(self._CACHE.keys()), int(self._MAX_SIZE / 2)):  # type: ignore[attr-defined]
                self._CACHE.pop(key, None)  # type: ignore[attr-defined]

    return Parser().parse(expression).search(data, options=options)


def quote_jmespath(raw_path: str) -> str:
    """jmespath 不支持空格/-等特殊字符"""
    paths = raw_path.split(".")
    normalized = []
    for path in paths:
        if not re.match(r'".*".*', path):
            match = re.match(r"(?P<path>[^\[]*)(?P<array_identifier>\[.*\])*$", path)
            quoted_path = match.groupdict()["path"]  # type: ignore[union-attr]
            array_identifier = match.groupdict()["array_identifier"] or ""  # type: ignore[union-attr]
            normalized.append(f'"{quoted_path}"{array_identifier}')
        else:
            normalized.append(path)
    return ".".join(normalized)


def unquote_jmespath(raw_path: str) -> str:
    paths = raw_path.split(".")
    normalized = []
    for path in paths:
        if match := re.match(r'"(?P<path>.*)"(?P<array_identifier>.*)', path):
            normalized.append("{}{}".format(match.groupdict()["path"], match.groupdict()["array_identifier"]))
        else:
            normalized.append(path)
    return ".".join(normalized)


def ensure_mirror_of_pb_enum(pb_enum_cls):
    """确保一个 enum class 是一个 protobuf enum 的镜像, 即两者的值和名称都一致.

    为什么要做 mirror? 因为 protobuf 的 enum 类型并没有继承 python 的 enum.Enum, 只是一个 int, 与 python 生态中的 sqlalchemy 等库不兼容.
    """
    PYE = TypeVar("PYE", bound=Enum)

    def decorator(py_enum_cls: type[PYE]):
        pb_members = dict(pb_enum_cls.items())
        py_members = dict(py_enum_cls.__members__)
        assert pb_members == py_members, f"{pb_enum_cls}({pb_members}) != {py_enum_cls}({py_members})"
        return py_enum_cls

    return decorator


def timestamp_to_log(timestamp: int):
    return arrow.get(timestamp, tzinfo="Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss")


def amount_diff(a, b):
    from decimal import ROUND_HALF_UP
    from decimal import Decimal

    a_decimal = Decimal(str(a)).quantize(Decimal("0.00"), rounding=ROUND_HALF_UP)
    b_decimal = Decimal(str(b)).quantize(Decimal("0.00"), rounding=ROUND_HALF_UP)
    amount_difference: Decimal = a_decimal - b_decimal
    return amount_difference


def empty_str_as_none(value):
    if isinstance(value, str) and value == "":
        return None
    return value


def with_app_context(fn):
    """装饰器，为函数添加 app context"""

    @wraps(fn)
    def wrapper(*args, **kwargs):
        from flask import has_app_context

        from robot_processor.app import app

        try:
            if has_app_context():
                return fn(*args, **kwargs)
            else:
                with app.app_context():
                    return fn(*args, **kwargs)
        finally:
            from leyan_logging import context

            context.clear()

    return wrapper


def string_wrapper(value):
    from google.protobuf.wrappers_pb2 import StringValue

    if value is None:
        return None
    return StringValue(value=str(value))


def int_wrapper(value: int | None):
    from google.protobuf.wrappers_pb2 import Int32Value

    if value is None:
        return None
    return Int32Value(value=value)


def uint_wrapper(value: int | None):
    from google.protobuf.wrappers_pb2 import UInt32Value

    if value is None:
        return None
    return UInt32Value(value=value)


def struct_wrapper(value: dict):
    from google.protobuf.json_format import ParseDict
    from google.protobuf.struct_pb2 import Struct

    return ParseDict(value, Struct())


def pb_value_wrapper(value: Any):
    from google.protobuf.json_format import ParseDict
    from google.protobuf.struct_pb2 import Value

    if value is None:
        return None
    return ParseDict(value, Value())


def get_dict_factory(exclude_none: bool = True, exclude_fields: set[str] | None = None):
    exclude_fields = exclude_fields or set()

    def dict_factory(x):
        return {k: v for (k, v) in x if ((exclude_none is False or v is not None) and (k not in exclude_fields))}

    return dict_factory


class Amount(Decimal):
    @classmethod
    def sum(cls, args: list[str | int | float | Decimal]):
        return cls(sum([Decimal(each) for each in args]))

    def divide(self, other):
        return Amount(self / Decimal(other))

    def multipy(self, other):
        return Amount(self * Decimal(other))

    def add(self, other):
        return Amount(self + Decimal(other))

    def subtract(self, other):
        return Amount(self - Decimal(other))

    def equal(self, other):
        precision = 2
        return self.format(precision=precision) == Amount(other).format(precision=precision)

    def format(self, precision=None):
        if precision is None:
            return str(self)
        else:
            return str(self.quantize(Decimal(f"0.{'0' * precision}"), rounding=ROUND_HALF_UP))


def safe_datetime(val: str | int | None):
    if val is None:
        return None
    if val == "":
        return None
    try:
        return arrow.get(val).naive
    except:  # noqa
        return None


def safe_enum_name(enum_val):
    if enum_val is None:
        return None
    return enum_val.name


@dataclass
class DataclassMixin:
    def to_dict(self, exclude_none=True):
        self_dict = asdict(self)
        if exclude_none:
            return filter_none(self_dict)
        else:
            return self_dict

    from_dict = classmethod(from_dict)  # type: ignore[var-annotated]


def get_time_spent_hours(since: datetime, until: datetime | None = None):
    until = datetime.now() if until is None else until
    return int((until - since).total_seconds() / 3600)
