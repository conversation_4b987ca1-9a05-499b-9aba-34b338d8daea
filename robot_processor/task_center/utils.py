from datetime import datetime
from enum import StrEnum
from typing import Any
from typing import Named<PERSON><PERSON>le
from typing import cast
from urllib.parse import unquote

import arrow
from loguru import logger
from sqlalchemy.orm.base import Mapped
from sqlalchemy.orm.query import Query
from sqlalchemy.sql import and_
from sqlalchemy.sql import asc as sql_asc
from sqlalchemy.sql import desc as sql_desc
from sqlalchemy.sql import func
from sqlalchemy.sql import not_
from sqlalchemy.sql import or_
from sqlalchemy.sql.elements import ColumnElement
from sqlalchemy.sql.elements import UnaryExpression

from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import JobApprover
from robot_processor.business_order.schema import FilterSchema
from robot_processor.business_order.seller.enums import MyBusinessOrdersTab
from robot_processor.business_order.seller.query_by_accessor import AccessorQuery
from robot_processor.business_order.utils.global_search_bo import global_search_by_multi_keywords
from robot_processor.client.conf import app_config
from robot_processor.database_util import QueryExplainer
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import Creator
from robot_processor.enums import FromType
from robot_processor.enums import JobStatus
from robot_processor.enums import StepType
from robot_processor.ext import db
from robot_processor.form.models import Form
from robot_processor.form.models import Step
from robot_processor.form.models import WidgetInfo
from robot_processor.shop.models import Shop
from robot_processor.task_center import errors


class CanJoinedClass(StrEnum):
    """
    可以 join 的类
    """

    JOB = "JOB"
    STEP = "STEP"
    JOB_APPROVER = "JOB_APPROVER"


class QueryCondition(NamedTuple):
    condition: ColumnElement
    need_joined_classes: set[CanJoinedClass]


class PageTabFilter:
    """
    tab 页的过滤。
    """

    def __init__(self, accessor_query: AccessorQuery) -> None:
        self.accessor_query = accessor_query

    def get_unprocessed_tab_condition(self) -> QueryCondition:
        """
        待我处理
        """
        return QueryCondition(
            and_(
                self.accessor_query.job_assignee_condition(),
                BusinessOrder.status.in_(BusinessOrderStatus.get_unprocessed()),
                Job.status == JobStatus.PENDING,
                BusinessOrder.current_job_id == Job.id,
            ),
            {
                CanJoinedClass.JOB,
            },
        )

    def get_to_be_approve_tab_condition(self) -> QueryCondition:
        """待审批"""
        return QueryCondition(
            and_(
                Job.unprocessed_condition(),
                Job.id == BusinessOrder.current_job_id,
                Step.step_type == StepType.approve,
                BusinessOrder.status.in_(
                    [
                        BusinessOrderStatus.PENDING,
                        BusinessOrderStatus.TO_BO_SUBMITTED,
                        BusinessOrderStatus.PAUSED,
                    ]
                ),
                JobApprover.user_id == self.accessor_query.user_id,
            ),
            {CanJoinedClass.JOB, CanJoinedClass.STEP, CanJoinedClass.JOB_APPROVER},
        )

    def get_processed_tab_condition(self) -> QueryCondition:
        """我已处理"""
        processed_tab_query = (
            db.ro_session.query(Job.business_order_id.distinct())
            .join(BusinessOrder, Job.business_order_id == BusinessOrder.id)
            .filter(
                self.accessor_query.job_assignee_condition(),
                Job.processed_condition(),
                or_(
                    BusinessOrder.status == BusinessOrderStatus.SUCCEED,
                    and_(BusinessOrder.status != BusinessOrderStatus.SUCCEED, Job.id != BusinessOrder.current_job_id),
                ),
            )
        )

        return QueryCondition(
            and_(BusinessOrder.id.in_(processed_tab_query.subquery())),  # type: ignore[arg-type]
            {
                CanJoinedClass.JOB,
            },
        )

    def get_created_tab_condition(self) -> QueryCondition:
        """我创建的"""
        return QueryCondition(self.accessor_query.bo_creator_condition(), set())

    def get_relate_tab_condition(self) -> QueryCondition:
        """候选人工单"""

        return QueryCondition(
            and_(
                BusinessOrder.form_id.in_(self.accessor_query.can_operate_form_info.operator_form_ids),
                BusinessOrder.status.in_(
                    [
                        BusinessOrderStatus.RUNNING,
                        BusinessOrderStatus.IN_EXCEPTION,
                        BusinessOrderStatus.PAUSED,
                        BusinessOrderStatus.PENDING,
                        BusinessOrderStatus.TO_BO_SUBMITTED,
                        BusinessOrderStatus.TO_BE_COLLECTED,
                    ]
                ),
                Step.id == Job.step_id,
                Job.id == BusinessOrder.current_job_id,
                Step.step_type == StepType.human,
                not_(self.accessor_query.job_assignee_condition()),
                Step.step_uuid.in_(self.accessor_query.can_operate_form_info.operator_step_uuids),
            ),
            {CanJoinedClass.JOB, CanJoinedClass.STEP},
        )

    def get_all_tab_condition(self, org_id: str) -> QueryCondition:
        """全部 应包含：待我处理+我已处理+我创建的+候选人工单+待审批"""
        if org_id in app_config.task_center_all_tab_whitelist:
            created_tab_condition = self.get_created_tab_condition()
            unprocessed_tab_condition = self.get_unprocessed_tab_condition()
            processed_tab_condition = self.get_processed_tab_condition()
            relate_tab_condition = self.get_relate_tab_condition()
            to_be_approve_tab_condition = self.get_to_be_approve_tab_condition()

            return QueryCondition(
                or_(
                    # FIXME 预期应该是工单管理员，但是现在工单管理员的逻辑和可创建是混在一起的。
                    BusinessOrder.form_id.in_(self.accessor_query.accessible_form_ids),
                    created_tab_condition.condition,
                    unprocessed_tab_condition.condition,
                    processed_tab_condition.condition,
                    relate_tab_condition.condition,
                    to_be_approve_tab_condition.condition,
                ),
                created_tab_condition.need_joined_classes
                | unprocessed_tab_condition.need_joined_classes
                | processed_tab_condition.need_joined_classes
                | relate_tab_condition.need_joined_classes
                | to_be_approve_tab_condition.need_joined_classes,
            )
        else:
            return QueryCondition(
                or_(
                    BusinessOrder.form_id.in_(self.accessor_query.accessible_form_ids),
                    self.accessor_query.job_assignee_condition(),
                    self.accessor_query.bo_creator_condition(),
                    self.get_relate_tab_condition().condition,
                ),
                {CanJoinedClass.JOB} | self.get_relate_tab_condition().need_joined_classes,
            )


def get_start_and_end_ts(date: str) -> tuple[int, int]:
    arrow_date = arrow.get(date).replace(tzinfo="Asia/Shanghai")
    start = int(arrow_date.floor("day").timestamp())
    end = int(arrow_date.ceil("day").timestamp())
    return start, end


def group_uuid_filter(column: Mapped, group_uuids: list[str]) -> ColumnElement:
    return or_(*[func.json_contains(column, func.json_object("uuid", _id)) for _id in group_uuids])


def filter_query_by_widgets(widget_filter: FilterSchema.WidgetFilter, query: Query) -> Query:
    widget_info_keys = [i for i in widget_filter.widget_key]

    # 找到所有的 widget_info。
    widget_infos: list[WidgetInfo] = db.ro_session.query(WidgetInfo).filter(WidgetInfo.key.in_(widget_info_keys)).all()

    if len(widget_infos) == 0:
        return query

    # 生成查询条件。
    conditions = []

    for widget_info in widget_infos:
        w_key = widget_info.key
        if widget_info.brief().get("option_value", {}).get("widget_type") == "datetime":
            # 时间戳以数组形式保存在keyword内
            start = arrow.get(widget_filter.keyword[0], tzinfo="Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss")
            end = arrow.get(widget_filter.keyword[1], tzinfo="Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss")
            # datetime 类型的组件用时间戳的范围匹配,但是bo.data里面存的值是"2024-03-14 15:09:24"这样的格式(仅系统组件而言)
            # 由于这种格式其实也是可以用字典序排序的，直接用大于小于符号即可比较，但是需要排除空值
            conditions.append(
                and_(
                    BusinessOrder.data[w_key] != "",
                    BusinessOrder.data[w_key] >= start,
                    BusinessOrder.data[w_key] <= end,
                ).self_group()
            )
        elif widget_info.brief().get("option_value", {}).get("widget_type") == "date":
            start_date = datetime.fromisoformat(widget_filter.keyword[0]).strftime("%Y-%m-%d")
            end_date = datetime.fromisoformat(widget_filter.keyword[1]).strftime("%Y-%m-%d")
            conditions.append(
                and_(
                    BusinessOrder.data[w_key] != "",
                    BusinessOrder.data[w_key] >= start_date,
                    BusinessOrder.data[w_key] <= end_date,
                ).self_group()
            )
        else:
            like_string = ""
            if isinstance(widget_filter.keyword, list):
                like_string = "%".join([unquote(str(k)) for k in widget_filter.keyword])
            else:
                like_string = unquote(str(widget_filter.keyword))
            if like_string != "":
                conditions.append(
                    and_(func.json_extract(BusinessOrder.data, f'$."{w_key}"').like(f"%{like_string}%")).self_group()
                )

    if len(conditions) == 0:
        return query

    # 加入新的筛选条件。
    return query.filter(or_(*conditions))


def normalize_order_by_field(order_by_field: str) -> Any:
    # 如果是创建时间或者主键，直接使用主键排序
    if order_by_field in ["created_at", "id"]:
        return BusinessOrder.id
    elif order_by_field.startswith("data."):
        json_fields = ".".join(["$"] + list(map(lambda path: f'"{path}"', order_by_field.split(".")[1:])))
        return func.json_extract(BusinessOrder.data, json_fields)
    else:
        return getattr(BusinessOrder, order_by_field)


def query_sort(query: Query, order_by_conditions: list[FilterSchema.OrderBy]) -> Query:
    order_by_list: list[UnaryExpression] = []
    try:
        for order_by_config in order_by_conditions:
            if order_by_config.strategy.upper() == "ASC":
                sort = sql_asc
            elif order_by_config.strategy.upper() == "DESC":
                sort = sql_desc
            else:
                raise ValueError(f"排序规则错误 {order_by_config.strategy}. raw: {order_by_conditions}")
            if len(order_by_config.fields) == 1:
                order_by_list.append(sort(normalize_order_by_field(order_by_config.fields[0])))
            else:
                fields = list(map(normalize_order_by_field, order_by_config.fields))
                order_by_list.append(sort(func.coalesce(*fields)))
    except Exception as e:
        logger.opt(exception=e).error("排序条件解析失败")
    if order_by_list:
        query = query.order_by(*order_by_list)
    else:
        query = query.order_by(sql_desc(BusinessOrder.updated_at))
    return query


def get_org_sids(org_id: str, sids: list[str] | None) -> list[str]:
    if isinstance(sids, list) and len(sids) != 0:
        shops = (
            db.ro_session.query(Shop).filter(Shop.org_id == org_id, Shop.sid.in_(sids)).with_entities(Shop.sid).all()
        )
        return list({shop.sid for shop in shops})
    else:
        shops = (
            db.ro_session.query(Shop)
            .filter(
                Shop.org_id == org_id,
            )
            .with_entities(Shop.sid)
            .all()
        )
        return list({shop.sid for shop in shops})


def generate_business_order_query(
    org_id: str,
    user_id: int,
    filters: FilterSchema,
    need_sort: bool = False,
    ignore_too_large_error: bool = False,
) -> tuple[Query, Query, str | None]:
    sid_list = get_org_sids(org_id, filters.sid)

    session = db.ro_session
    query = (
        session.query(BusinessOrder)
        .select_from(BusinessOrder)
        .filter(
            BusinessOrder.deleted.isnot(True),
            BusinessOrder.sid.in_(sid_list),
        )
    )

    query = query.with_hint(BusinessOrder, "IGNORE INDEX(PRIMARY)", "mysql")
    plain_query = query
    accessor_query = AccessorQuery(sid_list, user_id, int(org_id))
    page_tab_filter = PageTabFilter(accessor_query)
    page_tab_query_condition: QueryCondition | None = None
    need_joined_classes_set: set[CanJoinedClass] = set()

    # 使用了关键字查询，es里没搜到，就不用往下查了
    if filters.keyword:
        if es_query_ids := global_search_by_multi_keywords(sid_list, filters.keyword):
            query = query.filter(BusinessOrder.id.in_(es_query_ids))
        else:
            return query, plain_query, errors.NOT_FOUND_BOS_BY_KEYWORD

    if filters.id:
        if isinstance(filters.id, list):
            query = query.filter(BusinessOrder.id.in_(filters.id))
        else:
            query = query.filter(BusinessOrder.id == filters.id)

    # 优先使用 form_id，如果都没有，则使用传过来的 form_name。
    if filters.form_id:
        query = query.filter(BusinessOrder.form_id.in_(filters.form_id))
    elif not filters.form_id and filters.form_name:
        form_shops = (
            Form.Queries.form_shops_by_sids(sid_list)
            .join(Form.Options.from_form_shop)
            .filter(Form.name == filters.form_name)
        )
        form_ids = Form.Utils.form_ids_by_form_shops(form_shops)  # type: ignore[arg-type]
        query = query.filter(BusinessOrder.form_id.in_(form_ids))

    if filters.buyer_nick:
        query = query.filter(BusinessOrder.uid == filters.buyer_nick)

    if filters.status:
        query = query.filter(BusinessOrder.status.in_(filters.status))  # type: ignore[arg-type]

    if filters.created_at:
        start_at, end_at = get_start_and_end_ts(filters.created_at)
        query = query.filter(BusinessOrder.created_at >= start_at, BusinessOrder.created_at <= end_at)

    if filters.updated_at:
        start_at, end_at = get_start_and_end_ts(filters.updated_at)
        query = query.filter(BusinessOrder.updated_at >= start_at, BusinessOrder.updated_at <= end_at)

    if filters.create_start_at and filters.create_end_at:
        query = query.filter(
            BusinessOrder.created_at >= filters.create_start_at, BusinessOrder.created_at <= filters.create_end_at
        )

    if filters.update_start_at and filters.update_end_at:
        query = query.filter(
            BusinessOrder.updated_at >= filters.update_start_at, BusinessOrder.updated_at <= filters.update_end_at
        )

    # 创建人
    if filters.creator_id:
        query = query.filter(BusinessOrder.creator_user_id == filters.creator_id)

    if filters.platform_creator_id:
        query = query.filter(
            BusinessOrder.creator_user_id == filters.platform_creator_id, BusinessOrder.creator_type != Creator.LEYAN
        )

    if filters.feisuo_creator_id:
        query = query.filter(BusinessOrder.feisuo_creator_user_id == filters.feisuo_creator_id)

    if filters.creator_group_uuid:
        query = query.filter(
            or_(
                group_uuid_filter(BusinessOrder.creator_group, filters.creator_group_uuid),
                group_uuid_filter(BusinessOrder.feisuo_creator_group, filters.creator_group_uuid),
            )
        )

    if filters.platform_creator_group_uuid:
        query = query.filter(
            group_uuid_filter(BusinessOrder.creator_group, filters.platform_creator_group_uuid),
            BusinessOrder.creator_type != Creator.LEYAN,
        )

    if filters.feisuo_creator_group_uuid:
        query = query.filter(group_uuid_filter(BusinessOrder.feisuo_creator_group, filters.feisuo_creator_group_uuid))

    # 最近更新人
    if filters.updator_id:
        query = query.filter(BusinessOrder.updator_id == filters.updator_id)

    if filters.platform_updator_id:
        query = query.filter(
            BusinessOrder.updator_id == filters.platform_updator_id, BusinessOrder.updator_type != Creator.LEYAN
        )

    if filters.feisuo_updator_id:
        query = query.filter(BusinessOrder.feisuo_updator_id == filters.feisuo_updator_id)

    if filters.updator_group_uuid:
        query = query.filter(
            or_(
                group_uuid_filter(BusinessOrder.updator_group, filters.updator_group_uuid),
                group_uuid_filter(BusinessOrder.feisuo_updator_group, filters.updator_group_uuid),
            )
        )

    if filters.platform_updator_group_uuid:
        query = query.filter(
            group_uuid_filter(BusinessOrder.updator_group, filters.platform_updator_group_uuid),
            BusinessOrder.updator_type != Creator.LEYAN,
        )

    if filters.feisuo_updator_group_uuid:
        query = query.filter(group_uuid_filter(BusinessOrder.feisuo_updator_group, filters.feisuo_updator_group_uuid))

    if filters.current_step:
        need_joined_classes_set.add(CanJoinedClass.STEP)
        need_joined_classes_set.add(CanJoinedClass.JOB)
        query = query.filter(Step.name == filters.current_step, Job.id == BusinessOrder.current_job_id)

    if filters.is_timeout is not None:
        query = query.filter(BusinessOrder.is_timeout == filters.is_timeout)

    # 当前步骤处理人
    if filters.assignee_id:
        need_joined_classes_set.add(CanJoinedClass.JOB)
        query = query.filter(BusinessOrder.current_job_id == Job.id, Job.assignee_user_id == filters.assignee_id)

    # 当前步骤处理人(平台)
    if filters.platform_assignee_id:
        need_joined_classes_set.add(CanJoinedClass.JOB)
        query = query.filter(
            BusinessOrder.current_job_id == Job.id,
            Job.assignee_user_id == filters.platform_assignee_id,
            Job.assignee_type != Creator.LEYAN,
        )

    # 当前步骤处理人(飞梭)
    if filters.feisuo_assignee_id:
        need_joined_classes_set.add(CanJoinedClass.JOB)
        query = query.filter(
            BusinessOrder.current_job_id == Job.id, Job.feisuo_assignee_user_id == filters.feisuo_assignee_id
        )

    # 步骤处理人归属组
    if filters.assignee_group_uuid:
        need_joined_classes_set.add(CanJoinedClass.JOB)
        query = query.filter(
            BusinessOrder.current_job_id == Job.id,
            or_(
                group_uuid_filter(Job.assignee_group, filters.assignee_group_uuid),
                group_uuid_filter(Job.feisuo_assignee_group, filters.assignee_group_uuid),
            ),
        )

    # 平台步骤处理人归属组
    if filters.platform_assignee_group_uuid:
        need_joined_classes_set.add(CanJoinedClass.JOB)
        query = query.filter(
            BusinessOrder.current_job_id == Job.id,
            group_uuid_filter(Job.assignee_group, filters.platform_assignee_group_uuid),
            BusinessOrder.updator_type != Creator.LEYAN,
        )

    # 飞梭步骤处理人归属组
    if filters.feisuo_assignee_group_uuid:
        need_joined_classes_set.add(CanJoinedClass.JOB)
        query = query.filter(
            BusinessOrder.current_job_id == Job.id,
            group_uuid_filter(Job.feisuo_assignee_group, filters.feisuo_assignee_group_uuid),
        )

    if filters.from_type is not None:
        query = query.filter(BusinessOrder.from_type == FromType(filters.from_type))

    if filters.form_id and filters.widgets and isinstance(filters.widgets, dict):  # 仅指定了表单才筛选组件
        for widget_key, widget_value in filters.widgets.items():
            query = query.filter(
                func.json_extract(BusinessOrder.data, f'$."{widget_key}"').like(f"%{unquote(widget_value)}%")
            )

    elif filters.form_id and filters.widgets and isinstance(filters.widgets, list):
        for widget_filter in cast(list[FilterSchema.WidgetFilter], filters.widgets):
            # 一般的组件都是用字符串的,like匹配 datetime 类型的组件用时间戳的范围匹配
            try:
                query = filter_query_by_widgets(widget_filter, query)
            except Exception as e:
                logger.error(f"filter widget error: {e}")

    if isinstance(filters.flag, list) and len(filters.flag) != 0:
        query = query.filter(BusinessOrder.flag.in_(filters.flag))

    # todo(39): 补充 tab 页筛选条件
    match filters.page_tab:
        case MyBusinessOrdersTab.ALL:
            page_tab_query_condition = page_tab_filter.get_all_tab_condition(org_id)
        case MyBusinessOrdersTab.UNPROCESSED:
            page_tab_query_condition = page_tab_filter.get_unprocessed_tab_condition()
        case MyBusinessOrdersTab.PROCESSED:
            page_tab_query_condition = page_tab_filter.get_processed_tab_condition()
        case MyBusinessOrdersTab.CREATED:
            page_tab_query_condition = page_tab_filter.get_created_tab_condition()
        case MyBusinessOrdersTab.RELATE:
            page_tab_query_condition = page_tab_filter.get_relate_tab_condition()
        case MyBusinessOrdersTab.TO_BE_APPROVE:
            page_tab_query_condition = page_tab_filter.get_to_be_approve_tab_condition()

    if page_tab_query_condition:
        query = query.filter(page_tab_query_condition.condition)
        need_joined_classes_set = need_joined_classes_set | page_tab_query_condition.need_joined_classes

    # 配置需要 join 的类
    if CanJoinedClass.JOB in need_joined_classes_set:
        query = query.join(
            Job,
            Job.business_order_id == BusinessOrder.id,
        )
    if CanJoinedClass.STEP in need_joined_classes_set:
        query = query.join(Step, Job.step_id == Step.id)
    if CanJoinedClass.JOB_APPROVER in need_joined_classes_set:
        query = query.join(JobApprover, JobApprover.job_id == Job.id, isouter=True)

    if (explainer := QueryExplainer(query)) and explainer.is_slow_query():
        logger.warning(query.get_sql())  # type: ignore[attr-defined]
        if not ignore_too_large_error:
            return query, plain_query, errors.QUERY_COUNT_TOO_LARGE

    if not session.query(query.exists()).scalar():
        # 如果查询结果确定为空的话，避免进行 `order by xxx limit xx` 的查询
        # ref: https://git.leyantech.com/digismart/robot-processor/-/issues/72
        return query, plain_query, errors.NOT_FOUND_BOS

    if need_sort:
        query = query_sort(query, filters.order_by_condition)

    sub_query = query.group_by(BusinessOrder.id).with_entities(BusinessOrder.id).subquery()

    main_query = (
        session.query(BusinessOrder).select_from(BusinessOrder).join(sub_query, sub_query.c.id == BusinessOrder.id)
    )

    return main_query, plain_query, None
