from dataclasses import dataclass
from dataclasses import field
from typing import Dict
from typing import List
from typing import Optional

from typing_extensions import TypedDict

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.business_order.encryption.address_widget import AddressWidgetMask
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import BusinessOrderFlag
from robot_processor.business_order.models import Job
from robot_processor.enums import AssigneeRule
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import Creator
from robot_processor.enums import FromType
from robot_processor.enums import JobProcessMark
from robot_processor.enums import JobStatus
from robot_processor.enums import StepType
from robot_processor.form.models import Form
from robot_processor.form.models import FormShop
from robot_processor.form.models import FormWrapper
from robot_processor.form.models import Step
from robot_processor.form.models import WidgetInfo
from robot_processor.utils import ts2date
from robot_processor.utils import unwrap_optional


class ShopBrief(TypedDict):
    sid: Optional[str]
    title: Optional[str]
    nick: Optional[str]
    platform: Optional[str]


class JobBrief(TypedDict):
    job_id: int
    process_mark: JobProcessMark
    status: JobStatus
    data: dict

    task_id: int
    step_id: int
    step_uuid: str
    step_name: str  # 当前步骤名
    step_type: StepType
    step_assignee_rule: AssigneeRule
    step_status: str
    can_reject: bool
    ui_schema: List[dict]
    form_id: int

    # 执行客服信息
    assignee: Optional[str]  # 客服名
    platform_assignee: Optional[str]  # 平台当前步骤处理人
    feisuo_assignee: Optional[str]  # 飞梭当前步骤处理人
    assignee_group: List[str]  # 当前步骤处理人归属组
    platform_assignee_group: List[str]  # 平台当前步骤处理人归属组
    feisuo_assignee_group: List[str]  # 飞梭当前步骤处理人归属组
    assignee_status: int  # 客服状态 1正常 0禁用 -1删除

    prev_step_ids: Optional[List[str]]
    next_step_ids: Optional[List[str]]

    display_rule: Optional[List]

    # 遍历网关相关的信息
    iterate_gateway: Optional[dict]

    jump: dict | None
    deadlined_at: Optional[str]


class JobDetail(JobBrief):
    has_tracing_remind: bool


class JobInfoForCurrentJob(JobBrief):
    # 任务时效
    is_timeout: int


class StepInfo(TypedDict):
    step_id: int
    step_uuid: str
    step_name: str
    step_type: StepType
    index: int  # 第几步
    job_id: int  # 对应的job_id


class BusinessOrderAction(TypedDict, total=False):
    job_id: int

    next_human_job_need_assistant: bool
    next_human_job_step_uuid: str
    can_reject_steps: Optional[List[StepInfo]]


class AssigneeeGroup(TypedDict):
    uuid: str
    type: str
    name: str


class BusinessOrderBrief(TypedDict):
    id: int
    name: str
    description: Optional[str]
    status: BusinessOrderStatus
    from_type: FromType  # 创建来源
    form_category: Optional[str]
    form_deleted: bool  # 工单模板是否被删除
    form_version_id: int | None
    data: dict
    extra_data: dict
    aid: Optional[str]  # 客户 id
    platform_creator: Optional[str]  # 平台创建人
    feisuo_creator: Optional[str]  # 飞梭创建人
    creator_group: List[AssigneeeGroup]  # 创建人归属组
    platform_creator_group: List[AssigneeeGroup]  # 平台创建人归属组
    feisuo_creator_group: List[AssigneeeGroup]  # 飞梭创建人归属组
    mid: Optional[str]  # 飞梭账号
    uid: Optional[str]  # 买家 id
    buyer_open_uid: Optional[str]
    creator_type: Creator  # 创建人类别
    creator_user_id: int  # 创建人

    form_id: int
    sid: str

    created_at: str
    update_user: str  # 最近更新人
    platform_updator: str  # 平台最近更新人
    feisuo_updator: str  # 飞梭最近更新人
    updator_group: List[AssigneeeGroup]  # 最近更新人归属组
    platform_updator_group: List[AssigneeeGroup]  # 平台最近更新人归属组
    feisuo_updator_group: List[AssigneeeGroup]  # 飞梭最近更新人归属组
    update_action: Optional[str]  # 最近一次执行的操作
    update_reason: Optional[str]  # 最近一次执行操作的操作原因
    updated_at: str

    current_job: JobBrief | None  # 任务当前执行步骤
    current_jobs: List[JobBrief]
    actions: Dict[str, BusinessOrderAction]

    shop_info: ShopBrief
    flag: BusinessOrderFlag


class BusinessOrderDetail(BusinessOrderBrief):
    deadline_info: dict
    job_history: List
    job_road: List
    job_transition: List

    v_oid: str
    v_tid: str
    version: dict


def get_job_brief(job: Job) -> JobBrief:

    if job.assignee_type != Creator.LEYAN:
        _assignee_group = (job.assignee_group or []) + (job.feisuo_assignee_group or [])
    else:
        _assignee_group = job.feisuo_assignee_group or []
    job_deadline_ts = (job.deadline_info or {}).get("job_ts")
    if job_deadline_ts:
        deadline_at = ts2date(job_deadline_ts)
    else:
        deadline_at = None

    job_brief = JobBrief(
        job_id=job.id,
        process_mark=job.get_process_mark(),
        status=JobStatus(job.status),
        data={},
        task_id=job.raw_task_id,
        form_id=job.raw_step_v2.get("form_id"),
        step_id=job.step_id,  # type: ignore[typeddict-item]
        step_uuid=job.step_uuid,  # type: ignore[typeddict-item]
        step_name=job.raw_step_v2.get("name"),
        step_type=job.raw_step_type,
        step_assignee_rule=job.raw_step_assignee_rule,
        step_status=job.raw_step_v2.get("status"),
        can_reject=job.raw_step_v2.get("can_reject"),
        ui_schema=job.raw_ui_schema,
        assignee=job.assignee,
        platform_assignee=job.assignee if job.assignee_type != Creator.LEYAN else None,
        feisuo_assignee=job.feisuo_assignee,
        assignee_group=_assignee_group,
        platform_assignee_group=job.assignee_group if job.assignee_type != Creator.LEYAN else [],
        feisuo_assignee_group=job.feisuo_assignee_group,
        assignee_status=1,
        jump=Step.Utils.raw_auto_jump_config(job.step) if job.step is not None else None,
        prev_step_ids=job.raw_step_v2.get("prev_step_ids", []),
        next_step_ids=job.raw_step_v2.get("next_step_ids", []),
        display_rule=job.display_rule,
        iterate_gateway=None,
        deadlined_at=deadline_at,
    )
    if job.raw_step_type == StepType.iterate_gw_begin:
        widget_info = BusinessOrder.Utils.get_widget_info(job.business_order, job.widget_ref.key)
        if job.widget_ref.type == "array":
            data = job.business_order.data_wrapper.get(job.widget_ref, [])
        else:
            data = job.business_order.data_wrapper.get(job.widget_ref.raw_query, [])
        job_brief["iterate_gateway"] = {
            "widget_ref": job.widget_ref.dict(),
            "data": data,
            "ui_schema": widget_info.brief() if widget_info else None,
        }

    return job_brief


def get_business_order_brief(
    business_order: BusinessOrder, form: FormWrapper, operate_assistant: AccountDetailV2
) -> BusinessOrderBrief:
    """任务中心 - 任务列表页任务信息"""
    from robot_processor.utils import ts2date

    job = business_order.current_job

    if job is not None:
        current_job = JobInfoForCurrentJob(**get_job_brief(job=job), is_timeout=business_order.is_timeout)
    else:
        current_job = None

    shop = form.shop
    shop_info = ShopBrief(sid=shop.sid, title=shop.title, nick=shop.nick, platform=shop.platform)

    if business_order.creator_type != Creator.LEYAN:
        _creator_group = (business_order.creator_group or []) + (business_order.feisuo_creator_group or [])
        _updator_group = (business_order.updator_group or []) + (business_order.feisuo_updator_group or [])
    else:
        _creator_group = business_order.feisuo_creator_group or []
        _updator_group = business_order.feisuo_updator_group or []

    business_order_brief = BusinessOrderBrief(
        id=business_order.id,
        name=form.name,  # type: ignore[typeddict-item]
        description=form.description,
        status=BusinessOrderStatus(business_order.status),
        from_type=FromType(business_order.from_type),
        form_category=form.category,
        form_deleted=bool(form.deleted),
        form_version_id=business_order.form_version_id,
        data=business_order.data,
        extra_data=business_order.extra_data,
        aid=business_order.aid,
        platform_creator=business_order.aid if business_order.creator_type != Creator.LEYAN else None,
        feisuo_creator=business_order.feisuo_creator,
        creator_group=_creator_group,
        platform_creator_group=business_order.creator_group if business_order.creator_type != Creator.LEYAN else [],
        feisuo_creator_group=business_order.feisuo_creator_group,
        mid=business_order.mid,
        uid=business_order.uid,
        buyer_open_uid=business_order.buyer_open_uid,
        creator_type=Creator(business_order.creator_type),
        creator_user_id=business_order.creator_user_id,  # type: ignore[typeddict-item]
        form_id=form.id,
        sid=business_order.sid,  # type: ignore[typeddict-item]
        created_at=ts2date(business_order.created_at),
        update_user=business_order.update_user,  # type: ignore[typeddict-item]
        platform_updator=business_order.update_user if business_order.updator_type != Creator.LEYAN else None,  # type: ignore[typeddict-item]  # noqa: E501
        feisuo_updator=business_order.feisuo_updator,  # type: ignore[typeddict-item]
        updator_group=_updator_group,
        platform_updator_group=business_order.updator_group if business_order.updator_type != Creator.LEYAN else [],
        feisuo_updator_group=business_order.feisuo_updator_group,
        update_action=(business_order.extra_data or {}).get("operate_action", "UNPROCESSED"),
        update_reason=(business_order.extra_data or {}).get("operate_reason", None),
        updated_at=ts2date(business_order.updated_at),
        current_job=current_job,
        current_jobs=[current_job] if current_job else [],
        # 列表页不再返回 actions，因为耗时较长，放到单独接口可以并发请求获取
        actions={},
        shop_info=shop_info,
        flag=business_order.flag,  # type: ignore[typeddict-item]
    )

    return business_order_brief


def get_business_order_detail(
    business_order: BusinessOrder, form: FormWrapper, operate_assistant: AccountDetailV2, version=None
):
    """任务中心 - 任务列表页任务详情"""
    business_order_brief = get_business_order_brief(
        business_order=business_order,
        form=form,
        operate_assistant=operate_assistant,
    )

    ui_schema: list[dict] = []
    job_brief_mapping = {_job.id: get_job_brief(job=_job) for _job in business_order.all_jobs()}
    for job_id in sorted(job_brief_mapping.keys()):
        WidgetInfo.Utils.inplace_before_option_value(job_brief_mapping[job_id]["ui_schema"], ui_schema)
        WidgetInfo.Utils.extend_ui_schema(ui_schema, job_brief_mapping[job_id]["ui_schema"])

    job_transition = [item.dict() for item in business_order.job_transition_wrapper.views()]

    business_order_brief = AddressWidgetMask().mask_single(business_order_brief)
    return BusinessOrderDetail(
        **business_order_brief,
        deadline_info=business_order.deadline_info,
        job_history=[job_brief_mapping.get(job_id) for job_id in business_order.job_history],
        job_road=business_order.job_road,
        job_transition=job_transition,
        v_oid=business_order.v_oid,  # type: ignore[typeddict-item]
        v_tid=business_order.v_tid,  # type: ignore[typeddict-item]
        version=version,
    )


class BatchGetJobBrief:
    @dataclass
    class Cache:
        raw_step_mapping: dict[int, dict] = field(default_factory=dict, init=False)

    def __init__(self, jobs: list[Job]):
        self.jobs = jobs
        self.cache = BatchGetJobBrief.Cache()
        self.prepare()

    def batch_get(self):
        return [self.get_job_brief(job) for job in self.jobs]

    def prepare(self):
        step_ids = list({job.step_id for job in self.jobs})
        steps = Step.query.filter(Step.id.in_(step_ids)).with_entities(Step.id, Step.raw_step).all()
        self.cache.raw_step_mapping = {step.id: step.raw_step for step in steps}

    def get_job_brief(self, job: Job):
        raw_step = self.cache.raw_step_mapping.get(unwrap_optional(job.step_id), {})
        try:
            raw_step_type = StepType(raw_step.get("step_type") or StepType.auto)
        except ValueError:
            raw_step_type = StepType.auto
        try:
            raw_assignee_rule = AssigneeRule(raw_step.get("assignee_rule") or AssigneeRule.RANDOM)
        except ValueError:
            raw_assignee_rule = AssigneeRule.RANDOM
        if job.assignee_type != Creator.LEYAN:
            _assignee_group = (job.assignee_group or []) + (job.feisuo_assignee_group or [])
        else:
            _assignee_group = job.feisuo_assignee_group or []
        job_deadline_ts = (job.deadline_info or {}).get("job_ts")
        if job_deadline_ts:
            deadline_at = ts2date(job_deadline_ts)
        else:
            deadline_at = None

        job_brief = JobBrief(
            job_id=job.id,
            process_mark=job.get_process_mark(),
            status=JobStatus(job.status),
            data={},
            task_id=raw_step.get("data", {}).get("rpa_id"),
            form_id=raw_step.get("form_id"),  # type: ignore[typeddict-item]
            step_id=unwrap_optional(job.step_id),
            step_uuid=unwrap_optional(job.step_uuid),
            step_name=raw_step.get("name"),  # type: ignore[typeddict-item]
            step_type=raw_step_type,
            step_assignee_rule=raw_assignee_rule,
            step_status=raw_step.get("status"),  # type: ignore[typeddict-item]
            can_reject=raw_step.get("can_reject"),  # type: ignore[typeddict-item]
            ui_schema=raw_step.get("ui_schema", []),
            assignee=job.assignee,
            platform_assignee=job.assignee if job.assignee_type != Creator.LEYAN else None,
            feisuo_assignee=job.feisuo_assignee,
            assignee_group=_assignee_group,
            platform_assignee_group=job.assignee_group if job.assignee_type != Creator.LEYAN else [],
            feisuo_assignee_group=job.feisuo_assignee_group,
            assignee_status=1,
            jump=raw_step.get("jump"),
            prev_step_ids=raw_step.get("prev_step_ids", []),
            next_step_ids=raw_step.get("next_step_ids", []),
            display_rule=raw_step.get("display_rule", []),
            iterate_gateway=None,
            deadlined_at=deadline_at,
        )
        if job_brief["step_type"] == StepType.iterate_gw_begin:
            widget_info = BusinessOrder.Utils.get_widget_info(job.business_order, job.widget_ref.key)
            if job.widget_ref.type == "array":
                data = job.business_order.data_wrapper.get(job.widget_ref, [])
            else:
                data = job.business_order.data_wrapper.get(job.widget_ref.raw_query, [])
            job_brief["iterate_gateway"] = {
                "widget_ref": job.widget_ref.dict(),
                "data": data,
                "ui_schema": widget_info.brief() if widget_info else None,
            }
        return job_brief


class BatchGetBusinessOrderBrief:
    @dataclass
    class Cache:
        job_mapping: dict[int, JobBrief] = field(init=False, default_factory=dict)
        shop_mapping: dict[str, ShopBrief] = field(init=False, default_factory=dict)
        form_mapping: dict[int, Form] = field(init=False, default_factory=dict)
        form_shop_deleted: dict[tuple[int, str], bool] = field(init=False, default_factory=dict)

    def __init__(self, org_id: int, business_orders: list[BusinessOrder]):
        self.cache = BatchGetBusinessOrderBrief.Cache()
        self.org_id = org_id
        self.business_orders = business_orders
        self.prepare()

    def prepare(self):
        from robot_processor.shop.models import Shop

        job_ids = list({bo.current_job_id for bo in self.business_orders})
        jobs = Job.query.filter(Job.id.in_(job_ids)).all()
        batch_get_job_brief = BatchGetJobBrief(jobs)
        job_briefs = batch_get_job_brief.batch_get()
        self.cache.job_mapping = {job_brief["job_id"]: job_brief for job_brief in job_briefs}

        sids = list({bo.sid for bo in self.business_orders})
        shops = (
            Shop.query.filter(Shop.org_id == str(self.org_id), Shop.sid.in_(sids))
            .with_entities(Shop.sid, Shop.title, Shop.nick, Shop.platform)
            .all()
        )
        self.cache.shop_mapping = {
            shop.sid: ShopBrief(sid=shop.sid, title=shop.title, nick=shop.nick, platform=shop.platform)
            for shop in shops
        }

        form_ids = list({bo.form_id for bo in self.business_orders})
        forms = Form.query.filter(Form.id.in_(form_ids)).all()
        self.cache.form_mapping = {form.id: form for form in forms}

        form_shops = (
            FormShop.query.filter(FormShop.form_id.in_(form_ids))
            .with_entities(FormShop.form_id, FormShop.sid, FormShop.status)
            .all()
        )
        self.cache.form_shop_deleted = {
            (form_shop.form_id, form_shop.sid): form_shop.status.is_deleted for form_shop in form_shops
        }

    def batch_get(self):
        return [self.get_business_order_brief(business_order) for business_order in self.business_orders]

    def get_business_order_brief(self, business_order: BusinessOrder):

        job_brief = self.cache.job_mapping.get(unwrap_optional(business_order.current_job_id))
        if job_brief:
            current_job = JobInfoForCurrentJob(**job_brief, is_timeout=business_order.is_timeout)
        else:
            current_job = None
        shop_info = self.cache.shop_mapping.get(unwrap_optional(business_order.sid))
        if business_order.creator_type != Creator.LEYAN:
            _creator_group = (business_order.creator_group or []) + (business_order.feisuo_creator_group or [])
            _updator_group = (business_order.updator_group or []) + (business_order.feisuo_updator_group or [])
        else:
            _creator_group = business_order.feisuo_creator_group or []
            _updator_group = business_order.feisuo_updator_group or []
        form = self.cache.form_mapping[unwrap_optional(business_order.form_id)]
        business_order_brief = BusinessOrderBrief(
            id=business_order.id,
            name=unwrap_optional(form.name),
            description=form.description,
            status=BusinessOrderStatus(business_order.status),
            from_type=FromType(business_order.from_type),
            form_category=form.category,
            form_deleted=self.cache.form_shop_deleted.get(
                (unwrap_optional(business_order.form_id), unwrap_optional(business_order.sid)), True
            ),
            form_version_id=business_order.form_version_id,
            data=business_order.data,
            extra_data=business_order.extra_data,
            aid=business_order.aid,
            platform_creator=business_order.aid if business_order.creator_type != Creator.LEYAN else None,
            feisuo_creator=business_order.feisuo_creator,
            creator_group=_creator_group,
            platform_creator_group=business_order.creator_group if business_order.creator_type != Creator.LEYAN else [],
            feisuo_creator_group=business_order.feisuo_creator_group,
            mid=business_order.mid,
            uid=business_order.uid,
            buyer_open_uid=business_order.buyer_open_uid,
            creator_type=Creator(business_order.creator_type),
            creator_user_id=unwrap_optional(business_order.creator_user_id),
            form_id=form.id,
            sid=unwrap_optional(business_order.sid),
            created_at=ts2date(business_order.created_at),
            update_user=unwrap_optional(business_order.update_user),
            platform_updator=business_order.update_user if business_order.updator_type != Creator.LEYAN else None,  # type: ignore[typeddict-item]  # noqa: E501
            feisuo_updator=unwrap_optional(business_order.feisuo_updator),
            updator_group=_updator_group,
            platform_updator_group=business_order.updator_group if business_order.updator_type != Creator.LEYAN else [],
            feisuo_updator_group=business_order.feisuo_updator_group,
            update_action=(business_order.extra_data or {}).get("operate_action", "UNPROCESSED"),
            update_reason=(business_order.extra_data or {}).get("operate_reason", None),
            updated_at=ts2date(business_order.updated_at),
            current_job=current_job,
            current_jobs=[current_job] if current_job else [],
            actions={},
            shop_info=shop_info,  # type: ignore[typeddict-item]
            flag=business_order.flag,  # type: ignore[typeddict-item]
        )
        return business_order_brief
