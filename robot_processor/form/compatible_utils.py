"""飞梭数据类型改造过程中使用的一些兼容方法，在全部完成后会废弃"""

from dataclasses import dataclass
from typing import cast

from flask import current_app
from robot_types.core import Symbol

from robot_processor.form import named_type_spec
from robot_processor.form.models import Component
from robot_processor.form.models import Widget
from robot_processor.form.models import WidgetInfo
from robot_processor.form.symbol_table import FormSymbolEditableView
from robot_processor.form.symbol_table import FormSymbolOptions
from robot_processor.form.symbol_table import NamedTypeSpec
from robot_processor.form.symbol_table import SymbolTable
from robot_processor.form.symbol_table import SymbolTableEditableView
from robot_processor.form.symbol_table import TypeSpec
from robot_processor.utils import unwrap_optional


@dataclass
class WidgetId:
    widget: str
    data_type: str | None
    fixed_key: str | None
    label: str

    @classmethod
    def from_widget(cls, widget: Widget):
        return cls(
            cast(str, widget.schema.get("type")),
            widget.schema.get("widget_type"),
            (widget.system_widget_data or {}).get("key"),
            cast(str, widget.schema.get("label")),
        )


# fmt: off
def component_id_to_widget(component_id: str, widgets: list[Widget]):
    match component_id:
        case "string":
            widget_id = WidgetId("string", "string", None, "单行输入")
        case "textarea":
            widget_id = WidgetId("textarea", "string", None, "多行输入")
        case "number":
            widget_id = WidgetId("number", "number", None, "数值")
        case "datetime":
            widget_id = WidgetId("datetime", "datetime", None, "日期时间")
        case "date":
            widget_id = WidgetId("date", "date", None, "日期")
        case "time":
            widget_id = WidgetId("time", "time", None, "时间")
        case "table":
            widget_id = WidgetId("table", "table", None, "复合组件")
        case "text":
            widget_id = WidgetId("text", "string", None, "文本说明")
        case "upload-video":
            widget_id = WidgetId("upload", "table", None, "视频上传")
        case "upload-image":
            widget_id = WidgetId("upload", "table", None, "图片上传")
        case "upload-file":
            widget_id = WidgetId("upload", "table", None, "附件上传")
        case "select-tile-single":
            widget_id = WidgetId("radio-tile", "enum", None, "单选平铺")
        case "select-dropdown-single":
            widget_id = WidgetId("radio-dropdown", "enum", None, "单选下拉")
        case "select-tile-multi":
            widget_id = WidgetId("select-tile", "enum", None, "多选平铺")
        case "select-dropdown-multi":
            widget_id = WidgetId("select-dropdown", "enum", None, "多选下拉")
        case "order":
            widget_id = WidgetId("order", "table", None, "订单/子订单")
        case "usernick":
            widget_id = WidgetId("usernick", "string", None, "买家昵称")
        case "realname":
            widget_id = WidgetId("realname", "string", None, "真实姓名")
        case "address":
            widget_id = WidgetId("address", "object", None, "收货地址")
        case "payment-date":
            widget_id = WidgetId("payment-date", "date", None, "付款日期")
        case "payment-time":
            widget_id = WidgetId("payment-time", "datetime", None, "付款时间")
        case "delivery-date":
            widget_id = WidgetId("delivery-date", "date", None, "发货日期")
        case "delivery-time":
            widget_id = WidgetId("delivery-time", "datetime", None, "发货时间")
        case "order-date":
            widget_id = WidgetId("order-date", "date", None, "下单日期")
        case "order-time":
            widget_id = WidgetId("order-time", "datetime", None, "下单时间")
        case "price":
            widget_id = WidgetId("price", "number", None, "订单金额")
        case "payment":
            widget_id = WidgetId("payment", "number", None, "实付金额")
        case "received-amount":
            widget_id = WidgetId("received-amount", "number", None, "实收金额")
        case "invoiced-amount":
            widget_id = WidgetId("invoiced-amount", "number", None, "开票金额")
        case "contractor":
            widget_id = WidgetId("contractor", "string", None, "快递公司")
        case "tracking-num":
            widget_id = WidgetId("tracking-num", "string", None, "快递单号")
        case "payment-method":
            widget_id = WidgetId("payment-method", "object", None, "收款信息")
        case "alipay":
            widget_id = WidgetId("alipay", "string", None, "支付宝账号")
        case "out-sku-id":
            widget_id = WidgetId("out-sku-id", "string", None, "商品编码")
        case "reissue-product":
            widget_id = WidgetId("reissue-product", "table", None, "新补发商品")
        case "reissue-product_old":
            widget_id = WidgetId("reissue-product", None, None, "补发商品")
        case "product-spu":
            widget_id = WidgetId("product-spu", "string", None, "SPU商品编码")
        case "product-sku":
            widget_id = WidgetId("product-sku", "string", None, "SKU商品编码")
        case "product":
            widget_id = WidgetId("product", "table", None, "新商品")
        case "product_old":
            widget_id = WidgetId("product", None, None, "商品")
        case "order-question":
            widget_id = WidgetId("order-question", None, None, "订单问题细分")
        case "auto-number":
            widget_id = WidgetId("auto-number", "number", None, "自动编号")
        case "rate":
            widget_id = WidgetId("rate", "table", None, "评分")
        case "system_business_order_id":
            widget_id = WidgetId("number", "number", "system_business_order_id", "工单ID")
        case "system_business_order_status":
            widget_id = WidgetId("string", "string", "system_business_order_status", "工单状态")
        case "system_business_order_shop_platform_chinese":
            widget_id = WidgetId("string", "string", "system_business_order_shop_platform_chinese", "所属平台")
        case "system_business_order_shop_nick":
            widget_id = WidgetId("string", "string", "system_business_order_shop_nick", "所属店铺")
        case "system_business_order_feisuo_creator":
            widget_id = WidgetId("string", "string", "system_business_order_feisuo_creator", "创建人(飞梭)")
        case "system_business_order_creator_platform":
            widget_id = WidgetId("string", "string", "system_business_order_creator_platform", "创建人(平台)")
        case "system_business_order_feisuo_updator":
            widget_id = WidgetId("string", "string", "system_business_order_feisuo_updator", "最近更新人(飞梭)")
        case "system_business_order_update_user":
            widget_id = WidgetId("string", "string", "system_business_order_update_user", "最近更新人(平台)")
        case "system_business_order_created_at":
            widget_id = WidgetId("datetime", "datetime", "system_business_order_created_at", "创建时间")
        case "system_business_order_updated_at":
            widget_id = WidgetId("datetime", "datetime", "system_business_order_updated_at", "最近更新时间")
        case "system_business_order_current_step_name":
            widget_id = WidgetId("string", "string", "system_business_order_current_step_name", "当前步骤")
        case "system_business_order_current_job_assignee_feisuo":
            widget_id = WidgetId("string", "string", "system_business_order_current_job_assignee_feisuo", "当前步骤处理人(飞梭)")
        case "system_business_order_current_job_assignee_platform":
            widget_id = WidgetId(
                "string",
                "string",
                "system_business_order_current_job_assignee_platform",
                "当前步骤处理人(平台)"
            )
        case "system_business_order_deadline":
            widget_id = WidgetId("datetime", "datetime", "system_business_order_deadline", "预计完结时间")
        case "system_business_order_end_at":
            widget_id = WidgetId("datetime", "datetime", "system_business_order_end_at", "工单结束时间")
        case "system_business_order_creator_group_string":
            widget_id = WidgetId("string", "string", "system_business_order_creator_group_string", "创建人归属组（平台）")
        case "system_business_order_feisuo_creator_group_string":
            widget_id = WidgetId("string", "string", "system_business_order_feisuo_creator_group_string", "创建人归属组（飞梭）")
        case "system_business_order_feisuo_updator_group_string":
            widget_id = WidgetId(
                "string",
                "string",
                "system_business_order_feisuo_updator_group_string",
                "最近更新人归属组（飞梭）"
            )
        case "system_business_order_updator_group_string":
            widget_id = WidgetId(
                "string",
                "string",
                "system_business_order_updator_group_string",
                "最近更新人归属组（平台）"
            )
        case "system_business_order_current_job_assignee_group_feisuo_string":
            widget_id = WidgetId(
                "string",
                "string",
                "system_business_order_current_job_assignee_group_feisuo_string",
                "当前步骤执行人归属组（飞梭）"
            )
        case "system_business_order_current_job_assignee_group_platform_string":
            widget_id = WidgetId(
                "string",
                "string",
                "system_business_order_current_job_assignee_group_platform_string",
                "当前步骤执行人归属组（平台）"
            )
        case "system_business_order_receipt_url":
            widget_id = WidgetId(
                "upload",
                "string",
                "system_business_order_receipt_url",
                "打款凭证"
            )
        case "system_business_order_form_name":
            widget_id = WidgetId(
                "string",
                "string",
                "system_business_order_form_name",
                "工单模板名称"
            )
        case "system_business_order_current_job_exception_reason":
            widget_id = WidgetId(
                "string",
                "string",
                "system_business_order_current_job_exception_reason",
                "异常原因"
            )
        case "system_business_order_flag":
            widget_id = WidgetId("string", "string", "flag", "工单插旗")
        case "tracing-record":
            widget_id = WidgetId("tracing-record", "table", None, "跟进记录")
        case "erp-trades-wdt-enterprise":
            widget_id = WidgetId("erp-trades", "table", None, "旺店通[企业版]订单信息")
        case "erp-trades-jst":
            widget_id = WidgetId("erp-trades", "table", None, "聚水潭订单信息")
        case "erp-trades-jst-out":
            widget_id = WidgetId("erp-trades", "table", None, "聚水潭出库单信息")
        case "erp-trades-jackyun":
            widget_id = WidgetId("erp-trades", "table", None, "吉客云订单信息")
        case "erp-trades-wanliniu":
            widget_id = WidgetId("erp-trades", "table", None, "万里牛订单信息")
        case "erp-trades-wdt-ulti":
            widget_id = WidgetId("erp-trades", "table", None, "旺店通[旗舰版]订单信息")
        case "sub-trades":
            widget_id = WidgetId("sub-trades", "table", None, "子订单")
        case "collection":
            widget_id = WidgetId("collection", "collection", None, "容器组件")
        case "array":
            widget_id = WidgetId("array", "array", None, "列表组件")
        case "boolean":
            widget_id = WidgetId("boolean", "boolean", None, "布尔值")
        case "business-order-query":
            widget_id = WidgetId("business-order-query", "table", None, "工单数据查询")
        case "order-memo":
            widget_id = WidgetId("order-memo", "collection", None, "订单备注")
        case "erp-trades-baisheng":
            widget_id = WidgetId("erp-trades", "table", None, "百胜订单信息")
        case _:
            raise ValueError(f"component {component_id} not found in component_to_widget")

    for widget in widgets:
        if WidgetId.from_widget(widget) == widget_id:
            return widget
    else:
        raise ValueError(f"{widget_id} not found in widget")


def widget_to_component_id(widget: Widget):
    match WidgetId.from_widget(widget):
        case (
            WidgetId("string", "string", None, "单行输入")
            | WidgetId("string", None, None, "单行输入")
        ):
            return "string"

        case (
            WidgetId("textarea", "string", None, "多行输入")
            | WidgetId("textarea", None, None, "多行输入")
        ):
            return "textarea"

        case (
            WidgetId("number", "number", None, "数值")
            | WidgetId("number", None, None, "数值")
        ):
            return "number"

        case (
            WidgetId("datetime", "datetime", None, "日期时间")
            | WidgetId("datetime", None, None, "日期时间")
        ):
            return "datetime"

        case WidgetId("date", "date", None, "日期"):
            return "date"

        case WidgetId("time", "time", None, "时间"):
            return "time"

        case WidgetId("table", "table", None, "复合组件"):
            return "table"

        case WidgetId("text", "string", None, "文本说明"):
            return "text"

        case (
            WidgetId("upload", "table", None, "视频上传")
            | WidgetId("upload", None, None, "视频上传")
        ):
            return "upload-video"

        case (
            WidgetId("upload", "table", None, "图片上传")
            | WidgetId("upload", None, None, "图片上传")
        ):
            return "upload-image"

        case WidgetId("upload", "table", None, "附件上传"):
            return "upload-file"

        case WidgetId("radio-tile", "enum", None, "单选平铺"):
            return "select-tile-single"

        case (
            WidgetId("radio-dropdown", "enum", None, "单选下拉")
            | WidgetId("select", None, None, "下拉单选")
        ):
            return "select-dropdown-single"

        case WidgetId("select-tile", "enum", None, "多选平铺"):
            return "select-tile-multi"

        case (
            WidgetId("select-dropdown", "enum", None, "多选下拉")
            | WidgetId("select", None, None, "下拉多选")
        ):
            return "select-dropdown-multi"

        case WidgetId("order", "table", None, "订单/子订单"):
            return "order"

        case WidgetId("usernick", "string", None, "买家昵称"):
            return "usernick"

        case WidgetId("realname", "string", None, "真实姓名"):
            return "realname"

        case WidgetId("address", "object" | None, None, "收货地址"):
            return "address"

        case WidgetId("payment-date", "date", None, "付款日期"):
            return "payment-date"

        case WidgetId("payment-time", "datetime", None, "付款时间"):
            return "payment-time"

        case WidgetId("delivery-date", "date", None, "发货日期"):
            return "delivery-date"

        case WidgetId("delivery-time", "datetime", None, "发货时间"):
            return "delivery-time"

        case WidgetId("order-date", "date", None, "下单日期"):
            return "order-date"

        case WidgetId("order-time", "datetime", None, "下单时间"):
            return "order-time"

        case WidgetId("price", "number", None, "订单金额"):
            return "price"

        case WidgetId("payment", "number", None, "实付金额"):
            return "payment"

        case WidgetId("received-amount", "number", None, "实收金额"):
            return "received-amount"

        case WidgetId("invoiced-amount", "number", None, "开票金额"):
            return "invoiced-amount"

        case WidgetId("contractor", "string", None, "快递公司"):
            return "contractor"

        case WidgetId("tracking-num", "string", None, "快递单号"):
            return "tracking-num"

        case WidgetId("payment-method", "object", None, "收款信息"):
            return "payment-method"

        case WidgetId("alipay", "string", None, "支付宝账号"):
            return "alipay"

        case WidgetId("out-sku-id", "string", None, "商品编码"):
            return "out-sku-id"

        case WidgetId("reissue-product", "table", None, "新补发商品"):
            return "reissue-product"

        case (
            WidgetId("reissue-product", None, None, "补发商品")
            | WidgetId("reissue-product", None, None, "创建erp补发单")
        ):
            return "reissue-product_old"

        case WidgetId("product-spu", "string", None, "SPU商品编码"):
            return "product-spu"

        case WidgetId("product-sku", "string", None, "SKU商品编码"):
            return "product-sku"

        case WidgetId("product", "table", None, "新商品"):
            return "product"

        case WidgetId("product", None, None, "商品"):
            return "product_old"

        case WidgetId("order-question", None, None, "订单问题细分"):
            return "order-question"

        case WidgetId("auto-number", "number", None, "自动编号"):
            return "auto-number"

        case WidgetId("rate", "table", None, "评分"):
            return "rate"

        case WidgetId("number", "number", "system_business_order_id", "工单ID"):
            return "system_business_order_id"

        case WidgetId("string", "string", "system_business_order_status", "工单状态"):
            return "system_business_order_status"

        case WidgetId("string", "string", "system_business_order_shop_platform_chinese", "所属平台"):
            return "system_business_order_shop_platform_chinese"

        case WidgetId("string", "string", "system_business_order_shop_nick", "所属店铺"):
            return "system_business_order_shop_nick"

        case WidgetId("string", "string", "system_business_order_feisuo_creator", "创建人(飞梭)"):
            return "system_business_order_feisuo_creator"

        case WidgetId("string", "string", "system_business_order_creator_platform", "创建人(平台)"):
            return "system_business_order_creator_platform"

        case WidgetId("string", "string", "system_business_order_feisuo_updator", "最近更新人(飞梭)"):
            return "system_business_order_feisuo_updator"

        case WidgetId("string", "string", "system_business_order_update_user", "最近更新人(平台)"):
            return "system_business_order_update_user"

        case WidgetId("datetime", "datetime", "system_business_order_created_at", "创建时间"):
            return "system_business_order_created_at"

        case WidgetId("datetime", "datetime", "system_business_order_updated_at", "最近更新时间"):
            return "system_business_order_updated_at"

        case WidgetId("string", "string", "system_business_order_current_step_name", "当前步骤"):
            return "system_business_order_current_step_name"

        case WidgetId(
            "string",
            "string",
            "system_business_order_current_job_assignee_feisuo",
            "当前步骤处理人(飞梭)"
        ):
            return "system_business_order_current_job_assignee_feisuo"

        case WidgetId(
            "string",
            "string",
            "system_business_order_current_job_assignee_platform",
            "当前步骤处理人(平台)"
        ):
            return "system_business_order_current_job_assignee_platform"

        case WidgetId("datetime", "datetime", "system_business_order_deadline", "预计完结时间"):
            return "system_business_order_deadline"

        case WidgetId("datetime", "datetime", "system_business_order_end_at", "工单结束时间"):
            return "system_business_order_end_at"

        case WidgetId(
            "string",
            "string",
            "system_business_order_creator_group_string",
            "创建人归属组（平台）"
        ):
            return "system_business_order_creator_group_string"

        case WidgetId(
            "string",
            "string",
            "system_business_order_feisuo_creator_group_string",
            "创建人归属组（飞梭）"
        ):
            return "system_business_order_feisuo_creator_group_string"

        case WidgetId(
            "string",
            "string",
            "system_business_order_feisuo_updator_group_string",
            "最近更新人归属组（飞梭）"
        ):
            return "system_business_order_feisuo_updator_group_string"

        case WidgetId(
            "string",
            "string",
            "system_business_order_updator_group_string",
            "最近更新人归属组（平台）"
        ):
            return "system_business_order_updator_group_string"

        case WidgetId(
            "string",
            "string",
            "system_business_order_current_job_assignee_group_feisuo_string",
            "当前步骤执行人归属组（飞梭）"
        ):
            return "system_business_order_current_job_assignee_group_feisuo_string"

        case WidgetId(
            "string",
            "string",
            "system_business_order_current_job_assignee_group_platform_string",
            "当前步骤执行人归属组（平台）"
        ):
            return "system_business_order_current_job_assignee_group_platform_string"

        case WidgetId("upload", "string", "system_business_order_receipt_url", "打款凭证"):
            return "system_business_order_receipt_url"

        case WidgetId("string", "string", "system_business_order_form_name", "工单模板名称"):
            return "system_business_order_form_name"

        case WidgetId(
            "string",
            "string",
            "system_business_order_current_job_exception_reason",
            "异常原因"
        ):
            return "system_business_order_current_job_exception_reason"

        case WidgetId("string", "string", "flag", "工单插旗"):
            return "system_business_order_flag"

        case WidgetId("tracing-record", "table", None, "跟进记录"):
            return "tracing-record"

        case WidgetId("erp-trades", "table", None, "旺店通[企业版]订单信息"):
            return "erp-trades-wdt-enterprise"

        case WidgetId("erp-trades", "table", None, "聚水潭订单信息"):
            return "erp-trades-jst"

        case WidgetId("erp-trades", "table", None, "聚水潭出库单信息"):
            return "erp-trades-jst-out"

        case WidgetId("erp-trades", "table", None, "吉客云订单信息"):
            return "erp-trades-jackyun"

        case WidgetId("erp-trades", "table", None, "万里牛订单信息"):
            return "erp-trades-wanliniu"

        case WidgetId("erp-trades", "table", None, "旺店通[旗舰版]订单信息"):
            return "erp-trades-wdt-ulti"

        case WidgetId("sub-trades", "table", None, "子订单"):
            return "sub-trades"

        case WidgetId("collection", "collection", None, "容器组件") | WidgetId("collection", None, None, "容器组件"):
            return "collection"

        case WidgetId("array", "array", None, "列表组件"):
            return "array"

        case WidgetId("boolean", "boolean", None, "布尔值"):
            return "boolean"

        case WidgetId("business-order-query", "table", None, "工单数据查询"):
            return "business-order-query"

        case WidgetId("order-memo", "collection", None, "订单备注"):
            return "order-memo"

        case WidgetId("erp-trades", "table", None, "百胜订单信息"):
            return "erp-trades-baisheng"

        case _:
            raise ValueError(f"widget {WidgetId.from_widget(widget)} not found in widget_to_component")
# fmt: on


@dataclass
class WidgetComponentPair:
    widget_id: WidgetId
    component_id: str


def widget_info_to_editable_view(
    widget_info: WidgetInfo,
    widget_map: dict[int, Widget],
    component_map: dict[str, Component],
) -> FormSymbolEditableView:
    widget = widget_map[unwrap_optional(widget_info.widget_id)]
    component_id = widget_to_component_id(widget)
    component = component_map[component_id]

    view = FormSymbolEditableView(
        name=widget_info.key,
        type_spec=component.type_spec.copy(),
        options=FormSymbolOptions.validate(widget_info.option_value),
        component_id=component_id,
        render_config=widget_info.option_value,
        redirect=None if not widget_info.before else "replace me",
    )

    match view.component_id:
        case (
            "table"
            | "reissue-product"
            | "product"
            | "erp-trades-wdt-enterprise"
            | "erp-trades-wdt-ulti"
            | "erp-trades-jst"
            | "erp-trades-jackyun"
            | "erp-trades-jst-out"
            | "erp-trades-wanliniu"
            | "sub-trades"
            | "business-order-query"
            | "erp-trades-baisheng"
        ):
            collection_type_spec_spec = []
            collection_children = []
            if view.component_id == "reissue-product":
                collection_type_spec_typename = NamedTypeSpec.REISSUE_PRODUCT.typename
            elif view.component_id == "product":
                collection_type_spec_typename = NamedTypeSpec.PRODUCT.typename
            elif view.component_id == "erp-trades-wdt-enterprise":
                collection_type_spec_typename = NamedTypeSpec.WDT_TRADE_INFO.type_spec.typename
            elif view.component_id == "erp-trades-jst":
                collection_type_spec_typename = named_type_spec.JST.JST_TRADE_INFO.type_spec.typename
            elif view.component_id == "erp-trades-jst-out":
                collection_type_spec_typename = named_type_spec.JST.JST_OUT_ORDER_INFO.type_spec.typename
            elif view.component_id == "erp-trades-jackyun":
                collection_type_spec_typename = NamedTypeSpec.JACKYUN_TRADE_INFO.type_spec.typename
            elif view.component_id == "erp-trades-wanliniu":
                collection_type_spec_typename = named_type_spec.WLN.WLN_TRADE_INFO.type_spec.typename
            else:
                collection_type_spec_typename = None
            for child_obj in widget_info.option_value["fields"]:
                child = widget_info_to_editable_view(
                    WidgetInfo(
                        key=child_obj["key"],
                        widget_id=child_obj["id"],
                        option_value=child_obj.get("option_value", {}),
                        data_schema=child_obj.get("date_schema", {}),
                    ),
                    widget_map,
                    component_map,
                )
                collection_children.append(child)
                collection_type_spec_spec.append(child.type_spec.copy(update={"name": child.name}))
            collection = FormSymbolEditableView(
                name=f"_{widget_info.key}_ITEM_",
                type_spec=TypeSpec(
                    type="collection",
                    spec=collection_type_spec_spec,
                    typename=collection_type_spec_typename,
                ),
                children=collection_children,
            )

            view.type_spec.spec = [collection.type_spec]
            view.children = [collection]

        case "array":
            child_obj = widget_info.option_value["fields"][0]
            child = widget_info_to_editable_view(
                WidgetInfo(
                    key=child_obj["key"],
                    widget_id=child_obj["id"],
                    option_value=child_obj.get("option_value", {}),
                    data_schema=child_obj.get("data_schema", {}),
                ),
                widget_map,
                component_map,
            )
            view.type_spec.spec = [child.type_spec]
            view.children = [child]

        case "collection" | "order-memo":
            properties: dict[str, FormSymbolEditableView] = {}
            for child_obj in widget_info.option_value["fields"]:
                child = widget_info_to_editable_view(
                    WidgetInfo(
                        key=child_obj["key"],
                        widget_id=child_obj["id"],
                        option_value=child_obj.get("option_value", {}),
                        data_schema=child_obj.get("data_schema", {}),
                    ),
                    widget_map,
                    component_map,
                )
                assert child.name
                properties[child.name] = child
            view.type_spec.spec = [
                properties[field_name].type_spec.copy(update={"name": field_name}) for field_name in properties
            ]
            view.children = [properties[field_name] for field_name in properties]

        case "upload-image" | "upload-video" | "upload-file":
            view.children = [
                FormSymbolEditableView(
                    name=f"_{widget_info.key}_ITEM_",
                    type_spec=NamedTypeSpec.FILE,
                    children=[
                        FormSymbolEditableView(
                            name="fileName",
                            type_spec=TypeSpec(type="string"),
                            component_id="string",
                            render_config={"label": "文件名"},
                        ),
                        FormSymbolEditableView(
                            name="url",
                            type_spec=TypeSpec(type="string"),
                            component_id="string",
                            render_config={"label": "文件地址"},
                        ),
                    ],
                )
            ]

        case "payment-method":
            view.children = [
                FormSymbolEditableView(
                    name="payment_method",
                    type_spec=TypeSpec(type="number"),
                    component_id="string",
                    render_config={"label": "收款方式"},
                ),
                FormSymbolEditableView(
                    name="receive_account",
                    type_spec=TypeSpec(type="string"),
                    component_id="string",
                    render_config={"label": "收款账号"},
                ),
                FormSymbolEditableView(
                    name="receive_name",
                    type_spec=TypeSpec(type="string"),
                    component_id="string",
                    render_config={"label": "真实姓名"},
                ),
                FormSymbolEditableView(
                    name="tid",
                    type_spec=TypeSpec(type="string"),
                    component_id="string",
                    render_config={"label": "订单号"},
                ),
                FormSymbolEditableView(
                    name="alipay_no",
                    type_spec=TypeSpec(type="string"),
                    component_id="string",
                    render_config={"label": "支付宝交易号"},
                ),
            ]
        case "address":
            view.children = [
                FormSymbolEditableView(
                    name="name",
                    type_spec=TypeSpec(type="string"),
                    component_id="string",
                    render_config={"label": "姓名"},
                ),
                FormSymbolEditableView(
                    name="mobile",
                    type_spec=TypeSpec(type="string"),
                    component_id="string",
                    render_config={"label": "手机号"},
                ),
                FormSymbolEditableView(
                    name="state",
                    type_spec=TypeSpec(type="string"),
                    component_id="string",
                    render_config={"label": "省"},
                ),
                FormSymbolEditableView(
                    name="city",
                    type_spec=TypeSpec(type="string"),
                    component_id="string",
                    render_config={"label": "市"},
                ),
                FormSymbolEditableView(
                    name="zone",
                    type_spec=TypeSpec(type="string"),
                    component_id="string",
                    render_config={"label": "区"},
                ),
                FormSymbolEditableView(
                    name="town",
                    type_spec=TypeSpec(type="string"),
                    component_id="string",
                    render_config={"label": "街道/镇"},
                ),
                FormSymbolEditableView(
                    name="address",
                    type_spec=TypeSpec(type="string"),
                    component_id="string",
                    render_config={"label": "详细地址"},
                ),
            ]

        case "order":
            view.children = [
                FormSymbolEditableView(
                    name=f"_{widget_info.key}_ITEM_",
                    type_spec=NamedTypeSpec.TRADE_NO,
                    children=[
                        FormSymbolEditableView(
                            name="tid",
                            type_spec=TypeSpec(type="string"),
                            component_id="string",
                            render_config={"label": "订单号"},
                        ),
                        FormSymbolEditableView(
                            name="oid",
                            type_spec=TypeSpec(type="string"),
                            component_id="string",
                            render_config={"label": "子订单号"},
                        ),
                    ],
                )
            ]

    return view


def symbol_editable_view_to_widget_info(
    symbol: FormSymbolEditableView | Symbol,
    widget_map: dict[int, Widget],
    component_map: dict[str, Component],
):
    def reveal_children(symbol_: FormSymbolEditableView):
        widget_ = component_id_to_widget(unwrap_optional(symbol_.component_id), list(widget_map.values()))
        widget_id = WidgetId.from_widget(widget_)
        if widget_id.data_type == "table":
            # 这里是将 symbol 做 widget_info 的兼容，对 table 要做特殊处理
            return symbol_.children[0].children if symbol_.children else []
        elif widget_id == WidgetId("product", None, None, "商品"):
            return symbol_.children[0].children if symbol_.children else []
        else:
            return symbol_.children or []

    def build_object(widget_info_: WidgetInfo):
        widget_info_object = {
            "id": widget_info_.widget_id,
            "key": widget_info_.key,
            "before": widget_info_.before,
            "type": widget_map[unwrap_optional(widget_info_.widget_id)].schema["type"],
            "widget_type": widget_map[unwrap_optional(widget_info_.widget_id)].schema["widget_type"],
            "option_value": widget_info_.option_value,
            "data_schema": widget_info_.data_schema,
        }

        return widget_info_object

    def build_dataschema_obj(symbol_: FormSymbolEditableView):
        widget_ = component_id_to_widget(unwrap_optional(symbol_.component_id), list(widget_map.values()))
        dataschema = {
            "name": symbol_.name,
            "title": symbol_.render_config.get("label"),
            "type": widget_.schema.get("widget_type", widget_.schema.get("type")),
        }

        return dataschema

    def build_dataschema(symbol_: FormSymbolEditableView):
        if not symbol_.children:
            fields = [build_dataschema_obj(symbol_)]
        else:
            fields = []
            for child in reveal_children(symbol_):
                child_schema_obj = build_dataschema_obj(child)
                if child.children:
                    child_schema_obj["fields"] = build_dataschema(child)
                fields.append(child_schema_obj)

        return fields

    def build_option_value_fields(symbol_: FormSymbolEditableView):
        if symbol_.component_id not in [
            "table",
            "product",
            "reissue-product",
            "erp-trades-wdt-enterprise",
            "erp-trades-wdt-ulti",
            "erp-trades-jst",
            "collection",
            "array",
            "sub-trades",
            "erp-trades-jackyun",
            "erp-trades-jst-out",
            "erp-trades-wanliniu",
            "business-order-query",
            "order-memo",
            "erp-trades-baisheng",
        ]:
            return
        else:
            fields = []
            for child in reveal_children(symbol_):
                child_widget_info = symbol_editable_view_to_widget_info(child, widget_map, component_map)
                fields.append(build_object(child_widget_info))

            return fields

    if symbol.component_id is None:
        raise Exception(f"{symbol=} has no component_id")
    widget = component_id_to_widget(symbol.component_id, list(widget_map.values()))
    if isinstance(symbol, Symbol):
        option_value = symbol.render_config.copy()
    else:
        option_value = {**symbol.options.dict(), **symbol.render_config}
    widget_info = WidgetInfo(
        key=symbol.name,
        widget_id=widget.id,
        option_value=option_value,
        before=False if symbol.redirect is None else True,
        order=0,
    )
    if widget_type := widget.schema.get("widget_type"):
        widget_info.option_value.setdefault("widget_type", widget_type)
    widget_info.data_schema = {
        "title": symbol.render_config.get("label"),
        "fields": build_dataschema(symbol),
    }
    if option_value_fields := build_option_value_fields(symbol):
        widget_info.option_value["fields"] = option_value_fields

    return widget_info


def editable_view_to_symbol(editable_view: FormSymbolEditableView):
    symbol = SymbolTable.Symbol(
        type_spec=editable_view.type_spec,
        name=editable_view.name,
        label=editable_view.render_config.get("label", editable_view.name),
        children=None,
    )
    if editable_view.children:
        symbol.children = list(map(editable_view_to_symbol, editable_view.children))

    return symbol


def use_symbol() -> bool:
    """通过一个总开关来配置使用新旧 symbol 表的逻辑，全部完成后会废弃"""
    return current_app.config.get("USE_SYMBOL", True)


def need_compatible_processing_by_symbol_table(symbol_table: SymbolTable):
    """判断是否需要进行兼容处理"""
    for namespace in symbol_table.namespaces:
        if namespace.label == "起始步骤":
            return len(namespace.symbols) == 0
    else:
        return True


def need_compatible_processing_by_editable_view(editable_view: SymbolTableEditableView):
    """判断是否需要进行兼容处理"""
    for step in editable_view.steps:
        if step.step_name == "起始步骤":
            return len(step.symbols) == 0
    else:
        return True
