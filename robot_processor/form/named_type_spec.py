"""FIXME 由 fraxel-schema 项目维护并通过 CI/CD 方式生成"""
from abc import ABC
from collections import namedtuple
from datetime import datetime
from typing import TypeVar

from pydantic import BaseModel
from pydantic import Field

from robot_processor.form.symbol_table import TypeSpec
from robot_processor.utils import make_fields_optional


class TypeSpecModel(BaseModel, ABC):
    ...


TypeSpecModelT = TypeVar("TypeSpecModelT", bound=TypeSpecModel)

_ = namedtuple("_", ["type_spec", "model"])


# 任意类型 - 仅函数签名中可用
ANY = _(TypeSpec(type="any"), None)
# 范型 - 仅函数签名中可用
T = _(TypeSpec(type="T"), None)
# 仅函数签名中可用
ARRAY_ANY = _(TypeSpec(type="array", spec=[TypeSpec(type="any")]), None)
# 仅函数签名中可用
ARRAY_T = _(TypeSpec(type="array", spec=[TypeSpec(type="T")]), None)


class _model:
    class credential:
        class WDT(TypeSpecModel):
            sid: str
            after_sale_shop_no: str
            app_key: str
            app_secret: str

        class JST(TypeSpecModel):
            token: str
            co_id: str
            partner_id: str
            partner_key: str

        class WLN(TypeSpecModel):
            app_key: str
            app_secret: str

        class YTO(TypeSpecModel):
            partner_key: str
            partner_id: str

    class jst:
        @make_fields_optional
        class JstItem(TypeSpecModel):
            name: str
            shop_sku_id: str
            shop_i_id: str
            erp_name: str
            erp_short_name: str
            properties_value: str
            category: str
            qty: str
            price: str
            supplier_name: str
            sku_id: str
            i_id: str

        @make_fields_optional
        class JstTradeInfo(TypeSpecModel):
            so_id: str
            o_id: str
            has_split_order: str
            status: str
            type: str
            pay_amount: str
            logistics_company: str
            l_id: str
            items: list["_model.jst.JstItem"]
            drp_co_name: str
            supplier_name: str
            wave_id: str
            picker_name: str
            wms_co_name: str
            f_weight: str
            f_weight_float: float
            order_date: str
            pay_date: str
            send_date: str
            shop_name: str
            question_type: str
            question_desc: str

        @make_fields_optional
        class JstOutOrderInfo(TypeSpecModel):
            so_id: str  # 平台订单号
            o_id: str  # 内部单号
            io_id: str  # 出库单号
            status: str  # 出库单状态
            order_type: str  # 订单类型
            logistics_company: str  # 快递公司
            l_id: str  # 物流单号
            labels: str  # 标记
            f_weight: float  # 实际重量
            paid_amount: float  # 实付金额
            items: list["_model.jst.JstItem"]
            wms_co_name: str  # 仓库名称

    class wln:
        @make_fields_optional
        class WlnTradeInfo(TypeSpecModel):
            tp_tid: str = Field(description="线上订单号")
            buyer_account: str = Field(description="买家账号")
            trade_no: str = Field(description="系统单号")
            buyer_mobile: str = Field(description="买家电话")
            buyer: str = Field(description="买家昵称")
            buyer_msg: str = Field(description="买家留言")
            logistic_name: str = Field(description="快递公司名称")
            express_code: str = Field(description="快递单号")
            flag: str = Field(description="旗子（文本）")
            split_trade: bool = Field(description="是否拆分订单")
            has_refund: bool = Field(description="是否有退款")
            trade_type: str = Field(description="订单类型（文本）")
            storage_name: str = Field(description="发货仓")
            process_status: str = Field(description="万里牛单据状态（文本）")
            paid_fee: float = Field(description="应收款")
            real_payment: float = Field(description="买家实付")
            orders: list["_model.wln.WlnItem"] = Field(description="商品列表")

        @make_fields_optional
        class WlnItem(TypeSpecModel):
            tp_oid: str = Field(description="子订单号")
            item_name: str = Field(description="商品名称")
            is_package: bool = Field(description="是否组合商品")
            oln_item_name: str = Field(description="线上商品名称")
            item_platform_url: str = Field(description="商品线上的详情链接")
            item_image_url: str = Field(description="商品线上的图片url")
            oln_item_id: str = Field(description="线上商品ID")
            oln_item_code: str = Field(description="线上商品编码")
            oln_sku_id: str = Field(description="线上规格id")
            oln_sku_name: str = Field(description="线上商品规格属性")
            sku_code: str = Field(description="规格编码")
            oln_status: str = Field(description="线上状态（文本）")
            order_id: str = Field(description="明细id")
            payment: float = Field(description="应收款")
            price: float = Field(description="单价（商品标价）")
            remark: str = Field(description="明细备注")
            size: float = Field(description="数量")
            inventory_status: str = Field(description="库存状况（文本）")
            bar_code: str = Field(description="商品条码")
            discounted_unit_price: float = Field(description="折后单价")
            receivable: float = Field(description="销售金额")

    class yto:
        class InterceptReportResult(TypeSpecModel):
            succeed: bool
            status_code: int
            status_message: str

        class InterceptStatusPushMessage(TypeSpecModel):
            """圆通推送的拦截件结果状态"""

            class Data(TypeSpecModel):
                waybillNo: str
                result: str
                # 结果编码：1.拦截件已取消；2.超时拦截失败；3.已做退回/更址扫描操作；4.快件已签收；5.非指定网点签收，实物拦截失败。
                resultCode: str

            data: Data
            # 客户渠道
            channel: str

        class CheckInterceptStatus(TypeSpecModel):
            # 拦截状态
            # robot_processor.client.logistics_clients.enum.InterceptionStatus
            intercept_status: str
            # 拦截失败时，失败的原因
            failed_reason: str
            # 拦截失败时，失败的原因编码
            failed_reason_code: str

    class logistics:
        class InterceptEvent(TypeSpecModel):
            logistics_no: str
            logistics_company: str
            intercept_at: datetime
            result_at: datetime
            intercept_status: str
            event_status: str


class Credential:
    WDT = _(
        TypeSpec(
            typename="Credential.WDT",
            type="collection",
            spec=[
                TypeSpec(name="sid", type="string"),
                TypeSpec(name="after_sale_shop_no", type="string"),
                TypeSpec(name="app_key", type="string"),
                TypeSpec(name="app_secret", type="string"),
            ],
        ),
        _model.credential.WDT,
    )
    JST = _(
        TypeSpec(
            typename="Credential.JST",
            type="collection",
            spec=[
                TypeSpec(name="token", type="string"),
                TypeSpec(name="co_id", type="string"),
                TypeSpec(name="partner_id", type="string"),
                TypeSpec(name="partner_key", type="string"),
            ],
        ),
        _model.credential.JST,
    )
    WLN = _(
        TypeSpec(
            typename="Credential.WLN",
            type="collection",
            spec=[
                TypeSpec(name="app_key", type="string"),
                TypeSpec(name="app_secret", type="string"),
            ],
        ),
        _model.credential.WLN,
    )
    YTO = _(
        TypeSpec(
            typename="Credential.YTO",
            type="collection",
            spec=[
                TypeSpec(name="partner_key", type="string"),
                TypeSpec(name="partner_id", type="string"),
            ],
        ),
        _model.credential.YTO,
    )


class JST:
    JST_ITEM = _(
        TypeSpec(
            typename="JST.Item",
            type="collection",
            spec=[
                TypeSpec(name="name", type="string"),  # 平台商品名称
                TypeSpec(name="shop_sku_id", type="string"),  # 平台商品编码
                TypeSpec(name="shop_i_id", type="string"),  # 平台款式编码
                TypeSpec(name="erp_name", type="string"),  # erp商品名称
                TypeSpec(name="erp_short_name", type="string"),  # erp商品简称
                TypeSpec(name="properties_value", type="string"),  # 商品属性
                TypeSpec(name="category", type="string"),  # 商品类别
                TypeSpec(name="qty", type="string"),  # 商品数量
                TypeSpec(name="price", type="string"),  # 商品金额
                TypeSpec(name="supplier_name", type="string"),  # 供应商
                TypeSpec(name="i_id", type="string"),  # erp货品编码
                TypeSpec(name="sku_id", type="string"),  # erp商品编码
            ],
        ),
        _model.jst.JstItem,
    )
    JST_TRADE_INFO = _(
        TypeSpec(
            typename="JST.TradeInfo",
            type="collection",
            spec=[
                TypeSpec(name="so_id", type="string"),  # 平台订单号
                TypeSpec(name="o_id", type="string"),  # 内部单号
                TypeSpec(name="has_split_order", type="string"),  # 是否拆单
                TypeSpec(name="status", type="string"),  # 订单状态
                TypeSpec(name="type", type="string"),  # 订单类型
                TypeSpec(name="pay_amount", type="string"),  # 实付金额
                TypeSpec(name="logistics_company", type="string"),  # 快递公司
                TypeSpec(name="l_id", type="string"),  # 快递单号
                TypeSpec(name="items", type="array", spec=[JST_ITEM.type_spec]),
                TypeSpec(name="drp_co_name", type="string"),  # 分销商
                TypeSpec(name="supplier_name", type="string"),  # 供应商
                TypeSpec(name="wave_id", type="string"),  # 批次号
                TypeSpec(name="picker_name", type="string"),  # 拣货员
                TypeSpec(name="wms_co_name", type="string"),  # 仓库名称
                TypeSpec(name="f_weight", type="string"),  # 称重重量
                TypeSpec(name="f_weight_float", type="number"),  # 称重重量
                TypeSpec(name="order_date", type="string"),  # 下单时间
                TypeSpec(name="pay_date", type="string"),  # 付款时间
                TypeSpec(name="send_date", type="string"),  # 发货时间
                TypeSpec(name="shop_name", type="string"),  # 店铺名称
            ],
        ),
        _model.jst.JstTradeInfo,
    )
    JST_OUT_ORDER_INFO = _(
        TypeSpec(
            typename="JST.OutOrderInfo",
            type="collection",
            spec=[
                TypeSpec(name="so_id", type="string"),  # 平台订单号
                TypeSpec(name="o_id", type="string"),  # 内部单号
                TypeSpec(name="io_id", type="string"),  # 出库单号
                TypeSpec(name="status", type="string"),  # 出库单状态
                TypeSpec(name="order_type", type="string"),  # 订单类型
                TypeSpec(name="logistics_company", type="string"),  # 快递公司
                TypeSpec(name="l_id", type="string"),  # 快递单号
                TypeSpec(name="labels", type="string"),  # 标记
                TypeSpec(name="f_weight", type="number"),  # 实际重量
                TypeSpec(name="paid_amount", type="number"),  # 实付金额
                TypeSpec(name="items", type="array", spec=[JST_ITEM.type_spec]),
                TypeSpec(name="wms_co_name", type="string"),  # 仓库名称
            ],
        ),
        _model.jst.JstOutOrderInfo,
    )


class WLN:
    WLN_ITEM = _(
        TypeSpec(
            typename="WLN.Item",
            type="collection",
            spec=[
                TypeSpec(name="tp_oid", type="string"),
                TypeSpec(name="items_name", type="string"),
                TypeSpec(name="is_package", type="boolean"),
                TypeSpec(name="oln_item_name", type="string"),
                TypeSpec(name="item_platform_url", type="string"),
                TypeSpec(name="item_image_url", type="string"),
                TypeSpec(name="oln_item_id", type="string"),
                TypeSpec(name="oln_item_code", type="string"),
                TypeSpec(name="oln_sku_id", type="string"),
                TypeSpec(name="oln_sku_name", type="string"),
                TypeSpec(name="sku_code", type="string"),
                TypeSpec(name="oln_status_zh", type="string"),
                TypeSpec(name="order_id", type="string"),
                TypeSpec(name="payment", type="number"),
                TypeSpec(name="price", type="number"),
                TypeSpec(name="remark", type="string"),
                TypeSpec(name="size", type="number"),
                TypeSpec(name="inventory_status", type="string"),
                TypeSpec(name="bar_code", type="string"),
                TypeSpec(name="discounted_unit_price", type="number"),
                TypeSpec(name="receivable", type="number"),
            ],
        ),
        _model.wln.WlnItem,
    )

    WLN_TRADE_INFO = _(
        TypeSpec(
            typename="WLN.TradeInfo",
            type="collection",
            spec=[
                TypeSpec(name="tp_tid", type="string"),
                TypeSpec(name="buyer_account", type="string"),
                TypeSpec(name="trade_no", type="string"),
                TypeSpec(name="buyer_mobile", type="string"),
                TypeSpec(name="buyer", type="string"),
                TypeSpec(name="buyer_msg", type="string"),
                TypeSpec(name="logistic_name", type="string"),
                TypeSpec(name="express_code", type="string"),
                TypeSpec(name="flag_zh", type="string"),
                TypeSpec(name="split_trade", type="boolean"),
                TypeSpec(name="has_refund", type="boolean"),
                TypeSpec(name="trade_type_zh", type="string"),
                TypeSpec(name="storage_name", type="string"),
                TypeSpec(name="process_status", type="string"),
                TypeSpec(name="paid_fee", type="number"),
                TypeSpec(name="real_payment", type="number"),
                TypeSpec(name="orders", type="array", spec=[WLN_ITEM.type_spec]),
            ],
        ),
        _model.wln.WlnTradeInfo,
    )


class YTO:
    # 发起拦截上报的结果
    INTERCEPT_REPORT_RESULT = _(
        TypeSpec(
            typename="YTO.InterceptReportResult",
            type="collection",
            spec=[
                TypeSpec(name="succeed", type="boolean"),
                TypeSpec(name="status_code", type="number"),
                TypeSpec(name="status_message", type="string"),
            ],
        ),
        _model.yto.InterceptReportResult,
    )
    # 物流公司拦截状态推送的原始信息
    INTERCEPT_STATUS_PUSH_MESSAGE = _(
        TypeSpec(
            typename="YTO.InterceptStatusPushMessage",
            type="collection",
            spec=[
                TypeSpec(
                    name="data",
                    type="collection",
                    spec=[
                        TypeSpec(name="waybillNo", type="string"),
                        TypeSpec(name="result", type="string"),
                        TypeSpec(name="resultCode", type="string"),
                    ],
                ),
                TypeSpec(name="channel", type="string"),
            ],
        ),
        _model.yto.InterceptStatusPushMessage,
    )
    # 根据物流公司的拦截状态推送信息/物流轨迹判断拦截状态
    CHECK_INTERCEPT_STATUS = _(
        TypeSpec(
            typename="YTO.CheckInterceptStatusByPushMessage",
            type="collection",
            spec=[
                # 拦截状态: 成功;失败;未知(未收到推送)
                TypeSpec(name="intercept_status", type="string"),
                # 拦截失败时，失败的原因
                TypeSpec(name="failed_reason", type="string"),
                # 拦截失败时，失败的原因编码
                TypeSpec(name="failed_reason_code", type="string"),
            ],
        ),
        _model.yto.CheckInterceptStatus,
    )


class Logistics:
    """物流相关"""

    INTERCEPT_EVENT = _(
        TypeSpec(
            typename="Logistics.InterceptEvent",
            type="collection",
            spec=[
                TypeSpec(name="logistics_no", type="string"),
                TypeSpec(name="logistics_company", type="string"),
                TypeSpec(name="intercept_at", type="datetime"),
                TypeSpec(name="result_at", type="datetime"),
                TypeSpec(name="intercept_status", type="string"),
                TypeSpec(name="event_status", type="string"),
            ],
        ),
        _model.logistics.InterceptEvent,
    )


def _update_forward_refs():
    _model.jst.JstTradeInfo.update_forward_refs()
    _model.jst.JstOutOrderInfo.update_forward_refs()
    _model.wln.WlnTradeInfo.update_forward_refs()


_update_forward_refs()
