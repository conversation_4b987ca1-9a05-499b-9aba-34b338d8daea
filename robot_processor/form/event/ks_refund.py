from datetime import datetime
from typing import List
from typing import Optional

import arrow
from loguru import logger
from pydantic import BaseModel
from result import Err
from result import Result
from tcron_jobs import runner

from robot_processor.client import ks_client
from robot_processor.client.kuaishou import KuaishouRefund
from robot_processor.client.kuaishou import OrderItemInfo
from robot_processor.db import db
from robot_processor.logging import vars as log_vars
from robot_processor.symbol_table.named_typespec.component import ComponentProduct
from robot_processor.symbol_table.named_typespec.component import ComponentProductList
from robot_processor.symbol_table.named_typespec.component import ComponentTradeNo
from robot_processor.symbol_table.named_typespec.component import ComponentTradeNoList
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import timestamp_to_log


class LogisitcsInfo(BaseModel):
    express_no: str
    express_name: str


class KuaishouRefundInfo(BaseModel):
    oid: str
    refund_id: str
    readable_handing_way: str
    readable_negotiate_status: str
    refund_fee: float
    fs_trade_no: ComponentTradeNoList
    readable_status: str
    readable_refund_type: str
    refund_desc: str
    readable_receipt_status: str
    refund_reason_desc: str
    expire_time: Optional[str]
    update_time: Optional[str]
    create_time: Optional[str]
    fs_product_list: ComponentProductList
    logisitcs_info: list[LogisitcsInfo]  # 发货物流
    readable_delivery_status: Optional[str]  # 发货状态
    is_split_delivery_order: bool  # 是否拆单发货
    return_express_no: Optional[str]
    return_express_name: Optional[str]
    pay_and_refund_amount_diff: float
    is_refund_all: bool
    agree_user: str  # 同意退款人
    order_paid: Optional[str]
    agree_refund_apply_time: Optional[str]  # 同意退款时间


@runner.register
@wrap_tcron_job
def poll_kuaishou_shop_refund(shop_id: int) -> int:
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.KUAISHOU_REFUND)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(shop, EventType.KUAISHOU_REFUND)
    now = arrow.now()
    end_update_at = now.naive
    if now.shift(minutes=-30).naive > event_scheduler.executed_at:
        start_update_at = now.shift(minutes=-1440).naive
    else:
        start_update_at = event_scheduler.executed_at
    access_token = shop.get_access_token()
    if not access_token:
        logger.error(f"快手店铺 {shop.id} 没有 access_token")
        return -1
    refund_paginator = get_data_fetcher(
        access_token=access_token,
        update_time_range=(start_update_at, end_update_at),
    )
    result: list[Result] = []
    for refund in refund_paginator.chain_fetch():
        refund_update_time = arrow.get(refund.updateTime, tzinfo="Asia/Shanghai").naive
        if refund_update_time > event_scheduler.executed_at:
            event_scheduler.mark_executed(refund_update_time)
        try:
            result.extend(create_bo(shop, refund, access_token))
        except Exception as e:
            logger.opt(exception=e).error(f"创建售后单失败 {e}")
            result.append(Err(e))
    if now.naive > event_scheduler.executed_at:
        event_scheduler.mark_executed(now.naive)
    logger.info(f"创建售后单 {len([r for r in result if r.is_ok()])} 条，结果 {result}")
    return refund_paginator.total


def create_bo(shop, refund: KuaishouRefund, access_token: str) -> list[Result]:
    from robot_processor.business_order.business_order_manager import BusinessManager
    from robot_processor.form.event.models import EventConfig
    from robot_processor.form.event.models import EventType

    event = EventConfig.get_by_id(EventType.KUAISHOU_REFUND)
    log_vars.RefundId.set(str(refund.refundId))
    log_vars.Tid.set(str(refund.oid))
    logger.info("处理快手售后单")
    order_resp = ks_client.query_order(access_token, str(refund.oid))
    if order_resp.result != 1:
        return [Err(f"无法获取快手订单信息 oid:{refund.oid}")]
    order_fee_detail_resp = ks_client.order_fee_detail(access_token, str(refund.oid))
    if order_fee_detail_resp.result != 1:
        return [Err(f"无法获取快手订单金额信息 oid:{refund.oid}")]
    refund_detail_resp = ks_client.refund_detail(access_token, str(refund.refundId))
    if refund_detail_resp.result != 1:
        return [Err(f"无法获取快手售后单详情 refund_id:{refund.refundId}")]
    return_express_no = None
    return_express_name = None
    if refund_detail_resp.data.logisticsInfo:
        return_express_no = refund_detail_resp.data.logisticsInfo.expressNo
        return_express_name = refund_detail_resp.data.logisticsInfo.expressName
    readable_order_delivery_status = ""
    is_split_delivery_order = False
    if order_resp.data.orderDeliveryInfo:
        readable_order_delivery_status = order_resp.data.orderDeliveryInfo.readableDeliveryStatus
        is_split_delivery_order = order_resp.data.orderDeliveryInfo.splitDeliveryOrder
    comment_resp = ks_client.refund_comment_basic_list(access_token, str(refund.refundId))
    agree_refund_apply_time = None
    agree_user = ""
    if comment_resp.result == 1:
        for comment in comment_resp.data:
            if "卖家同意退款申请" in comment.title:
                agree_user = comment.staffAccountName
                if agree_user == "":
                    agree_user = "商家"
                agree_refund_apply_time = timestamp_to_log(comment.createTime)
            if "平台拦截快递成功，同意退款" in comment.title:
                agree_user = "系统"
                agree_refund_apply_time = timestamp_to_log(comment.createTime)
            if "卖家已确认收到退货" in comment.title:
                agree_user = comment.staffAccountName
                if agree_user == "":
                    agree_user = "商家"
                agree_refund_apply_time = timestamp_to_log(comment.createTime)

    data = KuaishouRefundInfo(
        oid=str(refund.oid),  # 快手的oid是主订单
        refund_id=str(refund.refundId),
        readable_handing_way=refund.readableHandlingWay,
        readable_negotiate_status=refund.readableNegotiateStatus,
        refund_fee=refund.refundFee / 100.0,
        fs_trade_no=build_fs_trade_no_list(str(refund.oid), None),
        readable_status=refund.readableStatus,
        readable_refund_type=refund.readableRefundType,
        refund_desc=refund.refundDesc,
        readable_receipt_status=refund.readableReceiptStatus,
        refund_reason_desc=refund.refundReasonDesc,
        expire_time=refund.expireDatetime,
        update_time=refund.updateDatetime,
        create_time=refund.createDatetime,
        logisitcs_info=[
            LogisitcsInfo(express_no=logistics.expressNo, express_name=logistics.expressName)
            for logistics in order_resp.data.orderLogisticsInfo
        ],
        return_express_no=return_express_no,
        return_express_name=return_express_name,
        readable_delivery_status=readable_order_delivery_status,
        is_split_delivery_order=is_split_delivery_order,
        fs_product_list=build_fs_product_list(refund, order_resp.data.orderItemInfo),
        pay_and_refund_amount_diff=round(
            (
                order_fee_detail_resp.data.buyerPay
                + order_fee_detail_resp.data.platformBearAmount
                - refund_detail_resp.data.refundFee
            )
            / 100,
            2,
        ),
        is_refund_all=(
            order_fee_detail_resp.data.buyerPay
            + order_fee_detail_resp.data.platformBearAmount
            - refund_detail_resp.data.refundFee
        )
        == 0,
        agree_user=agree_user,
        order_paid=arrow.get(order_resp.data.orderBaseInfo.payTime, tzinfo="Asia/Shanghai").strftime(
            "%Y-%m-%d " "%H:%M:%S"
        ),
        agree_refund_apply_time=agree_refund_apply_time,
    )

    create_result = BusinessManager.create_bo_by_event(event, shop, data.dict())
    return create_result


def get_data_fetcher(access_token: str, update_time_range: tuple[datetime, datetime]):
    from robot_processor.form.event.paginator import PaginateDataFetcher

    class KuaishouRefundDataFetcher(PaginateDataFetcher[KuaishouRefund]):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.access_token = access_token
            self.update_time_range = update_time_range

        def _do_fetch(self, page_no, page_size):
            refunds: List[KuaishouRefund] = []
            pcursor = ""
            while True:
                response = ks_client.refund_list(
                    access_token,
                    int(self.update_time_range[0].timestamp() * 1000),
                    int(self.update_time_range[1].timestamp() * 1000),
                    page_no=page_no + 1,  # 快手页码从 1 开始
                    page_size=page_size,
                    pcursor=pcursor,
                )
                if response.result != 1:
                    raise Exception(response.error_msg)
                refunds.extend(response.data.refundOrderInfoList)
                self.total = response.data.totalSize
                if response.data.pcursor == "nomore":
                    break
                pcursor = response.data.pcursor
            return refunds

        def after_total_init_hook(self):
            logger.info(
                "快手店铺 {}~{} 共有 {} 条售后单".format(
                    self.update_time_range[0].strftime("%Y-%m-%d %H:%M:%S"),
                    self.update_time_range[1].strftime("%Y-%m-%d %H:%M:%S"),
                    self.total,
                )
            )

    return KuaishouRefundDataFetcher()


def build_fs_trade_no_list(tid, oid):
    return ComponentTradeNoList([ComponentTradeNo(tid=tid, oid=oid)])


def build_fs_product_list(refund: KuaishouRefund, order_item_info: OrderItemInfo):
    return ComponentProductList(
        [
            ComponentProduct(
                TID=refund.oid,
                OID=None,
                PICTURE=order_item_info.itemPicUrl,
                TITLE=order_item_info.itemTitle,
                DESCRIPTION=order_item_info.skuDesc,
                SPU=str(refund.itemId),
                SKU=str(refund.skuId),
                SKU_NAME=order_item_info.skuDesc,
                SPU_OUTER=order_item_info.goodsCode,
                SKU_OUTER=refund.skuNick,
                PRICE=order_item_info.price / 100.0,
                INVENTORY=None,
                PAYMENT=None,
                COMBINE=None,
                COUNT=0,  # 快手售后单无商品数量
                SHORT_TITLE="",
                BATCH=[],
            )
        ]
    )
