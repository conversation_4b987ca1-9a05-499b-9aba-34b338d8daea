from typing import Optional

import arrow
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import LogisticsInfo
from loguru import logger
from pydantic import BaseModel
from result import Err
from result import Result
from tcron_jobs import runner
from tenacity import retry
from tenacity import retry_if_exception_type
from tenacity import stop_after_attempt
from tenacity import wait_fixed

from robot_processor.client.errors import PddOrderNotFoundError
from robot_processor.client.errors import PddRateLimitError
from robot_processor.logging import vars as log_vars
from robot_processor.symbol_table.named_typespec.component import ComponentFile
from robot_processor.symbol_table.named_typespec.component import ComponentFileList
from robot_processor.symbol_table.named_typespec.component import ComponentProduct
from robot_processor.symbol_table.named_typespec.component import ComponentProductList
from robot_processor.symbol_table.named_typespec.component import ComponentTradeNo
from robot_processor.symbol_table.named_typespec.component import ComponentTradeNoList
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.trace.pdd_trace import PddLogisticsInfoDict
from robot_processor.trace.pdd_trace import PddTrace
from robot_processor.utils import make_fields_optional


def refund_shipped_processor(refund_info: dict):
    if refund_info.get("after_sales_type") != 2:
        return False
    if refund_info.get("shipping_status") != 1:
        return False
    return True


def refund_with_return_shipped_processor(refund_info: dict):
    if refund_info.get("after_sales_type") != 3:
        return False
    if refund_info.get("shipping_status") != 1:
        return False
    return True


@make_fields_optional
class PddRefundInfo(BaseModel):
    refund_id: str  # 售后单号
    after_sales_status: int  # 售后状态
    after_sales_status_zh: str  # 售后状态
    after_sales_type: int  # 售后类型
    after_sales_type_zh: str
    refund_remark: str  # 用户申请输入的描述信息
    after_sale_reason: str  # 售后原因
    refund_created_time: str  # 售后申请时间
    refund_amount: float  # 退款金额（元）
    refund_operator_role: int  # 退款操作人
    refund_operator_role_zh: str  # 退款操作人
    refund_shipping_name: str  # 退货物流公司名称
    refund_tracking_number: str  # 快递运单号
    user_shipping_status: str  # 0-未勾选 1-消费者选择的收货状态为未收到货 2-消费者选择的收货状态为已收到货

    goods_id: int  # 商品编码
    goods_name: str  # 商品名称
    goods_number: str  # 商品数量
    goods_price: str  # 商品单价
    outer_goods_id: str  # 商家外部编码（商品）
    outer_id: str  # 商家外部编码（sku）
    sku_id: str  # 商品规格ID

    order_sn: str  # 订单号
    pay_amount: float  # 实付金额（元）
    shipping_status: int  # 订单发货状态 0: 未发货, 1: 已发货
    shipping_status_zh: str  # 订单发货状态
    order_status: int  # 发货状态
    order_status_zh: str
    order_paid: Optional[str]  # 订单支付时间
    logistics_id: str  # 发货快递公司编号
    logistics_name: str  # 发货快递公司
    tracking_number: str  # 发货快递单号

    evidence: ComponentFileList  # 退款凭证
    pay_and_refund_amount_diff: float
    fs_trade_no: ComponentTradeNoList
    fs_product_list: ComponentProductList
    agree_refund_apply_time: Optional[str]  # 同意退款时间
    is_speed_refund: bool  # 是否是极速退款
    has_speed_refund_flag: bool  # 是否有极速退款标签
    is_collected: bool  # 是否已揽收
    is_transported: bool  # 是否已经运输
    desensitization_address: str  # 脱敏的地址

    @classmethod
    def convert_after_sales_status(cls, after_sales_status: int):
        return {
            0: "无售后",
            2: "买家申请退款，待商家处理",
            3: "退货退款，待商家处理",
            4: "商家同意退款，退款中",
            5: "平台同意退款，退款中",
            6: "驳回退款，待买家处理",
            7: "已同意退货退款,待用户发货",
            8: "平台处理中",
            9: "平台拒绝退款，退款关闭",
            10: "退款成功",
            11: "买家撤销",
            12: "买家逾期未处理，退款失败",
            13: "买家逾期，超过有效期",
            14: "换货补寄待商家处理",
            15: "换货补寄待用户处理",
            16: "换货补寄成功",
            17: "换货补寄失败",
            18: "换货补寄待用户确认完成",
            21: "待商家同意维修",
            22: "待用户确认发货",
            24: "维修关闭",
            25: "维修成功",
            27: "待用户确认收货",
            31: "已同意拒收退款，待用户拒收",
            32: "补寄待商家发货",
            33: "待商家召回",
        }.get(after_sales_status)

    @classmethod
    def convert_after_sales_type(cls, after_sales_type: int):
        return {
            1: "全部",
            2: "仅退款",
            3: "退货退款",
            4: "换货",
            5: "缺货补寄",
            6: "维修",
        }.get(after_sales_type)

    @classmethod
    def convert_refund_operator_role(cls, refund_operator_role: int):
        return {
            0: "默认",
            1: "用户",
            2: "商家",
            3: "平台",
            4: "系统",
            5: "团长",
            6: "司机",
            7: "代理人",
        }.get(refund_operator_role)

    @classmethod
    def convert_order_status(cls, order_status: int):
        return {1: "待发货", 2: "已发货待签收", 3: "已签收"}.get(order_status)

    @classmethod
    def convert_shipping_status(cls, shipping_status: int):
        return {0: "未发货", 1: "已发货"}.get(shipping_status)

    @classmethod
    def convert_trade_no_list(cls, order_sn: str):
        return ComponentTradeNoList([ComponentTradeNo(tid=order_sn)])

    @classmethod
    def convert_product_list(cls, refund_info):
        from typing import cast

        from robot_processor.client.schema import PddRefundListResponse

        refund_info = cast(PddRefundListResponse.RefundIncrementGetResponse.Refund, refund_info)
        return ComponentProductList(
            [
                ComponentProduct(
                    TID=refund_info.order_sn,
                    OID=None,
                    PICTURE=refund_info.good_image,
                    TITLE=refund_info.goods_name,
                    DESCRIPTION="",
                    SPU=refund_info.goods_id,
                    SKU=refund_info.sku_id,
                    SKU_NAME=refund_info.outer_id,
                    SPU_OUTER=refund_info.outer_goods_id,
                    SKU_OUTER=refund_info.outer_id,
                    PRICE=refund_info.goods_price,
                    INVENTORY=None,
                    PAYMENT=None,
                    COMBINE=None,
                    COUNT=refund_info.goods_number,
                    SHORT_TITLE="",
                )
            ]
        )


@runner.register
@wrap_tcron_job
def poll_pdd_shop_refund(shop_id: int) -> int:
    """轮询拼多多店铺售后单"""
    from robot_processor.db import db
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.PDD_REFUND)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(shop, EventType.PDD_REFUND)
    now = arrow.now()
    end_update_at = now.int_timestamp
    # 只能查询时间跨度为 30 分钟的售后单列表。如果是任务暂停后重新启动，则只查询最近的售后单信息
    if now.shift(minutes=-30).naive > event_scheduler.executed_at:
        start_update_at = now.shift(minutes=-30).int_timestamp
    else:
        start_update_at = int(event_scheduler.executed_at.timestamp())

    refund_paginator = get_data_fetcher(
        sid=shop.sid,
        update_time_range=(start_update_at, end_update_at),
    )
    result: list[Result] = []
    for refund in refund_paginator.chain_fetch():
        refund_update_time = arrow.get(refund.updated_time).naive
        if refund_update_time > event_scheduler.executed_at:
            event_scheduler.mark_executed(refund_update_time)
        try:
            result.extend(create_bo(shop, refund))
        except Exception as e:
            logger.opt(exception=e).error(f"创建售后单失败 {e}")
            result.append(Err(e))
    if now.naive > event_scheduler.executed_at:
        event_scheduler.mark_executed(now.naive)
    logger.info(f"创建售后单 {len([r for r in result if r.is_ok()])} 条，结果 {result}")
    return refund_paginator.total


@retry(retry=retry_if_exception_type(PddRateLimitError), wait=wait_fixed(1), stop=stop_after_attempt(3), reraise=True)
def get_pdd_trade_info(sid: str, tid: str):
    from robot_processor.client import pdd_bridge_client
    from robot_processor.client.schema import PddOrderInformationGet

    return pdd_bridge_client.order_information_get(store_id=sid, payload=PddOrderInformationGet(order_sn=tid))


def create_bo(shop, refund_list_info) -> list[Result]:
    from robot_processor.business_order.business_order_manager import BusinessManager
    from robot_processor.client import pdd_bridge_client
    from robot_processor.client import trade_client
    from robot_processor.client.schema import PddOrderInformationGet
    from robot_processor.client.schema import PddRefundDetailPayload
    from robot_processor.form.event.models import EventConfig
    from robot_processor.form.event.models import EventType
    from robot_processor.utils import amount_diff
    from robot_processor.utils import empty_str_as_none
    from robot_processor.utils import message_to_dict

    event = EventConfig.get_by_id(EventType.PDD_REFUND)
    log_vars.RefundId.set(refund_list_info.id)
    log_vars.Tid.set(refund_list_info.order_sn)
    logger.info(f"处理拼多多售后单 {refund_list_info.json()}")
    refund_detail_info = pdd_bridge_client.refund_detail(
        store_id=shop.sid,
        payload=PddRefundDetailPayload(order_sn=refund_list_info.order_sn, after_sale_id=str(refund_list_info.id)),
    )
    try:
        order_info_response = get_pdd_trade_info(shop.sid, refund_list_info.order_sn)
        order_info = order_info_response.order_info_get_response.order_info
        logistics_info = LogisticsInfo(
            receiver_name=order_info.receiver_name,
            receiver_phone=order_info.receiver_phone,
            province=order_info.province,
            city=order_info.city,
            town=order_info.town,
            address=order_info.address,
            logistics_id=order_info.logistics_id,
            logistics_name=PddLogisticsInfoDict.get_logistics_company(order_info.logistics_id),
            tracking_number=order_info.tracking_number,
            country=order_info.country,
        )
    except PddOrderNotFoundError:
        if order_list := trade_client.get_trade_by_tid_and_channel([refund_list_info.order_sn], "PDD").trade_info_list:
            order_info = PddOrderInformationGet.Response.OrderInfoGetResponse.OrderInfo.parse_obj(
                message_to_dict(order_list[0].pdd_trade_info)
            )
            logistics_info = order_list[0].pdd_trade_info.logistics_info
        else:
            return [Err(f"未查询到订单 {refund_list_info.order_sn}")]
    except Exception as e:
        return [Err(f"未查询到订单 {refund_list_info.order_sn} {e}")]

    is_speed_refund = False
    if refund_detail_info.refund_operator_role:
        if (
            refund_detail_info.refund_operator_role == 3
            and int(refund_detail_info.updated_time) - int(refund_detail_info.recreated_at) <= 40
        ):
            is_speed_refund = True
    is_collected = False
    is_transported = False
    if order_info.tracking_number:
        traces = PddTrace.get_traces_by_tracking_number(order_info.tracking_number)
        if any("GOT" in trace.action for trace in traces):
            is_collected = True
        if any("ARRIVAL" in trace.action or "DEPARTURE" in trace.action for trace in traces):
            is_transported = True

    data = PddRefundInfo(
        refund_id=refund_list_info.id,
        after_sales_status=refund_list_info.after_sales_status,
        after_sales_status_zh=PddRefundInfo.convert_after_sales_status(refund_list_info.after_sales_status),
        after_sales_type=refund_list_info.after_sales_type,
        after_sales_type_zh=PddRefundInfo.convert_after_sales_type(refund_list_info.after_sales_type),
        refund_remark=refund_detail_info.remark,
        after_sale_reason=refund_list_info.after_sale_reason,
        refund_created_time=refund_list_info.created_time,
        refund_amount=refund_list_info.refund_amount,
        refund_operator_role=refund_list_info.refund_operator_role,
        refund_operator_role_zh=PddRefundInfo.convert_refund_operator_role(refund_list_info.refund_operator_role),
        refund_shipping_name=empty_str_as_none(refund_list_info.shipping_name),
        refund_tracking_number=empty_str_as_none(refund_list_info.tracking_number),
        user_shipping_status=refund_list_info.user_shipping_status,
        goods_id=refund_list_info.goods_id,
        goods_name=refund_list_info.goods_name,
        goods_number=refund_list_info.goods_number,
        goods_price=refund_list_info.goods_price,
        outer_goods_id=refund_list_info.outer_goods_id,
        outer_id=refund_list_info.outer_id,
        sku_id=refund_list_info.sku_id,
        order_sn=refund_list_info.order_sn,
        order_paid=order_info.pay_time,
        pay_amount=order_info.pay_amount,
        shipping_status=refund_detail_info.shipping_status,
        shipping_status_zh=PddRefundInfo.convert_shipping_status(refund_detail_info.shipping_status),
        order_status=order_info.order_status,
        order_status_zh=PddRefundInfo.convert_order_status(order_info.order_status),
        logistics_id=order_info.logistics_id,
        logistics_name=empty_str_as_none(logistics_info.logistics_name),
        tracking_number=empty_str_as_none(order_info.tracking_number),
        evidence=ComponentFileList(
            [ComponentFile(fileName="", url=image_url) for image_url in refund_detail_info.images]
        ),
        pay_and_refund_amount_diff=float(amount_diff(order_info.pay_amount, refund_list_info.refund_amount)),
        fs_trade_no=PddRefundInfo.convert_trade_no_list(refund_list_info.order_sn),
        fs_product_list=PddRefundInfo.convert_product_list(refund_list_info),
        agree_refund_apply_time=(refund_list_info.updated_time if refund_list_info.after_sales_status == 10 else None),
        is_speed_refund=is_speed_refund,
        has_speed_refund_flag=(True if refund_detail_info.speed_refund_flag == 1 else False),
        is_collected=is_collected,
        is_transported=is_transported,
        desensitization_address=build_desensitization_address(logistics_info),
    )
    return BusinessManager.create_bo_by_event(event, shop, data.dict())


def get_data_fetcher(sid: str, update_time_range: tuple[int, int]):
    from robot_processor.client.schema import PddRefundListResponse
    from robot_processor.form.event.paginator import PaginateDataFetcher

    class PddRefundDataFetcher(PaginateDataFetcher[PddRefundListResponse.RefundIncrementGetResponse.Refund]):
        def __init__(self, *args, **kwargs):
            super().__init__()
            self.sid = sid
            self.update_time_range = update_time_range

        def _do_fetch(self, page_no, page_size):
            from robot_processor.client import pdd_bridge_client
            from robot_processor.client.schema import PddRefundListPayload

            request = PddRefundListPayload(
                after_sales_type=1,  # 查询全部售后类型
                after_sales_status=0,  # 查询全部售后状态
                start_updated_at=self.update_time_range[0],
                end_updated_at=self.update_time_range[1],
                page=page_no + 1,  # 拼多多页码从 1 开始
                page_size=page_size,
            )
            response = pdd_bridge_client.refund_increment_list(store_id=self.sid, payload=request)
            self.total = response.refund_increment_get_response.total_count
            return response.refund_increment_get_response.refund_list

        def after_total_init_hook(self):
            logger.info(
                "拼多多店铺 {}~{} 共有 {} 条售后单".format(
                    arrow.get(self.update_time_range[0]).format("YYYY-MM-DD HH:mm:ss"),
                    arrow.get(self.update_time_range[1]).format("YYYY-MM-DD HH:mm:ss"),
                    self.total,
                )
            )

    return PddRefundDataFetcher()


def build_desensitization_address(logistics_info: LogisticsInfo):
    if not logistics_info:
        return ""
    desensitization_address = " ".join(
        [
            logistics_info.country or "",
            logistics_info.province or "",
            logistics_info.city or "",
            logistics_info.town or "",
        ]
    )
    return desensitization_address
