from datetime import datetime
from datetime import timed<PERSON><PERSON>
from typing import Generic
from typing import Iterator
from typing import TypeVar

T = TypeVar("T")


class PaginateDataFetcher(Generic[T]):
    def __init__(self, *args, **kwargs):
        self.page_no = 0
        self.page_size = 100
        self._total = None

    @property
    def total(self):
        return self._total

    @total.setter
    def total(self, value):
        if self._total is None:
            self._total = value
            self.after_total_init_hook()

    @property
    def offset(self):
        return self.page_no * self.page_size

    @property
    def has_next(self):
        return self.total is None or self.offset < self.total

    def _do_fetch(self, page_no: int, page_size: int) -> list[T]:
        raise NotImplementedError()

    def chain_fetch(self) -> Iterator[T]:
        while self.has_next:
            for item in self._do_fetch(self.page_no, self.page_size):
                yield item
            self.page_no += 1

    def after_total_init_hook(self):
        pass


class TimeRangePaginateDataFetcher(Generic[T]):
    def __init__(
        self, timerange: tuple[datetime, datetime], step: timedelta, page_no_start: int = 1, page_size: int = 100
    ):
        self.timerange = timerange
        self.step = step
        self.page_no_start = page_no_start
        self.page_size = page_size
        self._has_next = True

    @property
    def has_next(self):
        return self._has_next

    @has_next.setter
    def has_next(self, value):
        self._has_next = value

    def _do_fetch(self, start: datetime, end: datetime, page_no: int, page_size: int) -> list[T]:
        raise NotImplementedError()

    def chain_fetch(self) -> Iterator[T]:
        start, end = self.timerange
        cursor_start = start
        while cursor_start < end:
            cursor_end = min(cursor_start + self.step, self.timerange[1])
            page_no = self.page_no_start
            while self.has_next:
                yield from self._do_fetch(cursor_start, cursor_end, page_no, self.page_size)
                page_no += 1
            cursor_start = cursor_end
            self.has_next = True
