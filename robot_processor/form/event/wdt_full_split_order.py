import time

import arrow
import robot_types.helper
import robot_types.model
from loguru import logger
from robot_types.model.event.wdt.full_split_order_noti import \
    WdtFullSplitOrderNoti
from tcron_jobs import runner

from robot_processor.business_order.business_order_manager import \
    BusinessManager
from robot_processor.db import db
from robot_processor.enums import ErpType
from robot_processor.error.client_request import WdtRequestError
from robot_processor.form.event.models import EventConfig
from robot_processor.form.event.models import EventType
from robot_processor.form.event.models import FormEventTCronSchedule
from robot_processor.logging import vars as log_vars
from robot_processor.shop.auth_manager import get_wdt_shop_no_by_shop
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.models import Shop
from robot_processor.t_cron import wrap_tcron_job
from rpa.erp.wdt import WdtClient


def strftime(ts):
    time_struct = time.localtime(ts)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_struct)


@runner.register
@wrap_tcron_job
def poll_wdt_full_split_order(shop_id: int):
    shop = db.session.get(Shop, shop_id)
    assert shop, "店铺不存在"
    log_vars.Sid.set(shop.sid)
    log_vars.OrgId.set(shop.org_id)
    erp_info = ErpInfo.get_by_sid(shop.sid, ErpType.WDT)
    assert erp_info, "店铺未授权"
    event = EventConfig.get_by_id(EventType.WDT_FULL_SPLIT_ORDER_NOTI)
    erp_shop_id = get_wdt_shop_no_by_shop(shop)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(
        shop, EventType.WDT_FULL_SPLIT_ORDER_NOTI
    )
    wdt_client = WdtClient(shop.sid)
    now = arrow.now()
    start_ts = int(event_scheduler.executed_at.timestamp())
    now_ts = int(now.timestamp())

    while start_ts <= now_ts:
        next_ts = start_ts + 3600 if start_ts + 3600 <= now_ts else now_ts
        start_time = strftime(start_ts)
        end_time = strftime(next_ts)
        page_size = 100
        page_no = 0
        while True:
            try:
                resp = wdt_client.get_sent_orders_by_time(page_no, page_size,
                                                          start_time=start_time,
                                                          end_time=end_time,
                                                          shop_nos=erp_shop_id)
                for order in resp.response.trades:
                    if order.split_from_trade_id != "0":
                        src_tids = order.src_tids.split(',')
                        for src_tid in src_tids:
                            trade_resp = wdt_client.trade_query(src_tid)
                            wdt_orders = [wdt_order for wdt_order in
                                          trade_resp.response.trades if
                                          wdt_order.trade_type == 1]
                            if all(wdt_order.trade_status in [5, 95] for
                                   wdt_order in wdt_orders):
                                noti = WdtFullSplitOrderNoti(
                                    so_id=src_tid,
                                    logistics_info=[
                                        WdtFullSplitOrderNoti.LogisticsInfo(
                                            logistics_name=wdt_order.logistics_name,
                                            logisitcs_no=wdt_order.logistics_no,
                                        )
                                        for wdt_order in wdt_orders]
                                )
                                BusinessManager.create_bo_by_event(
                                    event, shop,
                                    robot_types.helper.serialize(noti)
                                )
                if not resp.response.trades:
                    break
                time.sleep(0.5)
            except WdtRequestError as e:
                logger.info(e.message)
            finally:
                page_no = page_no + 1
                start_ts = next_ts + 1
    event_scheduler.mark_executed(now.datetime)
