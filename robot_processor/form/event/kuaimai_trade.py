from datetime import datetime
from datetime import timed<PERSON><PERSON>
from decimal import Decima<PERSON>
from typing import TYPE_CHECKING

import arrow
import robot_types.model
from loguru import logger
from robot_types.helper import serialize
from tcron_jobs import runner

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.client import get_buyer_nick
from robot_processor.enums import ErpType
from robot_processor.form.event.models import EventConfig
from robot_processor.form.event.models import EventType
from robot_processor.form.event.models import FormEventTCronSchedule
from robot_processor.logging import log_vars
from robot_processor.plugin.trade_utils import ErpTradeManager
from robot_processor.shop.models import Shop
from robot_processor.t_cron import wrap_tcron_job
from rpa.erp.kuaimai import KmOrderSysStatusEnum
from rpa.erp.kuaimai import KmOrderTypeEnum
from rpa.erp.kuaimai import KmSDK
from rpa.erp.kuaimai import KuaimaiOrder

if TYPE_CHECKING:
    from robot_processor.form.event.models import ScheduleContext


@runner.register
@wrap_tcron_job
def poll_kuaimai_trade(org_id: int):
    erp_info_list = ErpTradeManager.get_erp_info_list(org_id, ErpType.KUAIMAI)
    if not erp_info_list:
        return
    erp_info = erp_info_list[0]
    log_vars.OrgId.set(org_id)
    log_vars.Event.set(EventType.KUAIMAI_TRADE)

    open_sdk = KmSDK(erp_info=erp_info)
    shop_map: dict[int, Shop | None] = dict()

    event = EventConfig.get_by_id(EventType.KUAIMAI_TRADE)
    event_scheduler = FormEventTCronSchedule.get_by_org_event(org_id, EventType.KUAIMAI_TRADE)
    start_at = event_scheduler.get_cursor_at(max_lag=timedelta(hours=1))
    timerange = (start_at, datetime.now())
    data_fetcher = get_data_fetcher(erp_info, timerange)
    for trade in data_fetcher.chain_fetch():
        modified = arrow.get(int(trade.modified) / 1000, tzinfo="Asia/Shanghai").naive
        if trade.userId in shop_map:
            shop = shop_map[trade.userId]
        else:
            shop_resp = open_sdk.get_shop_by_user_id(trade.userId)
            shop = Shop.query.filter_by(org_id=str(org_id), nick=shop_resp["title"]).first()
            shop_map[trade.userId] = shop
            if shop is None:
                logger.error(f"快麦店铺 {shop_resp['title']} 无法找到对应的飞梭店铺")
        if shop is None:
            event_scheduler.mark_executed(modified)
            continue
        BusinessManager.create_bo_by_event(event, shop, serialize(prepare_data(trade, shop)))
        event_scheduler.mark_executed(modified)


def get_scheduled_job_args(kwargs: "ScheduleContext"):
    from robot_processor.utils import unwrap_optional

    org_id = int(unwrap_optional(kwargs["shop"].org_id))
    return dict(org_id=org_id)


def prepare_data(trade: KuaimaiOrder, shop: Shop):
    if shop.is_taobao():
        buyer_nick = get_buyer_nick(shop.sid, tid=trade.tid)
    else:
        buyer_nick = "无昵称"

    order_type_zh_list = []
    for order_type in trade.type.split(","):
        try:
            order_type_zh_list.append(KmOrderTypeEnum(order_type).label)
        except ValueError:
            logger.error(f"未知的订单类型: {order_type}")
            order_type_zh_list.append("未知")
    data = robot_types.model.event.kuaimai.trade.KuaimaiTrade(
        fs_trade_no=[robot_types.model.component.TradeNo(tid=trade.tid)],
        buyer_nick=buyer_nick,
        sys_sid=Decimal(trade.sid),
        logistics_company=trade.expressCompanyName,
        logistics_no=trade.outSid,
        order_type_zh=order_type_zh_list,
        order_sys_status_zh=KmOrderSysStatusEnum.get_chinese(trade.sysStatus),
        split_sid=Decimal(trade.splitSid) if trade.splitSid else None,
    )
    return data


def get_data_fetcher(erp_info, timerange: tuple[datetime, datetime]):
    from robot_processor.form.event.paginator import TimeRangePaginateDataFetcher
    from rpa.erp.kuaimai import KuaimaiQmSDK

    qm_sdk = KuaimaiQmSDK(erp_info=erp_info)

    class KuaimaiTradeDataFetcher(TimeRangePaginateDataFetcher[KuaimaiOrder]):
        def _do_fetch(self, start, end, page_no, page_size):
            response = qm_sdk.get_all_orders_by_time(
                page_no=page_no,
                page_size=page_size,
                start_time=start.strftime("%Y-%m-%d %H:%M:%S"),
                end_time=end.strftime("%Y-%m-%d %H:%M:%S"),
            )
            self.has_next = response.hasNextPage
            return response.trades

    return KuaimaiTradeDataFetcher(timerange=timerange, step=timedelta(minutes=10), page_no_start=1, page_size=100)
