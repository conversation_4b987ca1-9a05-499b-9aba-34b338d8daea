from typing import List

import arrow
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import PddItem
from pydantic import BaseModel
from tcron_jobs import runner

from robot_processor.business_order.business_order_manager import \
    BusinessManager
from robot_processor.client import trade_client, TradeClient
from robot_processor.ext import db
from robot_processor.form.event.models import EventConfig
from robot_processor.logging import vars as log_vars
from robot_processor.symbol_table.named_typespec.component import \
    ComponentProduct
from robot_processor.symbol_table.named_typespec.component import \
    ComponentProductList
from robot_processor.symbol_table.named_typespec.component import \
    ComponentTradeNo
from robot_processor.symbol_table.named_typespec.component import \
    ComponentTradeNoList
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import make_fields_optional


@make_fields_optional
class PddTradeInfo(BaseModel):
    memo: str
    fs_trade_no: ComponentTradeNoList
    fs_product_list: ComponentProductList
    order_sn: str  # 订单号
    order_status_str:  str  # 发货状态


@runner.register
@wrap_tcron_job
def poll_pdd_shop_trade(shop_id: int) -> int:
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    event = EventConfig.get_by_id(EventType.PDD_TRADE)
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.PDD_TRADE)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(
        shop, EventType.PDD_TRADE
    )
    now = arrow.now()
    end_update_at = now.naive
    if now.shift(minutes=-30).naive > event_scheduler.executed_at:
        start_update_at = now.shift(minutes=-30).naive
    else:
        start_update_at = event_scheduler.executed_at
    count = 0
    page_no = 1
    page_size = 100
    while True:
        pb_resp = trade_client.get_period_trade_list_by_page(
            page_no,
            page_size,
            int(start_update_at.timestamp()),
            int(end_update_at.timestamp()),
            platform="PDD",
            sid=shop.sid,
            seller_nick=shop.nick,
        )
        for trade in pb_resp.trade_info_list:
            trade_info = PddTradeInfo(
                fs_trade_no=build_fs_trade_no_list(trade.pdd_trade_info.trade_id),
                memo=trade.pdd_trade_info.remark,
                fs_product_list=build_fs_product_list(
                    trade.pdd_trade_info.trade_id, trade.pdd_trade_info.item_list
                ),
                order_sn=trade.pdd_trade_info.trade_id,
                order_status_str=TradeClient.to_pdd_order_status_zh(trade.pdd_trade_info.order_status)
            )
            BusinessManager.create_bo_by_event(event, shop, trade_info.dict())
            count = count + 1
        page_no = page_no + 1
        if not pb_resp.has_more:
            break
    return count


def build_fs_trade_no_list(tid):
    return ComponentTradeNoList([ComponentTradeNo(tid=tid, oid=None)])


def build_fs_product_list(tid: str, orders: List[PddItem]):
    products = []
    for order in orders:
        products.append(
            ComponentProduct(
                TID=tid,
                OID=None,
                PICTURE=order.goods_img,
                TITLE=order.goods_name,
                DESCRIPTION=order.goods_spec,
                SPU=str(order.goods_id),
                SKU=order.sku_id,
                SKU_NAME=None,
                SPU_OUTER=order.outer_goods_id,
                SKU_OUTER=order.outer_id,
                PRICE=order.goods_price,
                INVENTORY=None,
                PAYMENT=None,
                COMBINE=None,
                COUNT=order.goods_count,
                SHORT_TITLE="",
                BATCH=[],
            )
        )
    return ComponentProductList(products)
