from datetime import datetime
from decimal import Decimal
from typing import List
from typing import Optional

import arrow
import robot_types.helper
import robot_types.model
import robot_types.model.event.xiaohongshu.refund.type_spec
from loguru import logger
from pydantic import BaseModel
from result import Err
from result import Result
from robot_types.model.component import Product
from tcron_jobs import runner

from robot_processor.client import xiaohongshu_client
from robot_processor.client.xiaohongshu_client import AfterSaleBasicInfo, \
    AfterSaleDetail
from robot_processor.db import db
from robot_processor.enums import AuthType
from robot_processor.logging import vars as log_vars
from robot_processor.symbol_table.named_typespec.component import \
    ComponentProductList
from robot_processor.symbol_table.named_typespec.component import \
    ComponentTradeNoList
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import amount_diff


class LogisitcsInfo(BaseModel):
    express_no: str
    express_name: str


class KuaishouRefundInfo(BaseModel):
    oid: str
    refund_id: str
    readable_handing_way: str
    readable_negotiate_status: str
    refund_fee: float
    fs_trade_no: ComponentTradeNoList
    readable_status: str
    readable_refund_type: str
    refund_desc: str
    readable_receipt_status: str
    refund_reason_desc: str
    expire_time: Optional[str]
    update_time: Optional[str]
    create_time: Optional[str]
    fs_product_list: ComponentProductList
    logisitcs_info: list[LogisitcsInfo]  # 发货物流
    readable_delivery_status: Optional[str]  # 发货状态
    is_split_delivery_order: bool  # 是否拆单发货
    return_express_no: Optional[str]
    return_express_name: Optional[str]
    pay_and_refund_amount_diff: float
    is_refund_all: bool
    agree_user: str  # 同意退款人
    order_paid: Optional[str]
    agree_refund_apply_time: Optional[str]  # 同意退款时间


@runner.register
@wrap_tcron_job
def poll_xiaohongshu_shop_refund(shop_id: int) -> int:
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.XIAOHONGSHU_REFUND)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(shop,
                                                               EventType.XIAOHONGSHU_REFUND)
    now = arrow.now()
    end_update_at = now.naive
    if now.shift(minutes=-30).naive > event_scheduler.executed_at:
        start_update_at = now.shift(minutes=-30).naive
    else:
        start_update_at = event_scheduler.executed_at
    access_token = shop.get_access_token(AuthType.XHS_ERP)
    if not access_token:
        logger.error(f"小红书店铺 {shop.id} 没有 access_token")
        return -1
    refund_paginator = get_data_fetcher(
        access_token=access_token,
        update_time_range=(start_update_at, end_update_at),
    )
    result: list[Result] = []
    for refund in refund_paginator.chain_fetch():
        refund_update_time = arrow.get(refund.updatedAt,
                                       tzinfo="Asia/Shanghai").naive
        if refund_update_time > event_scheduler.executed_at:
            event_scheduler.mark_executed(refund_update_time)
        try:
            result.extend(create_bo(shop, refund, access_token))
        except Exception as e:
            logger.opt(exception=e).error(f"创建售后单失败 {e}")
            result.append(Err(e))
    if now.naive > event_scheduler.executed_at:
        event_scheduler.mark_executed(now.naive)
    logger.info(
        f"创建售后单 {len([r for r in result if r.is_ok()])} 条，结果 {result}")
    return refund_paginator.total


def create_bo(shop, base_refund: AfterSaleBasicInfo,
              access_token: str) -> list[Result]:
    from robot_processor.business_order.business_order_manager import \
        BusinessManager
    from robot_processor.form.event.models import EventConfig
    from robot_processor.form.event.models import EventType

    event = EventConfig.get_by_id(EventType.XIAOHONGSHU_REFUND)
    log_vars.RefundId.set(base_refund.reasonId)
    log_vars.Tid.set(base_refund.orderId)
    logger.info("处理小红书售后单")
    refund_resp = xiaohongshu_client.aftersale_detail(access_token,
                                                      base_refund.returnsId)
    if not refund_resp.success:
        return [
            Err(f"无法获取小红书售后单详情 refund_id:{base_refund.returnsId}")]
    refund = refund_resp.data

    track_resp = xiaohongshu_client.order_tracking(access_token,
                                                   base_refund.orderId)
    if not track_resp.success:
        return [
            Err(f"无法获取小红书物流轨迹 order_id:{base_refund.orderId}")]
    is_collected = False
    is_transported = False
    is_signed = False

    if track_resp.data.orderTrackInfos:
        track = track_resp.data.orderTrackInfos[0]
        if track.currentStatusDesc in ["已揽件", "运输中", "已签收", "已完成"]:
            is_collected = True
        if track.currentStatusDesc in ["运输中", "已签收", "已完成"]:
            is_transported = True
        if track.currentStatusDesc in ["已签收", "已完成"]:
            is_signed = True

    data = robot_types.model.event.xiaohongshu.refund.Refund(
        fs_trade_no=build_fs_trade_no_list(refund.afterSaleInfo.orderId, None),
        order_id=refund.afterSaleInfo.orderId,
        refund_id=refund.afterSaleInfo.returnsId,
        return_type_zh=convert_return_type_zh(refund.afterSaleInfo.returnType),
        reason_name_zh=refund.afterSaleInfo.reasonNameZh,
        status_zh=convert_status_zh(refund.afterSaleInfo.status),
        support_carriage_insurance=refund.afterSaleInfo.supportCarriageInsurance,
        returns_tag_zh=convert_returns_tag_zh(refund.afterSaleInfo.returnsTag),
        applied_ship_fee_amount=Decimal(
            str(refund.afterSaleInfo.appliedShipFeeAmountYuan)),
        applied_skus_amount=Decimal(
            str(refund.afterSaleInfo.appliedSkusAmountYuan)),
        expected_refund_amount=Decimal(str(
            refund.afterSaleInfo.expectedRefundAmountYuan)),
        refund_amount=Decimal(str(refund.afterSaleInfo.refundAmountYuan)),
        refund_status_zh=convert_refund_status_zh(
            refund.afterSaleInfo.refundStatus),
        cargo_status_zh=convert_cargo_status_zh(
            refund.afterSaleInfo.cargoStatus),
        fs_product_list=build_fs_product_list(refund),
        express_no=(
            refund.logisticsInfo.order.expressNo
            if refund.logisticsInfo and refund.logisticsInfo.order
            else None
        ),
        express_name=(
            refund.logisticsInfo.order.expressCompanyName
            if refund.logisticsInfo and refund.logisticsInfo.order
            else None
        ),
        return_express_no=(
            refund.logisticsInfo.afterSale.expressNo
            if refund.logisticsInfo and refund.logisticsInfo.afterSale
            else None
        ),
        return_express_name=(
            refund.logisticsInfo.afterSale.expressCompanyName
            if refund.logisticsInfo and refund.logisticsInfo.afterSale
            else None
        ),
        is_refund_all=amount_diff(
            refund.afterSaleInfo.expectedRefundAmountYuan,
            refund.afterSaleInfo.appliedSkusAmountYuan).is_zero(),
        # pay_and_refund_amount_diff=None,
        # agree_user=None,
        # agree_refund_apply_time=None,
        # apply_time=None,
        is_collected=is_collected,
        is_transported=is_transported,
        is_signed=is_signed
    )
    create_result = BusinessManager.create_bo_by_event(event, shop,
                                                       robot_types.helper.serialize(
                                                           data))
    return create_result


def get_data_fetcher(access_token: str,
                     update_time_range: tuple[datetime, datetime]):
    from robot_processor.form.event.paginator import PaginateDataFetcher

    class XiaohongshuRefundDataFetcher(PaginateDataFetcher[AfterSaleBasicInfo]):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.access_token = access_token
            self.update_time_range = update_time_range

        def _do_fetch(self, page_no, page_size):
            refunds: List[AfterSaleBasicInfo] = []

            response = xiaohongshu_client.list_aftersale(
                access_token,
                page_no=page_no + 1,  # 小红书页码从 1 开始
                page_size=page_size,
                start_time=int(self.update_time_range[0].timestamp() *
                               1000),
                end_time=int(self.update_time_range[1].timestamp() * 1000),
                time_type=2
            )
            if not response.success:
                raise Exception(response.msg)
            refunds.extend(response.data.afterSaleBasicInfos)
            self.total = response.data.totalCount
            return refunds

        def after_total_init_hook(self):
            logger.info(
                "小红书店铺 {}~{} 共有 {} 条售后单".format(
                    self.update_time_range[0].strftime("%Y-%m-%d %H:%M:%S"),
                    self.update_time_range[1].strftime("%Y-%m-%d %H:%M:%S"),
                    self.total,
                )
            )

    return XiaohongshuRefundDataFetcher()


def convert_return_type_zh(return_type: int):
    # 1-退货 2-换货 4-已发货仅退款 5-未发货仅退款
    match return_type:
        case 1:
            return "退货"
        case 2:
            return "换货"
        case 4:
            return "已发货仅退款"
        case 5:
            return "未发货仅退款"
        case _:
            return "未知"


def convert_status_zh(status: int):
    # 1-待审核 2-待用户寄回 3-待商家收货 4-已完成 5-已取消6-已关闭 9-商家审核拒绝 9001-商家收货拒绝12-换货待商家发货 13-换货待用户确认收货 14-平台介入中
    match status:
        case 1:
            return "待审核"
        case 2:
            return "待用户寄回"
        case 3:
            return "待商家收货"
        case 4:
            return "已完成"
        case 5:
            return "已取消"
        case 6:
            return "已关闭"
        case 9:
            return "商家审核拒绝"
        case 9001:
            return "商家收货拒绝"
        case 12:
            return "换货待商家发货"
        case 13:
            return "换货待用户确认收货"
        case 14:
            return "平台介入中"
        case _:
            return "未知"


def convert_returns_tag_zh(returns_tag: int):
    # 0-未命中极速退款，1-命中平台极速退款，2-命中商家极速退款，3-命中售后助手
    match returns_tag:
        case 0:
            return "未命中极速退款"
        case 1:
            return "命中平台极速退款"
        case 2:
            return "命中商家极速退款"
        case 3:
            return "命中售后助手"
        case _:
            return "未知"


def convert_refund_status_zh(refund_status: int):
    # 108触发退款 1退款中 3退款失败 2退款成功 401已取消 101已创建 201待审核 301审核通过 302审核不通过 402自动关闭
    match refund_status:
        case 108:
            return "触发退款"
        case 1:
            return "退款中"
        case 3:
            return "退款失败"
        case 2:
            return "退款成功"
        case 401:
            return "已取消"
        case 101:
            return "已创建"
        case 201:
            return "待审核"
        case 301:
            return "审核通过"
        case 302:
            return "审核不通过"
        case 402:
            return "自动关闭"
        case _:
            return "未知"


def convert_cargo_status_zh(cargo_status: int):
    # 0:未选择 1：未收到货 2:已收到货
    match cargo_status:
        case 0:
            return "未选择"
        case 1:
            return "未收到货"
        case 2:
            return "已收到货"
        case _:
            return "未知"


def build_fs_trade_no_list(tid, oid):
    return [robot_types.model.component.TradeNo(tid=tid, oid=oid)]


def build_fs_product_list(refund: AfterSaleDetail):
    return [
        Product(
            TID=refund.afterSaleInfo.orderId,
            OID=None,
            PICTURE=sku.image,
            TITLE=sku.skuName,
            DESCRIPTION="",
            SPU=None,
            SKU=sku.skuId,
            SPU_OUTER=None,
            SKU_OUTER=sku.skuERPCode,
            PRICE=Decimal(str(sku.price)),
            INVENTORY=None,
            PAYMENT=None,
            COMBINE=None,
            COUNT=Decimal(str(sku.appliedCount)),
            SHORT_TITLE="",
            BATCH=[],
        ) for sku in refund.afterSaleInfo.skus
    ]
