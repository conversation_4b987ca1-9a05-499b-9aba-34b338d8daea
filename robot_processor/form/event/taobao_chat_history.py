import time

import arrow
import robot_types.helper
import robot_types.model
from robot_types.model.event.taobao.chat_history import TaobaoChat
from tcron_jobs import runner

from robot_processor.business_order.autofill.config import auto_fill_config
from robot_processor.business_order.business_order_manager import \
    BusinessManager
from robot_processor.client_mixins import Session
from robot_processor.ext import db
from robot_processor.form.event.models import FormEventTCronSchedule, EventType, \
    EventConfig
from robot_processor.logging import vars as log_vars
from robot_processor.shop.models import Shop
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import safe_datetime


def strftime(ts):
    time_struct = time.localtime(ts)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_struct)


@runner.register
@wrap_tcron_job
def poll_taobao_shop_chat_history(shop_id: int):
    shop = db.session.get(Shop, shop_id)
    assert shop, "店铺不存在"
    log_vars.Sid.set(shop.sid)
    log_vars.OrgId.set(shop.org_id)
    session = Session()
    event = EventConfig.get_by_id(EventType.TAOBAO_CHAT_HISTORY)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(
        shop, EventType.TAOBAO_CHAT_HISTORY
    )
    now = arrow.now()
    start_ts = int(event_scheduler.executed_at.timestamp())
    now_ts = int(now.timestamp())
    start_at = strftime(start_ts)
    end_at = strftime(now_ts)
    chats = session.get(
        auto_fill_config.CHAT_HISTORY_API,
        headers={"really": auto_fill_config.CHAT_HISTORY_AUTH},
        params={"seller_nick": shop.nick,
                "start_at": start_at,
                "end_at": end_at
                },
    ).json()["chat_history"]
    for chat in chats:
        if chat["body"]["format"] != "TEXT":
            continue
        send_by = "系统"
        if chat["sent_by"] in ["ASSISTANT", "QA", "MARKETING"]:
            send_by = "卖家"
        elif chat["sent_by"] in ["BUYER"]:
            send_by = "买家"
        out_chat = TaobaoChat(
            id=chat["uuid"],
            buyer_nick=chat["buyer_nick"],
            buyer_open_uid=chat["buyer_open_uid"],
            seller_nick=chat["seller_nick"],
            assistant_nick=chat["assistant_nick"],
            content=chat["body"]["content"],
            send_by=send_by,
            send_at=safe_datetime(chat["sent_at"])
        )
        BusinessManager.create_bo_by_event(
            event, shop,
            robot_types.helper.serialize(out_chat)
        )
    event_scheduler.mark_executed(now.datetime)
