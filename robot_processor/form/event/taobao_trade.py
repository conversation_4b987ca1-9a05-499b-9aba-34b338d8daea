from typing import List

import arrow
from leyan_proto.digismart.trade.dgt_trade_pb2 import Order
from leyan_proto.digismart.trade.dgt_trade_pb2 import TradeStatus
from pydantic import BaseModel
from tcron_jobs import runner

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.client import trade_client
from robot_processor.ext import db
from robot_processor.form.event.models import EventConfig
from robot_processor.logging import vars as log_vars
from robot_processor.symbol_table.named_typespec.component import ComponentProduct
from robot_processor.symbol_table.named_typespec.component import ComponentProductList
from robot_processor.symbol_table.named_typespec.component import ComponentTradeNo
from robot_processor.symbol_table.named_typespec.component import ComponentTradeNoList
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.types.common import BuyerInfo
from robot_processor.utils import make_fields_optional
from robot_processor.utils import message_to_dict


@make_fields_optional
class TaobaoTradeInfo(BaseModel):
    memo: str
    fs_trade_no: ComponentTradeNoList
    fs_product_list: ComponentProductList
    tid: str
    order_created: str  # 订单创建时间
    order_price: float  # 订单金额
    order_payment: float  # 实付金额
    trade_status_zh: str  # 订单状态


@runner.register
@wrap_tcron_job
def poll_taobao_shop_trade(shop_id: int) -> int:
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    event = EventConfig.get_by_id(EventType.TAOBAO_TRADE)
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.TAOBAO_TRADE)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(shop, EventType.TAOBAO_TRADE)
    now = arrow.now()
    end_update_at = now.naive
    if now.shift(minutes=-30).naive > event_scheduler.executed_at:
        start_update_at = now.shift(minutes=-30).naive
    else:
        start_update_at = event_scheduler.executed_at
    count = 0
    page_no = 1
    page_size = 100
    while True:
        pb_resp = trade_client.get_period_trade_list_by_page(
            page_no,
            page_size,
            int(start_update_at.timestamp()),
            int(end_update_at.timestamp()),
            platform="TAOBAO",
            sid=shop.sid,
            seller_nick=shop.nick,
        )
        for trade in pb_resp.trade_info_list:
            trade_status_zh = get_trade_status_zh(trade.tb_trade_info.status)
            trade_info = TaobaoTradeInfo(
                fs_trade_no=build_fs_trade_no_list(trade.tb_trade_info.trade_id),
                memo=trade.tb_trade_info.memo,
                fs_product_list=build_fs_product_list(trade.tb_trade_info.trade_id, trade.tb_trade_info.orders),
                tid=trade.tb_trade_info.trade_id,
                order_created=trade.tb_trade_info.created_at,
                order_price=message_to_dict(trade.tb_trade_info)["price"],
                order_payment=message_to_dict(trade.tb_trade_info)["payment"],
                trade_status_zh=trade_status_zh,
            )
            buyer_info = BuyerInfo(
                open_uid=trade.tb_trade_info.buyer_open_uid,
                nick=trade.tb_trade_info.buyer_nick,
            )
            BusinessManager.create_bo_by_event(event, shop, trade_info.dict(), buyer_info)
            count = count + 1
            event_scheduler.mark_executed(arrow.get(trade.tb_trade_info.modified_at, tzinfo="Asia/Shanghai").naive)
        page_no = page_no + 1
        if not pb_resp.has_more:
            break
    return count


def build_fs_trade_no_list(tid):
    return ComponentTradeNoList([ComponentTradeNo(tid=tid, oid=None)])


def build_fs_product_list(tid: str, orders: List[Order]):
    products = []
    for order in orders:
        products.append(
            ComponentProduct(
                TID=tid,
                OID=order.oid,
                PICTURE=order.pic_path,
                TITLE=order.title,
                DESCRIPTION=order.sku_description,
                SPU=order.spu_id,
                SKU=order.sku_id,
                SKU_NAME=order.title,
                SPU_OUTER=order.outer_spu_id,
                SKU_OUTER=order.outer_sku_id,
                PRICE=order.price,
                INVENTORY=None,
                PAYMENT=order.payment,
                COMBINE=None,
                COUNT=order.quantity,
                SHORT_TITLE="",
                BATCH=[],
            )
        )
    return ComponentProductList(products)


def get_trade_status_zh(status: int):
    if status == TradeStatus.WAIT_BUYER_PAY:
        return "等待买家付款"
    elif status == TradeStatus.WAIT_SELLER_SEND_GOODS:
        return "等待卖家发货"
    elif status == TradeStatus.SELLER_CONSIGNED_PART:
        return "卖家部分发货"
    elif status == TradeStatus.WAIT_BUYER_CONFIRM_GOODS:
        return "等待买家确认收货"
    elif status == TradeStatus.TRADE_BUYER_SIGNED:
        return "买家已签收（货到付款专用）"
    elif status == TradeStatus.TRADE_FINISHED:
        return "交易成功"
    elif status == TradeStatus.TRADE_CLOSED:
        return "交易关闭"
    elif status == TradeStatus.TRADE_CLOSED_BY_TAOBAO:
        return "交易被淘宝关闭"
    elif status == TradeStatus.TRADE_NO_CREATE_PAY:
        return "没有创建支付宝交易"
    elif status == TradeStatus.WAIT_PRE_AUTH_CONFIRM:
        return "余额宝0元购合约中"
    elif status == TradeStatus.PAY_PENDING:
        return "外卡支付付款确认中"
    elif status == TradeStatus.ALL_WAIT_PAY:
        return "所有买家未付款的交易"
    elif status == TradeStatus.ALL_CLOSED:
        return "所有关闭的交易"
    elif status == TradeStatus.PAID_FORBID_CONSIGN:
        return "该状态代表订单已付款但是处于禁止发货状态"
    return "未知"
