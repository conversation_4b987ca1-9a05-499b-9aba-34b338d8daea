"""抖店客服数据"""

import json
from datetime import datetime
from datetime import timedelta
from decimal import Decimal
from functools import wraps
from typing import TYPE_CHECKING
from typing import List

from loguru import logger
from pydantic import BaseModel
from tcron_jobs import runner

from robot_processor.ext import db
from robot_processor.logging import vars as log_vars
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import make_fields_optional
from rpa.conf import rpa_config

if TYPE_CHECKING:
    from robot_processor.form.event.models import ScheduleContext


@make_fields_optional
class StaffStat(BaseModel):
    # 时间
    stat_time: str
    # 客服名称
    staffName: str
    # 客服id
    staffId: str
    # 人工已接待会话量
    laborAmount: Decimal
    # 人工已接待人数
    laborUserAmount: Decimal
    # 预警会话量
    alarmConversationAmount: Decimal
    # 客服销售额
    salesAmount: Decimal
    # 询单人数
    inquirerAmount: Decimal
    # 支付人数
    purchasersAmount: Decimal
    # 满意率
    satisfactionRate: Decimal
    # 询单转化率
    inquiryConversionRate: Decimal
    # 飞鸽平均响应时长
    avgResponseDuration: str
    # 3分钟人工回复率(消息)
    threeMinResponseRate: Decimal
    # 3分钟人工回复率(会话)
    threeMinResponseConRate: Decimal
    # 账号名称
    staff_username: str
    # 每日可接待量(CPD)
    cpdMap: Decimal
    # 首次响应时长
    firstResponseDuration: str
    # 飞鸽不满意率
    dissatisfactionRate: Decimal
    # 下单人数
    createOrderAmount: Decimal

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        # 处理 Decimal 类型
        return json.loads(super().json(**kwargs))


@make_fields_optional
class StaffStatList(BaseModel):
    time: str  # 统计时间
    list: List[StaffStat]


def get_scheduled_job_args(kwargs: "ScheduleContext"):
    from robot_processor.client import rpa_client
    from robot_processor.utils import unwrap_optional

    template_name = "抖店店铺数据获取"
    shop_id = kwargs["shop"].id
    org_id = int(unwrap_optional(kwargs["shop"].org_id))
    workflow_id = rpa_client.get_workflow_id(template_name, org_id).data.workflow_id
    return dict(shop_id=shop_id, workflow_id=workflow_id)


@runner.register
@wrap_tcron_job
def poll_doudian_staff_stat(shop_id: int, workflow_id: int) -> str:
    from robot_processor.business_order.business_order_manager import BusinessManager
    from robot_processor.client import rpa_client
    from robot_processor.form.event.models import EventConfig
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    event_type = EventType.DOUDIAN_STAFF_STAT
    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return "店铺已失效"
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(event_type)

    ack_result = rpa_client.execute_workflow_wait_ack(
        workflow_id,
        None,
        dict(pid=shop.sid, method="get-staff-stat"),
        timeout=rpa_config.pdd_shop_metrics_wait_act_timeout,
    )
    if ack_result.is_err():
        return str(ack_result)
    ack = ack_result.unwrap()
    event = EventConfig.get_by_id(event_type)
    FormEventTCronSchedule.get_by_shop_event(shop, event_type).mark_executed()
    if not ack.success:
        return ack.json()
    output = ack.output[0]["object"]
    if not output["success"]:
        return str(output)
    yesterday = datetime.now().date() - timedelta(days=1)
    staff_stat_list = []
    for row in output["result"]:
        result_items = {key: row[key]["value"] if row[key]["value"] != "-" else None for key in row}
        if result_items.get("salesAmount"):
            result_items["salesAmount"] = result_items["salesAmount"].replace("元", "")
        if result_items.get("satisfactionRate"):
            result_items["satisfactionRate"] = result_items["satisfactionRate"].replace("%", "")
        if result_items.get("inquiryConversionRate"):
            result_items["inquiryConversionRate"] = result_items["inquiryConversionRate"].replace("%", "")
        if result_items.get("threeMinResponseRate"):
            result_items["threeMinResponseRate"] = result_items["threeMinResponseRate"].replace("%", "")
        if result_items.get("threeMinResponseConRate"):
            result_items["threeMinResponseConRate"] = result_items["threeMinResponseConRate"].replace("%", "")
        if result_items.get("dissatisfactionRate"):
            result_items["dissatisfactionRate"] = result_items["dissatisfactionRate"].replace("%", "")
        result_items["stat_time"] = yesterday.strftime("%Y-%m-%d")
        stat = StaffStat(**result_items)
        if stat.laborAmount == Decimal(0):
            continue
        staff_stat_list.append(stat)
    create_result = BusinessManager.create_bo_by_event(
        event,
        shop,
        StaffStatList(time=yesterday.strftime("%Y-%m-%d"), list=staff_stat_list).dict(),
    )
    logger.info(f"create bo result: {create_result}")
    return ""
