"""工单模板编辑页的 API"""

from typing import Any
from typing import Dict
from typing import List
from typing import Literal
from typing import Op<PERSON>
from typing import Union
from typing import cast

from flask import Blueprint
from flask import abort
from flask import current_app
from flask import g
from flask import jsonify
from flask import make_response
from flask import request
from pydantic import BaseModel
from pydantic import Field
from pydantic import ValidationError
from pydantic import root_validator
from pydantic import validator

from robot_processor.assistant.schema import AssistantV2
from robot_processor.business_order.models import BusinessOrder
from robot_processor.db import in_transaction
from robot_processor.decorators import shop_required
from robot_processor.enums import AssigneeRule
from robot_processor.enums import FormCategory
from robot_processor.enums import FormMold
from robot_processor.enums import StepType
from robot_processor.error.validate import ValidateError
from robot_processor.ext import db
from robot_processor.form.api.form_editor_schema import StepNotifyConfig
from robot_processor.form.api.utils import check_form_step
from robot_processor.form.api.utils import decorator_check_form_step
from robot_processor.form.event.models import EventType
from robot_processor.form.form_edit_lock import FormEditLock
from robot_processor.form.form_edit_lock import FormEditLockError
from robot_processor.form.form_edit_lock import LockInfo
from robot_processor.form.form_subscribe import FormSubscribe
from robot_processor.form.models import Form
from robot_processor.form.models import FormShop
from robot_processor.form.models import FormVersion
from robot_processor.form.models import FormWrapper
from robot_processor.form.models import Step
from robot_processor.form.models import StepWrapper
from robot_processor.form.models import WidgetInfo
from robot_processor.form.models import WidgetRef
from robot_processor.form.schemas import StepAutoJumpConfig
from robot_processor.form.schemas import StepValidationConfig
from robot_processor.form.symbol_table import FormSymbolEditableView
from robot_processor.form.system_widget_utils import create_step_0
from robot_processor.rpa_service.models import RpaContext
from robot_processor.shop.kiosk_models import KioskOrg
from robot_processor.shop.models import Shop
from robot_processor.utils import BaseResponse
from robot_processor.utils import GenericResponse
from robot_processor.utils import get_nonce
from robot_processor.utils import unwrap_optional
from robot_processor.validator import validate

api = Blueprint("form-editor-api", __name__)

api.register_error_handler(ValidateError, ValidateError.error_handler_with_code)


@api.errorhandler(FormEditLockError)
def edit_lock_error_wrapper(error: FormEditLockError):
    response = BaseResponse.Failed(str(error)).dict()
    response["nick"] = error.lock_info["nick"]
    return make_response(jsonify(response))


@api.before_request
@shop_required
def check_shop():
    import robot_processor.logging.vars as log_vars
    from robot_processor.constants import SHOP_EXPIRE_MSG

    shop: Shop = g.shop

    view_args = request.view_args or {}
    log_vars.LoginUserNick.set(g.nick)
    if form_id := view_args.get("form_id"):
        log_vars.FormId.set(form_id)
    if step_id := view_args.get("step_id"):
        log_vars.StepId.set(step_id)

    if request.method.upper() not in ["PUT", "POST", "PATCH", "DELETE"]:
        # GET 请求放行。店铺活期时，允许查看，不允许修改
        return

    if not shop.is_valid():
        response = BaseResponse.Failed(SHOP_EXPIRE_MSG, "shop expired")
        abort(make_response(jsonify(response.dict()), 403))
    if not KioskOrg.is_org_enabled(int(unwrap_optional(shop.org_id))):
        response = BaseResponse.Failed(SHOP_EXPIRE_MSG, "shop expired")
        abort(make_response(jsonify(response.dict()), 403))


class FormEditLockRequest(BaseModel):
    form_id: int


class FormEditLockResponse(BaseModel):
    succeed: bool
    nick: Optional[str] = Field(default=None)
    form_last_updated_at: Optional[int] = Field(default=None)

    @classmethod
    def Acquired(cls, lock_info: LockInfo):
        return cls(
            succeed=True,
            nick=lock_info["nick"],
            form_last_updated_at=lock_info["last_updated"],
        )

    @classmethod
    def Released(cls):
        return cls(succeed=True)


@api.put("/forms/edit-lock")
@validate
def acquire_form_edit_lock(body: FormEditLockRequest) -> FormEditLockResponse:
    shop: Shop = g.shop
    form_id = body.form_id
    check_form_step(form_id=form_id, shop=shop)

    form: FormWrapper = Form.query.get(form_id).wraps(shop)  # type: ignore[union-attr]
    lock_info = FormEditLock(g.nick, form).acquire()
    return FormEditLockResponse.Acquired(lock_info)


@api.delete("/forms/edit-lock")
@validate
def release_form_edit_lock(body: FormEditLockRequest) -> FormEditLockResponse:
    shop: Shop = g.shop
    form_id = body.form_id
    check_form_step(form_id=form_id, shop=shop)

    form = Form.query.get(form_id).wraps(shop)  # type: ignore[union-attr]
    FormEditLock(g.nick, form).release()
    return FormEditLockResponse.Released()


FormStepEditorResponse = GenericResponse[Step.View.FormEditor]


@api.get("/forms/<form_id>/steps/<step_id>")
@decorator_check_form_step
@validate
def get_form_step(form_id, step_id) -> FormStepEditorResponse:
    shop: Shop = g.shop
    step = Step.query.get(step_id).wraps(shop)  # type: ignore[union-attr]
    step_view = Step.View.FormEditor.from_orm(step)

    return FormStepEditorResponse.Success(step_view)


class FormStepEditRequest(BaseModel):
    class Config:
        arbitrary_types_allowed = True

    class StepAutoRetryConfig(BaseModel):
        retry_duration: int = 0
        retry_interval: int
        retry_times: int = 0
        can_retry: bool = False

        @root_validator(pre=True)
        def check_interval(cls, values):
            if values.get("can_retry"):
                from robot_processor.app import app

                if (values.get("retry_interval") or 0) < app.config.get("STEP_AUTO_RETRY_INTERVAL_MIN", 600):
                    raise ValidateError("重试间隔太小", code=400)
            return values

    notify_configs: Optional[List[StepNotifyConfig]] = Field(default_factory=list)

    edit_type: Optional[int] = Field(
        default=1, description="1:保存步骤且校验数据 2: 只保存不校验（用于更新prev/next的临时方式）"
    )

    name: str
    description: Optional[str]
    step_type: StepType
    can_retry: bool = Field(default=False)
    can_skip: bool = Field(default=False)
    can_auto_skip_when_fail: bool = Field(default=False)
    auto_retry_config: Optional[StepAutoRetryConfig] = Field(default=None)
    jump: Optional[StepAutoJumpConfig] = Field(default=None)

    prev_step_ids: List[str] = Field(default_factory=list)
    next_step_ids: List[str] = Field(default_factory=list)

    buyer_edit: bool = Field(default=False)
    buyer_reply: Optional[list[dict]] = Field(default=None)
    # step_type=human
    widget_collection_id: Optional[int] = Field(default=None)
    symbols: list[FormSymbolEditableView] | None = None
    assistants_v2: Optional[AssistantV2]
    assignee_rule: Optional[AssigneeRule] = Field(default=None)
    # step_type=auto
    task_id: Optional[int] = Field(default=None)
    key_map: Optional[dict] = Field(default_factory=dict)

    # 用于表示成对节点的指向；左节点指向右边节点；右节点指向左节点
    # 如遍历拆封的coupled_step_uuid指向遍历合并的step_uuid
    coupled_step_uuid: Optional[str]
    branch: Optional[List[dict]] = Field(default_factory=list)
    # 当前步骤引用的组件，不同类型的步骤会有不同的action
    # 在遍历网关中：表示要遍历的组件
    widget_ref: Optional[WidgetRef]

    notifier: Optional[Step.Schema.Notifier] = Field(default=None)
    display_rule: Optional[Any]

    # 在 event 类型的步骤中，标识事件的触发条件
    event: str | None
    event_shortcut: str | None
    event_filters: dict | None

    # 表单校验规则
    validation_config: StepValidationConfig = Field(default_factory=StepValidationConfig.default)

    # 在 api 请求中，从 auth 信息中自动绑定
    org_id: int
    # 在 api 请求中，从 request args 信息中自动绑定
    form_id: int
    # 在 api 请求中，从 request args 信息中自动绑定
    step_id: Optional[int]
    id: Optional[int] = Field(default=None, description="批量修改步骤时，会使用这个字段标识")

    @validator("event", pre=True)
    def validate_event(cls, value):
        if isinstance(value, str):
            try:
                EventType[value]
            except KeyError:
                raise ValueError(f"未知的事件类型: {value}")
        return value

    @validator("can_skip", pre=True)
    def set_can_skip(cls, can_skip):
        """默认为 False, 兼容前端传参为 None 的情况"""
        return can_skip if can_skip is not None else cls.__fields__["can_skip"].default

    @root_validator(pre=True)
    def set_assistant_v2_and_step_type(cls, values):
        """
        人工步骤一般都会有 assistants_v2 配置的
        但是有个例外，就是买家自助填写的工单模板，开始步骤的执行客服默认是隐藏的
        为了兼容工单模板发布校验，这里填一个默认值
        """
        raw_step_type = values.get("step_type")
        step_type = StepType.human
        if isinstance(raw_step_type, str):
            step_type = getattr(StepType, raw_step_type)
        if isinstance(raw_step_type, int):
            step_type = StepType(raw_step_type)
        values["step_type"] = step_type.value
        if step_type == StepType.human and values.get("assistants_v2") in (None, {}):
            values["assistants_v2"] = AssistantV2()

        return values

    @root_validator(pre=True)
    def set_auto_skip_false_if_skip_false(cls, values):
        """
        如果不允许跳过了，那也不允许自动跳过
        如果不允许重试了，那也不允许自动重试
        """
        if values.get("can_skip") is False:
            values["can_auto_skip_when_fail"] = False
        if values.get("can_retry") is False:
            values["auto_retry_config"] = cls.StepAutoRetryConfig(
                can_retry=False,
                retry_interval=0,
            )
        return values

    @root_validator(pre=True)
    def fix_task_id(cls, values):
        if values.get("task_id") is None and "rpa_id" in values.get("data", {}):
            values["task_id"] = values["data"]["rpa_id"]
        return values

    @root_validator(pre=True)
    def bind_shop_in_request_context(cls, values):
        if g and getattr(g, "shop", None):
            values["org_id"] = g.shop.org_id
        else:
            raise Exception("单元测试时，需要手动绑定 g.shop")

        return values

    @root_validator(pre=True)
    def bind_form_step_in_request_context(cls, values):
        view_args = request.view_args or {}
        if form_id := view_args.get("form_id"):
            values["form_id"] = form_id
        else:
            raise Exception("单元测试时，需要手动绑定 form_id")

        # step_id 可能为空，因为创建步骤时，不需要 step_id
        # 单步骤编辑时，在 request.view_args["step_id"] 字段上
        step_id = view_args.get("step_id")
        # 批量编辑时，在 id 字段上
        if step_id is None:
            step_id = values.get("id")
        values["step_id"] = step_id

        return values

    @root_validator(skip_on_failure=True)
    def check_step_name_unique(cls, values):
        new_name = values["name"]
        form = Form.query.get(values["form_id"])
        step_query = form.backend_steps.filter(Step.name == new_name)  # type: ignore[union-attr]
        if values["step_id"] and (step := Step.query.get(values["step_id"])):
            step_query = step_query.filter(Step.step_uuid != step.step_uuid)
        if db.session.query(step_query.exists()).scalar():
            raise ValidateError("步骤名称重复")

        return values

    @root_validator(skip_on_failure=True)
    def check_assignee_rule(cls, values):
        if values["edit_type"] == 2:
            return values
        # Step.assignee_rule 有默认值
        # 在实际做 step.update 时会重写 assignee_rule 字段，这里就不判断了
        # if (
        #     not values["prev_step_ids"]  # 姑且这么判断是不是第一步
        #     and values["assignee_rule"] is not None
        # ):
        #     raise ValidateError("第一步不允许选择分派逻辑")

        form = Form.query.get(values["form_id"])
        if (
            values["assignee_rule"] == AssigneeRule.MANUAL
            and db.session.query(
                form.backend_steps.filter(  # type: ignore[union-attr]
                    Step.step_type != StepType.human,
                    Step.step_uuid.in_(values["prev_step_ids"]),
                ).exists()
            ).scalar()
        ):
            raise ValidateError("上一步有非人工步骤，不允许选择手动分派逻辑")
        if (
            values["step_type"] == StepType.auto
            and db.session.query(
                form.backend_steps.filter(  # type: ignore[union-attr]
                    Step.assignee_rule == AssigneeRule.MANUAL,
                    Step.step_uuid.in_(values["next_step_ids"]),
                ).exists()
            ).scalar()
        ):
            raise ValidateError("当前步骤是自动步骤，下一步不允许选择手动分派逻辑")

        return values

    @root_validator(skip_on_failure=True)
    def check_when_step_type_human(cls, values):
        if values["edit_type"] == 2:
            return values
        if values["step_type"] != StepType.human:
            return values

        if values.get("assistants_v2") is None:
            raise ValidateError("人工步骤缺少执行客服")
        assistants_v2: AssistantV2 = values["assistants_v2"]
        if assistants_v2.online_only and values.get("assignee_rule") != AssigneeRule.RANDOM:
            raise ValidateError("分派给在线客服的分派方式只能是自动分派")
        # if values.get("widget_collection_id") is None:
        #     raise ValidateError("人工步骤缺失表单信息")
        if len(values["next_step_ids"]) > 1:
            raise ValidateError("人工步骤只能有一个后续步骤")
        if values["buyer_edit"] and values.get("buyer_reply") is None:
            raise ValidateError("缺失买家引导话术")
        return values

    @root_validator(skip_on_failure=True)
    def check_when_step_type_auto(cls, values):
        if values["edit_type"] == 2:
            return values
        if values["step_type"] != StepType.auto:
            return values
        if not values["task_id"]:
            raise ValidateError("缺少自动化任务")

        if (org_id := values["org_id"]) and not db.session.query(
            RpaContext.query.filter(
                RpaContext.org_id == org_id,
                RpaContext.rpa_id == values["task_id"],
                RpaContext.enabled,
            ).exists()
        ).scalar():
            raise ValidateError("自动化任务未订阅")

        if len(values["next_step_ids"]) > 1:
            raise ValidateError("自动化步骤只能有一个后续步骤")

        return values

    @root_validator(skip_on_failure=True)
    def check_branch(cls, values):
        from robot_processor.business_order.condition.condition import Operator
        from robot_processor.business_order.condition.condition import Relation

        if values["edit_type"] == 2:
            return values
        if values["step_type"] not in [
            StepType.exclusive_gateway,
            StepType.iterate_gw_begin,
        ]:
            return values

        if not values.get("branch"):
            raise ValidateError("缺少分支信息")

        def check_condition_group(condition_group_):
            if not condition_group_:
                raise ValidateError(f"分支[{branch_name}]缺少分支条件")
            relation = condition_group_.get("relation")
            if not relation:
                raise ValidateError(f"分支[{branch_name}]缺少关系操作符")
            if relation not in Relation.valid_relation():
                raise ValidateError(f"关系操作符[{relation}]未定义")
            condition_list = condition_group_.get("condition_list")
            if not condition_list:
                raise ValidateError(f"分支[{branch_name}]缺少条件")

            for condition in condition_list:
                if condition.get("type") == "single":
                    check_condition(condition)
                else:
                    check_condition_group(condition.get("data"))

        def check_condition(condition):
            condition_data = condition.get("data", {})
            required_attr = ["ref", "ref_type", "value", "value_type"]
            if not all(map(lambda attr: attr in condition_data, required_attr)):
                raise ValidateError("分支条件中缺少引用组件或值")
            op = condition_data.get("operator")
            if not op or op not in Operator.valid_operator():
                raise ValidateError(f"组件操作符[{op}]未定义")
            ref_type = condition_data["ref_type"]
            ref = condition_data["ref"]
            if ref_type == "table":
                if not isinstance(ref, dict):
                    raise ValidateError(f"分支条件不支持当前组件: {ref}@{type(ref)}")
                try:
                    WidgetRef.validate(ref)
                except ValidationError as error:
                    raise ValidateError(f"组件值格式错误，{ref=}, {error=}")

        branch_names = []
        has_default = False
        for branch_obj in values["branch"]:
            branch_name = branch_obj.get("name")
            if not branch_name:
                raise ValidateError("缺少分支名称")
            if branch_name in branch_names:
                raise ValidateError(f"分支名称[{branch_name}]重复")
            branch_names.append(branch_name)
            if "enable" not in branch_obj:
                raise ValidateError(f"分支[{branch_name}]缺少启用状态")
            if "type" not in branch_obj:
                raise ValidateError(f"分支[{branch_name}]缺少类型")
            if "order" not in branch_obj:
                raise ValidateError(f"分支[{branch_name}]缺少顺序")

            condition_group = branch_obj.get("condition_group")
            if branch_obj["type"] == "DEFAULT":
                if has_default:
                    raise ValidateError("只能有一个默认分支")
                has_default = True
                if condition_group:
                    raise ValidateError("默认分支不允许配置分支条件")
            else:
                check_condition_group(condition_group)

        if not has_default:
            raise ValidateError("缺少默认分支")

        return values

    @root_validator(skip_on_failure=True)
    def check_when_step_type_iterate_gateway_begin(cls, values):
        if values["edit_type"] == 2:
            return values
        if values["step_type"] != StepType.iterate_gw_begin:
            return values
        if request.method == "POST":  # 创建步骤时跳过检查
            return values

        if not values.get("widget_ref"):
            raise ValidateError("缺失【遍历拆封组件】")
        if not values.get("coupled_step_uuid"):
            raise ValidateError("缺失【遍历合并】节点的引用")

        return values

    @root_validator(skip_on_failure=True)
    def check_when_step_type_iterate_gateway_end(cls, values):
        if values["edit_type"] == 2:
            return values
        if request.method == "POST":  # 创建步骤时跳过检查
            return values
        if values["step_type"] != StepType.iterate_gw_end:
            return values
        if not values.get("coupled_step_uuid"):
            raise ValidateError("缺失【遍历合并】节点的引用")

        return values

    def replace_system_job_key(self, step_uuid):
        from robot_processor.business_order.models import JobSystemAttributes

        if self.symbols:
            for symbol in self.symbols:
                if symbol.name is not None:
                    symbol.name = JobSystemAttributes.replace_temp_symbol_name(symbol.name, step_uuid)


class BatchFormStepEditRequest(BaseModel):
    steps: List[FormStepEditRequest] = Field(default_factory=list)


@api.post("/forms/<form_id>/steps")
@decorator_check_form_step
def create_form_step(form_id):
    request_body = request.get_json()
    if "steps" in request_body:
        return batch_create_step(form_id=form_id, body=BatchFormStepEditRequest(**request_body))
    else:
        return create_step(form_id=form_id, body=FormStepEditRequest(**request_body))


def create_step(form_id, body: FormStepEditRequest) -> FormStepEditorResponse:
    shop: Shop = g.shop
    form = Form.query.get(form_id).wraps(shop)  # type: ignore[union-attr]

    with FormEditLock(g.nick, form).auto_refresh(), in_transaction():
        step = Step(step_uuid=get_nonce())
        body.replace_system_job_key(step_uuid=step.step_uuid)
        db.session.add(step)
        step.form = form
        # 创建步骤时前端没有步骤的 step_uuid 信息
        # 在步骤级别系统字段产品化之前，由前后端约定系统字段的规则，在创建时进行系统字段 key 的替换
        step.pipeline_update(update_user=g.nick, **body.dict(exclude_unset=True))
    step.wraps(shop).trigger_on_create(update_user=g.nick)
    step_view = Step.View.FormEditor.from_orm(step)
    return FormStepEditorResponse.Success(step_view)


def batch_create_step(form_id, body: BatchFormStepEditRequest) -> "BatchStepEditResponse":
    shop: Shop = g.shop
    form: FormWrapper = Form.query.get(form_id).wraps(shop)  # type: ignore[union-attr]

    with FormEditLock(g.nick, form).auto_refresh(), in_transaction():
        data = []
        for step_info in body.steps:
            step = Step()
            db.session.add(step)
            step.form = form
            step.pipeline_update(update_user=g.nick, **step_info.dict(exclude_unset=True))
            step.wraps(shop).trigger_on_create(update_user=g.nick)
            data.append(Step.View.FormEditor.from_orm(step))

    return BatchStepEditResponse.Success(data)


@api.put("/forms/<form_id>/steps/<step_id>")
@decorator_check_form_step
@validate
def update_form_step(form_id, step_id, body: FormStepEditRequest) -> FormStepEditorResponse:
    """保存步骤"""
    shop: Shop = g.shop
    form = Form.query.get(form_id).wraps(shop)  # type: ignore[union-attr]
    step = Step.query.get(step_id).wraps(shop)  # type: ignore[union-attr]

    # 更新步骤
    with FormEditLock(g.nick, form).auto_refresh():
        step = step.pipeline_update(update_user=g.nick, **body.dict(exclude_unset=True))
    step.wraps(shop).trigger_on_update(update_user=g.nick)
    step_view = Step.View.FormEditor.from_orm(step)

    return FormStepEditorResponse.Success(step_view)


@api.delete("/forms/<form_id>/steps/<step_id>")
@decorator_check_form_step
@validate
def delete_form_step(form_id, step_id) -> FormStepEditorResponse:
    # 申请锁，更新锁的时间
    shop = g.shop
    form: Form = Form.query.get(form_id).wraps(shop)  # type: ignore[union-attr]
    step: StepWrapper = Step.query.get(step_id).wraps(shop)  # type: ignore[union-attr]

    with FormEditLock(g.nick, form).auto_refresh():
        step.delete()
    step.trigger_on_delete(update_user=g.nick)
    step_view = Step.View.FormEditor.from_orm(step)

    return FormStepEditorResponse.Success(step_view)


class ListShopFormRequest(BaseModel):
    form_category: Optional[Literal["ALL", "ALIPAY"]] = Field(description="工单模板类型")
    form_name: Optional[str] = Field(description="工单名")
    enabled: Optional[bool] = Field(description="工单启用状态")


class PutFormRequest(BaseModel):
    """创建/更新工单模板"""

    category: Optional[str] = Field(default=None, alias="form_category")
    name: str
    description: Optional[str] = Field(default="", max_length=500)
    ignore_auto_create_start: Optional[bool] = Field(default=False)
    form_mold: Optional[FormMold] = Field(default=FormMold.CUSTOM)
    co_edit: Optional[bool] = Field(default=True)
    tags: Optional[List[str]] = Field(default_factory=list)
    enable_service_count: Optional[bool] = Field(default=False)
    need_archive: Optional[bool] = Field(default=False)
    deprecated_enabled: Optional[bool] = Field(default=None, alias="enabled", description="已弃用")
    expect_duration: Optional[int] = Field(default=None, description="预计工单执行时间")

    sync_shop_dict: Optional[Dict[str, bool]] = Field(default=None)
    owner_list: AssistantV2 | None = Field(default=None)

    def sync_to_orm(self, form: Form):
        """将前端设置的 form 属性同步到 orm 对象."""
        form.name = self.name
        fields_set = self.__fields_set__  # 前端显式赋值过的字段
        if "category" in fields_set:
            form.category = self.category
        if "description" in fields_set:
            form.description = self.description
        if "form_mold" in fields_set:
            form.form_mold = unwrap_optional(self.form_mold)
        if "co_edit" in fields_set:
            form.co_edit = unwrap_optional(self.co_edit)
        if "tags" in fields_set:
            form.tags = unwrap_optional(self.tags)
        if self.category == FormCategory.BUYER_TABLE.name:
            form.enable_service_count = True
        elif "enable_service_count" in fields_set:
            form.enable_service_count = unwrap_optional(self.enable_service_count)
        if "need_archive" in fields_set:
            form.need_archive = unwrap_optional(self.need_archive)
        if "expect_duration" in fields_set:
            form.expect_duration = self.expect_duration


FormEditorResponse = GenericResponse[Form.View.FormEditor]


@api.get("/forms")
@api.get("/forms/<form_id>")
@decorator_check_form_step
@validate
def list_shop_forms(query: ListShopFormRequest, form_id: Optional[int] = None):
    shop: Shop = g.shop

    if form_id is None:  # 工单模板列表
        response = list_shop_form(shop, param=query)
        return jsonify(response.dict())
    else:  # 工单模板详情
        form = Form.query.get(form_id)
        form_editor_view = Form.View.FormEditor.from_form_shop(form, shop)  # type: ignore[arg-type]
        response = FormEditorResponse.Success(form_editor_view)
        return jsonify(response.dict())


@api.post("/forms")
@decorator_check_form_step
@validate
def create_form(body: PutFormRequest) -> FormEditorResponse:
    shop: Shop = g.shop
    Form.Utils.check_form_name_unique(form_name=body.name, shop=shop)

    with in_transaction() as session:
        # 创建工单
        form = Form()
        session.add(form)
        form.update_user = g.nick
        body.sync_to_orm(form)
        session.flush()

        # 更新订阅信息
        form_subscribe = FormSubscribe(form)
        # 在创建工单时，至少要为 1 个店铺订阅该模板
        subscribe_info = body.sync_shop_dict or {shop.sid: True}
        for sid, enabled in subscribe_info.items():
            if not (subscribe_shop := Shop.Queries.optimal_shop_by_sid(sid, org_id=shop.org_id)):
                continue
            form_subscribe.subscribe(subscribe_shop, enabled=enabled)

    form = form.wraps(shop)
    form.trigger_on_create(update_user=g.nick)
    if not body.ignore_auto_create_start:
        step_begin = create_step_0(form, owners=body.owner_list)
        db.session.add(step_begin)
    db.session.commit()
    form_view = Form.View.FormEditor.from_form_shop(form, shop)
    return FormEditorResponse.Success(form_view).dict()


@api.put("/forms/<form_id>")
@decorator_check_form_step
@validate
def publish_form(form_id: int, body: PutFormRequest) -> BaseResponse:
    shop: Shop = g.shop
    form_orm = Form.query.get(form_id)
    if not form_orm:
        return BaseResponse.Failed("工单模版不存在")
    form: FormWrapper = form_orm.wraps(shop)
    Form.Utils.check_form_name_unique(form_name=body.name, shop=shop, form_id=form_id)
    begin_step: Step | None = form.get_begin_step()
    if begin_step is None:
        return BaseResponse.Failed("该工单模版缺少起始步骤")

    error_description = _check_form_can_publish(form_id)
    if error_description:
        raise ValidateError("工单发布失败", data=error_description)

    with FormEditLock(g.nick, form).auto_release(), in_transaction():
        form.update_user = g.nick
        body.sync_to_orm(form)
        if body.owner_list is not None:
            begin_step.set_owners(body.owner_list)
        form.publish()

        # 更新订阅信息，在工单发布时，前端不一定会传 sync_shop_dict
        # 没有的话就不做订阅相关的信息处理
        if subscribe_info := body.sync_shop_dict:
            form_subscribe = form.form_subscribe
            form_subscribe.pipeline_update_subscribe_info(subscribe_info)
        # 更新当前店铺的启用状态
        # 后续推动前端统一使用 sync_shop_dict 字段，这里就不需要再做兼容了
        elif body.deprecated_enabled is not None:
            form.subscribe(shop, enabled=body.deprecated_enabled)
    with in_transaction():
        form.trigger_on_publish(update_user=g.nick)

    return BaseResponse.Success(None)


class DeleteFormRequest(BaseModel):
    """删除工单"""

    with_instance: bool = False


DeleteFormResponse = GenericResponse[str]


@api.delete("/forms/<form_id>")
@decorator_check_form_step
@validate
def delete_form(form_id: int, body: DeleteFormRequest) -> DeleteFormResponse:
    # 先加锁，再释放
    shop: Shop = g.shop
    form: FormWrapper = Form.query.get(form_id).wraps(shop)  # type: ignore[union-attr]

    form_subscribe = FormSubscribe(form)
    form_subscribe.unsubscribe(shop, not body.with_instance)

    if body.with_instance:
        for bo in cast(List[BusinessOrder], form.business_orders):
            bo.delete()
    form.trigger_on_delete(update_user=g.nick)

    return DeleteFormResponse.Success("删除成功")


class ListShopFormResponse(GenericResponse[List[Form.View.FormEditorList]]):
    tags: List[str]


def list_shop_form(shop: Shop, param: ListShopFormRequest):
    """获取店铺的工单模板列表"""
    from sqlalchemy import desc
    from sqlalchemy.sql.operators import contains_op

    form_shop_query = (
        Form.Queries.form_shops_by_shop(shop)
        .join(Form.Options.from_form_shop)
        # 仅查询当前店铺订阅的工单
        .filter(Form.Filters.subscribed)
        .filter(Form.Filters.by_category(param.form_category))
        .order_by(desc(Form.updated_at))
    )
    if param.form_name:
        form_shop_query = form_shop_query.filter(contains_op(Form.name, param.form_name))
    if param.enabled is not None:
        form_shop_query = form_shop_query.filter(Form.Filters.by_enabled(param.enabled))

    # 工单需要展示店铺的订阅关系，为了减少循环查询，这里一次性查询出所有的工单订阅关系
    form_shop_list: List[FormShop] = form_shop_query.all()
    form_ids = [form_shop.form_id for form_shop in form_shop_list]
    # 查询租户下所有的店铺，用于展示工单的订阅关系时就不用单独查询了
    org_shops: List[Shop] = Shop.Queries.org_shops_by_org_id(shop.org_id).all()  # type: ignore[arg-type]
    org_shop_map = {shop.channel_id: shop for shop in org_shops}
    # 查询所有已订阅工单关联的店铺的 channel_id
    form_channel_ids_map = Form.Queries.subscribed_channel_ids_by_form_ids(form_ids)

    form_tags = current_app.config.get("FORM_TAGS", [])
    data = []
    for form_shop in form_shop_list:
        form_view = Form.View.FormEditorList.from_form_shops(
            form_shop,
            [
                org_shop_map[channel_id]
                for channel_id in form_channel_ids_map[form_shop.form_id]
                if channel_id in org_shop_map
            ],
        )
        data.append(form_view)

    return ListShopFormResponse(succeed=True, data=data, tags=form_tags)


class DeleteFormSyncDryRunRequest(BaseModel):
    form_id: int


class DeleteFormSyncDryRunResponse(BaseModel):
    success: bool = Field(default=True)
    bo_count: int

    @classmethod
    def Success(cls, bo_count: int):
        return cls(bo_count=bo_count)


@api.delete("/forms/dry-run")
@validate
def delete_form_dry_run(query: DeleteFormSyncDryRunRequest) -> DeleteFormSyncDryRunResponse:
    from robot_processor.business_order.models import BusinessOrder

    shop: Shop = g.shop
    form_id = query.form_id

    if not Form.Queries.is_subscribed(form_id=form_id, shop=shop):
        return DeleteFormSyncDryRunResponse.Success(0)

    bo_count = BusinessOrder.query.filter(
        BusinessOrder.form_id == form_id, BusinessOrder.sid == shop.sid, ~BusinessOrder.deleted
    ).count()

    return DeleteFormSyncDryRunResponse.Success(bo_count)


class CheckCanPublish(BaseModel):
    class ErrorDescription(BaseModel):
        class Config:
            orm_mode = True

        class Metadata(BaseModel):
            class Config:
                orm_mode = True

            step_id: int
            step_uuid: str
            step_type: str
            field_type: str
            branch_id: Optional[str] = Field(default=None)
            argument_name: Optional[str] = Field(default=None)
            concept_key: Optional[str] = Field(default=None)
            widget_info_is_deleted: Optional[bool] = Field(default=None)
            callback_type: Optional[str] = Field(default=None)

            @validator("step_id", pre=True)
            def set_int_type_default(cls, value):
                return value if value is not None else 0

            @validator("step_uuid", "step_type", "field_type", pre=True)
            def set_str_type_default(cls, value):
                return value if value is not None else ""

        step_name: str
        field_display: str
        error_display: str
        metadata: Metadata

        @validator("step_name", pre=True)
        def set_step_name_default(cls, step_name):
            return step_name if step_name is not None else ""

    can_publish: bool
    error_description: List[ErrorDescription]


CheckCanPublishResponse = GenericResponse[CheckCanPublish]


@api.get("/forms/<form_id>/check-can-publish")
@validate
def check_form_can_publish(form_id) -> CheckCanPublishResponse:
    error_description = _check_form_can_publish(form_id)

    return CheckCanPublishResponse.Success(
        CheckCanPublish(
            can_publish=len(error_description) == 0,
            error_description=error_description,
        )
    )


def _check_form_can_publish(form_id):
    from robot_processor.form.form_publish_check import form_publish_check

    ErrorDescription = CheckCanPublish.ErrorDescription
    publish_check_description = form_publish_check(form_id)
    error_description = [
        ErrorDescription(
            metadata=ErrorDescription.Metadata(
                step_id=item.metadata.step_id,
                step_uuid=item.metadata.step_uuid,
                step_type=item.metadata.step_type,
                field_type=item.metadata.field_type,
                branch_id=item.metadata.branch_id,
                argument_name=item.metadata.argument_name,
                concept_key=(
                    item.metadata.concept_key.key
                    if isinstance(item.metadata.concept_key, WidgetRef)
                    else item.metadata.concept_key
                ),
                widget_info_is_deleted=item.metadata.widget_info_is_deleted,
            ),
            step_name=item.display_info.step_name,
            field_display=item.display_info.field_display,
            error_display=item.display_info.error_display,
        )
        for item in publish_check_description
    ]

    return error_description


FormVersionResponse = GenericResponse[
    Union[
        list[FormVersion.View.BusinessOrderBasic],
        FormVersion.View.BusinessOrderDetail,
    ]
]


@api.get("/forms/<form_id>/versions")
@api.get("/forms/<form_id>/versions/<version_descriptor>")
@decorator_check_form_step
@validate
def list_form_versions(form_id, version_descriptor=None) -> FormVersionResponse:
    shop: Shop = g.shop
    form: Form = Form.query.get(form_id).wraps(shop)  # type: ignore[union-attr]

    form_version_query = form.versions.filter(~FormVersion.deleted)

    # 查看指定版本的详情
    if version_descriptor:
        form_version = form_version_query.filter(FormVersion.version_descriptor == version_descriptor).first()
        if not form_version:
            raise ValidateError("工单版本不存在", code=404)
        form_version_view = FormVersion.View.BusinessOrderDetail.from_orm_(form_version)

        return FormVersionResponse.Success(form_version_view)

    # 查看工单模板的所有版本
    else:
        form_versions = form_version_query.all()
        return FormVersionResponse.Success(
            [FormVersion.View.BusinessOrderBasic.from_orm(form_version) for form_version in form_versions]
        )


BatchStepEditResponse = GenericResponse[list[Step.View.FormEditor]]


@api.put("/forms/<form_id>/steps")
@decorator_check_form_step
@validate
def batch_update_form_steps(form_id, body: BatchFormStepEditRequest) -> BatchStepEditResponse:
    shop: Shop = g.shop
    form: FormWrapper = Form.query.get(form_id).wraps(shop)  # type: ignore[union-attr]

    # 更新步骤
    with FormEditLock(g.nick, form).auto_refresh(), in_transaction():
        data = []
        current_form_backend_steps: dict[int, Step] = {
            backend_step.id: backend_step for backend_step in form.backend_steps.all()
        }
        for step_info in body.steps:
            step = current_form_backend_steps.get(step_info.step_id)  # type: ignore[arg-type]
            if step is None:
                return BatchStepEditResponse.Failed(f"步骤 {step_info.name}({step_info.step_id}) 不存在", data=[])
            step = step.wraps(shop)
            step = step.pipeline_update(  # type: ignore[union-attr]
                update_user=g.nick, **step_info.dict(exclude_unset=True)
            )
            step.wraps(shop).trigger_on_update(update_user=g.nick)  # type: ignore[union-attr]
            data.append(Step.View.FormEditor.from_orm(step))  # type: ignore[arg-type]

    return BatchStepEditResponse.Success(data)


class BatchStepDeleteRequest(BaseModel):
    step_ids: List[int]


@api.delete("/forms/<form_id>/steps")
@decorator_check_form_step
@validate
def batch_delete_form_steps(form_id, body: BatchStepDeleteRequest) -> BaseResponse:
    shop: Shop = g.shop
    form: FormWrapper = Form.query.get(form_id).wraps(shop)  # type: ignore[union-attr]

    with FormEditLock(g.nick, form).auto_refresh(), in_transaction():
        steps = form.backend_steps.filter(Step.id.in_(body.step_ids)).all()
        for step in steps:
            step.delete()
            step.wraps(shop).trigger_on_delete(update_user=g.nick)

    return BaseResponse.Success(None)


class FormInfo(BaseModel):
    class Config:
        orm_mode = True

    id: int
    name: str
    description: Optional[str]


class ShopInfo(BaseModel):
    org_id: Optional[int]
    channel_id: Optional[int]
    sid: str
    nick: Optional[str]
    title: Optional[str]
    enabled: Optional[bool]


class GetFormSyncLinkRequest(BaseModel):
    form_id: int


class GetFormSyncLinkResponse(BaseModel):
    form: FormInfo
    shops: List[ShopInfo]


@api.get("/forms-sync/shop-links")
@validate
def get_form_sync_links(query: GetFormSyncLinkRequest) -> GetFormSyncLinkResponse:
    shop: Shop = g.shop
    check_form_step(query.form_id)
    form: FormWrapper = Form.query.get(query.form_id).wraps(shop)  # type: ignore[union-attr]

    form_view = FormInfo.from_orm(form)
    shops_view = []
    for subscribed_shop in form.subscribed_shops:
        shops_view.append(
            ShopInfo(
                org_id=subscribed_shop.org_id,
                channel_id=subscribed_shop.channel_id,
                sid=subscribed_shop.sid,
                nick=subscribed_shop.nick,
                title=subscribed_shop.title,
                enabled=form.wraps(subscribed_shop).enabled,
            )
        )

    return GetFormSyncLinkResponse(form=form_view, shops=shops_view)


class CreateFormSyncLinkRequest(BaseModel):
    form_id: int
    shop_dict: Dict[str, bool]


class CreateFormSyncLinkResponse(BaseResponse):
    errors: List[str] = Field(default_factory=list)


@api.put("/forms-sync/shop-links")
@validate
def create_form_sync_link(body: CreateFormSyncLinkRequest) -> CreateFormSyncLinkResponse:
    check_form_step(body.form_id)
    shop: Shop = g.shop
    form: FormWrapper = Form.query.get(body.form_id).wraps(shop)  # type: ignore[union-attr]

    form_subscribe = form.form_subscribe
    subscribe_info = body.shop_dict or {shop.sid: True}
    form_subscribe.pipeline_update_subscribe_info(subscribe_info)

    return CreateFormSyncLinkResponse.Success(None)


class CreateFormSyncLinkDryRunRequest(BaseModel):
    form_id: int
    shop_sid_dict: Dict[str, bool]


class CreateFormSyncLinkDryRunResponse(BaseModel):
    succeed: bool = True
    not_exist_count: int = 0
    form_is_diff_shop: List[str] = Field(default_factory=list)


@api.post("/forms-sync/shop-links/dry-run")
@validate
def create_form_sync_link_dry_run(body: CreateFormSyncLinkDryRunRequest) -> CreateFormSyncLinkDryRunResponse:
    check_form_step(body.form_id)
    shop: Shop = g.shop
    form: FormWrapper = Form.query.get(body.form_id).wraps(shop)  # type: ignore[union-attr]
    shops = set(
        map(
            lambda form_shop: form_shop.shop,
            filter(
                lambda form_shop: form_shop.status.is_subscribed,
                form.form_shops,
            ),
        )
    )
    diff = set(body.shop_sid_dict.keys()).difference([shop.sid for shop in shops])

    return CreateFormSyncLinkDryRunResponse(not_exist_count=len(diff), form_is_diff_shop=[])


@api.get("/forms-sync/config-diff")
def get_form_sync_config_diff():
    return jsonify(succeed=True, data=dict(diff=[]))


@api.put("/forms-sync/config-diff")
def update_form_sync_config_diff():
    return jsonify(succeed=False, message="[工单配置同步]请联系飞梭实施人员。")


class FlattenedWidgetInfo(BaseModel):
    widget_ref: WidgetRef
    label_list: list[str]
    widget_type_list: list[str | None]
    widget_info: WidgetInfo.View.ReferredBrief


class FlattenedWidgetsRes(BaseModel):
    step_id: int
    step_uuid: str
    step_name: str
    flattened_widget_info_list: list[FlattenedWidgetInfo]


class FlattenedWidgetsResponse(BaseResponse):
    data: list[FlattenedWidgetsRes]


class FlattenedWidgetsRequest(BaseModel):
    # 通过 step_uuid 进行过滤，仅查询 step_uuid 及之前的步骤
    truncated_step_uuid_list: list[str] | None = Field(default=None, alias="truncated_step_uuid[]")


@api.get("/forms/<int:form_id>/flattened-widgets")
@decorator_check_form_step
@validate
def get_form_flattened_widgets(form_id: int, query: FlattenedWidgetsRequest) -> FlattenedWidgetsResponse:
    response = FlattenedWidgetsResponse.Success(data=[])

    form: Form | None = Form.query.get(form_id)
    steps: list[Step] = form.backend_steps.all()  # type: ignore[union-attr]
    # 如果指定了 step_uuid_list，那么就只查询指定的步骤及指定步骤的前序步骤
    if query.truncated_step_uuid_list is not None:
        step_map = {}
        for step_uuid in query.truncated_step_uuid_list:
            step_map.update({step.step_uuid: step for step in Form.Utils.truncate_steps_by_step_uuid(steps, step_uuid)})
        steps = list(step_map.values())

    for step in steps:
        widget_info_list = list(map(WidgetInfo.View.RawStep.parse_obj, Step.Utils.raw_ui_schema(step)))
        step_res = FlattenedWidgetsRes(
            step_id=step.id, step_uuid=step.step_uuid, step_name=step.name, flattened_widget_info_list=[]
        )
        for widget_info in widget_info_list:
            paths = WidgetInfo.Utils.flatten_widget_info_tree_paths(widget_info)
            for path in paths:
                flattened_widget_info = FlattenedWidgetInfo(
                    widget_ref=path.as_widget_ref(),
                    label_list=path.label_list,
                    widget_type_list=path.widget_type_list,
                    widget_info=WidgetInfo.View.ReferredBrief(key=path.leaf.key, id=path.leaf.id),
                )
                step_res.flattened_widget_info_list.append(flattened_widget_info)
        response.data.append(step_res)

    return response


class RollbackFormVersionRequest(BaseModel):
    """回退工单模板版本"""

    version_no: str


@api.post("/forms/<form_id>/versions:reuse")
@validate
def reuse_form_version(form_id: int, body: RollbackFormVersionRequest) -> BaseResponse:
    """将工单模板重置为指定的版本"""

    shop: Shop = g.shop
    check_form_step(form_id=form_id, shop=shop)
    form: Form | None = db.session.get(Form, form_id)
    if not form:
        return BaseResponse.Failed("工单模板不存在")
    if (reuse_result := form.reuse_version(body.version_no)).is_err():
        return BaseResponse.Failed(str(reuse_result.unwrap_err()))
    return BaseResponse.Success(None)


@api.post("/forms/<form_id>/versions:drop-draft")
@validate
def drop_draft_form_version(form_id: int) -> BaseResponse:
    """丢弃工单模板草稿箱内容"""

    shop: Shop = g.shop
    check_form_step(form_id=form_id, shop=shop)
    form: Form | None = db.session.get(Form, form_id)
    if not form:
        return BaseResponse.Failed("工单模板不存在")
    form.drop_draft_version()

    return BaseResponse.Success(None)
