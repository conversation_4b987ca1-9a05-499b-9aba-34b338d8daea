from robot_types.core import Annotation
from robot_types.core import TypeSpec
from robot_types.helper.sql import JsonTableVisitor

from robot_processor.bi.dashboard import ReportPanel
from robot_processor.bi.database import ReportBusinessOrder
from robot_processor.bi.dataset.sql_builder.analysis import S<PERSON><PERSON><PERSON>er
from robot_processor.bi.dataset.sql_builder.analysis import schemas
from robot_processor.utils import message_to_dict


def from_data_type(data_type):
    return {
        "DATA_TYPE_STRING": "string",
        "DATA_TYPE_NUMBER": "number",
        "DATA_TYPE_DATETIME": "datetime",
        "DATA_TYPE_DATE": "date",
        "DATA_TYPE_ENUM_OBJECT_SELECT": "string",
    }[data_type]


def to_typespec(dimension):
    match dimension:
        case {"data_type": "DATA_TYPE_STRING" | "DATA_TYPE_ENUM_STRING"}:
            return TypeSpec("string")
        case {"data_type": "DATA_TYPE_NUMBER"}:
            return TypeSpec("number")
        case {"system_field": "DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME"}:
            return TypeSpec(
                "datetime",
                annotations={"orm": Annotation(kwargs={"mysql_type": "timestamp"})},
            )
        case {"data_type": "DATA_TYPE_DATETIME"}:
            return TypeSpec("datetime")
        case {"data_type": "DATA_TYPE_DATE"}:
            return TypeSpec("date")
        case {"data_type": "DATA_TYPE_ENUM_OBJECT_SELECT"}:
            return TypeSpec(
                "array",
                items=TypeSpec("collection", properties={"value": TypeSpec("string")}),
            )
        case _:
            print(dimension)
            raise TypeError()


def from_operator(operator: str):
    return {
        "OPERATOR_EQUAL": "eq",
        "OPERATOR_NOT_EQUAL": "ne",
        "OPERATOR_GREATER_THAN": "gt",
        "OPERATOR_GREATER_THAN_OR_EQUAL": "gte",
        "OPERATOR_LESS_THAN": "lt",
        "OPERATOR_LESS_THAN_OR_EQUAL": "lte",
        "OPERATOR_IN": "eq_any",
        "OPERATOR_NOT_IN": "ne_any",
        "OPERATOR_CONTAINS": "contains",
        "OPERATOR_NOT_CONTAINS": "disjoint",
        "OPERATOR_BETWEEN": "between",
        "OPERATOR_IS_TRUE": "is_true",
        "OPERATOR_IS_FALSE": "is_false",
        "OPERATOR_IS_NULL": "not_exists",
        "OPERATOR_IS_NOT_NULL": "exists",
        "OPERATOR_RELATIVE_DATE": "last_n",
        "OPERATOR_EXISTS": "exists",
        "OPERATOR_NOT_EXISTS": "not_exists",
    }[operator]


def to_field(
    data_dimension,
    table_source: schemas.TableSource,
    form_data_source: schemas.TableSource,
    json_table_visitor: JsonTableVisitor,
):
    match data_dimension["dimension_type"]:
        case "DATA_DIMENSION_TYPE_CUSTOM_FIELD":
            custom_field = data_dimension["custom_field"]
            if data_dimension["data_type"] == "DATA_TYPE_ENUM_OBJECT_SELECT":
                field = schemas.FieldSource(
                    table=form_data_source,
                    field=json_table_visitor.resolve_path_field_name([custom_field["sync_widget_key"] + "[*]", "value"])
                    or json_table_visitor.resolve_path_field_name(
                        [custom_field["sync_widget_key"] + "[*]", "[*]", "value"]
                    )
                    or json_table_visitor.resolve_path_field_name([custom_field["sync_widget_key"] + "[0]", "value"]),
                )
            elif parent_node_list := custom_field.get("extra_config", {}).get("parent_node_list"):
                field = schemas.FieldSource(
                    table=form_data_source,
                    field=json_table_visitor.resolve_path_field_name(
                        [f"{node}[*]" for node in parent_node_list] + [custom_field["sync_widget_key"]]
                    ),
                )
            else:
                field = schemas.FieldSource(
                    table=form_data_source,
                    field=json_table_visitor.resolve_path_field_name([custom_field["sync_widget_key"]]),
                )
        case "DATA_DIMENSION_TYPE_SYSTEM_FIELD":
            field = schemas.FieldSource(
                table=table_source,
                field={
                    "DATA_DIMENSION_SYSTEM_FIELD_BO_STATUS": "status",
                    "DATA_DIMENSION_SYSTEM_FIELD_SHOP_PLATFORM": "platform",
                    "DATA_DIMENSION_SYSTEM_FIELD_STEP": "step_name",
                    "DATA_DIMENSION_SYSTEM_FIELD_FORM": "form_id",
                    "DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME": "created_at",
                    "DATA_DIMENSION_SYSTEM_FIELD_BO_ID": "id",
                }[data_dimension["system_field"]],
            )
        case _:
            raise
    return field


def to_metadata(panel: ReportPanel) -> schemas.ReportMetadata:
    generate_schema = panel._generate_schema
    display_mode = panel.display_mode
    table = ReportBusinessOrder.__table__
    table_source = schemas.TableSource(
        id=generate_schema["data_source"]["sync_form_id"],
        table="business_order",
        alias="business_order",
    )
    form_data_source = SQLBuilder.build_form_data_table_source(table_source)
    json_table_visitor = JsonTableVisitor(
        SQLBuilder.get_json_table_schema(table_source),
        table.c.raw_data,
        table_alias=form_data_source.alias,
    )
    dimensions = []
    for dimension in generate_schema["dimensions"]:
        dimensions.append(
            schemas.DimensionSlot(
                alias=dimension["dimension"]["title"],
                type=from_data_type(dimension["dimension"]["data_type"]),
                fields=[
                    to_field(
                        dimension["dimension"],
                        table_source,
                        form_data_source,
                        json_table_visitor,
                    )
                ],
            )
        )

    metrics = []
    for slot in generate_schema["statistics"]:
        metric = schemas.MetricSlot(
            alias=slot["dimension"]["title"],
            field=to_field(slot["dimension"], table_source, form_data_source, json_table_visitor),
            agg_fn={  # type: ignore[arg-type]
                "OPERATOR_SUM": "sum",
                "OPERATOR_COUNT": "count",
                "OPERATOR_AVERAGE": "avg",
                "OPERATOR_RATIO": "ratio",
                "OPERATOR_SUM_RATIO": "sum_ratio",
            }[slot["operator"]],
        )
        metrics.append(metric)

    order_by: list[schemas.OrderBy.Asc | schemas.OrderBy.Desc] = []
    for dimension in generate_schema["dimensions"]:
        match dimension:
            case {"order_by": "ORDER_BY_DESC"}:
                order_by.append(schemas.OrderBy.Desc(alias=dimension["dimension"]["title"]))
            case {"order_by": "ORDER_BY_ASC"}:
                order_by.append(schemas.OrderBy.Asc(alias=dimension["dimension"]["title"]))
    for statistics in generate_schema["statistics"]:
        match statistics:
            case {"order_by": "ORDER_BY_DESC"}:
                order_by.append(schemas.OrderBy.Desc(alias=statistics["dimension"]["title"]))
            case {"order_by": "ORDER_BY_ASC"}:
                order_by.append(schemas.OrderBy.Asc(alias=statistics["dimension"]["title"]))

    condition_group = schemas.ConditionGroup("and")
    if condition_groups_raw := generate_schema.get("data_range_groups", {}).get("data_range_groups", {}):
        condition_group_raw = condition_groups_raw[0]
        if condition_group_raw["relation"] == "RELATION_OR":
            condition_group.relation = "or"
        for data_range in condition_group_raw.get("data_ranges", []):
            match data_range["value"]:
                case {"unit": str(), "value": int() | float()}:
                    value = {
                        "unit": data_range["value"]["unit"],
                        "expr": data_range["value"]["value"],
                    }
                case {"label": str(), "value": str()}:
                    value = data_range["value"]["value"]
                case _:
                    value = data_range["value"]
            condition = schemas.Condition(
                field=to_field(
                    data_range["dimension"],
                    table_source,
                    form_data_source,
                    json_table_visitor,
                ),
                type_spec=to_typespec(data_range["dimension"]),
                op=from_operator(data_range["operator"]),
                value=value,
            )
            condition_group.conditions.append(condition)

    metadata = schemas.ReportMetadata(
        source=schemas.DatasourceConfig(main=table_source, joins=[]),
        dimensions=dimensions,
        metrics=metrics,
        filters=condition_group,
        order_by=order_by,
        display_mode="drill_down" if display_mode == 1 else "multi_dimension",
    )
    if panel.drill_down_status.drill_filters:
        for drill_filter in panel.drill_down_status.drill_filters:
            raw = message_to_dict(drill_filter)
            metadata.drill_down.filters[raw["dimension"]] = raw["value"]

    return metadata
