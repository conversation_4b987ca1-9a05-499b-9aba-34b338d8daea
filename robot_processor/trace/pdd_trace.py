import datetime
from functools import lru_cache
from typing import Final

import sqlalchemy as sa
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column

from robot_processor.db import DbBaseModel
from robot_processor.db import db


class PddTrace(DbBaseModel):
    __bind_key__ = "fs_trade"
    __tablename__ = "pdd_trace"
    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    tracking_number: Mapped[str] = mapped_column(sa.String(128))
    shipping_id: Mapped[int] = mapped_column(sa.Integer)
    action: Mapped[str] = mapped_column(sa.String(255))
    desc: Mapped[str] = mapped_column(sa.String(512))
    status_time: Mapped[datetime.datetime] = mapped_column(sa.DateTime)
    mall_id: Mapped[str] = mapped_column(sa.String(64))
    node_description: Mapped[str] = mapped_column(sa.String(255))

    @classmethod
    def get_traces_by_tracking_number(cls, tracking_number: str) -> list["PddTrace"]:
        traces = PddTrace.query.filter_by(tracking_number=tracking_number).all()
        sorted_trades = sorted(traces, key=lambda trace: trace.status_time, reverse=True)
        return sorted_trades

    @classmethod
    def valid(cls, traces: list["PddTrace"]) -> bool:
        # 没有揽收时物流轨迹为空，这个不等于物流轨迹前面被拦截，虽然会有误判
        if not traces:
            return True
        # 假设正常的物流估计一定有GOT，那么从前面截断的物流轨迹一定没GOT
        if traces and traces[-1].action != "GOT":
            return False
        # 从后面截断无法判断
        return True


class PddLogisticsInfoDict(DbBaseModel):
    __bind_key__ = "fs_trade"
    __tablename__ = "pdd_logistics_info_dict"
    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    logistics_id: Mapped[int]
    logistics_company: Mapped[str] = mapped_column(sa.String(128))
    code: Mapped[str] = mapped_column(sa.String(64))
    available: Mapped[bool]

    @classmethod
    def get_logistics_company(cls, logistics_id: int | None):
        if logistics_id is None:
            return None
        for record in all_logistics_companies():
            if record.logistics_id == logistics_id:
                return record.logistics_company
        return None


@lru_cache()
def all_logistics_companies():
    records = PddLogisticsInfoDict.query.all()
    for record in records:
        db.session.expunge(record)
    return records


class PddTraceAction:
    # 已揽收
    GOT: Final = "GOT"
    # 快递发往...
    DEPARTURE: Final = "DEPARTURE"
    # 快递到达
    ARRIVAL: Final = "ARRIVAL"
    # 派件
    SEND: Final = "SEND"
    # 签收
    SIGN: Final = "SIGN"
    # 代签
    SIGN_ON_BEHALF: Final = "SIGN_ON_BEHALF"
    # 入柜
    IN_CABINET: Final = "IN_CABINET"
    # 取出
    OUT_CABINET: Final = "OUT_CABINET"
    # 拒收
    REJECTION: Final = "REJECTION"
    # 退回
    RETURN: Final = "RETURN"
    # 派件异常
    FAIL: Final = "FAIL"
    # 其它描述
    OTHER: Final = "OTHER"

    @classmethod
    def check_if_traces_valid(cls, traces: list[PddTrace]) -> bool:
        """检查是否是完整的物流轨迹"""
        if len(traces) > 0 and traces[-1].action != cls.GOT:
            return False
        if len(traces) == 0:  # 没有揽收时物流轨迹为空，这个不等于物流轨迹前面被拦截，虽然会有误判
            return True
        return True

    @classmethod
    def has_got_action(cls, traces: list[PddTrace]) -> bool:
        return any(trace.action == cls.GOT for trace in traces)

    @classmethod
    def has_departure_action(cls, traces: list[PddTrace]) -> bool:
        return any(trace.action == cls.DEPARTURE for trace in traces)

    @classmethod
    def has_arrival_action(cls, traces: list[PddTrace]) -> bool:
        return any(trace.action == cls.ARRIVAL for trace in traces)

    @classmethod
    def has_send_action(cls, traces: list[PddTrace]) -> bool:
        """是否已派件"""
        return any(trace.action == cls.SEND for trace in traces)

    @classmethod
    def check_is_collected(cls, traces: list[PddTrace]) -> bool:
        """检查是否已揽收"""
        return bool(traces and traces[-1].action == cls.GOT)

    @classmethod
    def check_only_collected(cls, traces: list[PddTrace]) -> bool:
        return cls.check_is_collected(traces) and len(traces) == 1

    @classmethod
    def check_in_send(cls, traces: list[PddTrace]) -> bool:
        """检查是否在派件中"""
        if not traces:
            return False
        return traces[0].action == cls.SEND

    @classmethod
    def check_is_signed(cls, traces: list[PddTrace]) -> bool:
        """检查是否已签收"""
        if not traces:
            return False
        return traces[0].action in [cls.SIGN, cls.SIGN_ON_BEHALF, cls.IN_CABINET, cls.OUT_CABINET]

    @classmethod
    def check_in_transport(cls, traces: list[PddTrace]) -> bool:
        """检查是否在运输中"""
        if not traces:
            return False
        if traces[0].action in [cls.DEPARTURE, cls.ARRIVAL]:
            return True
        if traces[0].action == cls.OTHER and any(trace.action in [cls.DEPARTURE, cls.ARRIVAL] for trace in traces):
            return True
        return False
