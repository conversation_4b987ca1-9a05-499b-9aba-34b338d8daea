from copy import copy
from dataclasses import asdict
from datetime import datetime
from enum import StrEnum
from typing import TYPE_CHECKING
from typing import Union

import sqlalchemy as sa
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
from robot_types.model.invoice.config_manager import NotificationChannel
from robot_types.model.invoice.config_manager import NotificationChannelType
from robot_types.model.invoice.config_manager import NotificationTemplate
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column

from robot_processor.db import DbBaseModel
from robot_processor.utils import message_to_dict
from robot_processor.utils import string_wrapper

if TYPE_CHECKING:
    from robot_processor.invoice.notification.services import EmailMessage
    from robot_processor.invoice.notification.services import PlatformMessage
    from robot_processor.invoice.workflow.models import InvoiceWorkflow


class NotificationLog(DbBaseModel):
    """消息发送记录"""

    __tablename__ = "invoice_notification_log"

    class Status(StrEnum):
        SUCCESS = "success"
        FAILED = "failed"

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    workflow_id: Mapped[int] = mapped_column(sa.ForeignKey("invoice_workflow.id"))
    created_at: Mapped[datetime] = mapped_column(sa.DateTime, default=datetime.now)
    channel_type: Mapped[NotificationChannelType] = mapped_column(
        sa.Enum(NotificationChannelType, native_enum=False, length=32)
    )
    channel: Mapped[str] = mapped_column(sa.String(32), comment="消息发送渠道")
    template: Mapped[str] = mapped_column(sa.String(32), comment="消息模板")
    raw: Mapped[dict] = mapped_column(sa.JSON)
    status: Mapped[Status] = mapped_column(sa.Enum(Status, native_enum=False, length=12), comment="发送状态")
    error_message: Mapped[str | None] = mapped_column(sa.Text, comment="发送消息失败时的错误信息")

    @classmethod
    def get_skeleton(cls, channel: NotificationChannel, template: NotificationTemplate, workflow: "InvoiceWorkflow"):
        self = cls(
            workflow_id=workflow.id,
            channel_type=channel.channel_type,
            channel=channel.name,
            template=template.name,
            raw=dict(),
        )
        self.set_channel(channel)
        self.set_template(template)
        return self

    def set_success(self):
        self.status = self.Status.SUCCESS
        return self

    def set_failed(self, error):
        self.status = self.Status.FAILED
        self.error_message = str(error)
        return self

    def set_message(self, message: Union["PlatformMessage", "EmailMessage"]):
        from robot_processor.invoice.notification.services import EmailMessage

        raw = copy(self.raw or {})
        raw["message"] = asdict(message)
        if isinstance(message, EmailMessage):
            raw["message"].pop("attachments", None)
        self.raw = raw
        return self

    def set_channel(self, channel: NotificationChannel):
        raw = copy(self.raw or {})
        raw["channel"] = asdict(channel)
        self.raw = raw
        return self

    def set_template(self, template: NotificationTemplate):
        raw = copy(self.raw or {})
        raw["template"] = asdict(template)
        self.raw = raw
        return self

    def to_dict(self, *args):
        return message_to_dict(self.to_pb())

    def to_pb(self):
        return pb_InvoiceWorkflow.NotificationLog(
            id=self.id,
            created_at=self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            channel_type=self.channel_type,
            channel_name=self.channel,
            template_name=self.template,
            status=self.status,
            error_message=string_wrapper(self.error_message),
        )
