import random
from dataclasses import dataclass
from typing import TYPE_CHECKING
from typing import <PERSON>V<PERSON>
from typing import Literal
from typing import Protocol
from typing import cast

import robot_types.core
from loguru import logger
from result import Err
from result import Ok
from robot_types.helper import deserialize
from robot_types.helper import serialize
from robot_types.helper.predefined import BizType
from robot_types.model.invoice.config_manager import EMailChannelConfig
from robot_types.model.invoice.config_manager import EmailTemplateConfig
from robot_types.model.invoice.config_manager import NotificationChannel
from robot_types.model.invoice.config_manager import NotificationChannelType
from robot_types.model.invoice.config_manager import NotificationRule
from robot_types.model.invoice.config_manager import NotificationTemplate
from robot_types.model.invoice.config_manager import PlatformChannelConfig
from robot_types.model.invoice.config_manager import PlatformTemplateConfig

from robot_processor.db import in_transaction
from robot_processor.ext import db
from robot_processor.function.conversion.text_template_render import TextTemplateRender
from robot_processor.invoice.config_manager import Config<PERSON>ey
from robot_processor.invoice.config_manager import ConfigManager
from robot_processor.invoice.notification.models import NotificationLog
from robot_processor.invoice.workflow.models import InvoiceWorkflow
from robot_processor.utils import Amount
from robot_processor.utils import DataclassMixin
from robot_processor.utils import raise_exception

if TYPE_CHECKING:
    from robot_processor.shop.models import Shop


@dataclass
class NotificationServicer:
    biz_type: ClassVar[BizType] = BizType.INVOICE_NOTIFICATION_TEXT_TEMPLATE
    config_manager: ConfigManager

    @classmethod
    def by_org_id(cls, org_id: int):
        return cls(ConfigManager(org_id))

    def select_channel(self, channel_id: str):
        config_key = ConfigKey.NOTIFICATION_CHANNELS
        return {
            channel.id: channel for channel in cast(list[NotificationChannel], self.config_manager.get(config_key))
        }[channel_id]

    def select_template(self, template_id: str):
        config_key = ConfigKey.NOTIFICATION_TEMPLATES
        return {
            template.id: template for template in cast(list[NotificationTemplate], self.config_manager.get(config_key))
        }[template_id]

    def list_rules(self) -> list[NotificationRule]:
        config_key = ConfigKey.NOTIFICATION_RULES
        rules: list[NotificationRule] = self.config_manager.get(config_key)
        return rules

    def get_render_parameters(self, workflow: InvoiceWorkflow):
        from robot_types.model.invoice.notification import NotificationRenderParameters

        render_parameters = NotificationRenderParameters(
            corporate_name=workflow.seller_name,
            shop_title=workflow.shop.title if workflow.shop else "",
            invoice_amount=Amount(workflow.invoice_issued.issuing_amount).format(precision=2),
            invoice_number=workflow.invoice_number,
            invoice_receipt_url=workflow.invoice_issued.get_receipt_pdf_url(),
        )
        return render_parameters

    def get_value_resolver(self, workflow: InvoiceWorkflow):
        from robot_types.helper import ValueResolver

        # TODO operator resolver
        context = {
            "shop": {},
            "invoice_amount": Amount(workflow.invoice_issued.issuing_amount).format(precision=2),
            "seller_name": workflow.seller_name,
            "source": workflow.source.name,
        }
        return ValueResolver(context)

    def get_pdf_receipt(self, workflow: InvoiceWorkflow) -> bytes:
        from robot_processor.client import rpa_control_oss

        return rpa_control_oss.get_object(workflow.invoice_issued.receipt["pdf"])

    def render_email_template(self, workflow: InvoiceWorkflow, template_config: EmailTemplateConfig):
        render_parameters = self.get_render_parameters(workflow)
        context = serialize(render_parameters)
        subject = (
            TextTemplateRender(
                context=context,
                template=template_config.subject,
                mode=self.biz_type,
            )
            .call()
            .unwrap_or_else(raise_exception)
        )
        content = (
            TextTemplateRender(
                context=context,
                template=template_config.content,
                mode=self.biz_type,
            )
            .call()
            .unwrap_or_else(raise_exception)
        )
        attachments = {"发票.pdf": self.get_pdf_receipt(workflow)}
        return EmailMessage(subject=subject, content=content, attachments=attachments)

    def render_platform_template(self, workflow: InvoiceWorkflow, template_config: PlatformTemplateConfig):
        render_parameters = self.get_render_parameters(workflow)
        context = serialize(render_parameters)
        content = (
            TextTemplateRender(
                context=context,
                template=template_config.content,
                mode=self.biz_type,
            )
            .call()
            .unwrap_or_else(raise_exception)
        )
        return PlatformMessage(content=content)

    @in_transaction()
    def send_by_email(
        self, channel: NotificationChannel, template: NotificationTemplate, workflow: InvoiceWorkflow, receiver: str
    ):
        message = self.render_email_template(workflow, deserialize(template.config, EmailTemplateConfig))
        log = NotificationLog.get_skeleton(channel, template, workflow).set_message(message)
        smtp_config = EMailChannelConfig(**channel.config)
        broker = EmailBroker(smtp_config, receiver, message)
        send_result = broker.send()
        if send_result.is_ok():
            log.set_success()
        else:
            log.set_failed(send_result.unwrap_err())
        db.session.add(log)
        return send_result

    def send_by_platform_msg(
        self, channel: NotificationChannel, template: NotificationTemplate, workflow: InvoiceWorkflow
    ):
        message = self.render_platform_template(workflow, deserialize(template.config, PlatformTemplateConfig))
        log = NotificationLog.get_skeleton(channel, template, workflow).set_message(message)
        channel_config = deserialize(channel.config, PlatformChannelConfig)
        broker = PlatformMessageBroker(channel_config, workflow.shop, workflow.tid, message)
        send_result = broker.send()
        if send_result.is_ok():
            log.set_success()
        else:
            log.set_failed(send_result.unwrap_err())
        db.session.add(log)
        return send_result

    def send_by_platform_memo(
        self, channel: NotificationChannel, template: NotificationTemplate, workflow: InvoiceWorkflow
    ):
        from robot_processor.function.trade.seller_memo import UpdateTradeSellerMemo

        message = self.render_platform_template(workflow, deserialize(template.config, PlatformTemplateConfig))
        log = NotificationLog.get_skeleton(channel, template, workflow).set_message(message)
        fn = UpdateTradeSellerMemo(
            shop=robot_types.model.resource.Shop(sid=workflow.shop.sid, platform=workflow.shop.platform),
            tid=workflow.tid,
            content=message.content,
            update_policy=robot_types.model.resource.RemarkUpdatePolicy.APPEND,
        )
        try:
            fn.call().unwrap_or_else(raise_exception)
        except Exception as e:
            logger.opt(exception=e).error(f"平台备注失败 {e}")
            log.status = NotificationLog.Status.FAILED
            log.error_message = str(e)
        else:
            log.status = NotificationLog.Status.SUCCESS
        db.session.add(log)
        return Ok(None)

    def auto_notify(self, workflow: InvoiceWorkflow):
        from robot_processor.symbol_table.services import FilterServicer

        if not self.list_rules():
            return
        if workflow.shop:
            shop = robot_types.model.resource.Shop(sid=workflow.shop.sid, platform=workflow.shop.platform)
        else:
            shop = None
        filter_context = serialize(
            robot_types.model.invoice.notification.RuleFilterContext(
                shop=shop,
                invoice_amount=workflow.issuing_items_total_amount,
                seller_name=workflow.seller_name,
                source=workflow.source.label,
            )
        )
        for rule in self.list_rules():
            predicate = self.rule_filters_to_predicate(rule.filters)
            if not FilterServicer.evaluate_predicate(predicate, filter_context):
                continue
            for notification in rule.notifications:
                channel = self.select_channel(notification.channel_id)
                template = self.select_template(notification.template_id)
                match channel.channel_type:
                    case NotificationChannelType.EMAIL:
                        receiver = (workflow.notification_info or {}).get("receiver_email")
                        if receiver:
                            self.send_by_email(channel=channel, template=template, workflow=workflow, receiver=receiver)
                        else:
                            logger.warning("未配置邮件接收人，跳过")
                    case NotificationChannelType.PLATFORM_MSG:
                        self.send_by_platform_msg(channel=channel, template=template, workflow=workflow)
                    case NotificationChannelType.PLATFORM_MEMO:
                        self.send_by_platform_memo(channel=channel, template=template, workflow=workflow)
            break

    @staticmethod
    def rule_filters_to_predicate(filters: dict):
        import dacite
        from robot_types.core import Condition
        from robot_types.core import Filter
        from robot_types.core import OperatorEnum

        return Filter(
            relation=filters.get("relation", "and"),
            conditions=[
                dacite.from_dict(
                    Condition,
                    dict(a=condition["a"], o=[each["operator"] for each in condition["o"]], b=condition.get("b")),
                    dacite.Config(type_hooks={OperatorEnum: lambda x: OperatorEnum[x]}),
                )
                for condition in filters["conditions"]
            ],
        )


class NotificationChannelBroker(Protocol):
    def send(self) -> Ok[None] | Err[Exception]:
        raise NotImplementedError()


@dataclass
class EmailMessage(DataclassMixin):
    subject: str
    content: str
    attachments: dict[str, bytes]


@dataclass
class EmailBroker(NotificationChannelBroker):
    smtp_config: EMailChannelConfig
    receiver: str
    message: EmailMessage

    def get_sender(self):
        from redmail import EmailSender

        return EmailSender(
            host=self.smtp_config.smtp_host,
            port=int(self.smtp_config.smtp_port),
            username=self.smtp_config.smtp_username,
            password=self.smtp_config.smtp_password,
            timeout=3,
        )

    def send(self):
        sender = self.get_sender()
        try:
            message = sender.send(
                subject=self.message.subject,
                sender=self.smtp_config.smtp_username,
                receivers=[self.receiver],
                text=self.message.content,
                attachments=self.message.attachments,
            )
            return Ok(message)
        except Exception as e:
            return Err(e)


@dataclass
class PlatformMessage(DataclassMixin):
    content: str


@dataclass
class PlatformMessageBroker(NotificationChannelBroker):
    channel_config: PlatformChannelConfig
    shop: "Shop"
    tid: str
    message: PlatformMessage

    RANDOM_ONLINE: ClassVar[Literal[1]] = 1  # 随机在线账号
    FIXED: ClassVar[Literal[2]] = 2  # 指定账号

    def get_limited_sender(self) -> Ok[list[str]] | Err[None]:
        limited_config = [
            limited
            for limited in self.channel_config.limited
            if limited.shop.sid == self.shop.sid and limited.shop.platform == self.shop.platform
        ]
        if limited_config:
            return Ok(limited_config[0].sender)
        else:
            return Err(None)

    def send(self):
        match self.shop.platform:
            case "TAOBAO" | "TMALL":
                return self.taobao_send()
            case "PDD":
                return self.cellar_send("pdd")
            case "DOUDIAN":
                return self.cellar_send("doudian")
            case _:
                return Err(NotImplementedError(f"未支持的平台 {self.shop.platform}"))

    def taobao_send(self):
        from robot_processor.client import chat_client

        online_assistants_result = chat_client.get_online_assistants_by_sid(self.shop.sid)
        if online_assistants_result.is_err():
            return Err(ValueError(online_assistants_result.unwrap_err()))
        online_assistants = set(online_assistants_result.unwrap())
        shop_limited_result = self.get_limited_sender()
        if shop_limited_result.is_ok():
            online_assistants = set(online_assistants).intersection(shop_limited_result.unwrap())
        if not online_assistants:
            return Err(ValueError("无在线客服"))
        assistant = random.choice(list(online_assistants))
        get_nick_res = chat_client.get_nick_by_tid(assistant, self.tid)
        if get_nick_res.is_err():
            return Err(ValueError(get_nick_res.unwrap_err()))
        get_nick_response = get_nick_res.unwrap()
        if (
            get_nick_response["data"]["ret"][0] == "SUCCESS::调用成功"
            and get_nick_response["data"]["data"]["code"] == "0"
            and get_nick_response["data"]["data"]["data"]
        ):
            buyer_nick = get_nick_response["data"]["data"]["data"][0]["nick"]
        else:
            return Err(ValueError("获取买家昵称失败"))
        return chat_client.send_message(assistant, buyer_nick, self.message.content, [])

    def cellar_send(self, platform: str):
        from rpa.cellar import cellar_client

        shop_limited_result = self.get_limited_sender()
        if shop_limited_result.is_ok():
            assistant = random.choice(shop_limited_result.unwrap())
            strategy = 2
        else:
            assistant = ""
            strategy = 1
        return cellar_client.send_message(
            platform=platform,
            tid=self.tid,
            sid=self.shop.sid,
            assistant_fetch_strategy=strategy,
            assistant=assistant,
            text=self.message.content,
            pic_url="",
        )
