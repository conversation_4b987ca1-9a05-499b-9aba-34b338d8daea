from dataclasses import dataclass
from dataclasses import field
from typing import TYPE_CHECKING

from loguru import logger

from ._abc import Context
from ._abc import IContext

__all__ = ["WorkflowBuilder", "Context", "IContext"]

if TYPE_CHECKING:
    from robot_processor.invoice.workflow.models import InvoiceWorkflow


class WorkflowBuilder:
    @classmethod
    def build(cls, context: IContext):
        from .base_info import BaseInfoModule
        from .buyer import BuyerModule
        from .customize import CustomizeModule
        from .issuing_item import IssuingItemModule
        from .platform_apply_info import PlatformApplyInfoModule
        from .seller import SellerModule
        from .trade import TradeModule

        registered_modules = [
            BaseInfoModule,
            SellerModule,
            BuyerModule,
            IssuingItemModule,
            TradeModule,
            PlatformApplyInfoModule,
            CustomizeModule,
        ]
        for module_cls in registered_modules:
            module = module_cls(context)  # type: ignore[abstract]
            module.execute()
            module.validate()

    @classmethod
    def create_workflow_with_context(cls, context: IContext, with_submit=True, creator=None):
        from robot_processor.invoice.common.models import rpa_user_info
        from robot_processor.invoice.workflow.models import InvoiceWorkflow
        from robot_processor.invoice.workflow.service import IssueBroker

        cls.build(context)
        with_submit = with_submit and (not context.has_errors) and context.provide_with_submit()
        result = WorkflowBuilder.CreateResult()
        pb_workflows = context.split_workflows if context.is_split else [context.pb_workflow]
        if context.abort_error:
            result.err.append(context.abort_error)
            return result
        for pb_workflow in pb_workflows:
            try:
                creator = creator or rpa_user_info
                workflow = InvoiceWorkflow.create(context.org_id, creator, pb_workflow, with_submit)
                if context.warnings:
                    workflow.extra = workflow.extra or dict()
                    workflow.extra["warnings"] = [warning_detail.to_dict() for warning_detail in context.warnings]
                if context.has_errors:
                    workflow.extra = workflow.extra or dict()
                    workflow.extra["validation_errors"] = [error_detail.to_dict() for error_detail in context.errors]
                else:
                    IssueBroker.after_workflow_create_hook(workflow).map(
                        lambda msg: logger.info(f"自动开票 {msg}")
                    ).map_err(lambda exc: logger.opt(exception=exc).error(f"自动开票失败 {exc}"))
                result.ok.append(workflow)
            except Exception as e:
                logger.opt(exception=e).error(f"创建开票流程失败 {e}")
                result.err.append(e)
        return result

    @dataclass
    class CreateResult:
        ok: list["InvoiceWorkflow"] = field(default_factory=list)
        err: list[Exception] = field(default_factory=list)

        @property
        def is_ok(self):
            return len(self.err) == 0

        @property
        def workflow(self):
            return self.ok[0]

        def unwrap_err(self):
            return self.err[-1]

        def as_result(self):
            from result import Err
            from result import Ok

            if self.is_ok:
                return Ok(self.workflow)
            else:
                return Err(self.unwrap_err())
