from typing import Any

from leyan_proto.digismart.robot.invoice.workflow_pb2 import Invoice as pb_Invoice

from ._abc import Context
from ._abc import DelegateModule
from ._abc import IModule


class BuyerModule(DelegateModule):
    def execute(self):
        match self.context:
            case Context.Manual():
                self._delegate(Manual)
            case Context.Trade():
                self._delegate(Trade)
            case Context.QNPlatformApply():
                self._delegate(QNPlatformApply)
            case Context.DoudianPlatformApply():
                self._delegate(DoudianPlatformApply)
            case Context.JDPlatformApply():
                self._delegate(JDPlatformApply)
            case Context.PddPlatformApply():
                self._delegate(PddPlatformApply)
            case _:
                self._delegate(Default)

    def validate(self):
        if not self.pb_workflow.buyer_name:
            self.context.add_error("购方名未填写")
        if self.pb_workflow.buyer_type == pb_Invoice.BuyerType.BUYER_TYPE_UNSPECIFIED:
            self.context.add_error("购方类型未填写")


class Default(IModule):
    def process(self):
        raise NotImplementedError("不支持的路径")


class Manual(IModule[Context.Manual]):
    def process(self):
        self.pb_workflow.buyer_type = pb_Invoice.BuyerType.ENTERPRISE


class Trade(IModule[Context.Trade]):
    def process(self):
        self.pb_workflow.buyer_type = pb_Invoice.BuyerType.ENTERPRISE


class ShowBuyerInfoMixin:
    context: Any

    def set_show_buyer_address(self):
        from robot_types.model.invoice.config_manager import ShowBuyerAddress

        from robot_processor.invoice.config_manager import ConfigKey

        match self.context.config_manager.get(ConfigKey.SHOW_BUYER_ADDRESS):
            case ShowBuyerAddress.SHOW:
                self.context.pb_workflow.show_buyer_address_phone = True
            case ShowBuyerAddress.HIDE:
                self.context.pb_workflow.show_buyer_address_phone = False

    def set_show_buyer_bank(self):
        from robot_types.model.invoice.config_manager import ShowBuyerBank

        from robot_processor.invoice.config_manager import ConfigKey

        match self.context.config_manager.get(ConfigKey.SHOW_BUYER_BANK):
            case ShowBuyerBank.SHOW:
                self.context.pb_workflow.show_buyer_bank = True
            case ShowBuyerBank.HIDE:
                self.context.pb_workflow.show_buyer_bank = False


class QNPlatformApply(IModule[Context.QNPlatformApply], ShowBuyerInfoMixin):
    def process(self):
        qn_invoice = self.context.qn_invoice
        self.pb_workflow.buyer_name = qn_invoice.payerName
        self.pb_workflow.buyer_type = qn_invoice.get_buyer_type().pb_value
        if qn_invoice.payerRegisterNo:
            self.pb_workflow.buyer_credit_id.value = qn_invoice.payerRegisterNo
        if qn_invoice.payerPhone:
            self.pb_workflow.buyer_phone.value = qn_invoice.payerPhone
        if qn_invoice.payerAddress:
            self.pb_workflow.buyer_address.value = qn_invoice.payerAddress
        if qn_invoice.payerBank:
            self.pb_workflow.buyer_bank.value = qn_invoice.payerBank
        if qn_invoice.payerBankaccount:
            self.pb_workflow.buyer_bank_account.value = qn_invoice.payerBankaccount
        self.set_show_buyer_bank()
        self.set_show_buyer_address()


class DoudianPlatformApply(IModule[Context.DoudianPlatformApply], ShowBuyerInfoMixin):
    def process(self):
        doudian_invoice = self.context.doudian_invoice
        self.pb_workflow.buyer_name = doudian_invoice.title
        self.pb_workflow.buyer_type = doudian_invoice.title_type.buyer_type.pb_value
        if doudian_invoice.tax_no:
            self.pb_workflow.buyer_credit_id.value = doudian_invoice.tax_no
        if doudian_invoice.company_address:
            self.pb_workflow.buyer_address.value = doudian_invoice.company_address
        if doudian_invoice.company_mobile:
            self.pb_workflow.buyer_phone.value = doudian_invoice.company_mobile
        if doudian_invoice.bank_name:
            self.pb_workflow.buyer_bank.value = doudian_invoice.bank_name
        if doudian_invoice.bank_no:
            self.pb_workflow.buyer_bank_account.value = doudian_invoice.bank_no
        self.set_show_buyer_address()
        self.set_show_buyer_bank()


class JDPlatformApply(IModule[Context.JDPlatformApply], ShowBuyerInfoMixin):
    def process(self):
        jd_invoice = self.context.jd_invoice
        self.pb_workflow.buyer_type = jd_invoice.get_buyer_type().pb_value
        self.pb_workflow.buyer_name = jd_invoice.invoiceTitle or "个人"
        if jd_invoice.consumerTaxId:
            self.pb_workflow.buyer_credit_id.value = jd_invoice.consumerTaxId
        if jd_invoice.consumerAddress:
            self.pb_workflow.buyer_address.value = jd_invoice.consumerAddress
        # 京东的买家信息都是加密的，现在的加密信息过长，会导致发票申请同步不进来，等确实需要相关信息的功能时再迭代.
        self.pb_workflow.buyer_phone.value = ""
        if jd_invoice.consumerBankName:
            self.pb_workflow.buyer_bank.value = jd_invoice.consumerBankName
        if jd_invoice.consumerBankAccount:
            self.pb_workflow.buyer_bank_account.value = jd_invoice.consumerBankAccount
        self.set_show_buyer_address()
        self.set_show_buyer_bank()


class PddPlatformApply(IModule[Context.PddPlatformApply], ShowBuyerInfoMixin):
    def process(self):
        pdd_invoice = self.context.pdd_invoice
        self.pb_workflow.buyer_type = pdd_invoice.get_buyer_type().pb_value
        self.pb_workflow.buyer_name = pdd_invoice.payer_name
        if pdd_invoice.payer_register_no:
            self.pb_workflow.buyer_credit_id.value = pdd_invoice.payer_register_no
        self.set_show_buyer_address()
        self.set_show_buyer_bank()
