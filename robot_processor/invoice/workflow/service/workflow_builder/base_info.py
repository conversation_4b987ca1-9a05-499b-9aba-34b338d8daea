from leyan_proto.digismart.robot.invoice.workflow_pb2 import Invoice as pb_Invoice
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow

from ._abc import Context
from ._abc import DelegateModule
from ._abc import IModule


class BaseInfoModule(DelegateModule):
    def execute(self):
        match self.context:
            case Context.Manual():
                self._delegate(Manual)
            case Context.Trade():
                self._delegate(Trade)
            case Context.QNPlatformApply():
                self._delegate(QNPlatformApply)
            case Context.DoudianPlatformApply():
                self._delegate(DoudianPlatformApply)
            case Context.JDPlatformApply():
                self._delegate(JDPlatformApply)
            case Context.PddPlatformApply():
                self._delegate(PddPlatformApply)
            case _:
                self._delegate(Default)

    def validate(self):
        if self.pb_workflow.invoice_type == pb_Invoice.INVOICE_TYPE_UNSPECIFIED:
            self.context.add_error("发票类型不能为空")
        if self.pb_workflow.issuing_type == pb_Invoice.ISSUING_TYPE_UNSPECIFIED:
            self.context.add_error("发票种类不能为空")
        if not self.pb_workflow.HasField("remark_template"):
            self.context.add_error("发票备注模板未初始化")


class Default(IModule):
    def process(self):
        raise NotImplementedError("不支持的路径")


class Manual(IModule[Context.Manual]):
    def process(self):
        """Do Nothing"""


class Trade(IModule[Context.Trade]):
    def process(self):
        from robot_processor.invoice.workflow.models import InvoiceType
        from robot_processor.invoice.workflow.models import IssuingType

        self.pb_workflow.source = pb_InvoiceWorkflow.Source.TRADE
        self.pb_workflow.invoice_type = InvoiceType.VAT_GENERAL.pb_value
        self.pb_workflow.issuing_type = IssuingType.BLUE.pb_value
        self.context.initialize_remark_template_if_needed()


class QNPlatformApply(IModule[Context.QNPlatformApply]):
    def process(self):
        from robot_processor.invoice.workflow.models import IssuingType

        self.pb_workflow.source = pb_InvoiceWorkflow.Source.PLATFORM_APPLY
        self.pb_workflow.invoice_type = self.context.qn_invoice.get_invoice_type().pb_value
        self.pb_workflow.issuing_type = self.context.qn_invoice.get_issuing_type().pb_value
        if self.pb_workflow.issuing_type == IssuingType.RED:
            if original_id := self.context.provide_original_workflow_id(self.context.qn_invoice.tid):
                self.pb_workflow.original_workflow_id.value = original_id
        self.context.initialize_remark_template_if_needed()


class DoudianPlatformApply(IModule[Context.DoudianPlatformApply]):
    def process(self):
        self.pb_workflow.source = pb_InvoiceWorkflow.Source.PLATFORM_APPLY
        self.pb_workflow.invoice_type = self.context.doudian_invoice.get_invoice_type().pb_value
        self.pb_workflow.issuing_type = self.context.doudian_invoice.get_issuing_type().pb_value
        self.context.initialize_remark_template_if_needed()


class JDPlatformApply(IModule[Context.JDPlatformApply]):
    def process(self):
        from robot_processor.invoice.workflow.models import IssuingType

        self.pb_workflow.source = pb_InvoiceWorkflow.Source.PLATFORM_APPLY
        self.pb_workflow.invoice_type = self.context.jd_invoice.get_invoice_type().pb_value
        issuing_type = self.context.jd_invoice.get_issuing_type()
        self.pb_workflow.issuing_type = issuing_type.pb_value
        if issuing_type == IssuingType.RED:
            if original_id := self.context.provide_original_workflow_id(str(self.context.jd_invoice.orderId)):
                self.pb_workflow.original_workflow_id.value = original_id
        self.context.initialize_remark_template_if_needed()


class PddPlatformApply(IModule[Context.PddPlatformApply]):
    def process(self):
        self.pb_workflow.source = pb_InvoiceWorkflow.Source.PLATFORM_APPLY
        self.pb_workflow.invoice_type = self.context.pdd_invoice.get_invoice_type().pb_value
        self.pb_workflow.issuing_type = self.context.pdd_invoice.get_issuing_type().pb_value
        self.context.initialize_remark_template_if_needed()
