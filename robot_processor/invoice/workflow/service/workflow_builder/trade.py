from ._abc import Context
from ._abc import DelegateModule
from ._abc import IModule


class TradeModule(DelegateModule):
    def execute(self):
        match self.context:
            case Context.Manual():
                self._delegate(Manual)
            case Context.Trade():
                self._delegate(Trade)
            case Context.QNPlatformApply():
                self._delegate(QNPlatformApply)
            case Context.DoudianPlatformApply():
                self._delegate(DoudianPlatformApply)
            case Context.JDPlatformApply():
                self._delegate(JDPlatformApply)
            case Context.PddPlatformApply():
                self._delegate(PddPlatformApply)
            case _:
                self._delegate(Default)

    def validate(self):
        pass


class Default(IModule):
    def process(self):
        raise NotImplementedError("不支持的路径")


class Manual(IModule[Context.Manual]):
    def process(self):
        pass


class Trade(IModule[Context.Trade]):
    def process(self):
        self.pb_workflow.trade_info.tid = self.context.provide_tid()
        if not self.context.shop:
            return
        self.pb_workflow.trade_info.sid = self.context.provide_shop().sid
        self.pb_workflow.trade_info.platform = self.context.provide_shop().platform
        if self.context.provide_if_platform_trade_builder_mode():
            match self.context.shop.platform:
                case "TAOBAO" | "TMALL":
                    self._delegate(TaobaoTradeModule)
                case "DOUDIAN":
                    self._delegate(DoudianTradeModule)
                case "JD":
                    self._delegate(JDTradeModule)
                case "PDD":
                    self._delegate(PddTradeModule)
        else:
            self._delegate(ErpTradeModule)


class QNPlatformApply(IModule[Context.QNPlatformApply]):
    def process(self):
        self.pb_workflow.trade_info.tid = self.context.provide_tid()
        self.pb_workflow.trade_info.sid = self.context.provide_shop().sid
        self.pb_workflow.trade_info.platform = self.context.provide_shop().platform
        if self.context.provide_if_platform_trade_builder_mode():
            self._delegate(TaobaoTradeModule)
        else:
            self._delegate(ErpTradeModule)


class DoudianPlatformApply(IModule[Context.DoudianPlatformApply]):
    def process(self):
        self.pb_workflow.trade_info.tid = self.context.provide_tid()
        self.pb_workflow.trade_info.sid = self.context.provide_shop().sid
        self.pb_workflow.trade_info.platform = self.context.provide_shop().platform
        if not self.context.provide_if_platform_trade_builder_mode():
            self._delegate(ErpTradeModule)


class JDPlatformApply(IModule[Context.JDPlatformApply]):
    def process(self):
        self.pb_workflow.trade_info.tid = self.context.provide_tid()
        self.pb_workflow.trade_info.sid = self.context.provide_shop().sid
        self.pb_workflow.trade_info.platform = self.context.provide_shop().platform
        if self.context.provide_if_platform_trade_builder_mode():
            self._delegate(JDTradeModule)
        else:
            self._delegate(ErpTradeModule)


class PddPlatformApply(IModule[Context.PddPlatformApply]):
    def process(self):
        self.pb_workflow.trade_info.tid = self.context.provide_tid()
        self.pb_workflow.trade_info.sid = self.context.provide_shop().sid
        self.pb_workflow.trade_info.platform = self.context.provide_shop().platform
        if self.context.provide_if_platform_trade_builder_mode():
            self._delegate(PddTradeModule)
        else:
            self._delegate(ErpTradeModule)


class TaobaoTradeModule(IModule):
    def process(self):
        from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow

        from robot_processor.client import trade_client
        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.utils import message_to_dict
        from robot_processor.utils import string_wrapper

        taobao_trade = self.context.provide_taobao_trade()
        if not taobao_trade.trade_id:
            self.context.add_warning("未找到发票订单信息")
            return
        trade_info = self.pb_workflow.trade_info
        trade_info.raw.update(message_to_dict(taobao_trade))
        trade_info.datasource = GoodsItem.Datasource.safe_init(self.context.provide_shop().platform).pb_value
        trade_info.trade_status = trade_client.to_taobao_trade_status_zh(taobao_trade.status)
        trade_info.paid = str(taobao_trade.payment)
        trade_info.trade_memo = taobao_trade.memo
        for order in taobao_trade.orders:
            trade_info.oid.append(order.oid)
            trade_info.goods_items.append(
                pb_InvoiceWorkflow.TradeInfo.GoodsItem(
                    title=order.title,
                    description=string_wrapper(order.sku_description),
                    picture=string_wrapper(order.pic_path),
                    sku=string_wrapper(order.sku_id),
                    sku_outer=string_wrapper(order.outer_sku_id),
                    spu=string_wrapper(order.spu_id),
                    spu_outer=string_wrapper(order.outer_spu_id),
                    num=string_wrapper(order.quantity),
                )
            )


class JDTradeModule(IModule):
    def process(self):
        from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow

        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.utils import message_to_dict
        from robot_processor.utils import string_wrapper

        jd_trade = self.context.provide_jd_trade()
        if jd_trade is None:
            self.context.add_error("未找到京东订单信息")
            return
        trade_info = self.pb_workflow.trade_info
        trade_info.raw.update(message_to_dict(jd_trade))
        trade_info.datasource = GoodsItem.Datasource.JD.pb_value
        trade_info.paid = jd_trade.actual_pay
        for jd_item in jd_trade.jd_items:
            trade_info.oid.append(jd_item.tid)
            trade_info.goods_items.append(
                pb_InvoiceWorkflow.TradeInfo.GoodsItem(
                    title=jd_item.ware_title,
                    description=string_wrapper(jd_item.property),
                    picture=string_wrapper(jd_item.img_url),
                    sku_outer=string_wrapper(jd_item.outer_sku_id),
                    sku=string_wrapper(jd_item.sku_id),
                    spu=string_wrapper(jd_item.ware_id),
                    num=string_wrapper(jd_item.qty),
                )
            )


class PddTradeModule(IModule):
    def process(self):
        from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow

        from robot_processor.client import trade_client
        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.utils import message_to_dict
        from robot_processor.utils import string_wrapper

        pdd_trade = self.context.provide_pdd_trade()
        if pdd_trade is None:
            self.context.add_error("未找到拼多多订单信息")
            return
        trade_info = self.pb_workflow.trade_info
        trade_info.raw.update(message_to_dict(pdd_trade))
        trade_info.datasource = GoodsItem.Datasource.PDD.pb_value
        trade_info.paid = str(pdd_trade.pay_amount)
        trade_info.trade_status = trade_client.to_pdd_order_status_zh(pdd_trade.order_status)
        trade_info.refund_status = trade_client.to_pdd_refund_status_zh(pdd_trade.refund_status)
        trade_info.trade_memo = pdd_trade.remark
        for item in pdd_trade.item_list:
            trade_info.goods_items.append(
                pb_InvoiceWorkflow.TradeInfo.GoodsItem(
                    title=item.goods_name,
                    description=string_wrapper(item.goods_spec),
                    picture=string_wrapper(item.goods_img),
                    sku_outer=string_wrapper(item.outer_id),
                    sku=string_wrapper(item.sku_id),
                    spu_outer=string_wrapper(item.outer_goods_id),
                    spu=string_wrapper(item.goods_id),
                    num=string_wrapper(item.goods_count),
                )
            )


class DoudianTradeModule(IModule):
    def process(self):
        from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow

        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.utils import Amount
        from robot_processor.utils import message_to_dict
        from robot_processor.utils import string_wrapper

        doudian_trade = self.context.provide_doudian_trade()
        if doudian_trade is None:
            return
        trade_info = self.pb_workflow.trade_info
        trade_info.raw.update(message_to_dict(doudian_trade))
        trade_info.datasource = GoodsItem.Datasource.DOUDIAN.pb_value
        trade_info.paid = Amount(doudian_trade.pay_amount).divide(100).format(2)
        trade_info.trade_status = doudian_trade.order_status_desc
        trade_info.trade_memo = doudian_trade.seller_words
        for item in doudian_trade.sku_order_list:
            trade_info.oid.append(str(item.sku_order_id))
            trade_info.goods_items.append(
                pb_InvoiceWorkflow.TradeInfo.GoodsItem(
                    title=item.product_name,
                    description=string_wrapper(
                        ";".join(f"{spec.name}:{spec.value}" for spec in item.spec),
                    ),
                    sku_outer=string_wrapper(item.out_sku_id),
                    sku=string_wrapper(item.sku_id),
                    spu_outer=string_wrapper(item.out_product_id),
                    spu=string_wrapper(item.product_id),
                    num=string_wrapper(item.product_count),
                )
            )


class ErpTradeModule(IModule):
    def process(self):
        from robot_processor.enums import ErpType

        erp_info = self.context.provide_erp_info()
        if erp_info is None:
            self.context.add_error("未找到订单信息")
            return
        match erp_info.erp_type:
            case ErpType.JST:
                self._delegate(JstTradeModule)
            case ErpType.WDT:
                self._delegate(WdtTradeModule)
            case ErpType.WDTULTI:
                self._delegate(WdtultiTradeModule)
            case _ as erp_type:
                raise NotImplementedError(f"不支持的 ERP 类型 {erp_type}")


class JstTradeModule(IModule):
    def process(self):
        from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow

        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.utils import string_wrapper

        jst_order = self.context.provide_jst_order()
        trade_info = self.pb_workflow.trade_info
        trade_info.raw.update(jst_order.dict(exclude_none=True))
        trade_info.datasource = GoodsItem.Datasource.JST.pb_value
        trade_info.erp_account.value = self.context.cache.erp_account
        trade_info.trade_status = jst_order.order_status
        trade_info.paid = str(jst_order.pay_amount)
        trade_info.trade_memo = jst_order.remark
        for jst_item in jst_order.items:
            trade_info.goods_items.append(
                pb_InvoiceWorkflow.TradeInfo.GoodsItem(
                    title=jst_item.name,
                    short_title=string_wrapper(jst_item.erp_short_name),
                    description=string_wrapper(jst_item.properties_value),
                    picture=string_wrapper(jst_item.pic),
                    sku=string_wrapper(jst_item.shop_sku_id),
                    sku_outer=string_wrapper(jst_item.sku_id),
                    spu=string_wrapper(jst_item.shop_i_id),
                    spu_outer=string_wrapper(jst_item.i_id),
                    num=string_wrapper(jst_item.qty),
                )
            )


class WdtTradeModule(IModule):
    def process(self):
        from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
        from robot_types.helper import serialize

        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.utils import string_wrapper

        wdt_order = self.context.provide_wdt_order()
        trade_info = self.pb_workflow.trade_info
        trade_info.raw.update(serialize(wdt_order.dict(exclude_none=True)))
        trade_info.datasource = GoodsItem.Datasource.WDT.pb_value
        trade_info.erp_account.value = self.context.cache.erp_account
        trade_info.paid = str(wdt_order.paid)
        if wdt_order.trade_status_zh:
            trade_info.trade_status = wdt_order.trade_status_zh
        if wdt_order.cs_remark:
            trade_info.trade_memo = wdt_order.cs_remark
        for wdt_goods_item in wdt_order.goods_list:
            trade_info.goods_items.append(
                pb_InvoiceWorkflow.TradeInfo.GoodsItem(
                    title=wdt_goods_item.goods_name,
                    description=string_wrapper(wdt_goods_item.spec_name),
                    sku_outer=string_wrapper(wdt_goods_item.spec_no),
                    sku=string_wrapper(wdt_goods_item.platform_spec_id),
                    spu_outer=string_wrapper(wdt_goods_item.goods_no),
                    spu=string_wrapper(wdt_goods_item.platform_goods_id),
                    num=string_wrapper(wdt_goods_item.num),
                )
            )


class WdtultiTradeModule(IModule):
    def process(self):
        from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow

        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.utils import string_wrapper

        wdtulti_order = self.context.provide_wdtulti_order()
        trade_info = self.pb_workflow.trade_info
        trade_info.raw.update(wdtulti_order.dict(exclude_none=True))
        trade_info.datasource = GoodsItem.Datasource.WDTULTI.pb_value
        trade_info.erp_account.value = self.context.cache.erp_account
        trade_info.paid = wdtulti_order.paid
        trade_info.trade_status = wdtulti_order.chinese_status
        trade_info.trade_memo = wdtulti_order.cs_remark
        for item in wdtulti_order.detail_list:
            trade_info.goods_items.append(
                pb_InvoiceWorkflow.TradeInfo.GoodsItem(
                    title=item.goods_name,
                    description=string_wrapper(item.spec_name),
                    sku_outer=string_wrapper(item.spec_no),
                    spu_outer=string_wrapper(item.goods_no),
                    num=string_wrapper(item.num),
                )
            )
