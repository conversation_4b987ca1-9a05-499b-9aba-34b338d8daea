from ._abc import Context
from ._abc import DelegateModule
from ._abc import IModule


class IssuingItemModule(DelegateModule):
    def execute(self):
        match self.context:
            case Context.Manual():
                self._delegate(Manual)
            case Context.Trade():
                self._delegate(Trade)
            case Context.QNPlatformApply():
                self._delegate(QNPlatformApply)
            case Context.DoudianPlatformApply():
                self._delegate(DoudianPlatformApply)
            case Context.JDPlatformApply():
                self._delegate(JDPlatformApply)
            case Context.PddPlatformApply():
                self._delegate(PddPlatformApply)
            case _:
                self._delegate(Default)

    def validate(self):
        if not self.pb_workflow.issuing_items:
            self.context.add_error("发票明细未设置")


class Default(IModule):
    def process(self):
        raise NotImplementedError("不支持的路径")


class Manual(IModule[Context.Manual]):
    def process(self):
        pass


class Trade(IModule[Context.Trade]):
    def process(self):
        self.pb_workflow.is_tax_included = True
        if self.context.shop is None:
            return
        if self.context.provide_if_platform_trade_builder_mode():
            match self.context.shop.platform:
                case "TAOBAO" | "TMALL":
                    self._delegate(TaobaoTradeModule)
                case "DOUDIAN":
                    self._delegate(DoudianTradeModule)
                case "JD":
                    self._delegate(JDTradeModule)
                case "PDD":
                    self._delegate(PddTradeModule)
        else:
            self._delegate(ErpTradeModule)


class FixedIssuingItemModule(IModule):
    def process(self):
        def process_fixed(amount: str, num: str | None):
            from robot_types.model.invoice.config_manager import FixedIssuingItemSyncGoodsNum

            from robot_processor.invoice.config_manager import ConfigKey
            from robot_processor.invoice.goods.models import IssuingItem
            from robot_processor.utils import Amount

            config_val = self.context.config_manager.get(ConfigKey.PLATFORM_APPLY_SYNC_ISSUING_ITEM_STRATEGY)
            if not config_val.fixed_issuing_item:
                self.context.add_error("未设置固定的发票明细")
                return

            fixed_issuing_item = IssuingItem.get_by_id(self.context.org_id, int(config_val.fixed_issuing_item))
            if not fixed_issuing_item:
                self.context.add_error("未设置固定的发票明细")
                return

            issuing_item = fixed_issuing_item.to_workflow_pb(
                is_small_taxpayer=self.context.provide_is_small_taxpayer(), goods_item=None
            )
            issuing_item.amount = amount

            from robot_processor.invoice.config_manager import ConfigKey

            config_val = self.context.config_manager.get(ConfigKey.FIXED_ISSUING_ITEM_SYNC_GOODS_NUM)
            need_sync_num = config_val == FixedIssuingItemSyncGoodsNum.SYNC
            if need_sync_num and num is not None:
                price = Amount(amount).divide(num)
                issuing_item.num.value = num.format()
                issuing_item.price.value = price.format(precision=8)
            self.pb_workflow.issuing_items.append(issuing_item)

        return process_fixed


class ErpTradeModule(IModule):
    def process(self):
        from robot_processor.enums import ErpType
        from robot_processor.shop.models import ErpInfo
        from robot_processor.utils import Amount
        from robot_processor.utils import unwrap_optional

        erp_info: ErpInfo | None = self.context.provide_erp_info()
        if erp_info is None:
            self.context.add_error("未找到订单信息")
            return

        erp_type = unwrap_optional(erp_info.erp_type)
        match erp_type:
            case ErpType.JST:
                goods_items = self.prepare_jst_skus()
            case ErpType.WDT:
                goods_items = self.prepare_wdt_skus()
            case ErpType.WDTULTI:
                goods_items = self.prepare_wdtulti_skus()
            case _:
                raise NotImplementedError(f"不支持的 ERP 类型 {erp_type}")

        goods_broker = self.context.provide_goods_broker()
        goods_mode = self.context.provide_goods_mode()
        for goods_item in goods_items:
            if Amount(goods_item.paid) == 0 and self.context.provide_skip_zero_price_items():
                continue
            issuing_item = goods_item.to_workflow_issuing_item_pb(
                goods_mode,
                goods_broker,
                self.context.provide_is_small_taxpayer(),
                goods_item.num,
                goods_item.paid,
            )
            if not issuing_item.tax_code:
                self.context.add_error(f"商品 {goods_item.title} 未绑定开票项目")
            self.pb_workflow.issuing_items.append(issuing_item)

    def prepare_jst_skus(self):
        """ERP订单存在拆单，需要还原合并拆单前的明细"""
        from robot_processor.invoice.goods.models import TradeGoodsItem
        from robot_processor.plugin.erp_utils import jst_get_order_skus
        from rpa.erp.jst import JstNewSDK

        _, skus = jst_get_order_skus(
            shop=self.context.provide_shop(),
            jst_new_sdk=JstNewSDK(erp_info=self.context.provide_erp_info()),
            tid=self.context.provide_tid(),
            oid=None,
        )
        return [
            TradeGoodsItem(
                id=0,
                datasource=TradeGoodsItem.Datasource.JST,
                datasource_type=TradeGoodsItem.DatasourceType.ERP,
                sid=self.context.cache.erp_account,
                title=sku_info.outer_sku_name or sku_info.outer_spu_name,
                short_title=sku_info.outer_sku_short_name,
                description=sku_info.outer_properties,
                sku_outer=sku_info.outer_sku_id,
                sku=sku_info.sku_id,
                spu_outer=sku_info.outer_spu_id,
                spu=sku_info.spu_id,
                num=str(sku_info.qty),
                paid=str(sku_info.payment),
            )
            for sku_info in (skus or [])
        ]

    def prepare_wdt_skus(self):
        """ERP订单存在拆单，需要还原合并拆单前的明细"""
        from decimal import Decimal

        from robot_processor.invoice.goods.models import TradeGoodsItem
        from rpa.erp.wdt import WdtClient

        items: dict[str, TradeGoodsItem] = dict()
        wdt_client = WdtClient(erp_info=self.context.provide_erp_info())
        all_trades = sorted(
            [
                trade
                for trade in wdt_client.trade_query(wdt_tid=self.context.provide_tid()).response.trades
                if trade.trade_type == 1
            ],
            key=lambda x: x.trade_id,
        )
        for trade in all_trades:
            for detail in trade.goods_list:
                if detail.spec_no not in items:
                    goods_item = TradeGoodsItem(
                        id=0,
                        datasource=TradeGoodsItem.Datasource.WDT,
                        datasource_type=TradeGoodsItem.DatasourceType.ERP,
                        sid=self.context.cache.erp_account,
                        title=detail.goods_name,
                        description=detail.spec_name,
                        sku_outer=detail.spec_no,
                        sku=detail.platform_spec_id,
                        spu_outer=detail.goods_no,
                        spu=detail.platform_goods_id,
                        num="0",
                        paid="0",
                    )
                    items[detail.spec_no] = goods_item
                else:
                    goods_item = items[detail.spec_no]
                goods_item.num = str(Decimal(goods_item.num) + Decimal(detail.num))
                goods_item.paid = str(Decimal(goods_item.paid) + Decimal(detail.paid))
        return list(items.values())

    def prepare_wdtulti_skus(self):
        """ERP订单存在拆单，需要还原合并拆单前的明细"""
        from decimal import Decimal

        from robot_processor.invoice.goods.models import TradeGoodsItem
        from rpa.erp.wdtulti import WdtUltiQM

        items: dict[str, TradeGoodsItem] = dict()
        qm_sdk = WdtUltiQM(erp_info=self.context.provide_erp_info())
        all_trades = sorted(
            [trade for trade in qm_sdk.get_orders(src_tid=self.context.provide_tid()).order if trade.trade_type == 1],
            key=lambda x: x.trade_id,
        )
        for trade in all_trades:
            for detail in trade.detail_list:
                if detail.spec_no not in items:
                    goods_item = TradeGoodsItem(
                        id=0,
                        datasource=TradeGoodsItem.Datasource.WDTULTI,
                        datasource_type=TradeGoodsItem.DatasourceType.ERP,
                        sid=self.context.cache.erp_account,
                        title=detail.goods_name,
                        description=detail.spec_name,
                        sku_outer=detail.spec_no,
                        spu_outer=detail.goods_no,
                        num="0",
                        paid="0",
                    )
                    items[detail.spec_no] = goods_item
                else:
                    goods_item = items[detail.spec_no]
                goods_item.num = str(Decimal(goods_item.num) + Decimal(detail.num))
                goods_item.paid = str(Decimal(goods_item.paid) + Decimal(detail.paid))
        return list(items.values())


class TaobaoTradeModule(IModule):

    def process(self):
        from robot_processor.invoice.goods.models import GoodsItem

        taobao_trade = self.context.provide_taobao_trade()
        if not taobao_trade.trade_id:
            self.context.add_error("未找到发票订单信息")
            return
        goods_broker = self.context.provide_goods_broker()
        goods_mode = self.context.provide_goods_mode()
        for order in taobao_trade.orders:
            self.pb_workflow.trade_info.oid.append(order.oid)
            if order.payment == 0 and self.context.provide_skip_zero_price_items():
                continue
            goods_item = GoodsItem(
                datasource=GoodsItem.Datasource[self.context.shop.platform],
                datasource_type=GoodsItem.DatasourceType.PLATFORM,
                sid=self.context.shop.sid,
                is_combined=not order.item_is_single_flag,
                title=order.title,
                sku=order.sku_id,
                sku_outer=order.outer_sku_id,
                spu=order.spu_id,
                spu_outer=order.outer_spu_id,
                picture=order.pic_path,
                description=order.sku_description,
            )
            issuing_item = goods_item.to_workflow_issuing_item_pb(
                goods_mode,
                goods_broker,
                self.context.provide_is_small_taxpayer(),
                str(order.quantity),
                str(order.payment),
            )
            if not issuing_item.tax_code:
                self.context.add_error(f"商品 {goods_item.title} 未绑定开票项目")
            self.pb_workflow.issuing_items.append(issuing_item)


class JDTradeModule(IModule):
    def process(self):
        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.utils import Amount

        jd_trade = self.context.provide_jd_trade()
        if jd_trade is None:
            self.context.add_error("未找到京东订单信息")
            return
        goods_broker = self.context.provide_goods_broker()
        goods_mode = self.context.provide_goods_mode()
        for jd_item in jd_trade.jd_items:
            if Amount(jd_item.jd_price) == 0 and self.context.provide_skip_zero_price_items():
                continue
            goods_item = GoodsItem(
                datasource=GoodsItem.Datasource.JD,
                datasource_type=GoodsItem.DatasourceType.PLATFORM,
                sid=self.context.shop.sid,
                title=jd_item.ware_title,
                description=jd_item.property,
                picture=jd_item.img_url,
                sku_outer=jd_item.outer_sku_id,
                sku=jd_item.sku_id,
                spu_outer="",
                spu=jd_item.ware_id,
            )
            issuing_item = goods_item.to_workflow_issuing_item_pb(
                goods_mode,
                goods_broker,
                self.context.provide_is_small_taxpayer(),
                jd_item.qty,
                str(jd_item.jd_price),
            )
            if not issuing_item.tax_code:
                self.context.add_error(f"商品 {goods_item.title} 未绑定开票项目")
            self.pb_workflow.issuing_items.append(issuing_item)


class PddTradeModule(IModule):

    def process(self):
        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.utils import Amount

        pdd_trade = self.context.provide_pdd_trade()
        if pdd_trade is None:
            self.context.add_error("未找到拼多多订单信息")
            return
        goods_broker = self.context.provide_goods_broker()
        goods_mode = self.context.provide_goods_mode()
        for item in pdd_trade.item_list:
            if Amount(item.goods_price).equal(0) and self.context.provide_skip_zero_price_items():
                continue
            goods_item = GoodsItem(
                datasource=GoodsItem.Datasource.PDD,
                datasource_type=GoodsItem.DatasourceType.PLATFORM,
                sid=self.context.shop.sid,
                title=item.goods_name,
                description=item.goods_spec,
                picture=item.goods_img,
                sku_outer=item.outer_id,
                sku=str(item.sku_id),
                spu_outer=item.outer_goods_id,
                spu=str(item.goods_id),
            )
            issuing_item = goods_item.to_workflow_issuing_item_pb(
                goods_mode,
                goods_broker,
                self.context.provide_is_small_taxpayer(),
                str(item.goods_count),
                Amount(str(item.goods_price)).multipy(str(item.goods_count)).format(precision=2),
            )
            if not issuing_item.tax_code:
                self.context.add_error(f"商品 {goods_item.title} 未绑定开票项目")
            self.pb_workflow.issuing_items.append(issuing_item)


class DoudianTradeModule(IModule):
    def process(self):
        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.utils import Amount

        doudian_trade = self.context.provide_doudian_trade()
        if doudian_trade is None:
            self.context.add_error("未找到抖店订单信息")
            return
        goods_broker = self.context.provide_goods_broker()
        goods_mode = self.context.provide_goods_mode()
        for item in doudian_trade.sku_order_list:
            if item.pay_amount == 0 and self.context.provide_skip_zero_price_items():
                continue
            goods_item = GoodsItem(
                datasource=GoodsItem.Datasource.DOUDIAN,
                datasource_type=GoodsItem.DatasourceType.PLATFORM,
                sid=self.context.shop.sid,
                title=item.product_name,
                description=";".join(f"{spec.name}:{spec.value}" for spec in item.spec),
                sku=str(item.sku_id),
                sku_outer=item.out_sku_id,
                spu=str(item.product_id),
                spu_outer=item.out_product_id,
            )
            issuing_item = goods_item.to_workflow_issuing_item_pb(
                goods_mode,
                goods_broker,
                self.context.provide_is_small_taxpayer(),
                str(item.item_num),
                Amount(item.pay_amount).divide(100).format(8),
            )
            if not issuing_item.tax_code:
                self.context.add_error(f"商品 {goods_item.title} 未绑定开票项目")
            self.pb_workflow.issuing_items.append(issuing_item)


class QNPlatformApply(IModule[Context.QNPlatformApply]):
    def process(self):
        from robot_processor.client import trade_client
        from robot_processor.utils import Amount

        self.pb_workflow.is_tax_included = True
        if self.context.provide_is_fixed_issuing_item_strategy():
            tb_trade_info = trade_client.get_trade_by_tid(self.context.shop.sid, self.context.qn_invoice.tid)
            if tb_trade_info.orders:
                num = str(Amount.sum([item.quantity for item in tb_trade_info.orders]))
            else:
                num = None
            self._delegate(FixedIssuingItemModule)(amount=self.context.qn_invoice.amount, num=num)

        elif self.context.provide_if_platform_trade_builder_mode():
            self._delegate(TaobaoTradeModule)
        else:
            self._delegate(ErpTradeModule)


class DoudianPlatformApply(IModule[Context.DoudianPlatformApply]):
    def process(self):
        from robot_processor.utils import Amount

        self.pb_workflow.is_tax_included = True
        if self.context.provide_is_fixed_issuing_item_strategy():
            amount = str(Amount(self.context.doudian_invoice.invoice_amount).divide(100).format(precision=2))
            num = str(sum([detail.ProductCount for _, detail in self.context.doudian_invoice.invoice_detail.items()]))
            self._delegate(FixedIssuingItemModule)(amount=amount, num=num)

        elif self.context.provide_if_platform_trade_builder_mode():
            self.process_with_platform_apply_info()
        else:
            self._delegate(ErpTradeModule)

    def process_with_platform_apply_info(self):
        from robot_types.model.invoice.config_manager import GoodsMode

        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.utils import Amount

        goods_broker = self.context.provide_goods_broker()
        doudian_invoice = self.context.doudian_invoice
        for _, detail in doudian_invoice.invoice_detail.items():
            if Amount(detail.ProductPrice) == 0 and self.context.provide_skip_zero_price_items():
                continue
            goods_item = GoodsItem(
                datasource=GoodsItem.Datasource.DOUDIAN,
                datasource_type=GoodsItem.DatasourceType.PLATFORM,
                sid=self.context.shop.sid,
                title=detail.ProductName,
                description=detail.to_description(),
                sku=detail.SkuCode,
                spu=detail.ProductId,
            )
            issuing_item = goods_item.to_workflow_issuing_item_pb(
                GoodsMode.PLATFORM,
                goods_broker,
                self.context.provide_is_small_taxpayer(),
                str(detail.ProductCount),
                Amount(detail.ProductPrice).divide(100).format(8),
            )
            if not issuing_item.tax_code:
                self.context.add_error(f"商品 {goods_item.title} 未绑定开票项目")
            self.pb_workflow.issuing_items.append(issuing_item)


class JDPlatformApply(IModule[Context.JDPlatformApply]):
    def process(self):
        from robot_processor.utils import Amount
        from robot_processor.utils import unwrap_optional

        self.pb_workflow.is_tax_included = True
        if self.context.provide_is_fixed_issuing_item_strategy():
            num = Amount(0)
            for item in self.context.jd_invoice.orderShouldInvoiceAmountDetailList or []:
                if item.detailType == 1:
                    num = num.add(item.num)
            amount = str(Amount(unwrap_optional(self.context.jd_invoice.shouldInvoiceAmount)).format(precision=2))
            self._delegate(FixedIssuingItemModule)(amount=amount, num=str(num))

        elif self.context.provide_if_platform_trade_builder_mode():
            self._delegate(JDTradeModule)
        else:
            self._delegate(ErpTradeModule)


class PddPlatformApply(IModule[Context.PddPlatformApply]):
    def process(self):
        from robot_processor.utils import Amount

        self.pb_workflow.is_tax_included = True
        if self.context.provide_is_fixed_issuing_item_strategy():
            amount = str(Amount(self.context.pdd_invoice.invoice_amount).divide(100).format(precision=2))
            self._delegate(FixedIssuingItemModule)(amount=amount, num=None)
        elif self.context.provide_if_platform_trade_builder_mode():
            self._delegate(PddTradeModule)
        else:
            self._delegate(ErpTradeModule)
