from __future__ import annotations

from abc import ABC
from abc import abstractmethod
from dataclasses import dataclass
from dataclasses import field
from typing import TYPE_CHECKING
from typing import ClassVar
from typing import Generic
from typing import Literal
from typing import Optional
from typing import TypeVar

from leyan_proto.digismart.robot.invoice.workflow_pb2 import Invoice as pb_Invoice
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
from leyan_proto.digismart.trade.dgt_dy_trade_pb2 import DyTradeInfo as pb_DyTradeInfo
from leyan_proto.digismart.trade.dgt_jd_trade_pb2 import JdTradeInfo as pb_JdTradeInfo
from leyan_proto.digismart.trade.dgt_ks_trade_pb2 import CommonTradeInfo as pb_KsTradeInfo
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import PddTradeInfo as pb_PddTradeInfo
from leyan_proto.digismart.trade.dgt_trade_pb2 import TradeInfo as pb_TradeInfo
from loguru import logger

if TYPE_CHECKING:
    from robot_processor.client.doudian import DoudianInvoice
    from robot_processor.client.jd_sdk import InvoiceApplyData as JDInvoiceApply
    from robot_processor.client.pdd_erp import PddInvoiceApplicationQuery
    from robot_processor.invoice.config_manager import ConfigManager
    from robot_processor.invoice.tax_bureau.models import Corporate
    from robot_processor.shop.models import ErpInfo
    from robot_processor.shop.models import Shop
    from rpa.erp.guanyiyunapi.schemas import GyyOrder
    from rpa.erp.jackyun.schemas import JackyunOrder
    from rpa.erp.jst import JstBaseOrder
    from rpa.erp.kuaimai import KuaimaiOrder
    from rpa.erp.wdt import Trade as WdtTrade
    from rpa.erp.wdtulti import WdtultiOrderModel
    from rpa.erp.wln import Order as WlnOrder
    from rpa.mola.schemas import QNInvoice


@dataclass(kw_only=True)
class ContextCache:
    taobao_trade: pb_TradeInfo = field(init=False, repr=False)
    # null 是订单未找到，not initialized 是未初始化
    jd_trade: pb_JdTradeInfo | None = field(init=False, repr=False)
    pdd_trade: pb_PddTradeInfo | None = field(init=False, repr=False)
    doudian_trade: pb_DyTradeInfo | None = field(init=False, repr=False)
    ks_trade: pb_KsTradeInfo | None = field(init=False, repr=False)
    erp_info: Optional["ErpInfo"] = field(init=False, repr=False)
    wdt_order: Optional["WdtTrade"] = field(init=False, repr=False)
    wdtulti_order: Optional["WdtultiOrderModel"] = field(init=False, repr=False)
    jst_order: Optional["JstBaseOrder"] = field(init=False, repr=False)
    wln_order: Optional["WlnOrder"] = field(init=False, repr=False)
    kuaimai_order: Optional["KuaimaiOrder"] = field(init=False, repr=False)
    jackyun_order: Optional["JackyunOrder"] = field(init=False, repr=False)
    gyy_order: Optional["GyyOrder"] = field(init=False, repr=False)

    def has_initialized(
        self,
        field_name: Literal[
            "taobao_trade",
            "jd_trade",
            "pdd_trade",
            "doudian_trade",
            "ks_trade",
            "erp_info",
            "wdt_order",
            "wdtulti_order",
            "jst_order",
            "wln_order",
            "kuaimai_order",
            "jackyun_order",
            "gyy_order",
        ],
    ):
        return hasattr(self, field_name)

    @property
    def erp_account(self):
        from robot_processor.enums import ErpType
        from robot_processor.utils import unwrap_optional

        if self.erp_info is None:
            return None
        erp_info = self.erp_info
        return {
            ErpType.JST: lambda: erp_info.meta["co_id"],
            ErpType.WDT: lambda: erp_info.meta["sid"],
            ErpType.WDTULTI: lambda: erp_info.meta["sid"],
        }[unwrap_optional(erp_info.erp_type)]()

    @property
    def erp_order(self):
        from robot_processor.enums import ErpType
        from robot_processor.utils import unwrap_optional

        if self.erp_info is None:
            return None
        erp_type = unwrap_optional(self.erp_info.erp_type)
        return {
            ErpType.JST: lambda: self.jst_order,
            ErpType.WDT: lambda: self.wdt_order,
            ErpType.WDTULTI: lambda: self.wdtulti_order,
            ErpType.WANLINIU: lambda: self.wln_order,
            ErpType.KUAIMAI: lambda: self.kuaimai_order,
            ErpType.JACKYUN: lambda: self.jackyun_order,
            ErpType.GUANYIYUN: lambda: self.gyy_order,
        }[erp_type]()

    def init_taobao_trade(self, tid: str, sid: str):
        from robot_processor.client import trade_client

        self.taobao_trade = trade_client.get_trade_by_tid(sid, tid)

    def init_jd_trade(self, tid: str, sid: str):
        from robot_processor.client import trade_client

        trade_resp = trade_client.get_trade_by_tid_and_channel(tids=[tid], channel_type="JD", sid=sid)
        if trade_resp.trade_info_list:
            self.jd_trade = trade_resp.trade_info_list[0].jd_trade_info
        else:
            self.jd_trade = None

    def init_pdd_trade(self, tid: str, sid: str):
        from robot_processor.client import trade_client

        trade_resp = trade_client.get_trade_by_tid_and_channel(tids=[tid], channel_type="PDD", sid=sid)
        if trade_resp.trade_info_list:
            self.pdd_trade = trade_resp.trade_info_list[0].pdd_trade_info
        else:
            self.pdd_trade = None

    def init_doudian_trade(self, tid: str, sid: str):
        from robot_processor.client import trade_client

        trade_resp = trade_client.get_trade_by_tid_and_channel(tids=[tid], channel_type="DOUDIAN", sid=sid)
        if trade_resp.trade_info_list:
            self.doudian_trade = trade_resp.trade_info_list[0].dy_trade_info
        else:
            self.doudian_trade = None

    def init_ks_trade(self, tid: str, sid: str):
        from robot_processor.client import trade_client

        trade_resp = trade_client.get_trade_by_tid_and_channel(tids=[tid], channel_type="KUAISHOU", sid=sid)
        if trade_resp.trade_info_list:
            self.ks_trade = trade_resp.trade_info_list[0].ks_trade_info
        else:
            self.ks_trade = None

    def init_jst_order(self, tid: str, org_id: int):
        from robot_processor.enums import ErpType
        from robot_processor.plugin.trade_utils import ErpTradeManager

        erp_info_list = ErpTradeManager.get_erp_info_list(org_id=org_id, erp_type=ErpType.JST)
        for erp_info in erp_info_list:
            jst_order = ErpTradeManager.from_erp_info(erp_info, tid)
            if jst_order is not None:
                self.erp_info = erp_info
                self.jst_order = jst_order
                break
        else:
            self.jst_order = None

    def init_wdt_order(self, tid: str, org_id: int):
        from robot_processor.enums import ErpType
        from robot_processor.plugin.trade_utils import ErpTradeManager

        erp_info_list = ErpTradeManager.get_erp_info_list(org_id=org_id, erp_type=ErpType.WDT)
        for erp_info in erp_info_list:
            wdt_order = ErpTradeManager.from_erp_info(erp_info, tid)
            if wdt_order is not None:
                self.erp_info = erp_info
                self.wdt_order = wdt_order
                break
        else:
            self.wdt_order = None

    def init_wdtulti_order(self, tid: str, org_id: int):
        from robot_processor.enums import ErpType
        from robot_processor.plugin.trade_utils import ErpTradeManager

        erp_info_list = ErpTradeManager.get_erp_info_list(org_id=org_id, erp_type=ErpType.WDTULTI)
        for erp_info in erp_info_list:
            wdtulti_order = ErpTradeManager.from_erp_info(erp_info, tid)
            if wdtulti_order is not None:
                self.erp_info = erp_info
                self.wdtulti_order = wdtulti_order
                break
        else:
            self.wdtulti_order = None

    def init_erp_order(self, tid: str, org_id: int):
        from robot_processor.enums import ErpType
        from robot_processor.plugin.trade_utils import ErpTradeManager

        detect_result = ErpTradeManager.detect_erp(org_id=org_id, tid=tid)
        if detect_result is None:
            self.erp_info = None
            return
        erp_info, erp_order = detect_result
        self.erp_info = erp_info
        match erp_info.erp_type:
            case ErpType.JST:
                self.jst_order = erp_order
            case ErpType.WDT:
                self.wdt_order = erp_order
            case ErpType.WDTULTI:
                self.wdtulti_order = erp_order
            case ErpType.WANLINIU:
                self.wln_order = erp_order
            case ErpType.KUAIMAI:
                self.kuaimai_order = erp_order
            case ErpType.JACKYUN:
                self.jackyun_order = erp_order
            case ErpType.GUANYIYUN:
                self.gyy_order = erp_order


@dataclass(kw_only=True)
class IContext(ABC):
    @dataclass
    class ErrorDetail:
        msg: str
        loc: list[str] | None = None

        def to_dict(self):
            from robot_processor.utils import filter_none

            return filter_none({"msg": self.msg, "loc": self.loc})

    cache: ContextCache = field(default_factory=ContextCache)
    source: pb_InvoiceWorkflow.Source = field(init=False)
    pb_workflow: pb_InvoiceWorkflow.EditableView = field(init=False, default_factory=pb_InvoiceWorkflow.EditableView)
    # errors 为开票申请信息不完整，无法进行开票
    errors: list[ErrorDetail] = field(init=False, default_factory=list)
    # warnings 是开票信息额外信息存在缺失，不影响开票流程
    warnings: list[ErrorDetail] = field(init=False, default_factory=list)
    config_manager: ConfigManager = field(init=False, repr=False)
    org_id: int

    # 支持拆票场景
    is_split: bool = field(init=False, default=False)
    split_workflows: list[pb_InvoiceWorkflow.EditableView] = field(init=False, default_factory=list)

    # 在 build context 过程中会遇到一些 error 导致 workflow 不能创建，通过 flag_abort 标识来控制
    abort_error: Exception | None = field(init=False, default=None)

    def __post_init__(self):
        from robot_processor.invoice.config_manager.services import ConfigManager

        self.config_manager = ConfigManager(self.org_id)

    def add_error(self, error: str | ErrorDetail):
        if isinstance(error, IContext.ErrorDetail):
            self.errors.append(error)
        else:
            self.errors.append(IContext.ErrorDetail(error))

    def add_warning(self, error: str | ErrorDetail):
        if isinstance(error, IContext.ErrorDetail):
            self.warnings.append(error)
        else:
            self.warnings.append(IContext.ErrorDetail(error))

    @property
    def has_errors(self) -> bool:
        return len(self.errors) > 0

    def initialize_remark_template_if_needed(self):
        from robot_processor.invoice.config_manager import ConfigKey
        from robot_processor.utils import pb_value_wrapper

        if not self.pb_workflow.HasField("remark_template"):
            default_remark_template = pb_value_wrapper(self.config_manager.get_raw(ConfigKey.INVOICE_REMARK_TEMPLATE))
            self.pb_workflow.remark_template.CopyFrom(default_remark_template)

    def adjust_issuing_item_amount_precision(self, pb_workflow: pb_InvoiceWorkflow.EditableView | None = None):
        from robot_processor.utils import Amount

        pb_workflow = pb_workflow or self.pb_workflow
        # 将 Decimal('179.89999389648438') 这样的金额调整为 Decimal('179.90')
        expect = Amount(Amount.sum([item.amount for item in pb_workflow.issuing_items]).format(precision=2))
        for item in pb_workflow.issuing_items:
            item.amount = Amount(item.amount).format(precision=2)
        actual = Amount.sum([item.amount for item in pb_workflow.issuing_items])
        if expect != actual:
            last_item = pb_workflow.issuing_items[-1]
            diff = expect.subtract(actual)
            last_item.amount = Amount(last_item.amount).add(diff).format(precision=2)

    def adjust_issuing_item_amount_if_needed(
        self, pb_workflow: pb_InvoiceWorkflow.EditableView | None = None, allow_expect_gt_actual: bool = False
    ):
        """根据总金额按比例调整发票明细列表中的每条明细的金额"""
        from robot_processor.utils import Amount

        pb_workflow = pb_workflow or self.pb_workflow
        if not pb_workflow.platform_apply_info.invoice_amount:
            raise ValueError("请先设置平台申请的总金额")
        self.adjust_issuing_item_amount_precision(pb_workflow)
        expect = Amount(pb_workflow.platform_apply_info.invoice_amount)
        actual = Amount.sum([item.amount for item in pb_workflow.issuing_items])
        if expect > actual and not allow_expect_gt_actual:
            logger.warning("申请开票金额大于以开票明细，转为人工处理")
            return
        if expect == actual:
            return
        ratio = expect.divide(actual)
        for item in pb_workflow.issuing_items:
            item.amount = Amount(item.amount).multipy(ratio).format(precision=2)
        # 在按比例调整后因为小数点进位的原因，可能存在差异。将差额不到最后一个明细中
        actual = Amount.sum([item.amount for item in pb_workflow.issuing_items])
        if actual != expect:
            last_item = pb_workflow.issuing_items[-1]
            diff = expect.subtract(actual)
            last_item.amount = Amount(last_item.amount).add(diff).format(precision=2)
        self.adjust_issuing_item_price_if_needed(pb_workflow)

    def adjust_issuing_item_price_if_needed(self, pb_workflow: pb_InvoiceWorkflow.EditableView | None = None):
        from robot_processor.utils import Amount

        pb_workflow = pb_workflow or self.pb_workflow
        for issuing_item in pb_workflow.issuing_items:
            if not issuing_item.HasField("num"):
                continue
            issuing_item.price.value = Amount(issuing_item.amount).divide(issuing_item.num.value).format(precision=8)

    def provide_shop(self) -> Shop:
        raise NotImplementedError(f"{self.__class__.__name__} 不支持提供店铺信息")

    def provide_tid(self):
        raise NotImplementedError(f"{self.__class__.__name__} 不支持提供订单号")

    def provide_if_platform_trade_builder_mode(self):
        from robot_types.model.invoice.config_manager import TradeBuilderMode

        from robot_processor.invoice.config_manager import ConfigKey

        return self.config_manager.get(ConfigKey.TRADE_BUILDER_MODE) == TradeBuilderMode.PLATFORM

    def provide_goods_mode(self):
        from robot_types.model.invoice.config_manager import GoodsMode

        from robot_processor.invoice.config_manager import ConfigKey
        from robot_processor.invoice.config_manager import is_configured

        if is_configured(self.org_id, ConfigKey.GOODS_MODE):
            return self.config_manager.get(ConfigKey.GOODS_MODE)
        else:
            return GoodsMode(self.config_manager.get(ConfigKey.TRADE_BUILDER_MODE))

    def provide_with_submit(self):
        from robot_types.model.invoice.config_manager import PlatformApplySyncAfterAction

        from robot_processor.invoice.config_manager import ConfigKey

        return (
            self.config_manager.get(ConfigKey.PLATFORM_APPLY_SYNC_AFTER_ACTION) == PlatformApplySyncAfterAction.SUBMIT
        )

    def provide_original_workflow_id(self, tid):
        from sqlalchemy.sql.operators import eq as sql_eq

        from robot_processor.invoice.workflow.models import InvoiceWorkflow
        from robot_processor.invoice.workflow.models import IssuingType

        assert self.pb_workflow.issuing_type == pb_Invoice.RED, "只有红票支持查询关联的蓝票申请"
        original = (
            InvoiceWorkflow.query.filter(
                sql_eq(InvoiceWorkflow.org_id, self.org_id),
                sql_eq(InvoiceWorkflow.tid, tid),
                sql_eq(InvoiceWorkflow.source, InvoiceWorkflow.Source.PLATFORM_APPLY),
                sql_eq(InvoiceWorkflow.issuing_type, IssuingType.BLUE),
            )
            .order_by(InvoiceWorkflow.id.desc())
            .first()
        )
        return original.id if original else None

    def provide_skip_zero_price_items(self):
        from robot_processor.invoice.config_manager import ConfigKey

        return self.config_manager.get(ConfigKey.SKIP_ZERO_PRICE_ITEMS)

    def provide_is_fixed_issuing_item_strategy(self):
        from robot_types.model.invoice.config_manager import IssuingItemStrategy

        from robot_processor.invoice.config_manager import ConfigKey

        config_val = self.config_manager.get(ConfigKey.PLATFORM_APPLY_SYNC_ISSUING_ITEM_STRATEGY)
        return config_val.mode == IssuingItemStrategy.Mode.FIXED

    def provide_taobao_trade(self):
        if not self.cache.has_initialized("taobao_trade"):
            self.cache.init_taobao_trade(tid=self.provide_tid(), sid=self.provide_shop().sid)
        return self.cache.taobao_trade

    def provide_jd_trade(self):
        if not self.cache.has_initialized("jd_trade"):
            self.cache.init_jd_trade(tid=self.provide_tid(), sid=self.provide_shop().sid)
        return self.cache.jd_trade

    def provide_pdd_trade(self):
        if not self.cache.has_initialized("pdd_trade"):
            self.cache.init_pdd_trade(tid=self.provide_tid(), sid=self.provide_shop().sid)
        return self.cache.pdd_trade

    def provide_doudian_trade(self):
        if not self.cache.has_initialized("doudian_trade"):
            self.cache.init_doudian_trade(tid=self.provide_tid(), sid=self.provide_shop().sid)
        return self.cache.doudian_trade

    def provide_ks_trade(self):
        if not self.cache.has_initialized("ks_trade"):
            self.cache.init_ks_trade(tid=self.provide_tid(), sid=self.provide_shop().sid)
        return self.cache.ks_trade

    def provide_jst_order(self):
        if not self.cache.has_initialized("jst_order"):
            self.cache.init_jst_order(tid=self.provide_tid(), org_id=self.org_id)
        return self.cache.jst_order

    def provide_wdt_order(self):
        if not self.cache.has_initialized("wdt_order"):
            self.cache.init_wdt_order(tid=self.provide_tid(), org_id=self.org_id)
        return self.cache.wdt_order

    def provide_wdtulti_order(self):
        if not self.cache.has_initialized("wdtulti_order"):
            self.cache.init_wdtulti_order(tid=self.provide_tid(), org_id=self.org_id)
        return self.cache.wdtulti_order

    def provide_erp_info(self):
        if not self.cache.has_initialized("erp_info"):
            self.cache.init_erp_order(tid=self.provide_tid(), org_id=self.org_id)
        return self.cache.erp_info


IContextT = TypeVar("IContextT", bound=IContext)


@dataclass(kw_only=True)
class ManualContext(IContext):
    """手动创建"""

    source: pb_InvoiceWorkflow.Source = field(  # type: ignore[assignment]
        default=pb_InvoiceWorkflow.Source.MANUAL, init=False
    )
    pb_workflow: pb_InvoiceWorkflow.EditableView


@dataclass(kw_only=True)
class PlatformApplyContext(IContext):
    """平台发票申请同步"""

    source: pb_InvoiceWorkflow.Source = field(  # type: ignore[assignment]
        default=pb_InvoiceWorkflow.Source.PLATFORM_APPLY, init=False
    )
    shop: Shop
    corporate: Corporate | None = field(init=False)

    def __post_init__(self):
        from robot_processor.invoice.tax_bureau.models import Corporate

        super().__post_init__()
        self.corporate = Corporate.get_by_shop(self.shop)

    def provide_shop(self):
        return self.shop

    def provide_is_small_taxpayer(self):
        if self.corporate is None:
            return False
        return self.corporate.is_small_taxpayer

    def provide_goods_broker(self):
        from robot_processor.invoice.goods.services import GoodsBroker

        return GoodsBroker.by_shop(self.shop)


@dataclass(kw_only=True)
class QNPlatformApplyContext(PlatformApplyContext):
    """千牛平台发票申请同步"""

    qn_invoice: QNInvoice

    def provide_tid(self):
        return self.qn_invoice.tid


@dataclass(kw_only=True)
class DoudianPlatformApplyContext(PlatformApplyContext):
    """抖店平台发票申请同步"""

    doudian_invoice: DoudianInvoice

    def provide_tid(self):
        return self.doudian_invoice.shop_order_id


@dataclass(kw_only=True)
class JDPlatformApplyContext(PlatformApplyContext):
    """京东平台发票申请同步"""

    jd_invoice: JDInvoiceApply

    def provide_tid(self):
        return str(self.jd_invoice.orderId)


@dataclass(kw_only=True)
class PddPlatformApplyContext(PlatformApplyContext):
    """拼多多平台发票申请同步"""

    pdd_invoice: PddInvoiceApplicationQuery.Response.InvoiceApplicationQueryResponse.InvoiceApplicationQueryItem

    def provide_tid(self):
        return self.pdd_invoice.order_sn


@dataclass(kw_only=True)
class TradeContext(IContext):
    """通过订单创建"""

    tid: str
    shop: Shop | None = field(init=False, default=None)

    source: pb_InvoiceWorkflow.Source = field(  # type: ignore[assignment]
        default=pb_InvoiceWorkflow.Source.TRADE, init=False
    )

    def provide_tid(self):
        return self.tid

    def provide_shop(self):
        assert self.shop is not None
        return self.shop

    def provide_goods_broker(self):
        from robot_processor.invoice.goods.services import GoodsBroker

        return GoodsBroker.by_shop(self.provide_shop())

    def provide_corporate(self):
        from robot_processor.invoice.tax_bureau.models import Corporate

        if self.shop is None:
            return None
        return Corporate.get_by_shop(self.shop)

    def provide_is_small_taxpayer(self):
        corporate = self.provide_corporate()
        if corporate is None:
            return False
        return corporate.is_small_taxpayer


class DelegateModule(ABC):
    def __init__(self, context: IContext):
        self.context = context

    @abstractmethod
    def validate(self): ...

    @abstractmethod
    def execute(self): ...

    def _delegate(self, module_cls):
        logger.info(f"delegate {self.__class__.__name__} to {module_cls.__name__}")
        module_cls(self.context).process()

    @property
    def pb_workflow(self):
        return self.context.pb_workflow


class IModule(ABC, Generic[IContextT]):
    def __init__(self, context: IContextT):
        self.context = context

    @abstractmethod
    def process(self): ...

    @property
    def pb_workflow(self):
        return self.context.pb_workflow

    def _delegate(self, module_cls):
        logger.info(f"delegate {self.__class__.__name__} to {module_cls.__name__}")
        return module_cls(self.context).process()


IModuleT = TypeVar("IModuleT", bound=IModule)


class Context:
    Manual: ClassVar = ManualContext
    PlatformApply: ClassVar = PlatformApplyContext
    QNPlatformApply: ClassVar = QNPlatformApplyContext
    DoudianPlatformApply: ClassVar = DoudianPlatformApplyContext
    JDPlatformApply: ClassVar = JDPlatformApplyContext
    PddPlatformApply: ClassVar = PddPlatformApplyContext
    Trade: ClassVar = TradeContext
