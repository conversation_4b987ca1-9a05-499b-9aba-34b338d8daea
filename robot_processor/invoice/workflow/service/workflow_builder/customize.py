from ._abc import DelegateModule
from ._abc import IModule


class CustomizeModule(DelegateModule):
    def execute(self):
        match self.context.org_id:
            case 2114:
                self._delegate(Org2114)
            case 2899:
                self._delegate(Org2899)
            case 3400:
                self._delegate(Org3400)
            case 569:
                self._delegate(Org569)
            case _:
                self._delegate(Default)

    def validate(self):
        pass


class Default(IModule):
    def process(self):
        pass


class Org2114(IModule):
    def process(self):
        from robot_processor.invoice.workflow.models import BuyerType

        self.pb_workflow.buyer_type = BuyerType.ENTERPRISE.pb_value


class Org2899(IModule):
    """自由点旗舰店"""

    def process(self):
        from robot_processor.invoice.goods.models import IssuingItem
        from robot_processor.utils import string_wrapper
        from robot_processor.utils import unwrap_optional

        fixed_issuing_item = unwrap_optional(
            IssuingItem.query.filter(IssuingItem.org_id == 2899, IssuingItem.deleted.is_(False)).first()
        )
        is_small_taxpayer = self.context.provide_is_small_taxpayer()
        tax_rate = "0.01" if is_small_taxpayer else str(fixed_issuing_item.tax_rate)

        for issuing_item in self.context.pb_workflow.issuing_items:
            if not issuing_item.HasField("goods_item"):
                continue
            issuing_item.title = issuing_item.goods_item.title.value
            if issuing_item.issuing_item_id:
                continue
            issuing_item.tax_name_abbr = fixed_issuing_item.tax_name_abbr
            issuing_item.tax_code = fixed_issuing_item.tax_code
            issuing_item.tax_rate = tax_rate
            issuing_item.description.MergeFrom(string_wrapper(fixed_issuing_item.description))
            issuing_item.unit.MergeFrom(string_wrapper(fixed_issuing_item.unit))
            issuing_item.tax_exemption_type = fixed_issuing_item.exemption_type
            issuing_item.preferential_tax_policy = fixed_issuing_item.preferential_policy
        self.context.adjust_issuing_item_price_if_needed()


class Org3400(IModule):
    """虹越旗舰店

    Task:
        https://redmine.leyantech.com/issues/685928
    """

    def process(self):
        from decimal import Decimal

        from robot_processor.invoice.goods.models import GoodsItem
        from robot_processor.invoice.goods.models import TaxCode
        from rpa.erp.wdt import WdtOpenAPIClient

        wdt_client = WdtOpenAPIClient(sid=self.context.provide_shop().sid)
        for idx, issuing_item in enumerate(self.pb_workflow.issuing_items):
            if not issuing_item.HasField("goods_item"):
                self.context.add_error(f"商品 {idx} 未绑定商品信息")
                continue
            if issuing_item.goods_item.datasource != GoodsItem.Datasource.WDT:
                self.context.add_error(f"商品 {issuing_item.goods_item.title.value} 不是旺店通商品")
                continue
            if not issuing_item.goods_item.HasField("sku_outer"):
                self.context.add_error(f"商品 {issuing_item.goods_item.title.value} 缺少 sku outer")
                continue
            sku_response = wdt_client.query_skus_by_sku_id(issuing_item.goods_item.sku_outer.value)
            if not sku_response.goods_list:
                self.context.add_error(f"商品 {issuing_item.goods_item.sku_outer.value} 未找到对应的商品信息")
                continue
            for spec_info in sku_response.goods_list[0].spec_list:
                if spec_info.spec_no == issuing_item.goods_item.sku_outer.value:
                    break
            else:
                self.context.add_error(f"商品 {issuing_item.goods_item.title.value} 未找到对应的规格信息")
                continue
            if spec_info.tax_code is None or spec_info.tax_rate is None:
                self.context.add_error(f"商品 {issuing_item.goods_item.title.value} 未设置税收编码或税率")
                continue
            issuing_item.tax_code = spec_info.tax_code
            issuing_item.tax_rate = str((Decimal(str(spec_info.tax_rate)) / Decimal("100")).quantize(Decimal("0.01")))
            issuing_item.tax_name_abbr = TaxCode.get_name_abbr_by_code(issuing_item.tax_code)
            issuing_item.title = issuing_item.goods_item.title.value


class Org569(IModule):
    """客户要根据商品 SKU 来拆分到不同的主体进行开票"""

    def process(self):
        from collections import defaultdict

        from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow

        from robot_processor.invoice.tax_bureau.models import Corporate
        from rpa.erp.jst import JstNewSDK

        self.context.is_split = True
        corporates = Corporate.get_by_org_id(self.context.org_id)
        corporate_map = {corporate.name: corporate for corporate in corporates}
        corporate_issuing_items_map = defaultdict(list)
        sku_ids = list(
            set([issuing_item.goods_item.sku_outer.value for issuing_item in self.context.pb_workflow.issuing_items])
        )
        sku_resp = JstNewSDK(self.context.provide_shop().sid).query_skus_by_sku_ids(sku_ids)
        sku_map = {sku.sku_id: sku for sku in sku_resp.data.datas}
        # 为每个开票项目选择拆分后新的开票主体
        for issuing_item in self.context.pb_workflow.issuing_items:
            select_corporate_result = self.select_corporate(sku_map, issuing_item, corporate_map)
            if select_corporate_result.is_err():
                self.context.abort_error = select_corporate_result.unwrap_err()
                return
            corporate = select_corporate_result.unwrap()
            corporate_issuing_items_map[corporate.name].append(issuing_item)
        # 为每个新票进行初始化
        origin_pb_workflow = self.context.pb_workflow
        for corporate_name, issuing_items in corporate_issuing_items_map.items():
            new_workflow = pb_InvoiceWorkflow.EditableView()
            new_workflow.CopyFrom(origin_pb_workflow)
            # 重新初始化开票项目
            new_workflow.ClearField("issuing_items")
            new_workflow.issuing_items.extend(issuing_items)
            # 重新初始化销方信息
            corporate_map[corporate_name].populate_seller_info(new_workflow)
            # 重新分配平台申请开票金额
            new_workflow.platform_apply_info.invoice_amount = self.calculate_split_invoice_amount(new_workflow)
            self.context.split_workflows.append(new_workflow)
        self.adjust_split_workflow_invoice_amount_diff()
        for split_workflow in self.context.split_workflows:
            self.context.adjust_issuing_item_amount_if_needed(split_workflow, allow_expect_gt_actual=True)

    def adjust_split_workflow_invoice_amount_diff(self):
        """检查拆分后的平台申请金额是否一致，差值补到最后一个发票申请中"""
        from robot_processor.utils import Amount

        split_invoice_amount = Amount.sum(
            [
                issuing_item.amount
                for workflow in self.context.split_workflows
                for issuing_item in workflow.issuing_items
            ]
        )
        diff = Amount(self.pb_workflow.platform_apply_info.invoice_amount) - split_invoice_amount
        if diff:
            last_workflow = self.context.split_workflows[-1]
            last_workflow.platform_apply_info.invoice_amount = (
                Amount(last_workflow.platform_apply_info.invoice_amount).add(diff).format(precision=2)
            )

    def select_corporate(self, sku_map: dict, issuing_item, corporate_map: dict):
        from typing import cast

        from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
        from result import Err
        from result import Ok

        from robot_processor.invoice.tax_bureau.models import Corporate
        from rpa.erp.jst import SkuQueryResp

        corporate_map = cast(dict[str, Corporate], corporate_map)
        issuing_item = cast(pb_InvoiceWorkflow.IssuingItem, issuing_item)
        if issuing_item.goods_item.sku_outer.value not in sku_map:
            return None
        sku_data: SkuQueryResp.SkuInfo = sku_map[issuing_item.goods_item.sku_outer.value]
        match sku_data.other_3, sku_data.other_4:
            case str(), None:
                corporate_name = sku_data.other_3  # type: ignore[unreachable]
            case None, str():
                corporate_name = sku_data.other_4  # type: ignore[unreachable]
            case str(), str():
                if self.context.provide_shop().sid == "*********":
                    corporate_name = sku_data.other_3
                elif self.context.provide_shop().sid == "*********":
                    corporate_name = sku_data.other_4
                else:
                    return Err("其他属性3和其他属性4都存在，无法区分")
            case _:
                return Err("没有其他属性，无法定位开票主体")
        if corporate_name not in corporate_map:
            return Err(f"找不到主体 {corporate_name}")
        return Ok(corporate_map[corporate_name])

    def calculate_split_invoice_amount(self, split_workflow):
        """根据拆分后的开票项目，进行平台申请开票金额的拆分"""
        from typing import cast

        from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow

        from robot_processor.utils import Amount

        split_workflow = cast(pb_InvoiceWorkflow.EditableView, split_workflow)
        ratio = Amount.sum([item.amount for item in split_workflow.issuing_items]).divide(
            Amount.sum([item.amount for item in self.context.pb_workflow.issuing_items])
        )
        return Amount(self.context.pb_workflow.platform_apply_info.invoice_amount).multipy(ratio).format(precision=2)
