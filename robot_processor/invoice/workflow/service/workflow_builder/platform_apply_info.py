from ._abc import Context
from ._abc import DelegateModule
from ._abc import IModule


class PlatformApplyInfoModule(DelegateModule):
    def execute(self):
        match self.context:
            case Context.QNPlatformApply():
                self._delegate(QNPlatformApply)
            case Context.DoudianPlatformApply():
                self._delegate(DoudianPlatformApply)
            case Context.JDPlatformApply():
                self._delegate(JDPlatformApply)
            case Context.PddPlatformApply():
                self._delegate(PddPlatformApply)
            case _:
                self._delegate(Default)

    def validate(self):
        from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow

        from robot_processor.utils import Amount

        if self.pb_workflow.source == pb_InvoiceWorkflow.PLATFORM_APPLY:
            if not self.pb_workflow.platform_apply_info.invoice_amount:
                self.context.add_error("平台同步的发票申请缺少申请开票金额")
            else:
                expect = Amount(self.pb_workflow.platform_apply_info.invoice_amount)
                actual = Amount.sum([Amount(item.amount) for item in self.pb_workflow.issuing_items])
                if expect != actual:
                    error = (
                        "发票应开金额和明细金额不一致。"
                        f"应开金额：{expect.format(precision=2)}，明细金额：{actual.format(precision=2)}"
                    )
                    self.context.add_error(error)


class Default(IModule):
    def process(self):
        pass


class QNPlatformApply(IModule[Context.QNPlatformApply]):
    def process(self):
        from robot_processor.invoice.common.models import PlatformApplySyncMethod

        qn_invoice = self.context.qn_invoice
        self.pb_workflow.platform_apply_info.sid = self.context.shop.sid
        self.pb_workflow.platform_apply_info.platform = self.context.shop.platform
        self.pb_workflow.platform_apply_info.sync_method = PlatformApplySyncMethod.QN.pb_value
        self.pb_workflow.platform_apply_info.tid = qn_invoice.tid
        self.pb_workflow.platform_apply_info.platform_apply_id = qn_invoice.serialNo
        self.pb_workflow.platform_apply_info.apply_time = qn_invoice.startTime
        self.pb_workflow.platform_apply_info.invoice_amount = qn_invoice.amount
        self.pb_workflow.platform_apply_info.raw.update(qn_invoice.dict(exclude_none=True))
        self.context.adjust_issuing_item_amount_if_needed()
        self.context.adjust_issuing_item_price_if_needed()


class DoudianPlatformApply(IModule[Context.DoudianPlatformApply]):
    def process(self):
        import arrow

        from robot_processor.invoice.common.models import PlatformApplySyncMethod
        from robot_processor.utils import Amount

        doudian_invoice = self.context.doudian_invoice
        self.pb_workflow.platform_apply_info.sid = self.context.shop.sid
        self.pb_workflow.platform_apply_info.platform = self.context.shop.platform
        self.pb_workflow.platform_apply_info.sync_method = PlatformApplySyncMethod.DOUDIAN.pb_value
        self.pb_workflow.platform_apply_info.tid = doudian_invoice.shop_order_id
        self.pb_workflow.platform_apply_info.platform_apply_id = doudian_invoice.registation_id
        self.pb_workflow.platform_apply_info.apply_time = arrow.get(doudian_invoice.apply_time).format(
            "YYYY-MM-DD HH:mm:ss"
        )
        invoice_amount = Amount(doudian_invoice.invoice_amount).divide(100)
        self.pb_workflow.platform_apply_info.invoice_amount = invoice_amount.format(precision=2)
        self.pb_workflow.platform_apply_info.raw.update(doudian_invoice.dict(exclude_none=True))
        self.context.adjust_issuing_item_amount_if_needed()
        self.context.adjust_issuing_item_price_if_needed()


class JDPlatformApply(IModule[Context.JDPlatformApply]):
    def process(self):
        from robot_processor.invoice.common.models import PlatformApplySyncMethod

        jd_invoice = self.context.jd_invoice
        self.pb_workflow.platform_apply_info.sid = self.context.shop.sid
        self.pb_workflow.platform_apply_info.platform = self.context.shop.platform
        self.pb_workflow.platform_apply_info.sync_method = PlatformApplySyncMethod.JD.pb_value
        self.pb_workflow.platform_apply_info.tid = str(jd_invoice.orderId)
        self.pb_workflow.platform_apply_info.platform_apply_id = str(jd_invoice.applyId)
        self.pb_workflow.platform_apply_info.apply_time = jd_invoice.applyTime
        self.pb_workflow.platform_apply_info.invoice_amount = str(jd_invoice.shouldInvoiceAmount)
        self.pb_workflow.platform_apply_info.raw.update(jd_invoice.dict(exclude_none=True))
        self.context.adjust_issuing_item_amount_if_needed()
        self.context.adjust_issuing_item_price_if_needed()


class PddPlatformApply(IModule[Context.PddPlatformApply]):
    def process(self):
        import arrow

        from robot_processor.invoice.common.models import PlatformApplySyncMethod
        from robot_processor.utils import Amount

        pdd_invoice = self.context.pdd_invoice
        self.pb_workflow.platform_apply_info.sid = self.context.shop.sid
        self.pb_workflow.platform_apply_info.platform = self.context.shop.platform
        self.pb_workflow.platform_apply_info.sync_method = PlatformApplySyncMethod.PDD.pb_value
        self.pb_workflow.platform_apply_info.tid = pdd_invoice.order_sn
        self.pb_workflow.platform_apply_info.platform_apply_id = pdd_invoice.order_sn
        self.pb_workflow.platform_apply_info.apply_time = arrow.get(pdd_invoice.apply_time).format(
            "YYYY-MM-DD HH:mm:ss"
        )
        self.pb_workflow.platform_apply_info.invoice_amount = Amount(pdd_invoice.invoice_amount).divide(100).format(2)
        self.pb_workflow.platform_apply_info.raw.update(pdd_invoice.to_dict())
        self.context.adjust_issuing_item_amount_if_needed()
        self.context.adjust_issuing_item_price_if_needed()
