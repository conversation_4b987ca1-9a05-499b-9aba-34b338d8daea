from ._abc import Context
from ._abc import DelegateModule
from ._abc import IModule


class SellerModule(DelegateModule):
    def execute(self):
        match self.context:
            case Context.Manual():
                self._delegate(Manual)
            case Context.Trade():
                self._delegate(Trade)
            case Context.PlatformApply():
                self._delegate(PlatformApply)
            case _:
                self._delegate(Default)

    def validate(self):
        if not self.pb_workflow.seller_name:
            self.context.add_error("销方名未填写")
        if not self.pb_workflow.seller_credit_id:
            self.context.add_error("销方纳税人识别号未填写")


class Default(IModule):
    def process(self):
        raise NotImplementedError("不支持的路径")


class Manual(IModule[Context.Manual]):
    def process(self):
        pass


class Trade(IModule[Context.Trade]):
    def process(self):
        from robot_types.model.invoice.config_manager import ShowSellerAddress
        from robot_types.model.invoice.config_manager import ShowSellerBank

        from robot_processor.invoice.config_manager import ConfigKey

        if self.context.provide_if_platform_trade_builder_mode():
            shop = self.detect_shop_by_platform()
        else:
            shop = self.detect_shop_by_erp()

        if shop is None:
            self.context.add_error("未找到订单匹配店铺")
            return
        self.context.shop = shop
        corporate = self.context.provide_corporate()
        if corporate is None:
            self.context.add_error(f"店铺 {shop.title}@{shop.platform} 未绑定企业信息")
        else:
            corporate.populate_seller_info(self.pb_workflow)

        self.context.pb_workflow.show_seller_address_phone = {
            ShowSellerAddress.SHOW: True,
            ShowSellerAddress.HIDE: False,
        }[self.context.config_manager.get(ConfigKey.SHOW_SELLER_ADDRESS)]
        self.context.pb_workflow.show_seller_bank = {
            ShowSellerBank.SHOW: True,
            ShowSellerBank.HIDE: False,
        }[self.context.config_manager.get(ConfigKey.SHOW_SELLER_BANK)]

    def detect_shop_by_platform(self):
        from robot_processor.client import trade_client
        from robot_processor.shop.models import Shop

        res = trade_client.get_channel_info_by_tid_list([self.context.provide_tid()], str(self.context.org_id))
        if not res:
            self.context.add_error("未找到订单信息")
            return None
        channel_info = res[0]
        if not channel_info.channel_no and channel_info.seller_nick:
            return Shop.query.filter(
                Shop.Filters.platform_taobao,
                Shop.nick == channel_info.seller_nick,
                Shop.org_id == str(self.context.org_id),
            ).first()
        else:
            return Shop.Queries.optimal_shop_by_sid(channel_info.channel_no, org_id=self.context.org_id)

    def detect_shop_by_erp(self):
        from robot_processor.enums import ErpType
        from robot_processor.shop.auth_manager import get_shop_by_erp_platform_sid

        erp_info = self.context.provide_erp_info()
        match erp_info.erp_type:
            case ErpType.JST:
                jst_order = self.context.provide_jst_order()
                erp_platform_sid = jst_order.shop_id
            case ErpType.WDT:
                erp_platform_sid = int(self.context.provide_wdt_order().shop_id)
            case ErpType.WDTULTI:
                erp_platform_sid = int(self.context.provide_wdtulti_order().shop_id)
            case _:
                self.context.add_error(f"不支持的 ERP 类型 {erp_info.erp_type}")
                return None
        result = get_shop_by_erp_platform_sid(self.context.org_id, erp_info.erp_type.name, erp_platform_sid)
        if result.is_err():
            self.context.add_error(str(result.unwrap_err()))
            return None
        else:
            return result.unwrap()


class PlatformApply(IModule[Context.PlatformApply]):
    def process(self):
        from robot_types.model.invoice.config_manager import ShowSellerAddress
        from robot_types.model.invoice.config_manager import ShowSellerBank

        from robot_processor.invoice.config_manager import ConfigKey

        if (corporate := self.context.corporate) is None:
            shop = self.context.shop
            self.context.add_error(f"店铺 {shop.title}@{shop.platform} 未绑定企业信息")
        else:
            corporate.populate_seller_info(self.pb_workflow)

        self.context.pb_workflow.show_seller_address_phone = {
            ShowSellerAddress.SHOW: True,
            ShowSellerAddress.HIDE: False,
        }[self.context.config_manager.get(ConfigKey.SHOW_SELLER_ADDRESS)]
        self.context.pb_workflow.show_seller_bank = {
            ShowSellerBank.SHOW: True,
            ShowSellerBank.HIDE: False,
        }[self.context.config_manager.get(ConfigKey.SHOW_SELLER_BANK)]
