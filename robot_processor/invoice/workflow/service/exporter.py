import os
import shutil
import tempfile
from dataclasses import dataclass
from dataclasses import field
from decimal import Decimal
from functools import cached_property
from string import Template

import pandas as pd
from google.protobuf.json_format import ParseDict
from leyan_proto.digismart.robot.symbol_table_pb2 import Filter as pb_Filter
from loguru import logger

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.client import rpa_control_oss
from robot_processor.invoice.workflow.models import InvoiceType
from robot_processor.invoice.workflow.models import InvoiceWorkflow


@dataclass
class ExportConfig:
    @dataclass
    class Title:
        name: str

    titles: list[Title]
    filename: str = "开票列表@{datetime}.xlsx"


@dataclass
class InvoiceWorkflowExporter:
    org_id: int
    user: AccountDetailV2
    export_config: ExportConfig
    filters: dict
    context: dict = field(default_factory=dict)

    data: list[InvoiceWorkflow] = field(init=False)
    dataframe: pd.DataFrame = field(init=False)

    def prepare_data(self):
        self.data = InvoiceWorkflow.get_by_filter(self.org_id, ParseDict(self.filters, pb_Filter()))

    def prepare_dataframe(self):
        columns: list[str] = list()
        dtype: dict[str, type] = dict()
        for title in self.export_config.titles:
            columns.append(title.name)
            dtype[title.name] = str
        dataframe = pd.DataFrame(columns=columns)
        self.dataframe = dataframe.astype(dtype)

    def process_workflow(self, workflow: InvoiceWorkflow, temp_dir):
        export_wrapper = ExportWrapper(workflow)
        data = export_wrapper.render(self.dataframe.columns.tolist())
        self.dataframe.loc[len(self.dataframe)] = data
        if pdf_receipt := export_wrapper.pdf_receipt:
            with open(os.path.join(temp_dir, f"{export_wrapper.invoice_number}.pdf"), "wb") as f:
                shutil.copyfileobj(pdf_receipt, f)

    def process(self):
        self.prepare_data()
        self.prepare_dataframe()
        with tempfile.TemporaryDirectory() as temp_dir:
            for workflow in self.data:
                self.process_workflow(workflow, temp_dir)
            excel_file_name = Template(self.export_config.filename).safe_substitute(self.context)
            if not excel_file_name.endswith("xlsx"):
                excel_file_name += ".xlsx"
            with pd.ExcelWriter(os.path.join(temp_dir, excel_file_name), engine="openpyxl") as writer:  # type: ignore
                mapping = {title.name: title.label for title in self.get_export_titles()}
                self.dataframe.rename(columns=mapping).to_excel(writer, index=False, header=True)
            zip_file = self.zip_folder(temp_dir)
        return zip_file

    @classmethod
    def zip_folder(cls, folder):
        import zipfile
        from io import BytesIO

        zip_buffer = BytesIO()
        with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, folder)
                    zipf.write(file_path, arcname)
        zip_buffer.seek(0)
        return zip_buffer

    @classmethod
    def get_export_titles(cls):
        from robot_types.helper.symbol import SymbolResolver
        from robot_types.model import invoice

        symbol_resolver = SymbolResolver()
        titles = [
            symbol_resolver.resolve(name, typespec)
            for name, typespec in invoice.export.type_spec.Titles.properties.items()
        ]
        return titles


class ExportWrapper:
    def __init__(self, workflow: InvoiceWorkflow):
        self.workflow = workflow

    def render(self, columns: list[str]):
        return {col: getattr(self, col, "") for col in columns}

    @cached_property
    def pdf_receipt(self):
        if (
            self.workflow.invoice_number
            and self.workflow.invoice_issued
            and self.workflow.invoice_issued.receipt
            and "pdf" in self.workflow.invoice_issued.receipt
        ):
            try:
                return rpa_control_oss.bucket.get_object(self.workflow.invoice_issued.receipt["pdf"])
            except Exception as e:
                logger.opt(exception=e).error(f"导出 pdf 失败 {e}")
                return None

    @cached_property
    def shop_title(self):
        from robot_processor.shop.models import Shop

        if not self.workflow.sid:
            return None
        shop = Shop.query.filter_by(org_id=str(self.workflow.org_id), sid=self.workflow.sid).first()
        return shop.title if shop else None

    @property
    def tid(self):
        return self.workflow.tid

    @property
    def state(self) -> str | None:
        return self.workflow.state.label if self.workflow.state is not None else None

    @property
    def platform_apply_post_back_state(self) -> str | None:
        if self.workflow.platform_apply_post_back_state is not None:
            return self.workflow.platform_apply_post_back_state.label
        else:
            return None

    @property
    def invoice_type(self):
        match self.workflow.invoice_type:
            case InvoiceType.VAT_GENERAL:
                return "数电票（普通发票）"
            case InvoiceType.VAT_SPECIAL:
                return "数电票（专用发票）"
            case _:
                return None

    @property
    def issuing_type(self):
        return self.workflow.issuing_type.label

    @property
    def issuing_amount(self):
        return self.workflow.issuing_items_total_amount.format(precision=2)

    @property
    def invoice_number(self):
        return self.workflow.invoice_number

    @property
    def issuing_time(self):
        if self.workflow.invoice_issued:
            return self.workflow.invoice_issued.strftime("%Y-%m-%d")
        else:
            return None

    @property
    def buyer_info(self):
        return "\n".join(
            filter(
                lambda x: x is not None,  # type: ignore[arg-type]
                [self.workflow.buyer_name, self.workflow.buyer_credit_id],
            )
        )

    @property
    def buyer_name(self):
        return self.workflow.buyer_name

    @property
    def buyer_credit_id(self):
        return self.workflow.buyer_credit_id

    @property
    def seller_info(self):
        return "\n".join([self.workflow.seller_name, self.workflow.seller_credit_id])

    @property
    def seller_name(self):
        return self.workflow.seller_name

    @property
    def seller_credit_id(self):
        return self.workflow.seller_credit_id

    @property
    def main_issuing_item_title(self):
        issuing_items = self.workflow.get_issuing_items()
        if issuing_items:
            return issuing_items[0].title
        else:
            return None

    @property
    def total_amount_without_tax(self):
        issuing_items = self.workflow.get_issuing_items()
        if self.workflow.is_tax_included:
            total = Decimal("0.00")
            for issuing_item in issuing_items:
                if not all([issuing_item.amount, issuing_item.tax_rate]):
                    continue
                total += (Decimal(issuing_item.amount) / (Decimal("1") + Decimal(issuing_item.tax_rate))).quantize(
                    Decimal(".01")
                )
            return str(total)
        else:
            return str(sum(Decimal(issuing_item.amount or "0") for issuing_item in issuing_items))

    @property
    def total_tax_amount(self):
        issuing_items = self.workflow.get_issuing_items()
        if self.workflow.is_tax_included:
            total = Decimal("0.00")
            for issuing_item in issuing_items:
                if not all([issuing_item.amount, issuing_item.tax_rate]):
                    continue
                total += (
                    Decimal(issuing_item.amount)
                    / (Decimal("1") + Decimal(issuing_item.tax_rate))
                    * Decimal(issuing_item.tax_rate)
                ).quantize(Decimal(".01"))
            return str(total)
        else:
            total = Decimal("0.00")
            for issuing_item in issuing_items:
                if not all([issuing_item.amount, issuing_item.tax_rate]):
                    continue
                total += (Decimal(issuing_item.amount) * Decimal(issuing_item.tax_rate)).quantize(Decimal(".01"))
            return str(total)

    @property
    def source(self) -> str:
        return self.workflow.source.label

    @property
    def platform_apply_time(self):
        if self.workflow.platform_apply_time:
            return self.workflow.platform_apply_time.strftime("%Y-%m-%d")
        else:
            return None

    @property
    def updated_at(self):
        return self.workflow.updated_at.strftime("%Y-%m-%d %H:%M:%S")
