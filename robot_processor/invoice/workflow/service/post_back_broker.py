from dataclasses import dataclass
from dataclasses import field

from result import Err
from result import Ok

from robot_processor.invoice.common.models import PlatformApplySyncMethod
from robot_processor.invoice.workflow.models import InvoiceWorkflow
from robot_processor.invoice.workflow.models import IssuingType
from robot_processor.invoice.workflow.third_party import jd


@dataclass
class PostBackBroker:
    invoice_workflow: InvoiceWorkflow

    need_platform_apply_post_back: bool = field(init=False)
    platform_apply_sync_method: PlatformApplySyncMethod | None = field(init=False)

    def __post_init__(self):
        from robot_processor.invoice.tax_bureau.models import CorporateShop

        if self.invoice_workflow.state != InvoiceWorkflow.State.INVOICED:
            raise PermissionError("当前状态不支持回传")
        if self.invoice_workflow.source == InvoiceWorkflow.Source.PLATFORM_APPLY:
            corporate_shop = CorporateShop.get_by_shop(self.invoice_workflow.sid, self.invoice_workflow.platform)
            self.need_platform_apply_post_back = corporate_shop.post_back_platform_apply
            self.platform_apply_sync_method = corporate_shop.platform_apply_sync_method
        else:
            self.need_platform_apply_post_back = False
            self.platform_apply_sync_method = None

    def check_split(self):
        same_platform_apply_workflows = InvoiceWorkflow.query.filter(
            InvoiceWorkflow.platform_apply_id == self.invoice_workflow.platform_apply_id,
            InvoiceWorkflow.state != InvoiceWorkflow.State.CLOSED,
        ).all()
        if len(same_platform_apply_workflows) > 1:
            return Err(ValueError("拆分发票不支持回传"))
        return Ok(None)

    def do_post_back(self):
        from robot_processor.invoice.workflow.third_party import doudian
        from robot_processor.invoice.workflow.third_party import pdd
        from robot_processor.invoice.workflow.third_party import qianniu
        from robot_processor.invoice.workflow.third_party import wdt

        if self.invoice_workflow.issuing_type is IssuingType.RED:
            return
        if self.invoice_workflow.platform_apply_post_back_state == InvoiceWorkflow.PostBackState.POSTED:
            return
        if self.platform_apply_sync_method is None:
            return
        if (check_split_result := self.check_split()).is_err():
            self.invoice_workflow.set_platform_apply_post_back_state(False, None, str(check_split_result.unwrap_err()))
            return

        self.invoice_workflow.set_platform_apply_post_back_posting()
        match self.platform_apply_sync_method:
            case PlatformApplySyncMethod.JD:
                match jd.post_back(self.invoice_workflow):
                    case Ok(True):
                        self.invoice_workflow.set_platform_apply_post_back_state(True, None)
                    case Ok(False):
                        self.invoice_workflow.set_platform_apply_post_back_state(False, None, "失败")
                    case Err(error):
                        self.invoice_workflow.set_platform_apply_post_back_state(False, None, str(error))
            case PlatformApplySyncMethod.QN:
                match qianniu.post_back(self.invoice_workflow):
                    case Ok({"code": 200} as raw):
                        self.invoice_workflow.set_platform_apply_post_back_state(True, raw)
                    case Ok({"message": error_message} as raw):
                        self.invoice_workflow.set_platform_apply_post_back_state(False, raw, error_message)
                    case Err(error):
                        self.invoice_workflow.set_platform_apply_post_back_state(False, None, str(error))
            case PlatformApplySyncMethod.WDT:
                match wdt.post_back(self.invoice_workflow):
                    case Ok({"code": 200} as raw):
                        self.invoice_workflow.set_platform_apply_post_back_state(True, raw)
                    case Ok({"message": error_message} as raw):
                        self.invoice_workflow.set_platform_apply_post_back_state(False, raw, error_message)
                    case Err(error):
                        self.invoice_workflow.set_platform_apply_post_back_state(False, None, str(error))
            case PlatformApplySyncMethod.DOUDIAN:
                match doudian.post_back(self.invoice_workflow).map(lambda r: r.dict()):
                    case Ok({"msg": "success"} as raw):
                        self.invoice_workflow.set_platform_apply_post_back_state(True, raw)
                    case Ok({"msg": msg} as raw):
                        self.invoice_workflow.set_platform_apply_post_back_state(False, raw, msg)
                    case Err(error):
                        self.invoice_workflow.set_platform_apply_post_back_state(False, None, str(error))
            case PlatformApplySyncMethod.PDD:
                match pdd.post_back(self.invoice_workflow).map(lambda r: r.to_dict()):
                    case Ok(dict() as raw):
                        self.invoice_workflow.set_platform_apply_post_back_state(True, raw)
                    case Err(error):
                        self.invoice_workflow.set_platform_apply_post_back_state(False, None, str(error))
