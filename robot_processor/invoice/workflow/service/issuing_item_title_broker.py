from typing import TYPE_CHECKING
from typing import Optional

from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
from robot_types.helper.predefined import BizType

from robot_processor.db import no_auto_flush
from robot_processor.function.conversion.text_template_render import TextTemplateRender
from robot_processor.utils import raise_exception

if TYPE_CHECKING:
    from robot_processor.invoice.goods.models import GoodsItem
    from robot_processor.invoice.goods.models import IssuingItem
    from robot_processor.invoice.goods.services import IssuingItemService


class IssuingItemTitleBroker:
    biz_type = BizType.INVOICE_ISSUING_ITEM_TITLE_TEXT_TEMPLATE

    @classmethod
    @no_auto_flush()
    def render_title_template(cls, issuing_item: "IssuingItem", goods_item: Optional["GoodsItem"]):
        from robot_processor.invoice.goods.models import GoodsItem

        if issuing_item.title_template:
            context = {
                "title": goods_item.title if goods_item else "",
                "description": goods_item.description if goods_item else "",
                "spec_value": "",
            }
            if goods_item and goods_item.datasource == GoodsItem.Datasource.DOUDIAN:
                ...
            text_template_render = TextTemplateRender(
                context=context,
                template=issuing_item.title_template,
                mode=cls.biz_type,
            )
            return text_template_render.call().unwrap_or_else(raise_exception)
        else:
            return issuing_item.title

    @staticmethod
    def batch_render_pb_issuing_items(
        issuing_item_service: "IssuingItemService", pb_issuing_items: list[pb_InvoiceWorkflow.IssuingItem]
    ):
        from robot_processor.invoice.goods.models import GoodsItem

        issuing_item_id_list = list({item.issuing_item_id for item in pb_issuing_items if item.issuing_item_id})
        issuing_item_instance_list = issuing_item_service.batch_get_issuing_item_by_ids(issuing_item_id_list)
        issuing_item_instance_map = {issuing_item.id: issuing_item for issuing_item in issuing_item_instance_list}
        for issuing_item in pb_issuing_items:
            if issuing_item.issuing_item_id not in issuing_item_instance_map:
                continue
            issuing_item_instance = issuing_item_instance_map[issuing_item.issuing_item_id]
            if issuing_item.HasField("goods_item"):
                goods_item = GoodsItem.from_pb(issuing_item.goods_item)
            else:
                goods_item = None
            issuing_item.title = IssuingItemTitleBroker.render_title_template(issuing_item_instance, goods_item)
