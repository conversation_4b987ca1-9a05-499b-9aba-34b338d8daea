import arrow
from google.protobuf.json_format import ParseDict
from leyan_proto.digismart.robot_web.common_pb2 import EmptyResponse
from leyan_proto.digismart.robot_web.invoice import workflow_pb2_grpc
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import BatchBuildInvoiceWorkflowFromFileResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import BatchBuildInvoiceWorkflowFromTradeResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import BuildInvoiceWorkflowFromTradeResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import CheckCorporateInvoiceThresholdResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import ExportInvoiceWorkflowResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import GetInvoiceWorkflowExportTaskResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import GetInvoiceWorkflowFilterContextResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import GetInvoiceWorkflowQuery
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import GetInvoiceWorkflowResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import InvoiceApprovalTemplateResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import IssueInvoiceWorkflowBody
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import ListBatchBuildFromFileRecordResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import ListBatchBuildFromFileVersionResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import ListInvoiceWorkflowExportTaskResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import ListInvoiceWorkflowExportTitlesResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import ListInvoiceWorkflowResponse
from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import ProcessInvoiceWorkflowBody
from loguru import logger
from result import Err
from result import Ok
from robot_types.model.invoice.config_manager import NotificationChannelType

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.db import in_transaction
from robot_processor.invoice.utils import catch_error_as_response
from robot_processor.invoice.utils import load_from_request
from robot_processor.invoice.utils import org_required
from robot_processor.invoice.utils import set_default_paginate
from robot_processor.invoice.workflow.models import ApprovalState
from robot_processor.invoice.workflow.models import InvoiceWorkflow
from robot_processor.invoice.workflow.models import InvoiceWorkflowExportTask
from robot_processor.invoice.workflow.models import InvoiceWorkflowTransition
from robot_processor.invoice.workflow.models import IssuingType
from robot_processor.invoice.workflow.third_party import jd
from robot_processor.utils import message_to_dict
from robot_processor.utils import raise_exception
from robot_processor.utils import struct_wrapper
from robot_processor.utils import unwrap_optional


class InvoiceWorkflowServicer(workflow_pb2_grpc.InvoiceWorkflowServicer):
    @catch_error_as_response(ListInvoiceWorkflowResponse)
    @org_required
    def ListInvoiceWorkflow(self, request, context):
        """获取发票申请列表

        Path:
            POST /v1/invoice/workflow/list
        """
        org_id, user_info = load_from_request()
        set_default_paginate(request.config)
        response = ListInvoiceWorkflowResponse(succeed=True)
        workflows = InvoiceWorkflow.get_by_filter_and_paginate(org_id, request.filter, request.config)
        response.data.paginate.MergeFrom(request.config)
        response.data.workflows.extend([workflow.to_detailed_view(user_info) for workflow in workflows])
        return response

    @catch_error_as_response(GetInvoiceWorkflowFilterContextResponse)
    @org_required
    def GetInvoiceWorkflowFilterContext(self, request, context):
        """获取发票申请列表的筛选项

        Path:
            GET /v1/invoice/workflow/filter-context
        """
        org_id = load_from_request().org_id
        namespace = InvoiceWorkflow.FilterContext.to_namespace(dict(org_id=org_id))
        response = GetInvoiceWorkflowFilterContextResponse(succeed=True, data=namespace.to_pb())

        return response

    @catch_error_as_response(GetInvoiceWorkflowResponse)
    @org_required
    def GetInvoiceWorkflow(self, request, context):
        """获取发票申请详情

        Path:
            GET /v1/invoice/workflow/info
        """
        org_id, user_info = load_from_request()
        response = GetInvoiceWorkflowResponse(succeed=True)
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).expect("获取发票申请流程失败")
        response.data.workflow.CopyFrom(workflow.to_detailed_view(user_info))
        return response

    @catch_error_as_response(GetInvoiceWorkflowResponse)
    @org_required
    def CreateInvoiceWorkflow(self, request, context):
        """创建发票申请

        Path:
            POST /v1/invoice/workflow/create
        """
        from robot_processor.invoice.workflow.service import WorkflowBuilder
        from robot_processor.invoice.workflow.service import WorkflowContext

        org_id, user_info = load_from_request()
        response = GetInvoiceWorkflowResponse(succeed=True)
        context = WorkflowContext.Manual(org_id=org_id, pb_workflow=request.workflow)
        workflow = (
            WorkflowBuilder.create_workflow_with_context(
                context,
                with_submit=request.action.value != "SAVE_DRAFT",
            )
            .as_result()
            .expect("CreateInvoiceWorkflow")
        )
        response.data.workflow.CopyFrom(workflow.to_detailed_view())
        return response

    @catch_error_as_response(BuildInvoiceWorkflowFromTradeResponse)
    @org_required
    def BuildInvoiceWorkflowFromTrade(self, request, context):
        """通过订单号来构建一个发票申请的信息

        Path:
            GET /v1/invoice/workflow/build-from-trade
        """
        from robot_processor.invoice.workflow.service import WorkflowBuilder
        from robot_processor.invoice.workflow.service import WorkflowContext

        response = BuildInvoiceWorkflowFromTradeResponse(succeed=True)
        org_id = load_from_request().org_id
        workflow_context = WorkflowContext.Trade(org_id=org_id, tid=request.tid)
        WorkflowBuilder.build(workflow_context)
        response.data.workflow.MergeFrom(workflow_context.pb_workflow)
        return response

    @catch_error_as_response(BatchBuildInvoiceWorkflowFromTradeResponse)
    @org_required
    def BatchBuildInvoiceWorkflowFromTrade(self, request, context):
        """通过订单号批量创建发票申请，并保存到草稿箱

        Path:
            GET /v1/invoice/workflow/batch-build-from-trade
        """
        from robot_processor.invoice.workflow.service import WorkflowBuilder
        from robot_processor.invoice.workflow.service import WorkflowContext

        org_id, user_info = load_from_request()
        response = BatchBuildInvoiceWorkflowFromTradeResponse(succeed=True)
        for tid in request.tid_list:
            workflow_context = WorkflowContext.Trade(org_id=org_id, tid=tid)
            create_result = WorkflowBuilder.create_workflow_with_context(context=workflow_context)
            if create_result.is_ok():
                response.data.results.append(response.Result(tid=tid, invoice_workflow_id=create_result.workflow.id))
            else:
                response.data.results.append(response.Result(tid=tid, error=str(create_result.unwrap_err())))

    def ListBatchBuildFromFileVersion(self, request, context):
        from external.oss import config
        from robot_processor.invoice.workflow.models import InvoiceBatchRecord

        return ListBatchBuildFromFileVersionResponse(
            succeed=True,
            data=[
                ListBatchBuildFromFileVersionResponse.VersionInfo(
                    version=InvoiceBatchRecord.Version.BY_ISSUING_ITEM.pb_value,
                    template_file_path=config.oss_base_url() + "invoice/file-builder/项目导入模版-发票.xlsx",
                    label="项目导入",
                    description=(
                        "项目导入，即批量导入需开票的项目信息。若飞梭后台已绑定项目及税率，批量导入后会自动同步项目对应的税率值进行开票；"
                        "若未绑定，则导入时需手动填写税收分类编码、税率进行开票。"
                    ),
                ),
                # FIXME 暂未支持
                # ListBatchBuildFromFileVersionResponse.VersionInfo(
                #     version=InvoiceBatchRecord.Version.BY_TRADE.pb_value,
                #     template_file_path=config.oss_base_url() + "invoice/file-builder/订单导入模版-发票.xlsx",
                #     label="订单导入",
                #     description=(
                #         "批量导入需开票的订单号，希望自动同步订单商品信息或输入订单号指定商品进行开票的模版。"
                #         "批量导入后根据订单自动同步商品信息，同时自动获取飞梭后台已绑定商品对应税率；"
                #         "若未绑定飞梭后台商品税率信息也支持批量导入时填写税收分类编码与税率。"
                #     )
                # )
            ],
        )

    @catch_error_as_response(BatchBuildInvoiceWorkflowFromFileResponse)
    @org_required
    def BatchBuildInvoiceWorkflowFromFile(self, request, context):
        from robot_processor.invoice.workflow.models import InvoiceBatchRecord

        from .file_builder import InvoiceWorkflowByIssuingItemFileBuilder

        org_id, user_info = load_from_request()
        response = BatchBuildInvoiceWorkflowFromFileResponse(succeed=True)
        match request.version:
            case InvoiceBatchRecord.Version.BY_ISSUING_ITEM:
                builder = InvoiceWorkflowByIssuingItemFileBuilder.init_batch(
                    org_id, request.filename, request.key_in_oss, user_info.user
                )
                if builder.batch.state == InvoiceBatchRecord.State.PENDING:
                    builder.build()
                response.data.MergeFrom(builder.batch.to_pb())

        return response

    @catch_error_as_response(ListBatchBuildFromFileRecordResponse)
    @org_required
    def ListBatchBuildFromFileRecord(self, request, context):
        import sqlalchemy as sa

        from robot_processor.ext import db
        from robot_processor.invoice.workflow.models import InvoiceBatchRecord
        from robot_processor.utils import unwrap_optional

        org_id = load_from_request().org_id
        response = ListBatchBuildFromFileRecordResponse(succeed=True)
        set_default_paginate(request.config)
        response.data.paginate.CopyFrom(request.config)
        stmt = sa.select(InvoiceBatchRecord).where(InvoiceBatchRecord.org_id == org_id)
        count_stmt = stmt.with_only_columns(sa.func.count())
        response.data.paginate.total.value = unwrap_optional(db.session.execute(count_stmt).scalar())
        if response.data.paginate.total.value == 0:
            return response
        stmt = (
            stmt.order_by(InvoiceBatchRecord.id.desc())
            .limit(request.config.per_page.value)
            .offset((request.config.page.value - 1) * request.config.per_page.value)
        )
        records = db.session.execute(stmt).scalars().all()
        response.data.items.extend([record.to_pb() for record in records])
        return response

    @catch_error_as_response(EmptyResponse)
    @org_required
    def SyncPlatformApply(self, request, context):
        """从平台同步发票申请

        Path:
            POST /v1/invoice/workflow/sync-platform-apply
        """
        from robot_processor.invoice.common.models import PlatformApplySyncMethod
        from robot_processor.invoice.tax_bureau.models import CorporateShop
        from robot_processor.invoice.workflow.third_party import doudian
        from robot_processor.invoice.workflow.third_party import pdd
        from robot_processor.invoice.workflow.third_party import qianniu
        from robot_processor.invoice.workflow.third_party import wdt
        from robot_processor.t_cron import scheduler

        org_id = load_from_request().org_id
        if request.shops:
            corporate_shops = [CorporateShop.get_by_shop(shop.sid, shop.platform) for shop in request.shops]
        else:
            corporate_shops = CorporateShop.get_by_org_id(org_id)
        if request.HasField("time_range"):
            timerange = "{}~{}".format(request.time_range.start, request.time_range.end)
        else:
            timerange = ""
        for corporate_shop in corporate_shops:
            if corporate_shop.platform_apply_sync_method is None:
                continue
            match corporate_shop.platform_apply_sync_method:
                case PlatformApplySyncMethod.QN:
                    scheduler.run_now(
                        qianniu.poll_qn_invoice_list,
                        shop_id=corporate_shop.shop.id,
                        timerange=timerange,
                    )
                case PlatformApplySyncMethod.WDT:
                    scheduler.run_now(
                        wdt.poll_wdtulti_invoice_list,
                        shop_id=corporate_shop.shop.id,
                        timerange=timerange,
                    )
                case PlatformApplySyncMethod.DOUDIAN:
                    scheduler.run_now(
                        doudian.poll_doudian_invoice_list,
                        shop_id=corporate_shop.shop.id,
                        timerange=timerange,
                    )
                case PlatformApplySyncMethod.JD:
                    scheduler.run_now(
                        jd.poll_jd_invoice_list,
                        shop_id=corporate_shop.shop.id,
                        timerange=timerange,
                    )
                case PlatformApplySyncMethod.PDD:
                    scheduler.run_now(
                        pdd.poll_pdd_invoice_list,
                        shop_id=corporate_shop.shop.id,
                        timerange=timerange,
                    )
        return EmptyResponse(succeed=True)

    @catch_error_as_response(GetInvoiceWorkflowResponse)
    @org_required
    def RushRedInvoiceWorkflow(self, request, context):
        """从发票申请创建一个红冲

        Path:
            POST /v1/invoice/workflow/rush-red
        """
        org_id, user_info = load_from_request()
        response = GetInvoiceWorkflowResponse(succeed=True)
        original_workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).expect("获取发票申请流程失败")
        red_workflow_editable = original_workflow.to_rush_red_editable()
        workflow = InvoiceWorkflow.create(org_id, user_info, red_workflow_editable)
        workflow.rush_red_reason = request.reason
        response.data.workflow.CopyFrom(workflow.to_detailed_view())
        return response

    @catch_error_as_response(EmptyResponse)
    @org_required
    def SubmitInvoiceWorkflow(self, request, context):
        """更新并提交发票申请

        Path:
            POST /v1/invoice/workflow/submit
        """
        org_id, user_info = load_from_request()
        workflow_id = request.workflow.id.value
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, workflow_id).unwrap_or_else(raise_exception)
        self.check_permission(workflow, InvoiceWorkflow.Action.SUBMIT)
        workflow.submit(request.workflow, user_info=user_info)
        workflow.get_approval_broker().try_auto_approve()
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def BatchSubmitInvoiceWorkflow(self, request, context):
        """批量提交发票申请
        Path:
            POST /v1/invoice/workflow/batch-submit
        """
        org_id, user_info = load_from_request()
        errors = []
        for workflow_id in request.ids:
            try:
                workflow = InvoiceWorkflow.get_and_check_workflow(org_id, workflow_id).unwrap_or_else(raise_exception)
                self.check_permission(workflow, InvoiceWorkflow.Action.SUBMIT)
                workflow.submit_without_edit(user_info=user_info)
                workflow.get_approval_broker().try_auto_approve()
            except Exception as e:
                errors.append((e, workflow_id))
        if errors:
            return EmptyResponse(
                succeed=False,
                msg="\n".join(
                    ["发票申请 {} 提审失败，原因: {}".format(workflow_id, error) for error, workflow_id in errors]
                ),
            )
        else:
            return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def SaveDraftInvoiceWorkflow(self, request, context):
        """更新发票申请草稿箱数据

        Path:
            POST /v1/invoice/workflow/save-draft
        """
        org_id, user_info = load_from_request()
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.workflow.id.value).expect(
            "获取发票申请流程失败"
        )
        self.check_permission(workflow, InvoiceWorkflow.Action.SAVE_DRAFT)
        workflow.save(request.workflow)
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def RevokeInvoiceWorkflow(self, request, context):
        """撤回发票申请

        Path:
            POST /v1/invoice/workflow/revoke
        """
        org_id, user_info = load_from_request()
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).unwrap_or_else(raise_exception)
        self.check_permission(workflow, InvoiceWorkflow.Action.REVOKE)
        workflow.revoke(user_info=user_info)
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def BatchRevokeInvoiceWorkflow(self, request, context):
        """批量撤回发票申请

        Path:
            POST /v1/invoice/workflow/batch-revoke
        """
        errors = []
        for workflow_id in request.ids:
            res = self.RevokeInvoiceWorkflow(GetInvoiceWorkflowQuery(id=workflow_id), context)
            if res.succeed is False:
                errors.append(res.msg)
        if errors:
            return EmptyResponse(succeed=False, msg="\n".join(errors))
        else:
            return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def CloseInvoiceWorkflow(self, request, context):
        """关闭发票申请

        Path:
            POST /v1/invoice/workflow/close
        """
        org_id, user_info = load_from_request()
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).unwrap_or_else(raise_exception)
        self.check_permission(workflow, InvoiceWorkflow.Action.CLOSE)
        workflow.close(user_info=user_info)
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def BatchCloseInvoiceWorkflow(self, request, context):
        """批量关闭发票申请

        Path:
            POST /v1/invoice/workflow/batch-close
        """
        errors = []
        for workflow_id in request.ids:
            res = self.CloseInvoiceWorkflow(GetInvoiceWorkflowQuery(id=workflow_id), context)
            if res.succeed is False:
                errors.append(res.msg)
        if errors:
            return EmptyResponse(succeed=False, msg="\n".join(errors))
        else:
            return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def PostBackInvoiceWorkflow(self, request, context):
        from robot_processor.invoice.workflow.service import PostBackBroker

        org_id = load_from_request().org_id
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).unwrap_or_else(raise_exception)
        if workflow.platform_apply_post_back_state == InvoiceWorkflow.PostBackState.POSTING:
            raise PermissionError("正在回传中")
        PostBackBroker(workflow).do_post_back()
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def BatchPostBackInvoiceWorkflow(self, request, context):
        from robot_processor.invoice.workflow.service import PostBackBroker

        org_id = load_from_request().org_id
        errors = []
        for workflow_id in request.ids:
            match InvoiceWorkflow.get_and_check_workflow(org_id, workflow_id):
                case Err(error):
                    errors.append(str(error))
                case Ok(workflow):
                    try:
                        if workflow.platform_apply_post_back_state == InvoiceWorkflow.PostBackState.POSTING:
                            raise PermissionError("正在回传中")
                        PostBackBroker(workflow).do_post_back()
                    except Exception as e:
                        errors.append(str(e))
        if errors:
            return EmptyResponse(succeed=False, msg="\n".join(errors))
        else:
            return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def NotifyInvoiceWorkflow(self, request, context):
        from robot_processor.invoice.notification.services import NotificationServicer

        org_id = load_from_request().org_id
        notify_servicer = NotificationServicer.by_org_id(org_id)
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.workflow_id).expect("NotifyInvoiceWorkflow")
        errors = []
        for notification in request.notifications:
            try:
                channel = notify_servicer.select_channel(notification.channel_id)
                template = notify_servicer.select_template(notification.template_id)
                match channel.channel_type:
                    case NotificationChannelType.EMAIL:
                        if not notification.HasField("email_extra"):
                            errors.append("邮件通知缺少收件人信息")
                            continue
                        notify_servicer.send_by_email(channel, template, workflow, notification.email_extra.receiver)
                    case NotificationChannelType.PLATFORM_MSG:
                        notify_servicer.send_by_platform_msg(channel, template, workflow)
                    case NotificationChannelType.PLATFORM_MEMO:
                        notify_servicer.send_by_platform_memo(channel, template, workflow)
            except Exception as e:
                logger.opt(exception=e).error(e)
                errors.append(str(e))
        if errors:
            return EmptyResponse(succeed=False, msg=";".join(errors))
        else:
            return EmptyResponse(succeed=True)

    @org_required
    def CheckCorporateInvoiceThreshold(self, request, context):
        """开票前企业主体开票金额阈值校验

        Path:
            GET /v1/invoice/workflow/check-corporate-invoice-threshold
        """
        from robot_processor.invoice.workflow.service import CorporateInvoiceThresholdBroker

        org_id = load_from_request().org_id
        broker = CorporateInvoiceThresholdBroker(org_id)
        check_result = broker.check(request.seller_credit_id)
        if check_result.is_err():
            return CheckCorporateInvoiceThresholdResponse(
                succeed=False,
                msg=str(check_result.err()),
                data=CheckCorporateInvoiceThresholdResponse.Data(forbidden=True),
            )
        error_or_none = check_result.unwrap()
        if error_or_none:  # 校验不通过，但是仅提示
            return CheckCorporateInvoiceThresholdResponse(
                succeed=False, msg=str(error_or_none), data=CheckCorporateInvoiceThresholdResponse.Data(forbidden=False)
            )
        return CheckCorporateInvoiceThresholdResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def IssueInvoiceWorkflow(self, request, context):
        """开票

        Path:
            POST /v1/invoice/workflow/issue
        """
        from robot_processor.invoice.tax_bureau.models import Corporate

        from .issue_broker import IssueBroker
        from .validation_broker import CorporateInvoiceThresholdBroker
        from .validation_broker import PlatformApplyInvoiceAmountChecker

        org_id, user_info = load_from_request()
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).expect("IssueInvoiceWorkflow")
        self.check_permission(workflow, InvoiceWorkflow.Action.ISSUE)
        corporate = Corporate.get_by_credit_id(org_id, workflow.seller_credit_id)
        check_corporate_invoice_threshold_broker = CorporateInvoiceThresholdBroker(org_id)
        check_corporate_invoice_threshold_broker.check(corporate.credit_id).map_err(raise_exception)
        PlatformApplyInvoiceAmountChecker.check(workflow).unwrap_or_else(raise_exception)
        match workflow.issuing_type:
            case IssuingType.BLUE:
                issue_broker = IssueBroker.init_issue_blue(
                    workflow.to_request_view(),
                    [workflow],
                    corporate.competent_tax_bureau.name,
                    request.account,
                ).expect("初始化开票请求失败")
            case IssuingType.RED:
                issue_broker = IssueBroker.init_issue_red(
                    workflow,
                    corporate.competent_tax_bureau.name,
                    request.account,
                ).expect("初始化开票请求失败")
            case _:
                raise ValueError("不支持的开票类型")
        issue_broker.do_issue(user_info=user_info, debug_mode=request.debug_mode)
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def MergeIssueInvoiceWorkflow(self, request, context):
        """合并开票

        Path:
            POST /v1/invoice/workflow/merge-issue
        """
        from robot_processor.invoice.tax_bureau.models import Corporate

        from .issue_broker import IssueBroker
        from .validation_broker import CorporateInvoiceThresholdBroker

        org_id, user_info = load_from_request()
        workflows = [
            (InvoiceWorkflow.get_and_check_workflow(org_id, workflow_id).expect("获取发票申请流程失败"))
            for workflow_id in request.ids
        ]
        for workflow in workflows:
            self.check_permission(workflow, InvoiceWorkflow.Action.ISSUE)
        corporate = Corporate.get_by_credit_id(org_id, request.invoice_request.seller_credit_id)
        check_corporate_invoice_threshold_broker = CorporateInvoiceThresholdBroker(org_id)
        check_corporate_invoice_threshold_broker.check(corporate.credit_id).map_err(raise_exception)
        if request.invoice_request.org_id == 0:
            # FIXME 由前端补充 org_id 字段
            request.invoice_request.org_id = org_id
        issue_broker = IssueBroker.init_issue_blue(
            request.invoice_request,
            workflows,
            corporate.competent_tax_bureau.name,
            request.account,
        ).expect("初始化开票请求失败")
        issue_broker.do_issue(user_info=user_info, debug_mode=request.debug_mode)
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def BatchIssueInvoiceWorkflow(self, request, context):
        """批量开票

        Path:
            POST /v1/invoice/workflow/batch-issue
        """
        errors = []
        for workflow_id in request.ids:
            res = self.IssueInvoiceWorkflow(
                IssueInvoiceWorkflowBody(id=workflow_id, account=request.account),
                context,
            )
            if res.succeed is False:
                errors.append(res.msg)
        if errors:
            return EmptyResponse(succeed=False, msg="\n".join(errors))
        else:
            return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def ApproveInvoiceWorkflow(self, request, context):
        """审批通过

        Path:
            POST /v1/invoice/workflow/approve
        """
        org_id, user_info = load_from_request()
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).expect("查询发票申请流程失败")
        approval_broker = workflow.get_approval_broker()
        approval = approval_broker.approve(user_info).expect("审批失败")
        if approval.state == ApprovalState.APPROVED:
            workflow.approve(user_info=user_info)

        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def BatchApproveInvoiceWorkflow(self, request, context):
        """批量审批通过

        Path:
            POST /v1/invoice/workflow/batch-approve
        """
        errors = []
        for workflow_id in request.ids:
            res = self.ApproveInvoiceWorkflow(ProcessInvoiceWorkflowBody(id=workflow_id), context)
            if res.succeed is False:
                errors.append(res.msg)
        if errors:
            return EmptyResponse(succeed=False, msg="\n".join(errors))
        else:
            return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def RejectInvoiceWorkflow(self, request, context):
        """审批拒绝

        Path:
            POST /v1/invoice/workflow/reject
        """
        org_id, user_info = load_from_request()
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).expect("查询发票申请流程失败")
        approval_broker = workflow.get_approval_broker()
        approval_broker.reject(user_info).expect("审批失败")
        workflow.reject(user_info=user_info)
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def BatchRejectInvoiceWorkflow(self, request, context):
        """批量审批拒绝

        Path:
            POST /v1/invoice/workflow/batch-reject
        """
        errors = []
        for workflow_id in request.ids:
            res = self.RejectInvoiceWorkflow(ProcessInvoiceWorkflowBody(id=workflow_id), context)
            if res.succeed is False:
                errors.append(res.msg)
        if errors:
            return EmptyResponse(succeed=False, msg="\n".join(errors))
        else:
            return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def FetchInvoiceIssued(self, request, context):
        """获取发票详情

        Path:
            POST /v1/invoice/workflow/fetch-issued
        """
        from .issue_broker import IssueBroker

        org_id, user_info = load_from_request()
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).expect("查询发票申请流程失败")
        self.check_permission(workflow, InvoiceWorkflow.Action.REFRESH_INVOICE_RECEIPT)
        issue_broker = IssueBroker.by_request(workflow.processed_invoice_request())
        fetch_issue_result = issue_broker.fetch_issue(user_info=user_info, debug_mode=request.debug_mode)
        if fetch_issue_result.is_ok():
            return EmptyResponse(succeed=True)
        else:
            raise fetch_issue_result.unwrap_err()

    @catch_error_as_response(EmptyResponse)
    @org_required
    def BatchFetchInvoiceIssued(self, request, context):
        """批量获取发票详情

        Path:
            POST /v1/invoice/workflow/batch-fetch-issued
        """
        from .issue_broker import IssueBroker

        org_id, user_info = load_from_request()
        errors = []
        for workflow_id in request.ids:
            try:
                workflow = InvoiceWorkflow.get_and_check_workflow(org_id, workflow_id).expect("未找到发票申请")
                self.check_permission(workflow, InvoiceWorkflow.Action.REFRESH_INVOICE_RECEIPT)
                issue_broker = IssueBroker.by_request(workflow.processed_invoice_request())
                issue_broker.fetch_issue(user_info=user_info, debug_mode=request.debug_mode).unwrap_or_else(
                    raise_exception
                )
            except Exception as e:
                errors.append((e, workflow_id))
        if errors:
            return EmptyResponse(
                succeed=False,
                msg="\n".join(["获取发票凭证失败: {} 原因: {}".format(workflow_id, e) for e, workflow_id in errors]),
            )
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def ManualInvoiceNumber(self, request, context):
        """手动补充发票号码

        Path:
            POST /v1/invoice/workflow/manual-invoice-number
        """
        from sqlalchemy.sql.operators import eq as sql_eq

        from .issue_broker import IssueBroker

        org_id, user_info = load_from_request()
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).expect("查询发票申请流程失败")
        self.check_permission(workflow, InvoiceWorkflow.Action.MANUAL_INVOICE_NUMBER)
        if exists_workflow := InvoiceWorkflow.query.filter(
            sql_eq(InvoiceWorkflow.org_id, org_id),
            sql_eq(InvoiceWorkflow.invoice_number, request.invoice_number),
        ).first():
            raise ValueError(f"发票号码 {request.invoice_number} 已被 {exists_workflow.serial_number} 使用")
        issue_broker = IssueBroker.by_request(workflow.latest_invoice_request())
        issue_broker.issued(user_info, request.invoice_number, arrow.get(request.issuing_time).naive)
        issue_broker.fetch_issued_failed("手动补充发票号码", user_info=user_info)
        issue_broker.fetch_issue(user_info=user_info, debug_mode=request.debug_mode).unwrap_or_else(raise_exception)
        return EmptyResponse(succeed=True)

    @catch_error_as_response(ListInvoiceWorkflowExportTitlesResponse)
    def ListInvoiceWorkflowExportTitles(self, request, context):
        from cattrs import override
        from cattrs.gen import make_dict_unstructure_fn
        from robot_types.core import Symbol
        from robot_types.helper import converter

        from robot_processor.invoice.workflow.service import InvoiceWorkflowExporter

        response = ListInvoiceWorkflowExportTitlesResponse(succeed=True)
        titles = InvoiceWorkflowExporter.get_export_titles()
        to_dict = make_dict_unstructure_fn(
            Symbol,
            converter,
            type_spec=override(omit=True),
            children=override(omit=True),
            component_id=override(omit=True),
            render_config=override(omit=True),
            extra=override(omit=True),
        )
        for title in titles:
            response.data.titles.append(struct_wrapper(to_dict(title)))
        return response

    @catch_error_as_response(ExportInvoiceWorkflowResponse)
    @org_required
    def ExportInvoiceWorkflow(self, request, context):
        from robot_processor.user_customize_config.models import UserCustomizeConfig

        response = ExportInvoiceWorkflowResponse(succeed=True)
        org_id, user_info = load_from_request()
        request_user = AccountDetailV2(
            user_type=user_info.user.type, user_id=user_info.user.id, user_nick=user_info.user.nick
        )
        leyan_user = request_user.get_bound_leyan_user()
        export_config = message_to_dict(request.export_config)
        if request.save_export_config:
            assert leyan_user, "非乐言账号，无法记住上次导出报表的规则设置"
            UserCustomizeConfig.Queries.upsert_invoice_workflow_export_config(
                org_id=org_id,
                user_id=unwrap_optional(leyan_user.user_id),
                user_type=unwrap_optional(leyan_user.user_type),
                export_config=export_config,
            )

        with in_transaction() as session:
            export_task = InvoiceWorkflowExportTask(
                org_id=org_id,
                user_id=(leyan_user or request_user).user_id,
                user_type=(leyan_user or request_user).user_type,
                user_nick=(leyan_user or request_user).user_nick,
                export_config=export_config,
                filters=message_to_dict(request.filter),
            )
            session.add(export_task)
        export_task.submit_task()
        response.data.MergeFrom(export_task.to_pb())
        return response

    @catch_error_as_response(ListInvoiceWorkflowExportTaskResponse)
    @org_required
    def ListInvoiceWorkflowExportTask(self, request, context):
        response = ListInvoiceWorkflowExportTaskResponse(succeed=True)
        org_id = load_from_request().org_id
        set_default_paginate(request.config)
        tasks = InvoiceWorkflowExportTask.get_paginate_by_org(org_id, request.config)
        response.data.paginate.MergeFrom(request.config)
        for task in tasks:
            response.data.tasks.append(task.to_pb())
        return response

    @catch_error_as_response(GetInvoiceWorkflowExportTaskResponse)
    @org_required
    def GetInvoiceWorkflowExportTask(self, request, context):
        org_id = load_from_request().org_id
        task = InvoiceWorkflowExportTask.get_by_id(org_id, request.id)
        if not task:
            response = GetInvoiceWorkflowExportTaskResponse(succeed=False, msg=f"无效的任务id {request.id}")
        else:
            response = GetInvoiceWorkflowExportTaskResponse(succeed=True, data=task.to_pb())
        return response

    @catch_error_as_response(EmptyResponse)
    @org_required
    def ReSyncInvoiceWorkflow(self, request, context):
        """重新同步发票信息

        Path:
            POST /v1/invoice/workflow/refresh
        """
        org_id = load_from_request().org_id
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).expect("查询发票申请流程失败")
        workflow.resync()
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def RefreshInvoiceWorkflowTradeInfo(self, request, context):
        """重新同步订单信息

        Path:
            POST /v1/invoice/workflow/refresh-trade-info
        """
        org_id = load_from_request().org_id
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).expect("查询发票申请流程失败")
        workflow.refresh_trade_info()
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def RefreshInvoiceWorkflowPlatformApplyInfo(self, request, context):
        """重新同步平台申请信息

        Path:
            POST /v1/invoice/workflow/refresh-platform-apply-info
        """
        org_id = load_from_request().org_id
        workflow = InvoiceWorkflow.get_and_check_workflow(org_id, request.id).expect("查询发票申请流程失败")
        workflow.refresh_platform_apply_info().unwrap_or_else(raise_exception)
        return EmptyResponse(succeed=True)

    @classmethod
    def check_permission(cls, workflow, action):
        """检查 workflow 状态是否支持 action

        Args:
            workflow (InvoiceWorkflow): 发票申请流程
            action (InvoiceWorkflow.Action): 操作
        """
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, action)
        if workflow.processing_invoice_request():
            raise PermissionError("发票申请正在开票中，请不要重复操作")


class InvoiceApprovalServicer(workflow_pb2_grpc.InvoiceApprovalServicer):
    @catch_error_as_response(InvoiceApprovalTemplateResponse)
    @org_required
    def CreateInvoiceApprovalTemplate(self, request, context):
        from robot_types.helper import deserialize
        from robot_types.model.invoice.config_manager import ApprovalRuleset

        from robot_processor.invoice.config_manager import ConfigKey
        from robot_processor.invoice.config_manager import update_config

        org_id = load_from_request().org_id

        deserialize(message_to_dict(request), ApprovalRuleset)
        config = update_config(org_id, ConfigKey.APPROVAL_RULESET, message_to_dict(request))
        response = InvoiceApprovalTemplateResponse(succeed=True)
        ParseDict(config.config_val, response.data)
        return response

    @catch_error_as_response(InvoiceApprovalTemplateResponse)
    @org_required
    def GetInvoiceApprovalTemplate(self, request, context):
        from robot_processor.invoice.config_manager import ConfigKey
        from robot_processor.invoice.config_manager import ConfigManager

        org_id = load_from_request().org_id
        config_manager = ConfigManager(org_id)
        response = InvoiceApprovalTemplateResponse(succeed=True)
        ParseDict(config_manager.get_raw(ConfigKey.APPROVAL_RULESET), response.data)
        return response
