from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

from leyan_proto.digismart.robot.invoice.workflow_pb2 import UserInfo as pb_UserInfo
from result import Err
from result import Ok
from robot_types.helper import serialize
from robot_types.model import resource
from robot_types.model.invoice.config_manager import ApprovalRuleset
from sqlalchemy import select as sql_select
from sqlalchemy.orm import Load
from sqlalchemy.sql.operators import eq as sql_eq

from robot_processor.db import db
from robot_processor.db import in_transaction
from robot_processor.enums import UserType
from robot_processor.invoice.workflow.errors import ApprovalNodeNotInApprovalFlowError
from robot_processor.invoice.workflow.errors import ApprovalStopIteration
from robot_processor.invoice.workflow.errors import NoActiveApprovalError
from robot_processor.invoice.workflow.errors import NoActiveApprovalNodeError
from robot_processor.invoice.workflow.errors import NoApprovalPermissionError
from robot_processor.invoice.workflow.models import ApprovalState
from robot_processor.invoice.workflow.models import ApproveAction
from robot_processor.invoice.workflow.models import InvoiceApproval
from robot_processor.invoice.workflow.models import InvoiceApprovalNode
from robot_processor.invoice.workflow.models import InvoiceApprovalNodeReviewer
from robot_processor.invoice.workflow.models import InvoiceWorkflow
from robot_processor.symbol_table.services import FilterServicer
from robot_processor.utils import filter_none
from robot_processor.utils import message_to_dict


@dataclass
class InitApproval:
    need_approval: bool
    approval: InvoiceApproval | None


@dataclass
class ApprovalBroker:
    """审批流程代理"""

    workflow: InvoiceWorkflow

    def init_approval(self):
        """为 workflow 初始化审批流程

        Returns:
            Ok[InitApproval] | Err[Exception]: 是否需要审批，如果需要审批，返回审批流程实例
        """
        from robot_processor.invoice.config_manager import ConfigKey
        from robot_processor.invoice.config_manager import ConfigManager

        match self.get_in_review_approval():
            case Ok():
                return Err(RuntimeError("存在审批中的审批流程"))
        config_manager = ConfigManager(self.workflow.org_id)
        ruleset = config_manager.get(ConfigKey.APPROVAL_RULESET)
        if not ruleset.routes:
            return Ok(InitApproval(False, None))
        match_route: ApprovalRuleset.Routes | None = None
        for route in ruleset.routes:
            pb_filter = FilterServicer.to_pb_from_dict(route.filter)
            if FilterServicer.evaluate(pb_filter, self.context):
                match_route = route
                break
        if match_route is None:
            return Ok(InitApproval(False, None))
        match match_route.mode:
            case ApprovalRuleset.Routes.Mode.AUTO:
                nodes = [self.auto_mapper_node()]
            case "manual":
                nodes = match_route.manual_mapper.nodes
            case _:
                raise ValueError()
        # 未配置审批节点，跳过审批
        if not nodes:
            return Ok(InitApproval(False, None))

        with in_transaction() as trx:
            approval = InvoiceApproval(
                org_id=self.workflow.org_id,
                workflow_id=self.workflow.id,
                template=filter_none(serialize(match_route)),
                state=ApprovalState.APPROVAL_STATE_UNSPECIFIED,
            )
            for node in nodes:
                approval_node = InvoiceApprovalNode(
                    strategy=InvoiceApprovalNode.Strategy(node.strategy),
                    state=ApprovalState.APPROVAL_STATE_UNSPECIFIED,
                )
                approval.nodes.append(approval_node)
                approval_node.reviewers = [
                    InvoiceApprovalNodeReviewer(
                        user_id=serialize(reviewer.id),
                        user_type=serialize(reviewer.type),
                    )
                    for reviewer in node.reviewers
                ]
            trx.add(approval)
            approval.state = ApprovalState.IN_REVIEW
            approval.nodes[0].state = ApprovalState.IN_REVIEW

        return Ok(InitApproval(True, approval))

    @in_transaction()
    def approve(self, reviewer: resource.User) -> Ok[InvoiceApproval] | Err[Exception]:
        """审批通过，并推进审批流程"""
        # 更新审批人审批记录
        match self.get_in_review_approval_node_reviewer(reviewer):
            case Ok(node_reviewer):
                node_reviewer.action = ApproveAction.AGREE
                node_reviewer.reviewed_at = datetime.now()
                approval_node: InvoiceApprovalNode = node_reviewer.node
                approval: InvoiceApproval = approval_node.approval
            case _ as err:
                return err
        # 处理当前节点的审批状态
        match approval_node.strategy:
            case InvoiceApprovalNode.Strategy.ANY:
                approval_node.state = ApprovalState.APPROVED
            case InvoiceApprovalNode.Strategy.ALL:
                if all(reviewer.action == ApproveAction.AGREE for reviewer in approval_node.reviewers):
                    approval_node.state = ApprovalState.APPROVED
        # 处理审批流程状态
        # 当前审批节点未完成，直接返回
        if approval_node.state != ApprovalState.APPROVED:
            return Ok(approval)
        # 推进到下一个审批节点
        match self.get_next_node(approval_node):
            case Err(ApprovalStopIteration()):
                approval.state = ApprovalState.APPROVED
            case Ok(next_node):
                next_node.state = ApprovalState.IN_REVIEW
            case _ as err:
                return err
        return Ok(approval)

    @in_transaction()
    def reject(self, reviewer: resource.User) -> Ok[None] | Err[Exception]:
        """审批拒绝，并推进审批流程"""
        # 更新审批人审批记录
        match self.get_in_review_approval_node_reviewer(reviewer):
            case Ok(node_reviewer):
                node_reviewer.action = ApproveAction.REJECT
                node_reviewer.reviewed_at = datetime.now()
                approval_node: InvoiceApprovalNode = node_reviewer.node
                approval_node.state = ApprovalState.REJECTED
                approval: InvoiceApproval = approval_node.approval
                approval.state = ApprovalState.REJECTED
                return Ok(None)
            case _ as err:
                return err

    @in_transaction()
    def close(self):
        match self.get_in_review_approval_node():
            case Ok(approval_node):
                approval_node.state = ApprovalState.CLOSED
        match self.get_in_review_approval():
            case Ok(approval):
                approval.state = ApprovalState.CLOSED

    def try_auto_approve(self):
        if self.workflow.state != InvoiceWorkflow.State.IN_REVIEW:
            return
        approval = self.get_in_review_approval().expect("查询审批流程失败")
        approval_template = approval.get_template()
        if approval_template.mode != "auto":
            return
        if approval_template.auto_mapper.action == ApproveAction.AGREE:
            self.approve(self.auto_mapper_reviewer())
        else:
            self.reject(self.auto_mapper_reviewer())

    @property
    def context(self):
        return message_to_dict(self.workflow.to_detailed_view())

    def get_in_review_approval(self):
        """获取审批中的审批流程"""
        stmt = (
            sql_select(InvoiceApproval)
            .where(sql_eq(InvoiceApproval.workflow_id, self.workflow.id))
            .where(sql_eq(InvoiceApproval.state, ApprovalState.IN_REVIEW))
            .options(Load(InvoiceApproval).joinedload(InvoiceApproval.nodes).joinedload(InvoiceApprovalNode.reviewers))
        )
        result = db.session.execute(stmt)
        if (approval := result.unique().scalar_one_or_none()) is None:
            return Err(NoActiveApprovalError())
        return Ok(approval)

    def get_in_review_approval_node(self):
        """获取审批中的审批节点

        Returns:
            Ok[InvoiceApprovalNode] | Err[Exception]: 成功返回审批节点，失败返回异常
        """
        if (approval_result := self.get_in_review_approval()).is_err():
            return Err(approval_result.unwrap_err())
        approval = approval_result.unwrap()
        try:
            return Ok(
                next(
                    filter(
                        lambda node: node.state == ApprovalState.IN_REVIEW,
                        approval.nodes,
                    )
                )
            )
        except StopIteration:
            return Err(NoActiveApprovalNodeError())

    def get_next_node(self, node):
        """获取审批节点的下一个审批节点

        Returns:
            Ok[InvoiceApprovalNode] | Err[Exception]: 成功返回审批节点，失败返回异常
        """
        if (approval_result := self.get_in_review_approval()).is_err():
            return Err(approval_result.unwrap_err())
        approval = approval_result.unwrap()
        index_map = dict((node.id, idx) for idx, node in enumerate(approval.nodes))
        if node.id not in index_map:
            return Err(ApprovalNodeNotInApprovalFlowError())
        next_index = index_map[node.id] + 1
        if next_index >= len(approval.nodes):
            return Err(ApprovalStopIteration())
        return Ok(approval.nodes[next_index])

    def get_in_review_approval_node_reviewer(self, reviewer: resource.User | pb_UserInfo.User):
        if (node_result := self.get_in_review_approval_node()).is_err():
            return Err(node_result.unwrap_err())
        node = node_result.unwrap()

        def match(node_reviewer: InvoiceApprovalNodeReviewer):
            if node_reviewer.user_id != int(reviewer.id):
                return False
            if node_reviewer.user_type.name == reviewer.type:
                return True
            if node_reviewer.user_type.value == reviewer.type:
                return True
            return False

        try:
            return Ok(next(filter(match, node.reviewers)))
        except StopIteration:
            return Err(NoApprovalPermissionError())

    @classmethod
    def auto_mapper_node(cls):
        return ApprovalRuleset.Routes.ManualMapper.Nodes(
            strategy=ApprovalRuleset.Routes.ManualMapper.Nodes.Strategy.ANY, reviewers=[cls.auto_mapper_reviewer()]
        )

    @classmethod
    def auto_mapper_reviewer(cls):
        return resource.User(id=Decimal(0), type=UserType.SYSTEM.name, nick="系统自动审批")
