from __future__ import annotations

import re
from dataclasses import dataclass
from dataclasses import replace
from datetime import date
from datetime import datetime
from decimal import Decimal
from enum import IntEnum
from enum import StrEnum
from enum import auto
from typing import TYPE_CHECKING
from typing import Any
from typing import ClassVar
from typing import TypeAlias

import arrow
import sqlalchemy as sa
from google.protobuf.json_format import ParseDict
from google.protobuf.wrappers_pb2 import StringValue
from leyan_proto.digismart.robot.invoice.workflow_pb2 import Invoice as pb_Invoice
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceApproval as pb_InvoiceApproval
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceIssued as pb_InvoiceIssued
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceIssuingItem as pb_InvoiceIssuingItem
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceRequest as pb_InvoiceRequest
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflowExportTask as pb_InvoiceWorkflowExportTask
from leyan_proto.digismart.robot.invoice.workflow_pb2 import UserInfo as pb_UserInfo
from leyan_proto.digismart.robot.symbol_table_pb2 import Filter as pb_Filter
from loguru import logger
from result import Err
from result import Ok
from result import as_result
from robot_types.helper import deserialize
from sqlalchemy.orm import Load
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy.sql.operators import eq as sql_eq

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.constants import TASK_QUEUE_JOB
from robot_processor.db import DbBaseModel
from robot_processor.db import db
from robot_processor.db import in_transaction
from robot_processor.enums import UserType
from robot_processor.ext import task_queue
from robot_processor.invoice.common.models import OperationLog
from robot_processor.invoice.common.models import PlatformApplySyncMethod
from robot_processor.invoice.common.models import PostBackMethod
from robot_processor.invoice.common.models import anonymous_user_info
from robot_processor.invoice.errors import InvoiceWorkflowStateError
from robot_processor.logging import to_log
from robot_processor.symbol_table import named_typespec
from robot_processor.symbol_table.filters import FilterContextField
from robot_processor.symbol_table.filters import FilterContextMixin
from robot_processor.symbol_table.models import EnumOption
from robot_processor.utils import Amount
from robot_processor.utils import current_trace_id
from robot_processor.utils import ensure_mirror_of_pb_enum
from robot_processor.utils import int_wrapper
from robot_processor.utils import message_to_dict
from robot_processor.utils import string_wrapper
from robot_processor.utils import unwrap_optional

if TYPE_CHECKING:
    from robot_processor.shop.models import Shop


@ensure_mirror_of_pb_enum(pb_Invoice.BuyerType)
class BuyerType(IntEnum):
    BUYER_TYPE_UNSPECIFIED = 0
    # 自然人
    INDIVIDUAL = 1
    # 企业
    ENTERPRISE = 2

    @property
    def pb_value(self):
        return pb_Invoice.BuyerType.Value(self.name)


@ensure_mirror_of_pb_enum(pb_Invoice.InvoiceType)
class InvoiceType(IntEnum):
    INVOICE_TYPE_UNSPECIFIED = 0, None
    VAT_GENERAL = 1, "增值税普通发票"
    VAT_SPECIAL = 2, "增值税专用发票"

    @property
    def pb_value(self):
        return pb_Invoice.InvoiceType.Value(self.name)

    @classmethod
    def get_enum_option(cls, *args, **kwargs):
        return [
            EnumOption(label=invoice_type_option.label, value=invoice_type_option.name)
            for invoice_type_option in cls.__members__.values()
            if invoice_type_option.label is not None
        ]

    def __new__(cls, value, _):
        self = int.__new__(cls, value)
        self._value_ = value
        return self

    def __init__(self, _, label):
        self.label = label


@ensure_mirror_of_pb_enum(pb_Invoice.IssuingType)
class IssuingType(IntEnum):
    ISSUING_TYPE_UNSPECIFIED = 0, None
    BLUE = auto(), "蓝票"
    RED = auto(), "红票"

    @property
    def pb_value(self):
        return pb_Invoice.IssuingType.Value(self.name)

    @classmethod
    def get_enum_option(cls, *args, **kwargs):
        return [
            EnumOption(label=issuing_type_option.label, value=issuing_type_option.name)
            for issuing_type_option in cls.__members__.values()
            if issuing_type_option.label is not None
        ]

    def __new__(cls, value, _):
        self = int.__new__(cls, value)
        self._value_ = value
        return self

    def __init__(self, _, label):
        self.label = label


@ensure_mirror_of_pb_enum(pb_Invoice.ReversingStatus)
class ReversingStatus(IntEnum):
    REVERSING_STATUS_UNSPECIFIED = 0, None
    NOT_REVERSED = auto(), "未冲红"
    FULLY_REVERSED = auto(), "全部冲红"
    PARTIALLY_REVERSED = auto(), "部分冲红"

    def __new__(cls, value, _):
        self = int.__new__(cls, value)
        self._value_ = value
        return self

    def __init__(self, _, label):
        self.label = label


@ensure_mirror_of_pb_enum(pb_InvoiceWorkflow.Source)
class InvoiceWorkflowSource(IntEnum):
    MANUAL = 0, "手动创建"
    TRADE = 1, "订单"
    PLATFORM_APPLY = 2, "平台开票申请同步"
    BUSINESS_ORDER = 3, "工单任务"
    BATCH = 4, "批量导入"

    @property
    def pb_value(self):
        return pb_InvoiceWorkflow.Source.Value(self.name)

    def __new__(cls, value, _):
        self = int.__new__(cls, value)
        self._value_ = value
        return self

    def __init__(self, _, label):
        self.label = label

    @classmethod
    def get_enum_option(cls, *args, **kwargs):
        return [
            EnumOption(label=source_option.label, value=source_option.name)
            for source_option in cls.__members__.values()
            if source_option.label is not None
        ]


class InvoiceWorkflow(DbBaseModel):
    """发票申请开票流程"""

    __tablename__ = "invoice_workflow"
    __table_args__ = (
        sa.PrimaryKeyConstraint("id"),
        sa.Index("idx_created_at", "created_at"),
        sa.Index(
            "idx_serial_number",
            "org_id",
            "serial_number",
        ),
        sa.Index("idx_state", "org_id", "state"),
        sa.Index("idx_org_batch", "org_id", "batch_id"),
    )

    @ensure_mirror_of_pb_enum(pb_InvoiceWorkflow.Action)
    class Action(IntEnum):
        ACTION_UNSPECIFIED = 0, None
        SUBMIT = 1, "提交"
        APPROVE = 2, "审批通过"
        REJECT = 3, "审批拒绝"
        ISSUE = 4, "开票"
        CLOSE = 5, "关闭"
        REVOKE = 6, "撤回"
        MARK_INVOICED = 7, "已开票"
        MARK_INVOICE_FAILED = 8, "开票失败"
        RUSH_RED = 9, "红冲"
        VIEW_ISSUED = 10, "查看发票"
        REFRESH_INVOICE_RECEIPT = 11, "获取发票凭证"
        MANUAL_INVOICE_NUMBER = 12, "已线下开票"
        SAVE_DRAFT = 13, "保存草稿"
        POST_BACK = 14, "平台开票申请回传"
        NOTIFY = 15, "下发通知"
        RESYNC = 16, "同步"

        @property
        def pb_value(self):
            return pb_InvoiceWorkflow.Action.Value(self.name)

        def __new__(cls, value, _):
            self = int.__new__(cls, value)
            self._value_ = value
            return self

        def __init__(self, _, label):
            self.label = label

    @ensure_mirror_of_pb_enum(pb_InvoiceWorkflow.State)
    class State(IntEnum):
        STATE_UNSPECIFIED = 0, None
        # 申请人还未提交申请
        DRAFT = 1, "待提交"
        # 申请人已提交申请，等待财务审核
        IN_REVIEW = 2, "审批中"
        # 财务审核通过，待开票
        PENDING_INVOICING = 3, "待开票"
        # 财务审核拒绝
        REJECTED = 4, "审批拒绝"
        # 提交了开票任务，等待开票结果
        INVOICING = 5, "开票中"
        # 超时未返回结果，未知的开票状态
        INVOICING_TIMEOUT = 11, "开票超时"
        # 已开票，未获取发票凭证
        INVOICED_WITHOUT_RECEIPT = 10, "已开票，未获取发票凭证"
        # 开票成功; [终止状态，不可编辑]
        INVOICED = 6, "开票完成"
        # 开票失败
        INVOICE_FAILED = 7, "开票失败"
        # 申请人取消申请; [终止状态，不可编辑]
        CLOSED = 8, "已关闭"
        SUCCEED = 9, None  # [预留状态，未启用]

        def __new__(cls, value, _):
            self = int.__new__(cls, value)
            self._value_ = value
            return self

        def __init__(self, _, label):
            self.label = label

        @classmethod
        def get_enum_option(cls, *args, **kwargs):
            return [
                EnumOption(label=source_option.label, value=source_option.name)
                for source_option in cls.__members__.values()
                if source_option.label is not None
            ]

    @ensure_mirror_of_pb_enum(pb_InvoiceWorkflow.PostBackState)
    class PostBackState(IntEnum):
        POST_BACK_STATE_UNSPECIFIED = 0, None
        PENDING = 1, "待回传"
        POSTED = 2, "已回传"
        POSTING = 3, "回传中"
        POST_FAILED = 4, "回传失败"
        NOT_SUPPORTED = 5, "不支持回传"
        RETRYING = 6, "失败重试中"
        NOOP = 7, "无需处理"

        def __new__(cls, value, _):
            self = int.__new__(cls, value)
            self._value_ = value
            return self

        def __init__(self, _, label):
            self.label = label

        @classmethod
        def get_enum_option(cls, *args, **kwargs):
            return [
                EnumOption(label=state_option.label, value=state_option.name)
                for state_option in cls.__members__.values()
                if state_option.label is not None
            ]

    Source: ClassVar = InvoiceWorkflowSource

    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    # 飞梭创建发票单唯一标识, 前缀: FP+日期+发票单量
    # 示例: FP202404180011
    serial_number = mapped_column(sa.String(18), nullable=False)
    # 通过批量导入时
    batch_id = mapped_column(sa.Integer, nullable=True)
    # 租户 id
    org_id = mapped_column(sa.Integer, nullable=False)
    # 发票申请流程当前状态
    state = mapped_column(sa.Enum(State), default=State.DRAFT, nullable=False)

    # 发票申请人 id
    applicant_id = mapped_column(sa.Integer, nullable=False)
    # 发票申请人类型
    applicant_type = mapped_column(sa.Enum(UserType), nullable=False)
    # 发票申请人姓名
    applicant_name = mapped_column(sa.String(32), nullable=False)
    # 发票申请人所属组信息
    # list(dict(uuid=str, type=str, name=str))
    applicant_groups = mapped_column(sa.JSON, nullable=True, default=list)
    # 发票申请时间
    # 创建后不可变更
    created_at = mapped_column(sa.DateTime, default=datetime.now, nullable=False)
    # 发票最后更新时间
    # 仅通过 set_state 方法更新
    updated_at = mapped_column(sa.DateTime, default=datetime.now, nullable=False)

    # 发票类型; VAT_GENERAL: 增值税普通发票, VAT_SPECIAL: 增值税专用发票
    invoice_type = mapped_column(sa.Enum(InvoiceType), nullable=False)
    # 开票类型; BLUE: 蓝票, RED: 红票
    issuing_type = mapped_column(sa.Enum(IssuingType), nullable=False)

    """发票信息-销售方信息"""
    # 销售方名称; Reference(Corporate.name)
    seller_name = mapped_column(sa.String(256), nullable=False)
    # 销售方纳税人识别号; Reference(Corporate.credit_id)
    seller_credit_id = mapped_column(sa.String(20), nullable=False)
    # 是否展示销售方地址和电话
    show_seller_address_phone = mapped_column(sa.Boolean, nullable=False, default=False)
    # 销售方地址; Reference(Corporate.address)
    seller_address: Mapped[str | None] = mapped_column(sa.String(256), nullable=True)
    # 销售方电话; Reference(Corporate.phone)
    seller_phone: Mapped[str | None] = mapped_column(sa.String(20), nullable=True)
    # 是否展示销售方银行信息
    show_seller_bank = mapped_column(sa.Boolean, nullable=False, default=False)
    # 销售方开户行; Reference(Corporate.bank)
    seller_bank = mapped_column(sa.String(256), nullable=True)
    # 销售方银行账号; Reference(Corporate.bank_account)
    seller_bank_account = mapped_column(sa.String(20), nullable=True)

    """发票信息-购买方信息"""
    # 购买方类型
    # Enum(自然人, 企业)
    buyer_type = mapped_column(sa.Enum(BuyerType), nullable=False)
    # 购买方名称
    buyer_name = mapped_column(sa.String(256), nullable=False)
    # 购买方纳税人识别号
    buyer_credit_id: Mapped[str | None] = mapped_column(sa.String(20), nullable=True)
    # 是否展示购买方地址和电话
    show_buyer_address_phone = mapped_column(sa.Boolean, nullable=False, default=False)
    # 购买方地址
    buyer_address: Mapped[str | None] = mapped_column(sa.String(256), nullable=True)
    # 购买方电话
    buyer_phone: Mapped[str | None] = mapped_column(sa.String(20), nullable=True)
    # 是否展示购买方银行信息
    show_buyer_bank = mapped_column(sa.Boolean, nullable=False, default=False)
    # 购买方开户行
    buyer_bank: Mapped[str | None] = mapped_column(sa.String(256), nullable=True)
    # 购买方银行账号
    buyer_bank_account: Mapped[str | None] = mapped_column(sa.String(20), nullable=True)

    """发票信息-明细"""
    # 商品金额是否含税
    is_tax_included = mapped_column(sa.Boolean, nullable=False)
    # 发票明细 - 商品行
    issuing_items = mapped_column(sa.JSON(), nullable=True, default=list)

    """额外信息"""
    remark = mapped_column(sa.Text(), nullable=True)
    remark_template = mapped_column(sa.JSON, nullable=True)
    extra = mapped_column(sa.JSON())

    invoice_number = mapped_column(sa.String(32), nullable=True)
    issuing_time = mapped_column(sa.DateTime(), nullable=True)
    issue_failed_reason: Mapped[str | None] = mapped_column(sa.Text(), nullable=True)
    original_workflow_id: Mapped[int | None] = mapped_column(sa.Integer, nullable=True)
    rush_red_reason: Mapped[str | None] = mapped_column(sa.String(255), nullable=True)
    reversing_status = mapped_column(sa.Enum(ReversingStatus), nullable=False, default=ReversingStatus.NOT_REVERSED)
    # 开票来源及相关信息
    source = mapped_column(sa.Enum(Source), default=Source.MANUAL)
    tid = mapped_column(sa.String(128), nullable=True)
    oid = mapped_column(sa.JSON(), nullable=True)
    platform = mapped_column(sa.String(64), nullable=True)
    sid = mapped_column(sa.String(64), nullable=True)
    business_order_id = mapped_column(sa.Integer, nullable=True)
    job_id = mapped_column(sa.Integer, nullable=True)
    trade_raw_info = mapped_column(sa.JSON, nullable=True)
    platform_apply_raw_info = mapped_column(sa.JSON, nullable=True)
    platform_apply_id = mapped_column(sa.String(128), nullable=True)
    business_order_raw_info = mapped_column(sa.JSON, nullable=True)
    source_raw = mapped_column(sa.JSON, nullable=True)
    # 平台发票申请回传状态
    platform_apply_post_back_state = mapped_column(
        sa.Enum(PostBackState, native_enum=False, length=32), default=PostBackState.POST_BACK_STATE_UNSPECIFIED
    )
    platform_apply_post_back_result = mapped_column(sa.JSON)

    invoice_issued = relationship(
        lambda: InvoiceIssued,
        primaryjoin="foreign(InvoiceWorkflow.invoice_number)==InvoiceIssued.invoice_number",
        viewonly=True,
    )
    # 订单信息需要冗余一份
    trade_info_updated_at = mapped_column(sa.DateTime, nullable=True)
    trade_info_datasource = mapped_column(sa.String(32), nullable=True)
    trade_status = mapped_column(sa.String(32), nullable=True)
    trade_refund_status = mapped_column(sa.String(32), nullable=True)
    trade_goods_items = mapped_column(sa.JSON, nullable=True)
    # 平台申请信息
    platform_apply_sync_method = mapped_column(
        sa.Enum(PlatformApplySyncMethod, native_enum=False, length=32),
        default=PlatformApplySyncMethod.SYNC_METHOD_UNSPECIFIED,
    )
    platform_apply_time = mapped_column(sa.DateTime, nullable=True, comment="申请开票时间")
    platform_apply_amount = mapped_column(sa.DECIMAL(10, 2), nullable=True, comment="申请开票金额")
    notification_info = mapped_column(sa.JSON, nullable=True, comment="登记的通知信息")
    shop: Mapped[Shop] = relationship(
        "Shop",
        viewonly=True,
        primaryjoin=(
            "and_(foreign(InvoiceWorkflow.sid)==Shop.sid,"
            "foreign(InvoiceWorkflow.platform)==Shop.platform,"
            "foreign(InvoiceWorkflow.org_id)==cast(Shop.org_id, Integer))"
        ),
    )

    def get_notification_logs(self):
        from robot_processor.invoice.notification.models import NotificationLog

        return NotificationLog.query.filter_by(workflow_id=self.id).all()

    @property
    def issuing_items_passed(self):
        return all(item.tax_code != "" for item in self.get_issuing_items())

    @property
    def issuing_items_total_amount(self):
        return Amount.sum([Decimal(item.amount) for item in self.get_issuing_items()])

    @property
    def trade_info(self):
        if not self.tid:
            return None
        info = pb_InvoiceWorkflow.TradeInfo(tid=self.tid, oid=self.oid, platform=self.platform, sid=self.sid)
        try:
            return ParseDict(self.trade_raw_info, info, ignore_unknown_fields=True)
        except Exception as e:
            logger.opt(exception=e).error(f"解析 trade_raw_info 失败 {e}")
        return info

    @trade_info.setter
    def trade_info(self, new: pb_InvoiceWorkflow.TradeInfo):
        self.tid = new.tid
        if new.oid:
            self.oid = [oid for oid in new.oid]
        if new.platform:
            self.platform = new.platform
        if new.sid:
            self.sid = new.sid
        self.trade_raw_info = message_to_dict(new)

    @property
    def platform_apply_info(self):
        if not self.platform_apply_raw_info:
            return None
        invoice_amount = str(self.platform_apply_amount)
        if self.platform_apply_sync_method is PlatformApplySyncMethod.QN:
            apply_time = self.platform_apply_time.strftime("%Y-%m-%d")
        else:
            apply_time = self.platform_apply_time.strftime("%Y-%m-%d %H:%M:%S")
        info = pb_InvoiceWorkflow.PlatformApplyInfo(
            sync_method=self.platform_apply_sync_method.pb_value,
            tid=self.tid,
            oid=self.oid,
            platform=self.platform,
            sid=self.sid,
            platform_apply_id=self.platform_apply_id,
            apply_time=apply_time,
            invoice_amount=invoice_amount,
        )
        match self.platform_apply_sync_method:
            case PlatformApplySyncMethod.QN:
                info.sync_method = PlatformApplySyncMethod.QN.pb_value
                qn_invoice = self.get_qn_platform_apply_info()
                info.apply_status = qn_invoice.get_status_zh()
                info.buyer_type = qn_invoice.get_buyer_type().pb_value
                info.buyer_name = qn_invoice.payerName
                if qn_invoice.payerRegisterNo:
                    info.buyer_credit_id.value = qn_invoice.payerRegisterNo
                if qn_invoice.payerAddress:
                    info.buyer_address.value = qn_invoice.payerAddress
                if qn_invoice.payerPhone:
                    info.buyer_phone.value = qn_invoice.payerPhone
                if qn_invoice.payerBank:
                    info.buyer_bank.value = qn_invoice.payerBank
                if qn_invoice.payerBankaccount:
                    info.buyer_bank_account.value = qn_invoice.payerBankaccount
                if qn_invoice.applySource:
                    info.remark.value = qn_invoice.applySource
                info.invoice_type = qn_invoice.get_invoice_type().pb_value
                info.issuing_type = qn_invoice.get_issuing_type().pb_value
            case PlatformApplySyncMethod.DOUDIAN:
                info.sync_method = PlatformApplySyncMethod.DOUDIAN.pb_value
                doudian_invoice = self.get_doudian_platform_apply_info()
                info.apply_status = doudian_invoice.invoice_status.label
                info.buyer_type = doudian_invoice.title_type.buyer_type.pb_value
                info.buyer_name = doudian_invoice.title
                if doudian_invoice.tax_no:
                    info.buyer_credit_id.value = doudian_invoice.tax_no
                if doudian_invoice.company_address:
                    info.buyer_address.value = doudian_invoice.company_address
                if doudian_invoice.company_mobile:
                    info.buyer_phone.value = doudian_invoice.company_mobile
                if doudian_invoice.bank_name:
                    info.buyer_bank.value = doudian_invoice.bank_name
                if doudian_invoice.bank_no:
                    info.buyer_bank_account.value = doudian_invoice.bank_no
                info.invoice_type = doudian_invoice.get_invoice_type().pb_value
                info.issuing_type = doudian_invoice.get_issuing_type().pb_value
            case PlatformApplySyncMethod.JD:
                info.sync_method = PlatformApplySyncMethod.JD.pb_value
                jd_invoice = self.get_jd_platform_apply_info()
                info.apply_status = jd_invoice.get_invoice_status_zh()
                info.buyer_type = jd_invoice.get_buyer_type().pb_value
                info.buyer_name = jd_invoice.invoiceTitle or "个人"
                if jd_invoice.consumerTaxId:
                    info.buyer_credit_id.value = jd_invoice.consumerTaxId
                if jd_invoice.consumerAddress:
                    info.buyer_address.value = jd_invoice.consumerAddress
                if jd_invoice.consumerBankName:
                    info.buyer_bank.value = jd_invoice.consumerBankName
                if jd_invoice.consumerBankAccount:
                    info.buyer_bank_account.value = jd_invoice.consumerBankAccount
                info.invoice_type = jd_invoice.get_invoice_type().pb_value
                info.issuing_type = jd_invoice.get_issuing_type().pb_value
            case PlatformApplySyncMethod.PDD:
                info.sync_method = PlatformApplySyncMethod.PDD.pb_value
                pdd_invoice = self.get_pdd_platform_apply_info()
                info.apply_status = pdd_invoice.application_status.label
                info.buyer_type = pdd_invoice.get_buyer_type().pb_value
                info.buyer_name = pdd_invoice.payer_name
                if pdd_invoice.payer_register_no:
                    info.buyer_credit_id.value = pdd_invoice.payer_register_no
                if pdd_invoice.payer_address:
                    info.buyer_address.value = pdd_invoice.payer_address
                if pdd_invoice.payer_bank:
                    info.buyer_bank.value = pdd_invoice.payer_bank
                if pdd_invoice.payer_account:
                    info.buyer_bank_account.value = pdd_invoice.payer_account
                info.invoice_type = pdd_invoice.get_invoice_type().pb_value
                info.issuing_type = pdd_invoice.get_issuing_type().pb_value

        info.raw.update(self.platform_apply_raw_info)
        return info

    @platform_apply_info.setter
    def platform_apply_info(self, new: pb_InvoiceWorkflow.PlatformApplyInfo):
        if self.source == InvoiceWorkflow.Source.PLATFORM_APPLY:
            self.tid = new.tid
            self.oid = [oid for oid in new.oid]
            self.platform = new.platform
            self.sid = new.sid
            self.platform_apply_id = new.platform_apply_id
            self.platform_apply_time = arrow.get(new.apply_time).naive
            self.platform_apply_amount = new.invoice_amount
            self.platform_apply_sync_method = PlatformApplySyncMethod(new.sync_method)
        self.platform_apply_raw_info = message_to_dict(new.raw)

    def get_pdd_platform_apply_info(self):
        import dacite

        from robot_processor.client.pdd_erp import PddInvoiceApplicationQuery
        from robot_processor.client.pdd_erp import PddInvoiceApplicationStatus

        pdd_invoice = dacite.from_dict(
            PddInvoiceApplicationQuery.Response.InvoiceApplicationQueryResponse.InvoiceApplicationQueryItem,
            self.platform_apply_raw_info,
            config=dacite.Config(cast=[PddInvoiceApplicationStatus, int]),
        )
        return pdd_invoice

    def get_qn_platform_apply_info(self):
        from rpa.mola.schemas import QNInvoice

        qn_invoice = QNInvoice(**self.platform_apply_raw_info)
        return qn_invoice

    def get_doudian_platform_apply_info(self):
        from robot_processor.client.doudian import DoudianInvoice

        doudian_invoice = DoudianInvoice(**self.platform_apply_raw_info)
        return doudian_invoice

    def get_jd_platform_apply_info(self):
        from robot_processor.client.jd_sdk import InvoiceApplyData as JDInvoiceApply

        jd_invoice = JDInvoiceApply(**self.platform_apply_raw_info)
        return jd_invoice

    @property
    def business_order_job_info(self):
        if not self.business_order_raw_info:
            return None
        info = pb_InvoiceWorkflow.BusinessOrderJobInfo(business_order_id=self.business_order_id, job_id=self.job_id)
        info.raw.update(self.business_order_raw_info)
        return info

    @business_order_job_info.setter
    def business_order_job_info(self, new: pb_InvoiceWorkflow.BusinessOrderJobInfo):
        if any([self.business_order_id, self.job_id]):
            raise PermissionError("source info cannot update")
        self.business_order_id = new.business_order_id
        self.job_id = new.job_id
        self.business_order_raw_info = message_to_dict(new.raw)

    def processing_invoice_request(self):
        request: InvoiceRequest | None = (
            InvoiceRequest.query.join(InvoiceWorkflowRequest, InvoiceWorkflowRequest.request_id == InvoiceRequest.id)
            .filter(InvoiceWorkflowRequest.workflow_id == self.id)
            .filter(InvoiceRequest.state.in_([InvoiceRequest.State.INVOICING, InvoiceRequest.State.QUEUED]))
            .order_by(InvoiceRequest.id.desc())
            .one_or_none()
        )
        return request

    def processed_invoice_request(self):
        request: InvoiceRequest = (
            InvoiceRequest.query.join(InvoiceWorkflowRequest, InvoiceWorkflowRequest.request_id == InvoiceRequest.id)
            .filter(InvoiceWorkflowRequest.workflow_id == self.id)
            .filter(InvoiceRequest.state == InvoiceRequest.State.SUCCEED)
            .one()
        )
        return request

    def latest_invoice_request(self):
        request = (
            InvoiceRequest.query.join(InvoiceWorkflowRequest, InvoiceWorkflowRequest.request_id == InvoiceRequest.id)
            .filter(InvoiceWorkflowRequest.workflow_id == self.id)
            .order_by(InvoiceRequest.id.desc())
            .first()
        )
        return request

    def to_detailed_view(self, user_info: pb_UserInfo | None = None) -> pb_InvoiceWorkflow.DetailedView:
        detailed_view = ParseDict(
            dict(
                id=self.id,
                serial_number=self.serial_number,
                state=self.state,
                platform_apply_post_back_state=self.platform_apply_post_back_state,
                applicant=dict(
                    id=self.applicant_id,
                    type=self.applicant_type,
                    nick=self.applicant_name,
                ),
                applicant_groups=self.applicant_groups,
                created_at=self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                updated_at=self.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                invoice_type=self.invoice_type,
                issuing_type=self.issuing_type,
                seller_name=self.seller_name,
                seller_credit_id=self.seller_credit_id,
                show_seller_address_phone=self.show_seller_address_phone,
                seller_address=self.seller_address,
                seller_phone=self.seller_phone,
                show_seller_bank=self.show_seller_bank,
                seller_bank=self.seller_bank,
                seller_bank_account=self.seller_bank_account,
                buyer_type=self.buyer_type,
                buyer_name=self.buyer_name,
                buyer_credit_id=self.buyer_credit_id,
                show_buyer_address_phone=self.show_buyer_address_phone,
                buyer_address=self.buyer_address,
                buyer_phone=self.buyer_phone,
                show_buyer_bank=self.show_buyer_bank,
                buyer_bank=self.buyer_bank,
                buyer_bank_account=self.buyer_bank_account,
                remark=self.remark,
                remark_template=self.remark_template,
                is_tax_included=self.is_tax_included,
                issuing_items=self.issuing_items,
                invoice_number=self.invoice_number,
                issuing_time=self.issuing_time.strftime("%Y-%m-%d %H:%M:%S") if self.issuing_time else None,
                issue_failed_reason=self.issue_failed_reason,
                original_workflow_id=self.original_workflow_id,
                rush_red_reason=self.rush_red_reason,
                source=self.source,
                batch_id=self.batch_id,
                notification_logs=[[log.to_dict() for log in self.get_notification_logs()]],
                notification_info=self.notification_info,
            ),
            pb_InvoiceWorkflow.DetailedView(),
            ignore_unknown_fields=True,
        )
        if (
            self.state is InvoiceWorkflow.State.INVOICED
            and self.invoice_number is not None
            and self.invoice_issued is not None
        ):
            detailed_view.invoice_issued.MergeFrom(self.invoice_issued.to_pb())
        if user_info:
            detailed_view.actions.extend(self.get_actions(user_info))
        if self.trade_info:
            detailed_view.trade_info.MergeFrom(self.trade_info)
        if self.platform_apply_info:
            detailed_view.platform_apply_info.MergeFrom(self.platform_apply_info)
        if self.business_order_raw_info:
            detailed_view.business_order_job_info.MergeFrom(self.business_order_job_info)
        if self.platform_apply_post_back_state == InvoiceWorkflow.PostBackState.POST_FAILED:
            detailed_view.platform_apply_post_back_failed_reason.value = (
                self.platform_apply_post_back_result or {}
            ).get("failed_reason") or "未知原因"
        return detailed_view

    def to_request_view(self):
        view = pb_InvoiceRequest(
            org_id=self.org_id,
            invoice_type=self.invoice_type.value,
            issuing_type=self.issuing_type.value,
            seller_name=self.seller_name,
            seller_credit_id=self.seller_credit_id,
            show_seller_address_phone=self.show_seller_address_phone,
            seller_address=StringValue(value=self.seller_address) if self.seller_address is not None else None,
            seller_phone=StringValue(value=self.seller_phone) if self.seller_phone is not None else None,
            show_seller_bank=self.show_seller_bank,
            seller_bank=StringValue(value=self.seller_bank) if self.seller_bank is not None else None,
            seller_bank_account=(
                StringValue(value=self.seller_bank_account) if self.seller_bank_account is not None else None
            ),
            buyer_type=self.buyer_type.value,
            buyer_name=self.buyer_name,
            buyer_credit_id=StringValue(value=self.buyer_credit_id) if self.buyer_credit_id is not None else None,
            show_buyer_address_phone=self.show_buyer_address_phone,
            buyer_address=StringValue(value=self.buyer_address) if self.buyer_address is not None else None,
            buyer_phone=StringValue(value=self.buyer_phone) if self.buyer_phone is not None else None,
            show_buyer_bank=self.show_buyer_bank,
            buyer_bank=StringValue(value=self.buyer_bank) if self.buyer_bank is not None else None,
            buyer_bank_account=(
                StringValue(value=self.buyer_bank_account) if self.buyer_bank_account is not None else None
            ),
            is_tax_included=self.is_tax_included,
            remark=StringValue(value=self.remark) if self.remark is not None else None,
        )
        view.issuing_items.extend(
            [ParseDict(item, pb_InvoiceIssuingItem(), ignore_unknown_fields=True) for item in self.issuing_items or []]
        )
        return view

    def to_rush_red_editable(self):
        view = pb_InvoiceWorkflow.EditableView(
            invoice_type=self.invoice_type,
            issuing_type=IssuingType.RED,  # type: ignore[arg-type]
            seller_name=self.seller_name,
            seller_credit_id=self.seller_credit_id,
            show_seller_address_phone=self.show_seller_address_phone,
            seller_address=string_wrapper(self.seller_address),
            seller_phone=string_wrapper(self.seller_phone),
            show_seller_bank=self.show_seller_bank,
            seller_bank=string_wrapper(self.seller_bank),
            seller_bank_account=string_wrapper(self.seller_bank_account),
            buyer_type=self.buyer_type,
            buyer_name=self.buyer_name,
            buyer_credit_id=string_wrapper(self.buyer_credit_id),
            show_buyer_address_phone=self.show_buyer_address_phone,
            buyer_address=string_wrapper(self.buyer_address),
            buyer_phone=string_wrapper(self.buyer_phone),
            show_buyer_bank=self.show_buyer_bank,
            buyer_bank=string_wrapper(self.buyer_bank),
            buyer_bank_account=string_wrapper(self.buyer_bank_account),
            issuing_items=self.get_issuing_items(),
            is_tax_included=self.is_tax_included,
            original_workflow_id=int_wrapper(self.id),
        )
        for issuing_item in view.issuing_items:
            if issuing_item.HasField("num"):
                issuing_item.num.value = str(-float(issuing_item.num.value))
            issuing_item.amount = str(-float(issuing_item.amount))
        return view

    def log_operation(self, user_info: pb_UserInfo, changes):
        OperationLog.create(
            OperationLog.Model.INVOICE_WORKFLOW,
            self.id,
            user_info,
            changes,
            datetime.now(),
        )

    def rebuild_context(self):
        from robot_processor.invoice.workflow.service.workflow_builder import Context

        match self.platform_apply_sync_method:
            case PlatformApplySyncMethod.PDD:
                pdd_invoice = self.get_pdd_platform_apply_info()
                context = Context.PddPlatformApply(org_id=self.org_id, shop=self.shop, pdd_invoice=pdd_invoice)
            case PlatformApplySyncMethod.JD:
                jd_invoice = self.get_jd_platform_apply_info()
                context = Context.JDPlatformApply(org_id=self.org_id, shop=self.shop, jd_invoice=jd_invoice)
            case PlatformApplySyncMethod.DOUDIAN:
                doudian_invoice = self.get_doudian_platform_apply_info()
                context = Context.DoudianPlatformApply(
                    org_id=self.org_id, shop=self.shop, doudian_invoice=doudian_invoice
                )
            case PlatformApplySyncMethod.QN:
                qn_invoice = self.get_qn_platform_apply_info()
                context = Context.QNPlatformApply(org_id=self.org_id, shop=self.shop, qn_invoice=qn_invoice)
            case _:
                raise NotImplementedError

        return context

    def resync(self):
        from robot_processor.invoice.workflow.service.workflow_builder import WorkflowBuilder

        context = self.rebuild_context()
        WorkflowBuilder.build(context)
        with in_transaction():
            self.edit(context.pb_workflow)

    def refresh_trade_info(self):
        from robot_processor.invoice.workflow.service.workflow_builder.trade import TradeModule

        context = self.rebuild_context()
        TradeModule(context).execute()
        if context.pb_workflow.HasField("trade_info"):
            with in_transaction():
                self.trade_info = context.pb_workflow.trade_info

    def refresh_platform_apply_info(self):
        from robot_processor.client import doudian_cloud
        from robot_processor.client.pdd_erp import PddInvoiceApplicationQuery
        from robot_processor.invoice.common.models import JdInvoice
        from robot_processor.invoice.workflow.service import WorkflowContext
        from robot_processor.invoice.workflow.service.workflow_builder.platform_apply_info import \
            PlatformApplyInfoModule
        from rpa.mola import MolaClient

        match self.platform_apply_sync_method:
            case PlatformApplySyncMethod.QN:
                rebuild_context = self.rebuild_context()
                time = rebuild_context.qn_invoice.startTime
                serial_no = rebuild_context.qn_invoice.serialNo
                mola = MolaClient(self.sid)
                mola_result = mola.qn_invoice_get_all(tid=self.tid, start_time=time, end_time=time)
                if mola_result.is_err():
                    return Err(mola_result.unwrap_err())
                mola_response = mola_result.unwrap()
                if not mola_response.data:
                    return Err(Exception("未找到发票申请信息"))
                qn_invoice = next(filter(lambda x: x.serialNo == serial_no, mola_response.data), None)
                if qn_invoice is None:
                    return Err(Exception("未找到发票申请信息"))
                context = WorkflowContext.QNPlatformApply(org_id=self.org_id, shop=self.shop, qn_invoice=qn_invoice)
            case PlatformApplySyncMethod.DOUDIAN:
                doudian_invoice_response = doudian_cloud.invoice_list(
                    store_id=self.sid,
                    order_id=self.tid,  # 抖店不支持通过发票 id 查询
                    page_no=0,
                    page_size=1,
                )
                if not doudian_invoice_response.invoice_list:
                    return Err(Exception("未找到发票申请信息"))
                doudian_invoice = doudian_invoice_response.invoice_list[0]
                context = WorkflowContext.DoudianPlatformApply(
                    org_id=self.org_id, shop=self.shop, doudian_invoice=doudian_invoice
                )
            case PlatformApplySyncMethod.JD:
                jd_invoice = JdInvoice.query.filter_by(serial_no=self.platform_apply_id).first()
                if not jd_invoice:
                    return Err(Exception("未找到发票申请信息"))
                context = WorkflowContext.JDPlatformApply(org_id=self.org_id, shop=self.shop, jd_invoice=jd_invoice)
            case PlatformApplySyncMethod.PDD:
                pdd_invoice_query = PddInvoiceApplicationQuery(
                    access_token=unwrap_optional(self.shop.get_pdd_erp_grant_record()).access_token,
                    order_sn=self.platform_apply_id,
                    page_size=1,
                )
                pdd_invoice_result = pdd_invoice_query.execute()
                if pdd_invoice_result.is_err():
                    return Err(pdd_invoice_result.unwrap_err())
                pdd_invoice_response = pdd_invoice_result.unwrap()
                if not pdd_invoice_response.invoice_application_query_response.invoice_application_list:
                    return Err(Exception("未找到发票申请信息"))
                pdd_invoice = pdd_invoice_response.invoice_application_query_response.invoice_application_list[0]
                context = WorkflowContext.PddPlatformApply(org_id=self.org_id, shop=self.shop, pdd_invoice=pdd_invoice)
            case _:
                raise NotImplementedError
        PlatformApplyInfoModule(context).execute()
        with in_transaction():
            self.platform_apply_info = context.pb_workflow.platform_apply_info
        return Ok(None)

    def edit(self, view, user_info=anonymous_user_info):
        """更新发票申请

        Args:
            view (pb_InvoiceWorkflow.EditableView): 提交的发票申请数据
            user_info (pb_UserInfo): 操作人信息
        """
        from robot_processor.invoice.workflow.service import InvoiceRemarkBroker

        diff = {}
        if self.invoice_type != (new_invoice_type := InvoiceType(view.invoice_type)):  # type: ignore[call-arg]
            diff["invoice_type"] = dict(old=self.invoice_type, new=new_invoice_type)
            self.invoice_type = new_invoice_type
        if self.issuing_type != (new_issuing_type := IssuingType(view.issuing_type)):  # type: ignore[call-arg]
            diff["issuing_type"] = dict(old=self.issuing_type, new=new_issuing_type)
            self.issuing_type = new_issuing_type
        if self.issuing_type is IssuingType.RED:
            if not view.HasField("original_workflow_id"):
                raise PermissionError("红票申请必须指定原发票流程 id")
            self.original_workflow_id = view.original_workflow_id.value
        if self.seller_name != view.seller_name:
            diff["seller_name"] = dict(old=self.seller_name, new=view.seller_name)
            self.seller_name = view.seller_name
        if self.seller_credit_id != view.seller_credit_id:
            diff["seller_credit_id"] = dict(old=self.seller_credit_id, new=view.seller_credit_id)
            self.seller_credit_id = view.seller_credit_id
        if self.show_seller_address_phone != view.show_seller_address_phone:
            diff["show_seller_address_phone"] = dict(
                old=self.show_seller_address_phone, new=view.show_seller_address_phone
            )
            self.show_seller_address_phone = view.show_seller_address_phone
        new_seller_address = view.seller_address.value if view.HasField("seller_address") else None
        if self.seller_address != new_seller_address:
            diff["seller_address"] = dict(old=self.seller_address, new=new_seller_address)
            self.seller_address = new_seller_address
        new_seller_phone = view.seller_phone.value if view.HasField("seller_phone") else None
        if self.seller_phone != new_seller_phone:
            diff["seller_phone"] = dict(old=self.seller_phone, new=new_seller_phone)
            self.seller_phone = new_seller_phone
        if self.show_seller_bank != view.show_seller_bank:
            diff["show_seller_bank"] = dict(old=self.show_seller_bank, new=view.show_seller_bank)
            self.show_seller_bank = view.show_seller_bank
        new_seller_bank = view.seller_bank.value if view.HasField("seller_bank") else None
        if self.seller_bank != new_seller_bank:
            diff["seller_bank"] = dict(old=self.seller_bank, new=new_seller_bank)
            self.seller_bank = new_seller_bank
        new_seller_bank_account = view.seller_bank_account.value if view.HasField("seller_bank_account") else None
        if self.seller_bank_account != new_seller_bank_account:
            diff["seller_bank_account"] = dict(old=self.seller_bank_account, new=new_seller_bank_account)
            self.seller_bank_account = new_seller_bank_account
        if self.buyer_type != (new_buyer_type := BuyerType(view.buyer_type)):
            diff["buyer_type"] = dict(old=self.buyer_type, new=new_buyer_type)
            self.buyer_type = new_buyer_type
        if self.buyer_name != view.buyer_name:
            diff["buyer_name"] = dict(old=self.buyer_name, new=view.buyer_name)
            self.buyer_name = view.buyer_name
        if self.show_buyer_address_phone != view.show_buyer_address_phone:
            diff["show_buyer_address_phone"] = dict(
                old=self.show_buyer_address_phone, new=view.show_buyer_address_phone
            )
            self.show_buyer_address_phone = view.show_buyer_address_phone
        new_buyer_credit_id = view.buyer_credit_id.value if view.HasField("buyer_credit_id") else None
        if self.buyer_credit_id != new_buyer_credit_id:
            diff["buyer_credit_id"] = dict(old=self.buyer_credit_id, new=new_buyer_credit_id)
            self.buyer_credit_id = new_buyer_credit_id
        new_buyer_address = view.buyer_address.value if view.HasField("buyer_address") else None
        if self.buyer_address != new_buyer_address:
            diff["buyer_address"] = dict(old=self.buyer_address, new=new_buyer_address)
            self.buyer_address = new_buyer_address
        new_buyer_phone = view.buyer_phone.value if view.HasField("buyer_phone") else None
        if self.buyer_phone != new_buyer_phone:
            diff["buyer_phone"] = dict(old=self.buyer_phone, new=new_buyer_phone)
            self.buyer_phone = new_buyer_phone
        if self.show_buyer_bank != view.show_buyer_bank:
            diff["show_buyer_bank"] = dict(old=self.show_buyer_bank, new=view.show_buyer_bank)
            self.show_buyer_bank = view.show_buyer_bank
        new_buyer_bank = view.buyer_bank.value if view.HasField("buyer_bank") else None
        if self.buyer_bank != new_buyer_bank:
            diff["buyer_bank"] = dict(old=self.buyer_bank, new=new_buyer_bank)
            self.buyer_bank = new_buyer_bank
        new_buyer_bank_account = view.buyer_bank_account.value if view.HasField("buyer_bank_account") else None
        if self.buyer_bank_account != new_buyer_bank_account:
            diff["buyer_bank_account"] = dict(old=self.buyer_bank_account, new=new_buyer_bank_account)
            self.buyer_bank_account = new_buyer_bank_account
        new_remark_template = message_to_dict(view.remark_template) if view.HasField("remark_template") else None
        if self.remark_template != new_remark_template:
            diff["remark_template"] = dict(old=self.remark_template, new=new_remark_template)
            self.remark_template = new_remark_template
        new_remark = InvoiceRemarkBroker(self).render_remark_template()
        if self.remark != new_remark:
            diff["remark"] = dict(old=self.remark, new=new_remark)
            self.remark = new_remark
        if self.is_tax_included != view.is_tax_included:
            diff["is_tax_included"] = dict(old=self.is_tax_included, new=view.is_tax_included)
            self.is_tax_included = view.is_tax_included
        if issuing_items_diff := self.set_issuing_items(view.issuing_items):
            diff["issuing_items"] = issuing_items_diff
        new_notification_info = message_to_dict(view.notification_info)
        if self.notification_info != new_notification_info:
            diff["notification_info"] = dict(old=self.notification_info, new=new_notification_info)
            self.notification_info = new_notification_info
        if self.id is not None:  # 创建的日志由 create 负责
            self.log_operation(user_info, dict(action="edit", diff=diff))
        return self

    @classmethod
    @in_transaction()
    def create(
        cls, org_id: int, user_info: pb_UserInfo, view: pb_InvoiceWorkflow.EditableView, with_submit=True
    ) -> "InvoiceWorkflow":
        self = cls(
            org_id=org_id,
            serial_number="",
            state=cls.State.DRAFT,
            source=InvoiceWorkflowSource(view.source),  # type: ignore[call-arg]
        )
        if view.HasField("trade_info"):
            self.trade_info = view.trade_info
        if view.HasField("platform_apply_info"):
            self.platform_apply_info = view.platform_apply_info
        if view.HasField("business_order_job_info"):
            self.business_order_job_info = view.business_order_job_info
        if view.HasField("batch_id"):
            self.batch_id = view.batch_id.value
        self.set_applicant_info(user_info)
        self.edit(view)
        db.session.add(self)
        db.session.flush()
        SerialNumber.init_workflow(self)

        if with_submit:
            try:
                self.submit_without_edit()
            except Exception as e:
                logger.warning(f"自动提交发票申请失败 {e}")

        self.log_operation(user_info, dict(action="create", init_data=message_to_dict(view)))
        return self

    @classmethod
    @as_result(Exception)
    def safe_create(cls, org_id: int, user_info: pb_UserInfo, view: pb_InvoiceWorkflow.EditableView, with_submit=True):
        return cls.create(org_id, user_info, view, with_submit)

    @in_transaction()
    def submit(self, view: pb_InvoiceWorkflow, user_info=anonymous_user_info):
        """提交发票申请流程，进入审批流程"""
        return self.edit(view, user_info).submit_without_edit(user_info)

    @in_transaction()
    def submit_without_edit(self, user_info=anonymous_user_info):
        """提交发票申请流程，进入审批流程"""
        if self.state not in InvoiceWorkflowTransition.get_action_valid_state(InvoiceWorkflow.Action.SUBMIT):
            raise PermissionError(f"当前状态 {self.state.name} 不支持提交")
        if not self.issuing_items_passed:
            raise ValueError("发票明细未完善，不支持提交审批/开票")
        init_approval = self.get_approval_broker().init_approval().expect("InvoiceWorkflow.submit_without_edit")
        diff = dict(state=dict(old=self.state.name))
        if init_approval.need_approval:
            diff["state"]["new"] = InvoiceWorkflow.State.IN_REVIEW.name
            self.state = InvoiceWorkflow.State.IN_REVIEW
        else:
            diff["state"]["new"] = InvoiceWorkflow.State.PENDING_INVOICING.name
            self.state = InvoiceWorkflow.State.PENDING_INVOICING
        self.updated_at = datetime.now()
        self.log_operation(user_info, dict(action="submit without edit", diff=diff))
        return self

    @in_transaction()
    def save(self, view, user_info=anonymous_user_info):
        if self.state not in InvoiceWorkflowTransition.get_action_valid_state(InvoiceWorkflow.Action.SAVE_DRAFT):
            raise PermissionError(f"当前状态 {self.state.name} 不支持保存草稿")
        self.edit(view, user_info)
        self.state = InvoiceWorkflow.State.DRAFT
        self.updated_at = datetime.now()
        return self

    @in_transaction()
    def revoke(self, user_info=anonymous_user_info):
        if self.state not in InvoiceWorkflowTransition.get_action_valid_state(InvoiceWorkflow.Action.REVOKE):
            raise PermissionError(f"当前状态 {self.state.name} 不支持撤回")
        diff = dict(state=dict(old=self.state.name, new=self.State.DRAFT.name))
        self.get_approval_broker().close()
        self.state = InvoiceWorkflow.State.DRAFT
        if request := self.processing_invoice_request():
            diff["request"] = dict(
                id=request.id, state=dict(old=request.state.name, new=InvoiceRequest.State.CLOSED.name)
            )
            request.state = InvoiceRequest.State.CLOSED
            InvoiceIssueQueue.close(self.org_id, request.id)
        self.updated_at = datetime.now()
        self.log_operation(user_info, dict(action="revoke", diff=diff))
        return self

    @in_transaction()
    def close(self, user_info=anonymous_user_info):
        if self.state not in InvoiceWorkflowTransition.get_action_valid_state(InvoiceWorkflow.Action.CLOSE):
            raise PermissionError(f"当前状态 {self.state.name} 不支持关闭")
        diff = dict(state=dict(old=self.state.name, new=self.State.CLOSED.name))
        self.get_approval_broker().close()
        self.state = InvoiceWorkflow.State.CLOSED
        if request := self.processing_invoice_request():
            diff["request"] = dict(
                id=request.id, state=dict(old=request.state.name, new=InvoiceRequest.State.CLOSED.name)
            )
            request.state = InvoiceRequest.State.CLOSED
            InvoiceIssueQueue.close(self.org_id, request.id)
        self.updated_at = datetime.now()
        self.log_operation(user_info, dict(action="close", diff=diff))
        return self

    @in_transaction()
    def approve(self, user_info=anonymous_user_info):
        if self.state not in InvoiceWorkflowTransition.get_action_valid_state(InvoiceWorkflow.Action.APPROVE):
            raise PermissionError(f"当前状态 {self.state.name} 不支持审批")
        self.state = InvoiceWorkflow.State.PENDING_INVOICING
        self.updated_at = datetime.now()
        self.log_operation(
            user_info,
            dict(action="approve", diff=dict(state=dict(old=self.state.name, new=self.State.PENDING_INVOICING.name))),
        )
        return self

    @in_transaction()
    def reject(self, user_info=anonymous_user_info):
        if self.state not in InvoiceWorkflowTransition.get_action_valid_state(InvoiceWorkflow.Action.REJECT):
            raise PermissionError(f"当前状态 {self.state.name} 不支持拒绝")
        self.state = InvoiceWorkflow.State.REJECTED
        self.updated_at = datetime.now()
        self.log_operation(
            user_info, dict(action="reject", diff=dict(state=dict(old=self.state.name, new=self.State.REJECTED.name)))
        )
        return self

    @in_transaction()
    def mark_queued(self, user_info=anonymous_user_info):
        request: InvoiceRequest = unwrap_optional(self.processing_invoice_request())
        self.log_operation(
            user_info,
            dict(
                action="issue",
                diff=dict(
                    state=dict(old=self.state.name, new=self.State.PENDING_INVOICING.name), request=dict(id=request.id)
                ),
            ),
        )
        self.state = InvoiceWorkflow.State.INVOICING
        self.updated_at = datetime.now()
        request.state = InvoiceRequest.State.QUEUED
        return self

    @in_transaction()
    def mark_invoicing(self, user_info=anonymous_user_info):
        request: InvoiceRequest = unwrap_optional(self.processing_invoice_request())
        self.log_operation(
            user_info,
            dict(
                action="issue",
                diff=dict(state=dict(old=self.state.name, new=self.State.INVOICING.name), request=dict(id=request.id)),
            ),
        )
        self.state = InvoiceWorkflow.State.INVOICING
        self.updated_at = datetime.now()
        request.state = InvoiceRequest.State.INVOICING
        return self

    @in_transaction()
    def mark_invoicing_timeout(self, user_info=anonymous_user_info):
        """开票超时未获取结果"""
        request: InvoiceRequest = unwrap_optional(self.processing_invoice_request())
        self.log_operation(
            user_info,
            dict(
                action="invoice timeout",
                diff=dict(state=dict(old=self.state.name, new=self.State.INVOICING_TIMEOUT.name)),
                request=dict(id=request.id, state=dict(old=request.state.name, new=InvoiceRequest.State.FAILED.name)),
            ),
        )
        self.state = InvoiceWorkflow.State.INVOICING_TIMEOUT
        self.issue_failed_reason = "开票超时"
        self.updated_at = datetime.now()
        request.state = InvoiceRequest.State.FAILED
        request.failed_reason = "开票超时"
        return self

    @in_transaction()
    def mark_invoiced_waiting_receipt(self, user_info=anonymous_user_info):
        """开票成功，仅标记 request 状态成功"""
        request: InvoiceRequest = unwrap_optional(self.processing_invoice_request())
        self.log_operation(
            user_info,
            dict(
                action="invoiced waiting receipt",
                diff=dict(
                    request=dict(
                        id=request.id,
                        state=dict(old=request.state.name, new=InvoiceRequest.State.SUCCEED.name),
                    )
                ),
            ),
        )
        request.state = InvoiceRequest.State.SUCCEED
        return self

    @in_transaction()
    def mark_invoiced_without_receipt(self, user_info=anonymous_user_info):
        """开票成功，获取发票文件失败"""
        request: InvoiceRequest = unwrap_optional(self.processing_invoice_request())
        self.log_operation(
            user_info,
            dict(
                action="invoiced without receipt",
                diff=dict(state=dict(old=self.state.name, new=self.State.INVOICED_WITHOUT_RECEIPT.name)),
                request=dict(
                    id=request.id,
                    state=dict(old=request.state.name, new=InvoiceRequest.State.SUCCEED.name),
                ),
            ),
        )
        self.state = InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT
        self.updated_at = datetime.now()
        return self

    @in_transaction()
    def mark_fetch_issued_failed(self, reason, user_info=anonymous_user_info):
        self.log_operation(
            user_info,
            dict(
                action="fetch issued",
                diff=dict(
                    state=dict(old=self.state.name, new=self.State.INVOICED_WITHOUT_RECEIPT.name),
                    reason=dict(old=self.issue_failed_reason, new=str(reason)),
                ),
            ),
        )
        self.state = InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT
        self.issue_failed_reason = str(reason)
        self.updated_at = datetime.now()
        return self

    @in_transaction()
    def mark_invoice_failed(self, reason, user_info=anonymous_user_info):
        """开票失败"""
        reason = str(reason)
        request: InvoiceRequest = unwrap_optional(self.processing_invoice_request())
        self.log_operation(
            user_info,
            dict(
                action="invoice failed",
                diff=dict(
                    state=dict(old=self.state.name, new=self.State.INVOICE_FAILED.name),
                    issue_failed_reason=dict(old=self.issue_failed_reason, new=reason),
                    request=dict(
                        id=request.id, state=dict(old=request.state.name, new=InvoiceRequest.State.FAILED.name)
                    ),
                ),
            ),
        )
        self.issue_failed_reason = reason
        self.state = InvoiceWorkflow.State.INVOICE_FAILED
        self.updated_at = datetime.now()
        request.state = InvoiceRequest.State.FAILED
        request.failed_reason = reason
        return self

    @in_transaction()
    def mark_invoiced(self, user_info=anonymous_user_info):
        """已开票

        Args:
            user_info (pb_UserInfo): 操作人信息
        """
        request = self.processing_invoice_request()
        changes: dict[str, Any] = dict(
            action="invoiced", diff=dict(state=dict(old=self.state.name, new=self.State.INVOICED.name))
        )
        if request:
            changes["diff"]["request"] = dict(
                id=request.id, state=dict(old=request.state.name, new=InvoiceRequest.State.SUCCEED.name)
            )
        self.log_operation(user_info, changes)
        self.state = InvoiceWorkflow.State.INVOICED
        self.updated_at = datetime.now()
        if request:
            request.state = InvoiceRequest.State.SUCCEED
        return self

    def set_issued(self, invoice_issued, user_info=anonymous_user_info):
        """

        Args:
            invoice_issued (InvoiceIssued): 已开票信息
            user_info (pb_UserInfo): 操作人信息
        """
        self.log_operation(
            user_info,
            dict(
                action="set issued",
                diff=dict(
                    invoice_number=dict(old=self.invoice_number, new=invoice_issued.invoice_number),
                    issuing_time=dict(old=str(self.issuing_time), new=str(invoice_issued.issuing_time)),
                ),
            ),
        )
        self.invoice_number = invoice_issued.invoice_number
        self.issuing_time = invoice_issued.issuing_time
        # 开票成功但是未获取到发票文件时，展示为进行中的状态
        # 仅在获取发票的任务明确失败时，才标记为 INVOICED_WITHOUT_RECEIPT
        # self.set_state(self.State.INVOICED_WITHOUT_RECEIPT)
        if self.issuing_type is IssuingType.RED:
            original = db.session.get(InvoiceWorkflow, self.original_workflow_id)
            original.mark_rushed()  # type: ignore[union-attr]
        if self.source == InvoiceWorkflowSource.PLATFORM_APPLY:
            self.platform_apply_post_back_state = InvoiceWorkflow.PostBackState.PENDING
        else:
            self.platform_apply_post_back_state = InvoiceWorkflow.PostBackState.NOT_SUPPORTED
        return self

    @in_transaction()
    def mark_rushed(self):
        self.reversing_status = ReversingStatus.FULLY_REVERSED
        self.invoice_issued.reversing_status = ReversingStatus.FULLY_REVERSED

    @in_transaction()
    def set_platform_apply_post_back_posting(self):
        self.platform_apply_post_back_state = InvoiceWorkflow.PostBackState.POSTING

    @in_transaction()
    def set_platform_apply_post_back_state(
        self, succeed: bool, raw, failed_reason: str | None = None, method: PostBackMethod | None = None
    ):
        method = method or self.get_default_post_back_method()
        self.platform_apply_post_back_result = dict(
            method=method, succeed=succeed, raw=raw, failed_reason=failed_reason
        )
        if succeed:
            self.platform_apply_post_back_state = InvoiceWorkflow.PostBackState.POSTED
        else:
            self.platform_apply_post_back_state = InvoiceWorkflow.PostBackState.POST_FAILED
        return self

    @classmethod
    def get_action_support_state(cls, action):
        return InvoiceWorkflowTransition.get_action_valid_state(action)

    def get_default_post_back_method(self):
        return {
            PlatformApplySyncMethod.JD: PostBackMethod.JD_PLATFORM_APPLY,
            PlatformApplySyncMethod.QN: PostBackMethod.QN_PLATFORM_APPLY,
            PlatformApplySyncMethod.WDT: PostBackMethod.WDT_PLATFORM_APPLY,
            PlatformApplySyncMethod.DOUDIAN: PostBackMethod.DOUDIAN_PLATFORM_APPLY,
            PlatformApplySyncMethod.PDD: PostBackMethod.PDD_PLATFORM_APPLY,
        }[self.platform_apply_sync_method]

    def get_applicant_info(self):
        return pb_UserInfo(
            user=pb_UserInfo.User(
                id=self.applicant_id,
                type=self.applicant_type.value,
                nick=self.applicant_name,
            ),
            groups=[pb_UserInfo.Group(**group) for group in self.applicant_groups],
        )

    def set_applicant_info(self, applicant_info: pb_UserInfo):
        if self.applicant_id is not None:
            raise PermissionError("申请人信息不可编辑")
        self.applicant_id = applicant_info.user.id
        self.applicant_type = UserType(applicant_info.user.type)
        self.applicant_name = applicant_info.user.nick
        self.applicant_groups = list(map(message_to_dict, applicant_info.groups))

    def get_issuing_items(self):
        return [ParseDict(item, pb_InvoiceWorkflow.IssuingItem()) for item in (self.issuing_items or [])]

    def set_issuing_items(self, issuing_items: list[pb_InvoiceWorkflow.IssuingItem]):
        from robot_processor.invoice.goods.services import IssuingItemService
        from robot_processor.invoice.workflow.service import IssuingItemTitleBroker

        diff = [item for item in self.issuing_items or []]
        IssuingItemTitleBroker.batch_render_pb_issuing_items(IssuingItemService(self.org_id), issuing_items)
        self.issuing_items = [message_to_dict(item) for item in issuing_items]
        return diff

    def get_actions(self, user_info):
        user = user_info.user
        actions = []
        submit = pb_InvoiceWorkflow.SupportAction(action=self.Action.SUBMIT.pb_value)
        if self.state in [self.State.DRAFT, self.State.REJECTED]:
            if self.applicant_type == UserType.SYSTEM:
                submit.valid = True
            elif (self.applicant_id, self.applicant_type) != (user.id, user.type):
                submit.valid = False
                submit.reason = "只有创建人才可以编辑并提交"
            else:
                submit.valid = True
        else:
            submit.valid = False
            submit.reason = "只有草稿状态或被拒绝状态的发票申请流程可以提交"
        actions.append(submit)

        save_draft = pb_InvoiceWorkflow.SupportAction(action=InvoiceWorkflow.Action.SAVE_DRAFT.pb_value)
        if self.state in [InvoiceWorkflow.State.DRAFT]:
            if self.applicant_type == UserType.SYSTEM:
                submit.valid = True
            elif (self.applicant_id, self.applicant_type) != (user.id, user.type):
                save_draft.valid = False
                save_draft.reason = "只有创建人才可以编辑发票申请数据"
            else:
                save_draft.valid = True
        else:
            save_draft.valid = False
            save_draft.reason = "只有草稿状态的发票申请流程可以保存草稿"
        actions.append(save_draft)

        approve = pb_InvoiceWorkflow.SupportAction(action=self.Action.APPROVE.pb_value)
        reject = pb_InvoiceWorkflow.SupportAction(action=self.Action.REJECT.pb_value)
        if self.state == self.State.IN_REVIEW:
            if self.get_approval_broker().get_in_review_approval_node_reviewer(user_info.user).is_ok():
                approve.valid = reject.valid = True
            else:
                approve.valid = reject.valid = False
                approve.reason = reject.reason = "当前用户不是审批人"
        else:
            approve.valid = reject.valid = False
            approve.reason = reject.reason = "只有审批中的发票申请流程可以审批"
        actions.append(approve)
        actions.append(reject)

        issue = pb_InvoiceWorkflow.SupportAction(action=self.Action.ISSUE.pb_value)
        if self.state == self.State.PENDING_INVOICING:
            if all(item.tax_code != "" for item in self.get_issuing_items()):
                issue.valid = True
            else:
                issue.valid = False
                issue.reason = "发票明细缺少税务信息"
        elif self.state in [self.State.INVOICE_FAILED, self.State.INVOICING_TIMEOUT]:
            issue.valid = True
        else:
            issue.valid = False
            issue.reason = "只有待开票状态的发票申请流程可以开票"
        actions.append(issue)

        close = pb_InvoiceWorkflow.SupportAction(action=self.Action.CLOSE.pb_value)
        if self.state not in [
            self.State.DRAFT,
            self.State.IN_REVIEW,
            self.State.REJECTED,
            self.State.PENDING_INVOICING,
            self.State.INVOICE_FAILED,
            self.State.INVOICING_TIMEOUT,
        ]:
            close.valid = False
            close.reason = "只有待提审、审批中、被拒绝、待开票、开票失败状态的发票申请流程可以关闭"
        else:
            close.valid = True
        actions.append(close)

        revoke = pb_InvoiceWorkflow.SupportAction(action=self.Action.REVOKE.pb_value)
        if self.state not in [
            self.State.IN_REVIEW,
            self.State.REJECTED,
            self.State.PENDING_INVOICING,
            self.State.INVOICE_FAILED,
            self.State.INVOICING_TIMEOUT,
        ]:
            revoke.valid = False
            revoke.reason = "只有审批中、被拒绝、待开票、开票失败状态的发票申请流程可以撤回"
        else:
            revoke.valid = True
        actions.append(revoke)

        rush_red = pb_InvoiceWorkflow.SupportAction(action=self.Action.RUSH_RED.pb_value)
        if (
            self.issuing_type is IssuingType.BLUE
            and self.state is InvoiceWorkflow.State.INVOICED
            and self.reversing_status is ReversingStatus.NOT_REVERSED
        ):
            rush_red.valid = True
        else:
            rush_red.valid = False
            rush_red.reason = "只有开票成功状态的蓝字发票申请流程可以冲红"
        actions.append(rush_red)

        refresh_receipt = pb_InvoiceWorkflow.SupportAction(
            action=InvoiceWorkflow.Action.REFRESH_INVOICE_RECEIPT.pb_value
        )
        if self.state is InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT:
            refresh_receipt.valid = True
        else:
            refresh_receipt.valid = False
            refresh_receipt.reason = "只有已开票未获取到发票凭证时需要重新获取"
        actions.append(refresh_receipt)

        manual_invoice_number = pb_InvoiceWorkflow.SupportAction(
            action=InvoiceWorkflow.Action.MANUAL_INVOICE_NUMBER.pb_value
        )
        if self.state is InvoiceWorkflow.State.INVOICING_TIMEOUT:
            manual_invoice_number.valid = True
        else:
            manual_invoice_number.valid = False
            manual_invoice_number.reason = "仅开票超时需要人工判断开票结果"
        actions.append(manual_invoice_number)

        view_issued = pb_InvoiceWorkflow.SupportAction(action=self.Action.VIEW_ISSUED.pb_value)
        if self.state is InvoiceWorkflow.State.INVOICED:
            view_issued.valid = True
        else:
            view_issued.valid = False
            view_issued.reason = "只有开票成功状态的发票申请流程可以查看已开票信息"
        actions.append(view_issued)

        post_back = pb_InvoiceWorkflow.SupportAction(action=InvoiceWorkflow.Action.POST_BACK.pb_value)
        if self.state is not InvoiceWorkflow.State.INVOICED:
            post_back.valid = False
            post_back.reason = "只有开票成功状态的发票申请流程可以回传"
        elif self.source != InvoiceWorkflow.Source.PLATFORM_APPLY:
            post_back.valid = False
            post_back.reason = "不是从平台发票申请同步的开票申请流程，不支持回传"
        elif self.platform_apply_post_back_state not in [
            InvoiceWorkflow.PostBackState.POST_FAILED,
            InvoiceWorkflow.PostBackState.PENDING,
        ]:
            post_back.valid = False
            platform_apply_post_back_state = (
                self.platform_apply_post_back_state or InvoiceWorkflow.PostBackState.POST_BACK_STATE_UNSPECIFIED
            )
            post_back.reason = f"发票回传状态不支持 {platform_apply_post_back_state.label}"
        else:
            post_back.valid = True
        actions.append(post_back)

        notify = pb_InvoiceWorkflow.SupportAction(action=InvoiceWorkflow.Action.NOTIFY.pb_value)
        if self.state != InvoiceWorkflow.State.INVOICED:
            notify.valid = False
            notify.reason = "只有开票成功的状态可以下发通知"
        else:
            notify.valid = True
        actions.append(notify)

        return actions

    @classmethod
    def get_by_filter(cls, org_id, workflow_filter):
        from robot_processor.symbol_table.services import FilterServicer

        return cls.query.filter(
            sql_eq(cls.org_id, org_id), FilterServicer.to_sql_criteria(workflow_filter, cls.FilterContext.get_column)
        ).all()

    @classmethod
    def get_by_filter_and_paginate(cls, org_id, workflow_filter: pb_Filter, paginate):
        """根据条件过滤并分页查询

        Args:
            org_id (int): 租户 id
            workflow_filter (pb_Filter): 过滤条件
            paginate (common_pb2.PaginateConfig): 分页条件
        Returns:
            list[InvoiceWorkflow]: 发票申请流程列表
        """
        from robot_processor.symbol_table.services import FilterServicer

        stmt = sa.select(cls).where(sql_eq(cls.org_id, org_id))
        stmt = stmt.where(FilterServicer.to_sql_criteria(workflow_filter, cls.FilterContext.get_column))
        count_stmt = stmt.with_only_columns(sa.func.count())
        paginate.total.value = db.session.execute(count_stmt).scalar()
        if paginate.total.value == 0:
            return []

        if paginate.HasField("order_by"):
            stmt = stmt.order_by(sa.text(paginate.order_by.value))
        page, per_page = paginate.page.value, paginate.per_page.value
        stmt = stmt.limit(per_page).offset(per_page * (page - 1))
        result = db.session.execute(stmt)
        items = result.scalars().unique().all()

        return items

    @classmethod
    def get_by_id(cls, workflow_id):
        """根据 id 查询发票申请流程

        Args:
            workflow_id (int): 发票申请流程 id
        Returns:
            InvoiceWorkflow: 发票申请流程
        """
        stmt = sa.select(cls).where(cls.id == workflow_id)
        result = db.session.execute(stmt)
        return result.scalar_one_or_none()

    @classmethod
    def get_and_check_workflow(cls, org_id, workflow_id):
        """根据 id 查询发票申请流程并检查

        Args:
            org_id (int): 租户 id
            workflow_id (int): 发票申请流程 id
        Returns:
            Ok[InvoiceWorkflow] | Err[Exception]: 成功返回发票申请流程，失败返回异常
        """
        if not workflow_id:
            return Err(ValueError("发票申请流程 id 不能为空"))
        if not (workflow := cls.get_by_id(workflow_id)):
            return Err(ValueError("发票申请流程不存在"))
        if workflow.org_id != org_id:
            return Err(PermissionError("无权操作该发票申请流程"))
        return Ok(workflow)

    def get_approval_broker(self):
        from .service import ApprovalBroker

        return ApprovalBroker(self)

    class FilterContext(FilterContextMixin):
        __name__ = "invoice_workflow"

        serial_number: str = FilterContextField("发票流水号")
        invoice_number: str = FilterContextField("发票号码")
        created_at: datetime = FilterContextField("申请时间")
        updated_at: datetime = FilterContextField("最近更新时间")
        applicant: named_typespec.resource.LeyanUser = FilterContextField(
            title="申请人",
            get_enum_option=named_typespec.resource.LeyanUser.get_enum_option,
        )
        reviewer: named_typespec.resource.LeyanUser = FilterContextField(
            title="当前审批人",
            get_enum_option=named_typespec.resource.LeyanUser.get_enum_option,
        )
        seller_name: str = FilterContextField("销售方名称")
        seller_credit_id: str = FilterContextField("销售方纳税人识别号")
        buyer_name: str = FilterContextField("购买方名称")
        buyer_credit_id: str = FilterContextField("购买方纳税人识别号")
        invoice_type: str = FilterContextField("发票类型", InvoiceType.get_enum_option)
        issuing_type: str = FilterContextField("开票类型", IssuingType.get_enum_option)
        source: str = FilterContextField("开票来源", InvoiceWorkflowSource.get_enum_option)
        state: str = FilterContextField("开票状态", lambda _: InvoiceWorkflow.State.get_enum_option())
        tid: str = FilterContextField("订单号")
        batch_id: int = FilterContextField("批量导入批次号")
        shop: named_typespec.resource.Shop = FilterContextField(
            title="店铺",
            get_enum_option=named_typespec.resource.Shop.get_enum_option,
        )
        platform_apply_post_back_state: str = FilterContextField(
            title="回传状态",
            get_enum_option=lambda _: InvoiceWorkflow.PostBackState.get_enum_option(),
        )

        @classmethod
        def get_column(cls, path):
            *_, field = path.split(".", 1)
            if field in {
                "serial_number",
                "invoice_number",
                "created_at",
                "updated_at",
                "seller_name",
                "seller_credit_id",
                "buyer_name",
                "buyer_credit_id",
                "invoice_type",
                "issuing_type",
                "source",
                "state",
                "tid",
                "oid",
                "platform_apply_post_back_state",
                "batch_id",
                # "shop"  FIXME 考虑如何处理复杂的字段
            }:
                return getattr(InvoiceWorkflow, field)
            raise ValueError(f"未知的字段 {path}")

        @classmethod
        def reviewer_condition(cls, condition, stmt):
            return (
                stmt.join(InvoiceApproval, InvoiceApproval.workflow_id == InvoiceWorkflow.id)
                .join(
                    InvoiceApprovalNode,
                    InvoiceApprovalNode.approval_id == InvoiceApproval.id,
                )
                .join(
                    InvoiceApprovalNodeReviewer,
                    InvoiceApprovalNodeReviewer.approval_node_id == InvoiceApprovalNode.id,
                )
                .where(
                    sql_eq(InvoiceWorkflow.state, InvoiceWorkflow.State.IN_REVIEW),
                    sql_eq(InvoiceApproval.state, ApprovalState.IN_REVIEW),
                )
            )


class InvoiceWorkflowTransition:
    @dataclass
    class _:
        from_state: InvoiceWorkflow.State | None
        to_state: InvoiceWorkflow.State
        action: InvoiceWorkflow.Action
        desc: str | None = None

        def __hash__(self):
            return hash((self.from_state, self.to_state, self.action))

    State: TypeAlias = InvoiceWorkflow.State
    Action: TypeAlias = InvoiceWorkflow.Action

    ROUTES = {
        _(None, State.IN_REVIEW, Action.SUBMIT, "发票创建并提交，进入审批状态"),
        _(None, State.PENDING_INVOICING, Action.SUBMIT, "发票创建并提交，无需审批，直接进入待开票状态"),
        _(None, State.DRAFT, Action.SAVE_DRAFT, "发票创建并保存草稿"),
        _(State.DRAFT, State.IN_REVIEW, Action.SUBMIT, "发票草稿提交，进入审批状态"),
        _(State.DRAFT, State.PENDING_INVOICING, Action.SUBMIT, "发票草稿提交，无需审批，直接进入待开票状态"),
        _(State.DRAFT, State.DRAFT, Action.SAVE_DRAFT, "发票草稿保存"),
        _(State.DRAFT, State.CLOSED, Action.CLOSE, "关闭发票申请"),
        _(State.IN_REVIEW, State.PENDING_INVOICING, Action.APPROVE, "审批通过，进入待开票状态"),
        _(State.IN_REVIEW, State.REJECTED, Action.REJECT, "审批拒绝"),
        _(State.IN_REVIEW, State.CLOSED, Action.CLOSE, "关闭发票申请"),
        _(State.IN_REVIEW, State.DRAFT, Action.REVOKE, "发票未审批时可以撤回到草稿箱状态"),
        _(State.REJECTED, State.CLOSED, Action.CLOSE, "关闭发票申请"),
        _(State.REJECTED, State.DRAFT, Action.REVOKE, "发票未审批时可以撤回到草稿箱状态"),
        _(State.REJECTED, State.DRAFT, Action.SAVE_DRAFT, "重新编辑开票申请"),
        _(State.REJECTED, State.IN_REVIEW, Action.SUBMIT, "重新提交开票申请"),
        _(State.REJECTED, State.PENDING_INVOICING, Action.SUBMIT, "重新提交开票申请"),
        _(State.PENDING_INVOICING, State.INVOICING, Action.ISSUE, "开票校验通过，进入开票中状态"),
        _(State.PENDING_INVOICING, State.INVOICE_FAILED, Action.ISSUE, "开票校验失败，进入开票失败状态"),
        _(State.PENDING_INVOICING, State.CLOSED, Action.CLOSE, "关闭发票申请"),
        _(State.PENDING_INVOICING, State.DRAFT, Action.REVOKE, "发票未开票时可以撤回到草稿箱状态"),
        _(State.INVOICING, State.INVOICED_WITHOUT_RECEIPT, Action.MARK_INVOICED, "客户端上报发票号"),
        _(State.INVOICING, State.INVOICED, Action.MARK_INVOICED, "客户端上传发票完整信息"),
        _(State.INVOICING, State.INVOICE_FAILED, Action.MARK_INVOICE_FAILED, "开票失败"),
        _(State.INVOICING, State.INVOICING_TIMEOUT, Action.MARK_INVOICE_FAILED, "开票超时未响应"),
        _(State.INVOICED_WITHOUT_RECEIPT, State.INVOICED, Action.REFRESH_INVOICE_RECEIPT, "获取发票凭证"),
        _(
            State.INVOICED_WITHOUT_RECEIPT,
            State.INVOICED_WITHOUT_RECEIPT,
            Action.REFRESH_INVOICE_RECEIPT,
            "获取发票凭证",
        ),
        _(State.INVOICED_WITHOUT_RECEIPT, State.INVOICED_WITHOUT_RECEIPT, Action.MARK_INVOICE_FAILED, "获取发票凭证"),
        _(State.INVOICING_TIMEOUT, State.INVOICED_WITHOUT_RECEIPT, Action.MANUAL_INVOICE_NUMBER, "手动补充发票号码"),
        _(State.INVOICING_TIMEOUT, State.DRAFT, Action.REVOKE, "发票未开票时可以撤回到草稿箱状态"),
        _(State.INVOICING_TIMEOUT, State.INVOICING, Action.ISSUE, "重新开票"),
        _(State.INVOICE_FAILED, State.DRAFT, Action.REVOKE, "发票未开票时可以撤回到草稿箱状态"),
        _(State.INVOICE_FAILED, State.INVOICING, Action.ISSUE, "开票失败后重新开票"),
        _(State.INVOICE_FAILED, State.CLOSED, Action.CLOSE, "关闭发票申请"),
    }

    @classmethod
    def get_action_valid_state(cls, action: InvoiceWorkflow.Action):
        return {route.from_state for route in cls.ROUTES if route.action == action}

    @classmethod
    def get_state_valid_transition_state(cls, state: InvoiceWorkflow.State):
        return {route.to_state for route in cls.ROUTES if route.from_state == state}

    @classmethod
    def ensure_valid_state_for_action(cls, workflow: InvoiceWorkflow, action: InvoiceWorkflow.Action):
        if workflow.state not in cls.get_action_valid_state(action):
            raise InvoiceWorkflowStateError(workflow)


@dataclass
class SerialNumber:
    day: date
    index: int

    pattern: ClassVar = re.compile(r"FP(?P<year>\d{4})(?P<month>\d{2})(?P<day>\d{2})(?P<index>\d{6})")

    @classmethod
    def parse(cls, serial_number_str: str):
        match = cls.pattern.match(serial_number_str)

        return cls(
            date(
                int(match.group("year")),
                int(match.group("month")),
                int(match.group("day")),
            ),
            int(match.group("index")),
        )

    @classmethod
    def init_workflow(cls, workflow: InvoiceWorkflow):
        """生成发票单号

        需要先找到当前日期最大的发票单号，然后在此基础上加 (id - last_id)
        发票单号的格式为 FP+日期+发票单量
        """
        assert not workflow.serial_number, "发票单号已存在"
        day = workflow.created_at.date()
        last = db.session.execute(
            sa.select(InvoiceWorkflow)
            .where(sa.func.date(InvoiceWorkflow.created_at) == day)
            .where(InvoiceWorkflow.serial_number != "")
            .options(Load(InvoiceWorkflow).load_only(InvoiceWorkflow.id, InvoiceWorkflow.serial_number, raiseload=True))
            .order_by(InvoiceWorkflow.id.desc())
            .limit(1)
        ).scalar_one_or_none()
        if last:
            serial_number = cls.parse(last.serial_number).move(workflow.id - last.id)
        else:
            serial_number = cls(day, 1)
        workflow.serial_number = serial_number.to_str()
        return serial_number

    def move(self, step: int):
        return replace(self, index=self.index + step)

    def to_str(self):
        return "FP{}{}".format(self.day.strftime("%Y%m%d"), str(self.index).zfill(6))


@ensure_mirror_of_pb_enum(pb_InvoiceApproval.ApprovalState)
class ApprovalState(IntEnum):
    APPROVAL_STATE_UNSPECIFIED = 0
    # 审批中
    IN_REVIEW = auto()
    # 审批通过
    APPROVED = auto()
    # 审批拒绝
    REJECTED = auto()
    # 审批关闭
    # 当发票申请流程被发起方撤回时，审批流程也会被关闭
    CLOSED = auto()


@ensure_mirror_of_pb_enum(pb_InvoiceApproval.ApproveAction)
class ApproveAction(IntEnum):
    APPROVE_ACTION_UNSPECIFIED = 0
    # 同意
    AGREE = 1
    # 拒绝
    REJECT = 2


# fmt: off
class InvoiceApproval(DbBaseModel):
    """发票申请审批流程"""
    __tablename__ = "invoice_approval"
    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    org_id = mapped_column(sa.Integer)
    workflow_id = mapped_column(sa.Integer, sa.ForeignKey("invoice_workflow.id"))
    template = mapped_column(sa.JSON)
    state = mapped_column(sa.Enum(ApprovalState), default=ApprovalState.IN_REVIEW)
    nodes = relationship(lambda: InvoiceApprovalNode, back_populates="approval")
    workflow = relationship(lambda: InvoiceWorkflow)

    def get_template(self):
        from robot_types.model.invoice.config_manager import ApprovalRuleset
        return deserialize(self.template, ApprovalRuleset.Routes)


class InvoiceApprovalNode(DbBaseModel):
    __tablename__ = "invoice_approval_node"

    @ensure_mirror_of_pb_enum(pb_InvoiceApproval.ApproveNodeStrategy)
    class Strategy(IntEnum):
        APPROVE_NODE_STRATEGY_UNSPECIFIED = 0
        # 任意一个审批人审批即可
        ANY = 1
        # 所有审批人审批通过
        ALL = 2

    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    approval_id = mapped_column(sa.Integer, sa.ForeignKey("invoice_approval.id"))
    strategy = mapped_column(sa.Enum(Strategy))
    state = mapped_column(sa.Enum(ApprovalState), default=ApprovalState.APPROVAL_STATE_UNSPECIFIED)
    reviewers = relationship(lambda: InvoiceApprovalNodeReviewer, back_populates="node")
    approval = relationship(lambda: InvoiceApproval, back_populates="nodes")


class InvoiceApprovalNodeReviewer(DbBaseModel):
    __tablename__ = "invoice_approval_node_reviewer"
    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    approval_node_id = mapped_column(sa.Integer, sa.ForeignKey("invoice_approval_node.id"))
    user_id = mapped_column(sa.Integer)
    user_type = mapped_column(sa.Enum(UserType))
    reviewed_at = mapped_column(sa.DateTime, nullable=True, default=None)
    action = mapped_column(sa.Enum(ApproveAction), default=ApproveAction.APPROVE_ACTION_UNSPECIFIED)
    node = relationship(lambda: InvoiceApprovalNode, back_populates="reviewers")


class InvoiceWorkflowRequest(DbBaseModel):
    __tablename__ = "invoice_workflow_request"
    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    workflow_id = mapped_column(sa.Integer, sa.ForeignKey("invoice_workflow.id"))
    request_id = mapped_column(sa.Integer, sa.ForeignKey("invoice_request.id"))
    workflow = relationship(lambda: InvoiceWorkflow)
    request = relationship(lambda: InvoiceRequest)

    @classmethod
    @in_transaction()
    def create(cls, workflow, request):
        self = cls(workflow=workflow, request=request)
        db.session.add(self)
        return self


class InvoiceRequest(DbBaseModel):
    __tablename__ = "invoice_request"

    @ensure_mirror_of_pb_enum(pb_InvoiceRequest.State)
    class State(IntEnum):
        INVOICING = 0
        QUEUED = 1
        FAILED = 2
        SUCCEED = 3
        CLOSED = 4

    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    org_id = mapped_column(sa.Integer, nullable=False)
    state = mapped_column(sa.Enum(State), nullable=False)
    invoicing_time = mapped_column(sa.DateTime)
    """发票基本信息"""
    invoice_type = mapped_column(sa.Enum(InvoiceType))
    issuing_type = mapped_column(sa.Enum(IssuingType))

    """蓝票信息"""
    """发票信息-销售方信息"""
    seller_name = mapped_column(sa.String(256), nullable=False)
    seller_credit_id = mapped_column(sa.String(20), nullable=False)
    show_seller_address_phone = mapped_column(sa.Boolean, default=False)
    seller_address = mapped_column(sa.String(256), nullable=True)
    seller_phone = mapped_column(sa.String(32), nullable=True)
    show_seller_bank = mapped_column(sa.Boolean, default=False)
    seller_bank = mapped_column(sa.String(256), nullable=True)
    seller_bank_account = mapped_column(sa.String(32), nullable=True)
    """发票信息-购买方信息"""
    buyer_type = mapped_column(sa.Enum(BuyerType), nullable=False)
    buyer_name = mapped_column(sa.String(256), nullable=False)
    buyer_credit_id = mapped_column(sa.String(20), nullable=True)
    show_buyer_address_phone = mapped_column(sa.Boolean, default=False)
    buyer_address = mapped_column(sa.String(256), nullable=True)
    buyer_phone = mapped_column(sa.String(32), nullable=True)
    show_buyer_bank = mapped_column(sa.Boolean, default=False)
    buyer_bank = mapped_column(sa.String(256), nullable=True)
    buyer_bank_account = mapped_column(sa.String(32), nullable=True)
    """发票信息-发票明细"""
    is_tax_included = mapped_column(sa.Boolean, nullable=False)
    issuing_items = mapped_column(sa.JSON(), nullable=False)
    """发票信息-额外信息"""
    remark = mapped_column(sa.Text(), nullable=True)

    """红票信息"""
    rush_red_invoice_number = mapped_column(sa.String(32))
    rush_red_issuing_time = mapped_column(sa.DateTime())
    rush_red_reason = mapped_column(sa.String(255))

    competent_tax_bureau = mapped_column(sa.String(256), nullable=False)
    account = mapped_column(sa.String(256), nullable=False)

    failed_reason = mapped_column(sa.Text, nullable=True)
    invoice_issued_id = mapped_column(sa.Integer, nullable=True)
    invoice_issued = relationship(
        lambda: InvoiceIssued,
        primaryjoin="foreign(InvoiceRequest.invoice_issued_id)==InvoiceIssued.id"
    )
    workflows = relationship(
        InvoiceWorkflow, secondary="invoice_workflow_request", viewonly=True,
        primaryjoin="foreign(InvoiceRequest.id)==invoice_workflow_request.c.request_id",
        secondaryjoin="foreign(InvoiceWorkflow.id)==invoice_workflow_request.c.workflow_id"
    )

    def prepare_workflows(self) -> list[InvoiceWorkflow]:
        for workflow in self.workflows:
            db.session.refresh(workflow, with_for_update=True)
        return self.workflows

    def to_issue_blue(self):
        issue_blue_data = dict(
            invoice_type=self.invoice_type.name,
            issuing_type=self.issuing_type.name,
            seller_name=self.seller_name,
            seller_credit_id=self.seller_credit_id,
            show_seller_address_phone=self.show_seller_address_phone,
            seller_address=self.seller_address,
            seller_phone=self.seller_phone,
            show_seller_bank=self.show_seller_bank,
            seller_bank=self.seller_bank,
            seller_bank_account=self.seller_bank_account,
            buyer_type=self.buyer_type.name,
            buyer_name=self.buyer_name,
            buyer_credit_id=self.buyer_credit_id,
            show_buyer_address_phone=self.show_buyer_address_phone,
            buyer_address=self.buyer_address,
            buyer_phone=self.buyer_phone,
            show_buyer_bank=self.show_buyer_bank,
            buyer_bank=self.buyer_bank,
            buyer_bank_account=self.buyer_bank_account,
            is_tax_included=self.is_tax_included,
            issuing_items=self.issuing_items,
            remark=self.remark
        )
        return issue_blue_data

    def to_issue_red(self):
        return dict(
            invoice_number=self.rush_red_invoice_number,
            issuing_time=self.rush_red_issuing_time.strftime("%Y-%m-%d %H:%M:%S"),
            reason=self.rush_red_reason,
        )

    @classmethod
    @as_result(Exception)
    @in_transaction()
    def create(
            cls, view: pb_InvoiceRequest, competent_tax_bureau: str, account: str,
            rush_red_reason: str | None = None, original_workflow_id: int | None = None
    ):
        self = cls(
            org_id=view.org_id,
            state=cls.State.QUEUED,
            invoicing_time=None,
            invoice_type=InvoiceType(view.invoice_type),  # type: ignore[call-arg]
            issuing_type=IssuingType(view.issuing_type),  # type: ignore[call-arg]
            seller_name=view.seller_name,
            seller_credit_id=view.seller_credit_id,
            show_seller_address_phone=view.show_seller_address_phone,
            show_seller_bank=view.show_seller_bank,
            buyer_type=BuyerType(view.buyer_type),
            buyer_name=view.buyer_name,
            show_buyer_address_phone=view.show_buyer_address_phone,
            show_buyer_bank=view.show_buyer_bank,
            is_tax_included=view.is_tax_included,
            issuing_items=[message_to_dict(item) for item in view.issuing_items],
            competent_tax_bureau=competent_tax_bureau,
            account=account,
        )
        if view.HasField("seller_address"):
            self.seller_address = view.seller_address.value
        if view.HasField("seller_phone"):
            self.seller_phone = view.seller_phone.value
        if view.HasField("seller_bank"):
            self.seller_bank = view.seller_bank.value
        if view.HasField("seller_bank_account"):
            self.seller_bank_account = view.seller_bank_account.value
        if view.HasField("buyer_credit_id"):
            self.buyer_credit_id = view.buyer_credit_id.value
        if view.HasField("buyer_address"):
            self.buyer_address = view.buyer_address.value
        if view.HasField("buyer_phone"):
            self.buyer_phone = view.buyer_phone.value
        if view.HasField("buyer_bank"):
            self.buyer_bank = view.buyer_bank.value
        if view.HasField("buyer_bank_account"):
            self.buyer_bank_account = view.buyer_bank_account.value
        if view.HasField("remark"):
            self.remark = view.remark.value
        if self.issuing_type is IssuingType.RED:
            assert original_workflow_id
            self.rush_red_reason = rush_red_reason
            original_workflow = unwrap_optional(db.session.get(InvoiceWorkflow, original_workflow_id))
            self.rush_red_invoice_number = original_workflow.invoice_number
            original_invoice_issued = db.session.execute(
                sa.select(InvoiceIssued)
                .select_from(InvoiceIssued)
                .join(InvoiceRequest, InvoiceRequest.invoice_issued_id == InvoiceIssued.id)
                .join(InvoiceWorkflowRequest, InvoiceWorkflowRequest.request_id == InvoiceRequest.id)
                .where(InvoiceWorkflowRequest.workflow_id == original_workflow_id)
            ).scalar_one()
            self.rush_red_issuing_time = original_invoice_issued.issuing_time
        db.session.add(self)
        # 不支持合并红票
        return self

    @in_transaction()
    def mark_invoicing(self):
        self.state = InvoiceRequest.State.INVOICING
        self.invoicing_time = datetime.now()

    @in_transaction()
    def mark_timeout(self):
        if self.invoice_issued_id:
            self.state = InvoiceRequest.State.SUCCEED
        else:
            self.state = InvoiceRequest.State.FAILED
            self.failed_reason = "开票超时"

    @in_transaction()
    def mark_failed(self, reason: str):
        self.state = InvoiceRequest.State.FAILED
        self.failed_reason = reason

    @in_transaction()
    def mark_succeed(self):
        self.state = InvoiceRequest.State.SUCCEED

    @in_transaction()
    def mark_closed(self):
        self.state = InvoiceRequest.State.CLOSED


@to_log.register
def invoice_request_to_log(val: InvoiceRequest, **kwargs):
    return f"InvoiceRequest(id={val.id}, state={val.state.name})"


class InvoiceIssued(DbBaseModel):
    __tablename__ = "invoice_issued"
    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    org_id = mapped_column(sa.Integer, nullable=False)
    # 发票号码; 税务部门给予发票的号码，其与发票代码组合成每张发票的唯一标识
    invoice_number = mapped_column(sa.String(20), nullable=False)
    # 发票类型; Enum(增值税普通发票, 增值税专用发票)
    invoice_type = mapped_column(sa.Enum(InvoiceType), nullable=False)
    # 开票类型; 用来标明发票的类型是蓝字发票还是红字发票
    issuing_type = mapped_column(sa.Enum(IssuingType), nullable=False)
    # 开票时间; 电子发票开具的时间，主要为电子发票数据产生时间
    issuing_time = mapped_column(sa.DateTime(), nullable=False)
    # 合计金额
    total_amount_without_tax = mapped_column(sa.DECIMAL(22, 2), nullable=False)
    # 合计税额
    total_tax_amount = mapped_column(sa.DECIMAL(22, 2), nullable=False)
    # 价税合计金额
    issuing_amount = mapped_column(sa.DECIMAL(22, 2), nullable=False)
    issuing_amount_ch = mapped_column(sa.String(32), nullable=False)
    # 冲红状态; 0: 未冲红, 1: 已冲红, 2: 部分冲红
    reversing_status = mapped_column(sa.Enum(ReversingStatus), nullable=False, default=ReversingStatus.NOT_REVERSED)
    # 被冲红的蓝字电子发票的发票号码
    original_invoice_number: Mapped[str | None] = mapped_column(sa.String(20), nullable=True)
    # 发票冲红的原因
    rush_red_reason: Mapped[str | None] = mapped_column(sa.String(255), nullable=True)
    # 销售方信息 - 销售方纳税人识别号
    seller_credit_id = mapped_column(sa.String(18), nullable=False)
    # 销售方信息 - 销售方名称
    seller_name = mapped_column(sa.String(256), nullable=False)
    # 销售方信息 - 销售方地址
    seller_address: Mapped[str | None] = mapped_column(sa.String(256), nullable=True)
    # 销售方信息 - 销售方电话
    seller_phone: Mapped[str | None] = mapped_column(sa.String(32), nullable=True)
    # 销售方信息 - 销售方开户行
    seller_bank: Mapped[str | None] = mapped_column(sa.String(256), nullable=True)
    # 销售方信息 - 销售方银行账号
    seller_bank_account: Mapped[str | None] = mapped_column(sa.String(32), nullable=True)
    # 购买方信息 - 购买方类型; INDIVIDUAL: 个人, ENTERPRISE: 企业
    buyer_type = mapped_column(sa.Enum(BuyerType), nullable=False)
    # 购买方信息 - 购买方纳税人识别号
    buyer_credit_id: Mapped[str | None] = mapped_column(sa.String(20), nullable=True)
    # 购买方信息 - 购买方名称
    buyer_name = mapped_column(sa.String(256), nullable=False)
    # 购买方信息 - 购买方地址
    buyer_address: Mapped[str | None] = mapped_column(sa.String(256), nullable=True)
    # 购买方信息 - 购买方电话
    buyer_phone: Mapped[str | None] = mapped_column(sa.String(32), nullable=True)
    # 购买方信息 - 购买方开户行
    buyer_bank: Mapped[str | None] = mapped_column(sa.String(256), nullable=True)
    # 购买方信息 - 购买方银行账号
    buyer_bank_account: Mapped[str | None] = mapped_column(sa.String(32), nullable=True)
    """发票明细"""
    # 商品金额是否含税
    is_tax_included: Mapped[bool] = mapped_column(sa.Boolean, nullable=False)
    # 发票关联商品信息
    issuing_items = mapped_column(sa.JSON())
    # 发票原始信息
    raw = mapped_column(sa.JSON())
    receipt = mapped_column(sa.JSON())

    def get_receipt_pdf_url(self):
        from robot_processor.client import rpa_control_oss

        if "pdf_url" not in self.receipt:
            pdf_url = rpa_control_oss.gen_download_url(self.receipt["pdf"], expires=365 * 24 * 60 * 60)
            self.receipt["pdf_url"] = pdf_url
            flag_modified(self, "receipt")
            db.session.add(self)
            db.session.flush()
        else:
            pdf_url = self.receipt["pdf_url"]
        # FIXME 判断链接失效需要重新生成
        return pdf_url

    def to_pb(self):
        from robot_processor.client import rpa_control_oss
        receipt = {
            format_key: rpa_control_oss.gen_download_url(self.receipt[format_key])
            for format_key in (self.receipt or {})
            if format_key in ["pdf", "ofd", "xml"]
        }
        pb = ParseDict(
            dict(
                id=self.id,
                org_id=self.org_id,
                invoice_number=self.invoice_number,
                invoice_type=self.invoice_type,
                issuing_type=self.issuing_type,
                issuing_time=self.issuing_time.strftime("%Y-%m-%d %H:%M:%S"),
                total_amount_without_tax=str(self.total_amount_without_tax),
                total_tax_amount=str(self.total_tax_amount),
                issuing_amount=str(self.issuing_amount),
                issuing_amount_ch=self.issuing_amount_ch,
                reversing_status=self.reversing_status,
                seller_credit_id=self.seller_credit_id,
                seller_name=self.seller_name,
                seller_address=self.seller_address,
                seller_phone=self.seller_phone,
                seller_bank=self.seller_bank,
                seller_bank_account=self.seller_bank_account,
                buyer_type=self.buyer_type,
                buyer_name=self.buyer_name,
                buyer_credit_id=self.buyer_credit_id,
                buyer_address=self.buyer_address,
                buyer_phone=self.buyer_phone,
                buyer_bank=self.buyer_bank,
                buyer_bank_account=self.buyer_bank_account,
                is_tax_included=self.is_tax_included,
                issuing_items=self.issuing_items,
                original_invoice_number=self.original_invoice_number,
                rush_red_reason=self.rush_red_reason,
                raw=self.raw,
                receipt=receipt,
            ),
            pb_InvoiceIssued(),
        )
        return pb

    def get_pb_issuing_items(self):
        return [
            ParseDict(item, pb_InvoiceIssuingItem())
            for item in (self.issuing_items or [])
        ]


class InvoiceBatchRecord(DbBaseModel):
    """批量导入的记录"""
    __tablename__ = "invoice_batch_record"

    @ensure_mirror_of_pb_enum(pb_InvoiceWorkflow.BatchInfo.State)
    class State(IntEnum):
        PENDING = 0
        SUCCESS = 1
        FAILED = 2
        PARTIAL_SUCCESS = 3

        @property
        def pb_value(self):
            return pb_InvoiceWorkflow.BatchInfo.State.Value(self.name)

        def is_failed(self):
            return self is InvoiceBatchRecord.State.FAILED

    @ensure_mirror_of_pb_enum(pb_InvoiceWorkflow.BatchInfo.Version)
    class Version(IntEnum):
        BY_ISSUING_ITEM = 0
        BY_TRADE = 1

        @property
        def pb_value(self):
            return pb_InvoiceWorkflow.BatchInfo.Version.Value(self.name)

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    created_at: Mapped[datetime] = mapped_column(sa.DateTime, default=datetime.now)
    created_user_id: Mapped[int] = mapped_column(sa.Integer)
    created_user_type: Mapped[UserType] = mapped_column(sa.Enum(UserType, native_enum=False, length=16))
    created_user_nick: Mapped[str] = mapped_column(sa.String(32))

    org_id: Mapped[int] = mapped_column(sa.Integer)
    version = mapped_column(sa.Enum(Version, native_enum=False, length=16))
    state = mapped_column(sa.Enum(State, native_enum=False, length=16))
    failed_reason: Mapped[str | None] = mapped_column(sa.Text())
    file_name: Mapped[str] = mapped_column(sa.String(128))
    origin_file_path: Mapped[str] = mapped_column(sa.String(256))
    failure_items_file_path: Mapped[str | None] = mapped_column(sa.String(256))

    @property
    def created_by(self):
        return pb_UserInfo.User(
            id=self.created_user_id,
            type=pb_UserInfo.User.Type.Value(self.created_user_type.name),
            nick=self.created_user_nick
        )

    @property
    def created_user_info(self):
        return pb_UserInfo(user=self.created_by)

    @classmethod
    @in_transaction()
    def create(
            cls,
            org_id: int,
            version: "InvoiceBatchRecord.Version",
            file_name,
            file_path,
            user_info: pb_UserInfo.User
    ):
        self = cls(
            created_user_id=user_info.id,
            created_user_type=user_info.type,
            created_user_nick=user_info.nick,
            org_id=org_id,
            version=version,
            state=cls.State.PENDING,
            file_name=file_name,
            origin_file_path=file_path
        )
        db.session.add(self)
        return self

    @in_transaction()
    def mark_failed(self, reason: str):
        self.state = self.State.FAILED
        self.failed_reason = reason

    @in_transaction()
    def mark_success(self):
        self.state = self.State.SUCCESS

    @in_transaction()
    def mark_partial_success(self, failure_items_file_path: str):
        self.state = self.State.PARTIAL_SUCCESS
        self.failure_items_file_path = failure_items_file_path

    def to_pb(self):
        return pb_InvoiceWorkflow.BatchInfo(
            id=self.id,
            version=self.version.pb_value,
            created_by=self.created_by,
            created_at=self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            state=self.state.pb_value,
            file_name=self.file_name,
            origin_file_path=self.origin_file_path,
            failure_items_file_path=string_wrapper(self.failure_items_file_path),
            failed_reason=string_wrapper(self.failed_reason)
        )


class InvoiceIssueQueue(DbBaseModel):
    __tablename__ = "invoice_issue_queue"
    __table_args__ = (
        sa.Index("idx_invoice_issue_running", "running_at"),
        sa.UniqueConstraint("org_id", "request_id", name="uk_invoice_issue_queue_org_request_id"),
    )

    class Task(StrEnum):
        DO_ISSUE = "DO_ISSUE"  # 开票
        FETCH_ISSUED = "FETCH_ISSUED"  # 获取凭证

    class State(StrEnum):
        QUEUED = "QUEUED"
        RUNNING = "RUNNING"

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    org_id: Mapped[int]
    task = mapped_column(sa.Enum(Task, native_enum=False, length=16), nullable=False)
    state = mapped_column(sa.Enum(State, native_enum=False, length=16), nullable=False)
    request_id: Mapped[int]
    trace_id: Mapped[str | None] = mapped_column(sa.String(32))
    created_at: Mapped[datetime] = mapped_column(sa.DateTime, default=datetime.now)
    running_at: Mapped[datetime | None] = mapped_column(sa.DateTime)
    invoice_request: Mapped[InvoiceRequest] = relationship(
        InvoiceRequest, viewonly=True,
        primaryjoin="foreign(InvoiceIssueQueue.request_id)==InvoiceRequest.id"
    )

    @property
    def acquired(self):
        return self.state == InvoiceIssueQueue.State.RUNNING and self.trace_id == current_trace_id()

    @classmethod
    def get_running(cls, org_id: int):
        running: InvoiceIssueQueue | None = cls.query.filter_by(org_id=org_id, state=cls.State.RUNNING).first()
        return running

    @classmethod
    def close(cls, org_id: int, request_id: int):
        from robot_processor.invoice.tasks.jobs import submit_move_on

        logger.bind(org_id=org_id, invoice_request_id=request_id).info("队列任务结束")
        db.session.execute(
            sa.delete(InvoiceIssueQueue)
            .where(InvoiceIssueQueue.org_id == org_id)
            .where(InvoiceIssueQueue.request_id == request_id)
        )
        submit_move_on(org_id)

    @classmethod
    def enqueue(cls, org_id: int, request_id: int, task: Task):
        record = db.session.execute(
            sa.select(InvoiceIssueQueue).select_from(InvoiceIssueQueue)
            .where(InvoiceIssueQueue.org_id == org_id)
            .where(InvoiceIssueQueue.request_id == request_id)
        ).scalar_one_or_none()
        if not record:
            with in_transaction():
                record = cls(org_id=org_id, request_id=request_id, task=task, state=cls.State.QUEUED)
                db.session.add(record)
        db.session.refresh(record, with_for_update=True)
        return record

    @in_transaction()
    def acquire(self):
        running = InvoiceIssueQueue.get_running(self.org_id)
        if running is None:
            self.logger.info("竞争开票锁成功，更新状态为 RUNNING")
            self.state = InvoiceIssueQueue.State.RUNNING
            self.trace_id = current_trace_id()
            self.running_at = datetime.now()
        elif running.request_id == self.request_id:
            self.logger.info(f"当前任务已经是 RUNNING 状态，无需更新 {to_log(running)}")
        else:
            self.logger.info(f"竞争开票锁失败，正在运行的任务：{to_log(running)}")
        return self

    def get_queue_position(self):
        return db.session.scalar(
            sa.select(sa.func.count())
            .where(InvoiceIssueQueue.org_id == self.org_id)
            .where(InvoiceIssueQueue.id < self.id)
            .where(InvoiceIssueQueue.state == InvoiceIssueQueue.State.QUEUED)
        )

    def release(self):
        from robot_processor.invoice.tasks.jobs import submit_move_on

        org_id = self.org_id
        logger.bind(org_id=org_id, invoice_request_id=self.request_id).info(f"队列任务结束 {to_log(self)}")
        with in_transaction():
            db.session.delete(self)
        submit_move_on(org_id)

    @classmethod
    def move_on(cls, org_id: int):
        head = db.session.query(cls).filter_by(org_id=org_id, state=cls.State.QUEUED).order_by(cls.id).first()
        if not head:
            logger.bind(org_id=org_id).info("队列清空，无需继续处理")
            return
        head.logger.info(f"队列任务开始执行: {to_log(head)}")
        issue_broker = head.get_issue_broker()
        match head.task:
            case InvoiceIssueQueue.Task.DO_ISSUE:
                issue_broker.do_issue()
            case InvoiceIssueQueue.Task.FETCH_ISSUED:
                issue_broker.fetch_issue()

    @classmethod
    def check_ttl(cls, org_id: int):
        from robot_processor.invoice.configs import invoice_config
        self = cls.get_running(org_id)
        if not self:
            return
        if self.running_at + invoice_config.issue_timeout > datetime.now():
            return
        issue_broker = self.get_issue_broker()
        if self.invoice_request.invoice_issued_id:
            issue_broker.fetch_issued_failed("超时未响应")
        else:
            issue_broker.issue_timeout()
        self.release()

    @property
    def logger(self):
        return logger.bind(org_id=self.org_id, invoice_request_id=self.request_id)

    def get_issue_broker(self):
        from robot_processor.invoice.workflow.service import IssueBroker

        return IssueBroker.by_request(self.invoice_request)


@to_log.register
def issue_queue_to_log(val: InvoiceIssueQueue, **kwargs):
    return (
        f"InvoiceIssueQueue(id={val.id}, org_id={val.org_id}, request_id={val.request_id}, "
        f"task={val.task.name}, state={val.state.name}, running_at={val.running_at}, "
        f"invoice_request={to_log(val.invoice_request)})"
    )


class InvoiceWorkflowExportTask(DbBaseModel):
    class State(StrEnum):
        PENDING = "导出中"
        FINISHED = "已完成"

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    created_at: Mapped[datetime] = mapped_column(sa.DateTime, default=datetime.now)
    state = mapped_column(sa.Enum(State, native_enum=False, length=12), default=State.PENDING)
    org_id: Mapped[int]
    user_id: Mapped[int]
    user_type: Mapped[int]
    user_nick: Mapped[str | None] = mapped_column(sa.String(32), nullable=True)
    export_config: Mapped[dict] = mapped_column(sa.JSON())
    filters: Mapped[dict] = mapped_column(sa.JSON())

    def get_exporter(self):
        from robot_processor.invoice.workflow.service.exporter import ExportConfig
        from robot_processor.invoice.workflow.service.exporter import InvoiceWorkflowExporter
        return InvoiceWorkflowExporter(
            org_id=self.org_id,
            user=AccountDetailV2(user_id=self.user_id, user_type=self.user_type, user_nick=self.user_nick),
            export_config=deserialize(self.export_config, ExportConfig),
            filters=self.filters,
            context=dict(datetime=self.created_at.strftime("%Y-%m-%d %H:%M:%S"))
        )

    @property
    def oss_key(self):
        return "invoice/export/{org_id}/发票导出_{id}.zip".format(org_id=self.org_id, id=self.id)

    @property
    def oss_download_url(self):
        from external.oss import config
        return config.oss_base_url() + self.oss_key

    @in_transaction()
    def mark_finished(self, zip_buffer):
        import oss2

        from robot_processor.client import oss_client
        headers = {
            "x-oss-storage-class": oss2.BUCKET_STORAGE_CLASS_STANDARD,
            "x-oss-object-acl": oss2.OBJECT_ACL_PUBLIC_READ
        }
        oss_client.bucket.put_object(self.oss_key, zip_buffer, headers=headers)
        self.state = InvoiceWorkflowExportTask.State.FINISHED

    def to_pb(self):
        pb = pb_InvoiceWorkflowExportTask(
            id=self.id,
            created_at=self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            state=self.state,
            user=pb_UserInfo.User(id=self.user_id, type=self.user_type, nick=self.user_nick),  # type: ignore[arg-type]
        )
        if self.state == InvoiceWorkflowExportTask.State.FINISHED:
            pb.download_url = self.oss_download_url
        return pb

    @classmethod
    def get_paginate_by_org(cls, org_id, paginate):
        stmt = sa.select(cls).where(sql_eq(cls.org_id, org_id))
        count_stmt = stmt.with_only_columns(sa.func.count())
        paginate.total.value = db.session.execute(count_stmt).scalar()
        if paginate.total.value == 0:
            return []
        stmt = stmt.order_by(cls.id.desc())
        page, per_page = paginate.page.value, paginate.per_page.value
        stmt = stmt.limit(per_page).offset(per_page * (page - 1))
        result = db.session.execute(stmt)
        items = result.scalars().unique().all()
        return items

    @classmethod
    def get_by_id(cls, org_id, task_id):
        instant: InvoiceWorkflowExportTask | None = cls.query.filter_by(id=task_id, org_id=org_id).first()
        return instant

    def submit_task(self):
        do_invoice_workflow_export_task.send(self.id)


@task_queue.actor(queue_name=TASK_QUEUE_JOB)
def do_invoice_workflow_export_task(task_id: int):
    _logger = logger.bind(export_task_id=task_id)
    task = db.session.get(InvoiceWorkflowExportTask, task_id)
    if not task:
        _logger.error("未找到导出任务")
        return
    if task.state == InvoiceWorkflowExportTask.State.FINISHED:
        _logger.error("导出任务已完成")
        return
    exporter = task.get_exporter()
    zip_buffer = exporter.process()
    task.mark_finished(zip_buffer)
