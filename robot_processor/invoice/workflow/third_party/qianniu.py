from datetime import datetime
from typing import TYPE_CHECKING

import arrow
from result import Err
from sqlalchemy.sql.operators import eq as sql_eq
from sqlalchemy.sql.operators import ne as sql_ne
from tcron_jobs import runner

from robot_processor.ext import db
from robot_processor.logging import vars as log_vars
from robot_processor.t_cron import wrap_tcron_job


@runner.register
@wrap_tcron_job
def poll_qn_invoice_list(shop_id: int, timerange: str = "") -> str:
    from robot_processor.invoice.tax_bureau.models import CorporateShop
    from robot_processor.shop.models import Shop
    from rpa.mola import MolaClient

    if not (shop := db.session.get(Shop, shop_id)):
        return "店铺不存在"
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set("poll_qn_invoice_list")
    corporate_shop = CorporateShop.get_by_shop(shop.sid, shop.platform)
    now = arrow.now()
    if timerange.strip():
        start_raw, end_raw = timerange.split("~")
        start = arrow.get(start_raw).naive
        end = arrow.get(end_raw).naive
    else:
        end = now.naive
        # 千牛对于 2 个月的定义为 day 相同时, month delta <= 2; 当 day 不同时, month delta < 2
        # 如 2025/02/28 ～ 2025/04/28 是有效范围; 2025/02/29 ～ 2025/04/28 判定为超出2个月; 2025/03/01 ～ 2025/04/28 才有效
        prepare_start = now.shift(months=-2)
        if prepare_start.naive.day == end.day:
            start = prepare_start.naive
        else:
            start = prepare_start.shift(days=1).naive
    qn_invoice_paginator = get_data_fetcher(MolaClient(shop.sid), start, end)
    result: dict[str, list] = dict(ok=[], err=[])
    try:
        qn_invoice_list = list(qn_invoice_paginator.chain_fetch())[::-1]
    except Exception as e:
        return str(e)
    for qn_invoice in qn_invoice_list:
        try:
            create_invoice_workflow(shop, qn_invoice).map(result["ok"].append).map_err(result["err"].append)
        except Exception as e:
            result["err"].append(Err(e))
    corporate_shop.platform_apply_schedule_executed_at = now.naive
    db.session.commit()
    return str(result)


if TYPE_CHECKING:
    from robot_processor.shop.models import Shop
    from rpa.mola.schemas import QNInvoice


def create_invoice_workflow(shop: "Shop", qn_invoice: "QNInvoice"):
    from robot_processor.invoice.workflow.models import InvoiceWorkflow
    from robot_processor.invoice.workflow.service import WorkflowBuilder
    from robot_processor.invoice.workflow.service import WorkflowContext

    org_id = int(shop.org_id)  # type: ignore[arg-type]
    if qn_invoice.invoiceKind not in [0, 1, 2, 3, 4, 5]:
        return Err(ValueError("仅支持增值税普通发票和增值税专用发票"))
    if qn_invoice.applyStatus != 2:
        return Err(ValueError("发票申请已取消，无需开票"))
    if qn_invoice.applyStatus == 2 and qn_invoice.virtualRegisterAgree == "1":
        return Err(ValueError("系统发票无需处理"))

    if org_id == 2088:
        if not qn_invoice.rightsDueDate:
            return Err(ValueError("不是即将逾期的开票申请"))
    if db.session.query(
        InvoiceWorkflow.query.filter(
            sql_eq(InvoiceWorkflow.org_id, org_id),
            sql_eq(InvoiceWorkflow.platform_apply_id, qn_invoice.serialNo),
            sql_ne(InvoiceWorkflow.state, InvoiceWorkflow.State.CLOSED),
        ).exists()
    ).scalar():
        return Err(ValueError(f"发票流水号 {qn_invoice.serialNo} 已创建开票申请流程"))
    context = WorkflowContext.QNPlatformApply(org_id=org_id, shop=shop, qn_invoice=qn_invoice)
    return WorkflowBuilder.create_workflow_with_context(context).as_result()


def get_data_fetcher(mola, start, end):
    from robot_processor.form.event.paginator import PaginateDataFetcher
    from rpa.mola import MolaClient
    from rpa.mola.schemas import QNInvoice

    class QnInvoiceListDataFetcher(PaginateDataFetcher[QNInvoice]):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.mola: MolaClient = mola
            self.start: datetime = start
            self.end: datetime = end
            self.page_size = 50

        def _do_fetch(self, page_no, page_size):
            response = self.mola.qn_invoice_get_agreed(
                page_no=page_no + 1,
                page_size=page_size,
                start_time=self.start.strftime("%Y-%m-%d"),
                end_time=self.end.strftime("%Y-%m-%d"),
            ).expect("获取发票列表失败")
            if self.total is None:
                self.total = response.total
            return response.data

    return QnInvoiceListDataFetcher()


if TYPE_CHECKING:
    from robot_processor.invoice.workflow.models import InvoiceWorkflow


def post_back(invoice_workflow: "InvoiceWorkflow"):
    from robot_processor.client import rpa_control_oss
    from rpa.mola import MolaClient

    qn_platform_apply = invoice_workflow.get_qn_platform_apply_info()
    return MolaClient(invoice_workflow.sid).qn_invoice_submit_manual(
        rpa_control_oss.gen_download_url(invoice_workflow.invoice_issued.receipt["pdf"]),
        invoice_workflow.invoice_number,
        invoice_workflow.issuing_time.strftime("%Y-%m-%d"),
        qn_platform_apply.serialNo,
        qn_platform_apply.tid,
        qn_platform_apply.platformCode,
        qn_platform_apply.startTime,
    )
