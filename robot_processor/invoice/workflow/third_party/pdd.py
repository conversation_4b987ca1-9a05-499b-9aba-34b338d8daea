from copy import deepcopy
from decimal import Decimal
from itertools import chain
from typing import TYPE_CHECKING

import arrow
from result import Err
from result import as_result
from tcron_jobs import runner

from robot_processor.client import rpa_control_oss
from robot_processor.client.pdd_erp import PddInvoiceApplicationQuery
from robot_processor.client.pdd_erp import PddInvoiceApplicationStatus
from robot_processor.client.pdd_erp import PddInvoiceDetailUpload
from robot_processor.db import db
from robot_processor.form.event.paginator import PaginateDataFetcher
from robot_processor.logging import vars as log_vars
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import raise_exception
from robot_processor.utils import unwrap_optional

if TYPE_CHECKING:
    from robot_processor.invoice.workflow.models import InvoiceWorkflow


@runner.register
@wrap_tcron_job
def poll_pdd_invoice_list(shop_id: int, timerange: str = "") -> str:
    from robot_processor.invoice.tax_bureau.models import CorporateShop
    from robot_processor.shop.models import Shop

    shop = db.session.get_one(Shop, shop_id)
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set("poll_pdd_invoice_list")
    corporate_shop = CorporateShop.get_by_shop(shop.sid, shop.platform)
    if not (grant_record := shop.get_pdd_erp_grant_record()):
        raise ValueError("店铺没有授权")
    access_token = unwrap_optional(grant_record).access_token
    if timerange:
        start_raw, end_raw = timerange.split("~")
        start = arrow.get(start_raw).int_timestamp * 1000
        end = arrow.get(end_raw).int_timestamp * 1000
    else:
        start = arrow.now().shift(days=-14).int_timestamp * 1000
        end = arrow.now().int_timestamp * 1000

    query = PddInvoiceApplicationQuery(access_token=access_token, update_start_time=start, update_end_time=end)
    pending_query = deepcopy(query)
    pending_query.status = 1
    approved_query = deepcopy(query)
    approved_query.status = 2
    pending_pdd_invoice_paginator = get_data_fetcher(pending_query)
    approved_query_pdd_invoice_paginator = get_data_fetcher(approved_query)
    result: dict[str, list] = dict(ok=[], err=[])
    for pdd_invoice in chain(
        pending_pdd_invoice_paginator.chain_fetch(), approved_query_pdd_invoice_paginator.chain_fetch()
    ):
        # 根据线上接口请求来看，限定 status=2 时，仍然会获取到 application_status=3 的返回结果
        if pdd_invoice.application_status not in [
            PddInvoiceApplicationStatus.PENDING,
            PddInvoiceApplicationStatus.APPROVED,
        ]:
            continue
        create_invoice_workflow(shop, pdd_invoice).map(result["ok"].append).map_err(result["err"].append)
    corporate_shop.platform_apply_schedule_executed_at = arrow.now().naive
    db.session.commit()
    return str(result)


def create_invoice_workflow(
    shop, pdd_invoice: PddInvoiceApplicationQuery.Response.InvoiceApplicationQueryResponse.InvoiceApplicationQueryItem
):
    from sqlalchemy.sql.operators import eq as sql_eq
    from sqlalchemy.sql.operators import ne as sql_ne

    from robot_processor.invoice.workflow.models import InvoiceWorkflow
    from robot_processor.invoice.workflow.service import WorkflowBuilder
    from robot_processor.invoice.workflow.service import WorkflowContext

    org_id = int(shop.org_id)
    exists = InvoiceWorkflow.query.filter(
        sql_eq(InvoiceWorkflow.org_id, org_id),
        sql_eq(InvoiceWorkflow.platform_apply_id, pdd_invoice.order_sn),
        sql_ne(InvoiceWorkflow.state, InvoiceWorkflow.State.CLOSED),
    ).exists()
    if db.session.query(exists).scalar():
        return Err(ValueError(f"发票流水号 {pdd_invoice.order_sn} 已创建开票申请流程"))
    context = WorkflowContext.PddPlatformApply(org_id=org_id, shop=shop, pdd_invoice=pdd_invoice)
    return WorkflowBuilder.create_workflow_with_context(context).as_result()


def get_data_fetcher(query: PddInvoiceApplicationQuery):
    class PddInvoiceListDataFetcher(
        PaginateDataFetcher[
            PddInvoiceApplicationQuery.Response.InvoiceApplicationQueryResponse.InvoiceApplicationQueryItem
        ]
    ):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.query = query
            self.page_no = 1
            self.page_size = 50
            self._has_next = True

        @property
        def has_next(self):
            return self._has_next

        @has_next.setter
        def has_next(self, new):
            self._has_next = new

        def _do_fetch(self, page_no, page_size):
            query = deepcopy(self.query)
            query.page = page_no
            query.page_size = page_size
            response = query.execute().unwrap_or_else(raise_exception)
            if len(response.invoice_application_query_response.invoice_application_list) < query.page_size:
                self.has_next = False
            return response.invoice_application_query_response.invoice_application_list

    return PddInvoiceListDataFetcher()


@as_result(Exception)
def post_back(invoice_workflow: "InvoiceWorkflow"):
    from base64 import b64encode

    from robot_processor.invoice.workflow.models import BuyerType
    from robot_processor.shop.models import Shop

    shop = Shop.Queries.optimal_shop_by_sid(invoice_workflow.sid, org_id=str(invoice_workflow.org_id), platform="PDD")
    if not shop:
        raise ValueError("店铺不存在")
    if not (grant_record := shop.get_pdd_erp_grant_record()):
        raise ValueError("店铺没有授权")
    access_token = unwrap_optional(grant_record).access_token

    if invoice_workflow.buyer_type == BuyerType.INDIVIDUAL:
        business_type = 0
        payer_register_no = None
    else:
        business_type = 1
        payer_register_no = invoice_workflow.buyer_credit_id
    return (
        PddInvoiceDetailUpload(
            access_token=access_token,
            order_sn=invoice_workflow.tid,
            business_type=business_type,
            payee_operator="新柒加",
            payer_name=invoice_workflow.buyer_name,
            invoice_time=int(invoice_workflow.invoice_issued.issuing_time.timestamp() * 1000),
            sum_price=int(invoice_workflow.invoice_issued.total_amount_without_tax * 100),
            sum_tax=int(invoice_workflow.invoice_issued.total_tax_amount * 100),
            tax_rate=int(Decimal(invoice_workflow.invoice_issued.issuing_items[0]["tax_rate"]) * 100),
            invoice_code="",
            invoice_no=invoice_workflow.invoice_number,
            invoice_amount=int(invoice_workflow.issuing_items_total_amount * 100),
            invoice_file_content=b64encode(
                rpa_control_oss.get_object(invoice_workflow.invoice_issued.receipt["pdf"])
            ).decode("utf-8"),
            payer_register_no=payer_register_no,
        )
        .init_payer_from_apply_info(invoice_workflow.platform_apply_raw_info)
        .execute()
        .unwrap_or_else(raise_exception)
    )
