import base64
from datetime import datetime
from typing import TYPE_CHECKING

import arrow
import requests
from result import Err
from result import Ok
from tcron_jobs import runner

from robot_processor.client import jd_sdk
from robot_processor.client import rpa_control_oss
from robot_processor.client.jd_sdk import BlueInvoiceUploadReq
from robot_processor.db import db
from robot_processor.form.event.paginator import PaginateDataFetcher
from robot_processor.invoice.common.models import JdInvoice
from robot_processor.logging import vars as log_vars
from robot_processor.t_cron import wrap_tcron_job


@runner.register
@wrap_tcron_job
def poll_jd_invoice_list(shop_id: int, timerange: str = "") -> str:
    """拉取店铺的待开票发票列表并创建发票流程"""
    from robot_processor.invoice.tax_bureau.models import CorporateShop
    from robot_processor.shop.models import Shop

    if not (shop := db.session.get(Shop, shop_id)):
        return "店铺不存在"
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set("poll_jd_invoice_list")
    corporate_shop = CorporateShop.get_by_shop(shop.sid, shop.platform)
    now = arrow.now()
    if timerange:
        start_raw, end_raw = timerange.split("~")
        start = arrow.get(start_raw).naive
        end = arrow.get(end_raw).naive
    else:
        start = now.shift(months=-1).naive
        end = now.naive

    jd_invoice_paginator = get_data_fetcher(shop.sid, start, end)
    result: dict[str, list] = dict(ok=[], err=[])
    for jd_invoice in jd_invoice_paginator.chain_fetch():
        create_invoice_workflow(shop, jd_invoice).map(result["ok"].append).map_err(result["err"].append)
    corporate_shop.platform_apply_schedule_executed_at = now.naive
    db.session.commit()
    return str(result)


if TYPE_CHECKING:
    from robot_processor.shop.models import Shop


def create_invoice_workflow(shop: "Shop", jd_invoice: "JdInvoice"):
    from sqlalchemy.sql.operators import eq as sql_eq

    from robot_processor.enums import AuthType
    from robot_processor.invoice.workflow.models import InvoiceWorkflow
    from robot_processor.invoice.workflow.service import WorkflowBuilder
    from robot_processor.invoice.workflow.service import WorkflowContext

    access_token = shop.get_access_token(auth_type=AuthType.JD_FS)
    if access_token is None:
        return Err(ValueError("店铺没有飞梭的授权"))
    resp = jd_sdk.query_invoice_apply(access_token, jd_invoice.tid)
    if resp.response.success and resp.response.data:
        apply_data = resp.response.data
        org_id = int(shop.org_id)  # type: ignore[arg-type]
        if resp.response.data.invoiceStatus != 1:
            return Err(ValueError(f"不是待开发票 {resp.response.data.applyId}"))
        if resp.response.data.shouldInvoiceAmount is None:
            return Err(ValueError(f"不是待开发票 {resp.response.data.applyId}"))
        exists = InvoiceWorkflow.query.filter(
            sql_eq(InvoiceWorkflow.org_id, org_id),
            sql_eq(InvoiceWorkflow.platform_apply_id, jd_invoice.serial_no),
        ).exists()
        if db.session.query(exists).scalar():
            return Err(ValueError(f"发票流水号 {jd_invoice.serial_no} 已创建开票申请流程"))
        context = WorkflowContext.JDPlatformApply(org_id=org_id, shop=shop, jd_invoice=apply_data)
        return WorkflowBuilder.create_workflow_with_context(context).as_result()
    else:
        return Err(ValueError(f"查询发票失败: {resp.response.message}"))


def get_data_fetcher(sid, start, end):
    class JdInvoiceListDataFetcher(PaginateDataFetcher[JdInvoice]):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.start: datetime = start
            self.end: datetime = end

        def _do_fetch(self, page_no, page_size):
            from sqlalchemy import func
            from sqlalchemy import select
            from sqlalchemy.sql.operators import between_op

            sql = (
                select(JdInvoice)
                .select_from(JdInvoice)
                .where(
                    JdInvoice.seller_id == sid,
                    JdInvoice.invoice_status == 1,
                    between_op(JdInvoice.modified, self.start, self.end),
                )
                .limit(self.page_size)
                .offset(self.offset)
            )
            if self.total is None:
                self.total = db.session.execute(sql.with_only_columns(func.count())).scalar()
            invoice_list = db.session.execute(sql).scalars().all()
            return invoice_list

    return JdInvoiceListDataFetcher()


if TYPE_CHECKING:
    from robot_processor.invoice.workflow.models import InvoiceWorkflow


def post_back(invoice_workflow: "InvoiceWorkflow"):
    from robot_processor.enums import AuthType
    from robot_processor.invoice.tax_bureau.models import Corporate
    from robot_processor.invoice.workflow.models import IssuingType
    from robot_processor.shop.models import Shop

    access_token = invoice_workflow.shop.get_access_token(auth_type=AuthType.JD_FS)
    if access_token is None:
        return Err(ValueError("店铺没有飞梭的授权"))
    pdf_url = rpa_control_oss.gen_download_url(invoice_workflow.invoice_issued.receipt["pdf"])
    pdf_info = base64.b64encode(requests.get(pdf_url).content).decode()
    shop = Shop.query.filter_by(sid=invoice_workflow.sid).first()
    corporate = Corporate.get_by_shop(shop)
    req = BlueInvoiceUploadReq(
        orderId=invoice_workflow.tid,
        receiverTaxNo=corporate.credit_id,
        receiverName=corporate.name,
        invoiceNo=invoice_workflow.invoice_number,
        ivcTitle=invoice_workflow.buyer_name,
        totalPrice=invoice_workflow.issuing_items_total_amount,
        invoiceTime=invoice_workflow.issuing_time.date().isoformat(),
        pdfInfo=pdf_info,
    )
    if invoice_workflow.issuing_type == IssuingType.BLUE:
        resp = jd_sdk.blue_invoice_upload(access_token, req)
        return Ok(resp.applyinvoiceforown_result.success)
    return Err(ValueError("红票不支持回传"))
