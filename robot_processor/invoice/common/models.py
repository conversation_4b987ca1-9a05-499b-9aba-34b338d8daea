from datetime import datetime
from enum import IntEnum
from enum import StrEnum
from enum import auto
from typing import cast

import sqlalchemy as sa
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
from leyan_proto.digismart.robot.invoice.workflow_pb2 import UserInfo as pb_UserInfo
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column

from robot_processor.db import DbBaseModel
from robot_processor.db import db
from robot_processor.db import in_transaction
from robot_processor.utils import ensure_mirror_of_pb_enum
from robot_processor.utils import message_to_dict


class OperationLog(DbBaseModel):
    __tablename__ = "invoice_operation_log"
    __table_args__ = (sa.Index("idx_relation", "model", "model_id"),)

    class Model(StrEnum):
        # 企业信息管理
        CORPORATE = auto()
        # 企业绑定的办税人账号信息
        CORPORATE_TAXER = auto()
        # 企业绑定的飞梭店铺信息
        CORPORATE_SHOP = auto()
        # RPA 客户端登录
        RPA_SESSION = auto()
        # 商品税收规则
        GOODS_CUSTOMIZE_RULESET = auto()
        # 发票申请
        INVOICE_WORKFLOW = auto()
        # 发票信息
        INVOICE_ISSUED = auto()
        # 发票申请审批规则
        INVOICE_APPROVAL_TEMPLATE = auto()
        # 商品税收配置
        ITEM_TAX_CONFIG = auto()

    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    # 操作日志关联的实体类型
    model = mapped_column(sa.Enum(Model))
    # 操作日志关联的实体ID
    model_id = mapped_column(sa.Integer)
    # 操作人信息
    user_info = mapped_column(sa.JSON)
    # 操作时间
    operate_at = mapped_column(sa.DateTime)
    # 变更记录
    # 不同的 model 有不同的变更记录格式
    changes = mapped_column(sa.JSON)

    @classmethod
    @in_transaction()
    def create(cls, model, model_id, user_info, changes, operate_at=None):
        """创建操作日志

        Args:
            model (OperationLog.Model): 操作日志关联的实体类型
            model_id (int): 操作日志关联的实体ID
            user_info (pb_UserInfo): 操作人信息
            changes (dict): 变更记录
            operate_at (datetime | None): 操作时间
        """
        operate_at = operate_at or datetime.now()
        log = cls(
            model=model,
            model_id=model_id,
            user_info=message_to_dict(cast(pb_UserInfo, user_info)),
            operate_at=operate_at,
            changes=changes,
        )
        db.session.add(log)

        return log


rpa_user_info = pb_UserInfo(user=pb_UserInfo.User(id=0, type=pb_UserInfo.User.SYSTEM, nick="rpa 客户端"))
anonymous_user_info = pb_UserInfo(user=pb_UserInfo.User(id=0, type=pb_UserInfo.User.SYSTEM, nick="<未提供>"))


@ensure_mirror_of_pb_enum(pb_InvoiceWorkflow.PlatformApplySyncMethod)
class PlatformApplySyncMethod(IntEnum):
    """平台发票申请来源"""

    SYNC_METHOD_UNSPECIFIED = 0
    QN = 1  # 千牛[客户端]
    DOUDIAN = 2  # 抖店[api]
    WDT = 3  # 旺店通[api]
    JD = 4  # 京东[api]
    PDD = 5  # 拼多多[api]

    @property
    def pb_value(self):
        return pb_InvoiceWorkflow.PlatformApplySyncMethod.Value(self.name)


class PostBackMethod(StrEnum):
    """回传方式"""

    MAIL = "mail"  # 邮箱发送
    SMS = "sms"  # 短信发送
    QN_PLATFORM_APPLY = "qn_platform_apply"  # 千牛发票申请回传
    DOUDIAN_PLATFORM_APPLY = "doudian_platform_apply"
    WDT_PLATFORM_APPLY = "wdt_platform_apply"
    JD_PLATFORM_APPLY = "jd_platform_apply"
    PDD_PLATFORM_APPLY = "pdd_platform_apply"


class JdInvoice(DbBaseModel):
    __bind_key__ = "fs_trade"
    __tablename__ = "jd_invoice"
    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    serial_no: Mapped[str] = mapped_column(sa.String(32))
    tid: Mapped[str] = mapped_column(sa.String(128))
    seller_id: Mapped[str] = mapped_column(sa.String(32))
    invoice_status: Mapped[int] = mapped_column(sa.Integer)
    # 1:待开票 3:开票中 4:开票成功 5:开票失败 6:冲红中 7:冲红成功 8:冲红失败 9:已驳回 11:蓝票审核失败
    # 12:红票审核失败 13:待换开 14:换开中 15:换开驳回 16:退款关闭 17:协商关闭
    modified: Mapped[datetime] = mapped_column(sa.DateTime)
    create_at: Mapped[datetime] = mapped_column(sa.DateTime)
    update_at: Mapped[datetime] = mapped_column(sa.DateTime)
