import concurrent.futures
from datetime import datetime
from datetime import timedelta

from loguru import logger
from result import Err
from result import Ok
from tcron_jobs import runner

from robot_processor.t_cron import wrap_tcron_job


@runner.register
@wrap_tcron_job
def invoice_auto_retry():
    from robot_processor.invoice.tax_bureau.models import Corporate
    from robot_processor.invoice.workflow.models import InvoiceWorkflow
    from robot_processor.invoice.workflow.models import IssuingType
    from robot_processor.invoice.workflow.service import IssueBroker

    # 重试客户端不在线的
    for workflow in InvoiceWorkflow.query.filter(
        InvoiceWorkflow.state == InvoiceWorkflow.State.INVOICE_FAILED,
        InvoiceWorkflow.issue_failed_reason == "无在线客户端",
        InvoiceWorkflow.created_at > datetime.now() - timedelta(days=1),
    ):
        workflow_logger = logger.bind(invoice_workflow_id=workflow.id, invoice_workflow_state=workflow.state.name)
        workflow_logger.info("自动重试无在线客户端的开票申请")
        corporate = Corporate.get_by_credit_id(workflow.org_id, workflow.seller_credit_id)
        taxer = corporate.get_default_taxer()
        if not taxer:
            workflow_logger.info("没有开票账号，无法自动重试")
            continue
        match workflow.issuing_type:
            case IssuingType.BLUE:
                init_result = IssueBroker.init_issue_blue(
                    workflow.to_request_view(),
                    [workflow],
                    corporate.competent_tax_bureau.name,
                    taxer.account,
                )
            case IssuingType.RED:
                init_result = IssueBroker.init_issue_red(workflow, corporate.competent_tax_bureau.name, taxer.account)
            case _:
                continue
        if init_result.is_err():
            workflow_logger.warning(f"自动重试失败: {init_result.unwrap_err()}")
            continue
        issue_broker = init_result.unwrap()
        try:
            issue_broker.do_issue(workflow.get_applicant_info())
            workflow_logger.info("提交开票任务成功")
        except Exception as e:
            workflow_logger.opt(exception=e).error(f"提交开票任务失败 {e}")

    # 重试获取凭证失败
    for workflow in InvoiceWorkflow.query.filter(
        InvoiceWorkflow.state == InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT,
        InvoiceWorkflow.created_at > datetime.now() - timedelta(days=1),
    ):
        workflow_logger = logger.bind(invoice_workflow_id=workflow.id, invoice_workflow_state=workflow.state.name)
        workflow_logger.info("自动重试获取凭证失败的开票申请")
        issue_broker = IssueBroker.by_request(workflow.processed_invoice_request())
        match issue_broker.fetch_issue():
            case Ok():
                workflow_logger.info("提交获取开票凭证任务成功")
            case Err(e):
                workflow_logger.error(f"提交获取开票凭证任务失败 {e}")


# ============ 发票队列 相关 ============


@runner.register
@wrap_tcron_job
def mark_invoice_timeout(timeout: int):
    from robot_processor.invoice.workflow.models import InvoiceIssueQueue

    timeout_tasks: list[str] = []
    for task in (
        InvoiceIssueQueue.query.filter(
            InvoiceIssueQueue.state == InvoiceIssueQueue.State.RUNNING,
            InvoiceIssueQueue.running_at < datetime.now() - timedelta(minutes=timeout),
        )
        .with_for_update()
        .all()
    ):
        InvoiceIssueQueue.check_ttl(task.org_id)
        timeout_tasks.append(f"{task.org_id}@{task.request_id}@{task.task}")
    return str(timeout_tasks)


executor = concurrent.futures.ThreadPoolExecutor(max_workers=10)


def submit_move_on(org_id: int):
    from robot_processor.app import app
    from robot_processor.invoice.workflow.models import InvoiceIssueQueue
    from robot_processor.logging import to_log

    def move_on(org_id_: int):
        with app.app_context():
            if running := InvoiceIssueQueue.get_running(org_id_):
                logger.bind(org_id=org_id_).info(f"租户 {org_id_} 队列执行中 {to_log(running)}")
            else:
                InvoiceIssueQueue.move_on(org_id_)

    executor.submit(move_on, org_id)


@runner.register
@wrap_tcron_job
def invoice_issue_queue_move_on():
    from sqlalchemy import text

    from robot_processor.ext import db

    org_id_list = db.session.scalars(text("SELECT DISTINCT org_id FROM invoice_issue_queue")).all()
    for org_id in org_id_list:
        submit_move_on(org_id=org_id)
