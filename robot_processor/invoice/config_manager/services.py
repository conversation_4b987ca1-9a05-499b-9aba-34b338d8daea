from dataclasses import dataclass
from dataclasses import field
from typing import Any

from leyan_proto.digismart.robot_web.common_pb2 import EmptyResponse
from leyan_proto.digismart.robot_web.invoice import config_manager_pb2_grpc
from leyan_proto.digismart.robot_web.invoice.config_manager_pb2 import GetInvoiceConfigByKeyResponse
from leyan_proto.digismart.robot_web.invoice.config_manager_pb2 import GetInvoiceConfigResponse
from loguru import logger
from robot_types.helper import serialize

from robot_processor.invoice.config_manager import ConfigKey
from robot_processor.invoice.config_manager import is_configured
from robot_processor.invoice.config_manager import list_org_configured_configs
from robot_processor.invoice.config_manager import update_config
from robot_processor.invoice.utils import catch_error_as_response
from robot_processor.invoice.utils import load_from_request
from robot_processor.invoice.utils import org_required
from robot_processor.utils import filter_none
from robot_processor.utils import message_to_dict
from robot_processor.utils import pb_value_wrapper
from robot_processor.utils import raise_exception
from robot_processor.utils import struct_wrapper


@dataclass
class ConfigManager:
    org_id: int
    config_vals: dict[ConfigKey, Any] = field(init=False)

    def __post_init__(self):
        self.config_vals = list_org_configured_configs(self.org_id)
        for config_key in ConfigKey:
            self.config_vals.setdefault(config_key, serialize(config_key.extra_info.default_value))

    def get_raw(self, config_key: ConfigKey):
        import robot_types.model

        raw = self.config_vals[config_key]
        match config_key:
            case ConfigKey.NOTIFICATION_TEMPLATES:
                system_templates = [
                    serialize(
                        robot_types.model.invoice.config_manager.NotificationTemplate(
                            id="system_default_email_template",
                            name="邮件默认模板",
                            channel_type=robot_types.model.invoice.config_manager.NotificationChannelType.EMAIL,
                            config=serialize(
                                robot_types.model.invoice.config_manager.EmailTemplateConfig(
                                    subject=[
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "const",
                                            "const": {"value": "您收到一张来自"},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "var",
                                            "var": {"path": "shop_title"},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "const",
                                            "const": {"value": "的电子发票【发票金额："},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "var",
                                            "var": {"path": "invoice_amount"},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "const",
                                            "const": {"value": "】"},
                                        },
                                    ],
                                    content=[
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "const",
                                            "const": {"value": "尊敬的客户您好：您收到来自"},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "var",
                                            "var": {"path": "shop_title"},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "const",
                                            "const": {"value": "的电子发票【发票金额："},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "var",
                                            "var": {"path": "invoice_amount"},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "const",
                                            "const": {"value": "】"},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "const",
                                            "const": {"value": "发票链接："},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "var",
                                            "var": {"path": "invoice_receipt_url"},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "const",
                                            "const": {"value": "，可以通过附件进行下载。"},
                                        },
                                    ],
                                )
                            ),
                        )
                    ),
                    serialize(
                        robot_types.model.invoice.config_manager.NotificationTemplate(
                            id="system_default_platform_template",
                            name="平台默认模板",
                            channel_type=robot_types.model.invoice.config_manager.NotificationChannelType.PLATFORM_MSG,
                            config=serialize(
                                robot_types.model.invoice.config_manager.PlatformTemplateConfig(
                                    content=[
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "const",
                                            "const": {"value": "亲，您的发票已开具，"},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "const",
                                            "const": {"value": "请注意查收。发票链接："},
                                        },
                                        {
                                            "type_spec": {"type": "string"},
                                            "qualifier": "var",
                                            "var": {"path": "invoice_receipt_url"},
                                        },
                                    ]
                                )
                            ),
                        ),
                    ),
                ]
                raw = system_templates + raw
            case ConfigKey.NOTIFICATION_CHANNELS:
                system_channels = [
                    serialize(
                        robot_types.model.invoice.config_manager.NotificationChannel(
                            id="system_default_platform_memo_channel",
                            name="平台订单备注默认渠道",
                            channel_type=robot_types.model.invoice.config_manager.NotificationChannelType.PLATFORM_MEMO,
                            config=dict(),
                        )
                    )
                ]
                raw = system_channels + raw
        return raw

    def get(self, config_key: ConfigKey):
        config_val_raw = self.get_raw(config_key)
        if config_val_raw is None:
            return None
        return config_key.get_converter()(config_val_raw)


class InvoiceConfigMangerServicer(config_manager_pb2_grpc.InvoiceConfigManagerServicer):
    @catch_error_as_response(GetInvoiceConfigResponse)
    @org_required
    def GetInvoiceConfig(self, request, context):
        from robot_processor.invoice.config_manager.defination import symbol_resolver

        org_id = load_from_request().org_id

        response = GetInvoiceConfigResponse(succeed=True)
        config_manager = ConfigManager(org_id)
        for config_key in ConfigKey:
            if config_key is ConfigKey.GOODS_MODE:
                continue
            config_val_raw = config_manager.get_raw(config_key)
            if config_key == ConfigKey.NOTIFICATION_CHANNELS:
                for each in config_val_raw:
                    each["config"].pop("smtp_password", None)
            symbol = symbol_resolver.resolve(config_key, config_key.extra_info.type_spec, org_id=org_id)
            response.data.configs.append(
                GetInvoiceConfigResponse.Config(
                    category=config_key.extra_info.category.label,
                    key=config_key,
                    description=config_key.extra_info.description,
                    symbol=struct_wrapper(serialize(symbol)),
                    val=pb_value_wrapper(config_val_raw),
                )
            )
        return response

    @catch_error_as_response(EmptyResponse)
    @org_required
    def UpdateInvoiceConfig(self, request, context):
        org_id = load_from_request().org_id
        config_key = ConfigKey(request.key)
        config_val = filter_none(message_to_dict(request)["val"])
        config_key.get_converter()(config_val)  # 数据类型校验
        config_key.before_update_check(org_id, config_val).unwrap_or_else(raise_exception)  # 更新前校验
        config_key.update_prepare_process(org_id, config_val)
        update_config(org_id, config_key, config_val)
        return EmptyResponse(succeed=True)

    @catch_error_as_response(GetInvoiceConfigByKeyResponse)
    @org_required
    def GetInvoiceConfigByKey(self, request, context):
        org_id = load_from_request().org_id
        config_key = ConfigKey(request.key)
        response = GetInvoiceConfigByKeyResponse(succeed=True)
        response.data.configured = is_configured(org_id, config_key)
        config_manager = ConfigManager(org_id)
        response.data.val.CopyFrom(pb_value_wrapper(config_manager.get_raw(config_key)))
        return response

    def TestSmtpConnection(self, request, context):
        from redmail import EmailSender

        sender = EmailSender(
            host=request.smtp_host,
            port=request.smtp_port,
            username=request.smtp_username,
            password=request.smtp_password,
        )
        try:
            with sender:
                pass
        except Exception as e:
            logger.opt(exception=e).error(f"测试连接失败 {e}")
            return EmptyResponse(succeed=False, msg="测试连接失败")
        return EmptyResponse(succeed=True)
