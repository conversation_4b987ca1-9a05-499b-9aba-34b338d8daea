from datetime import datetime
from datetime import timedelta
from typing import cast

import arrow
from google.protobuf.empty_pb2 import Empty
from leyan_proto.digismart.robot.invoice.rpa_pb2 import FaceRecognition as pb_FaceRecognition
from leyan_proto.digismart.robot_web.common_pb2 import EmptyResponse
from leyan_proto.digismart.robot_web.invoice import rpa_pb2_grpc
from leyan_proto.digismart.robot_web.invoice.rpa_pb2 import FaceRecognitionResponse
from leyan_proto.digismart.robot_web.invoice.rpa_pb2 import FaceRecognitionResult
from leyan_proto.digismart.robot_web.invoice.rpa_pb2 import FaceRecognitionResultResponse
from leyan_proto.digismart.robot_web.invoice.rpa_pb2 import GetLoginResultResponse
from leyan_proto.digismart.robot_web.invoice.rpa_pb2 import ListRpaAccountsResponse
from leyan_proto.digismart.robot_web.invoice.rpa_pb2 import <PERSON>gin<PERSON><PERSON>ult
from loguru import logger
from requests.adapters import HTTPAdapter
from requests_opentracing import SessionTracing
from result import Err
from result import Ok
from result import as_result

from robot_processor.db import db
from robot_processor.ext import cache
from robot_processor.invoice.common.models import rpa_user_info
from robot_processor.invoice.configs import invoice_config
from robot_processor.invoice.configs import rpa_control_config
from robot_processor.invoice.utils import catch_error_as_response
from robot_processor.invoice.utils import desensitize
from robot_processor.invoice.utils import load_from_request
from robot_processor.invoice.utils import org_required
from robot_processor.invoice.workflow.models import InvoiceIssueQueue
from robot_processor.invoice.workflow.models import InvoiceRequest
from robot_processor.invoice.workflow.models import IssuingType
from robot_processor.utils import current_trace_id
from robot_processor.utils import int_wrapper
from robot_processor.utils import proto_to_json
from robot_processor.utils import response_to_log
from robot_processor.utils import string_wrapper


class InvoiceRpaServicer(rpa_pb2_grpc.InvoiceRpaServicer):
    @catch_error_as_response(ListRpaAccountsResponse)
    @org_required
    def ListRpaAccounts(self, request, context):
        from robot_processor.invoice.tax_bureau.models import Corporate
        from robot_processor.invoice.tax_bureau.models import CorporateTaxer

        org_id = load_from_request().org_id
        corporate_map = {corporate.id: corporate for corporate in Corporate.get_by_org_id(org_id)}
        taxers = CorporateTaxer.get_by_org(org_id)
        response = ListRpaAccountsResponse(succeed=True)
        for taxer in taxers:
            corporate = corporate_map[taxer.corporate_id]
            pb_account = response.TaxerSession(
                id=taxer.id,
                account=taxer.account,
                corporate_id=taxer.corporate_id,
                corporate_name=corporate.name,
                corporate_credit_id=corporate.credit_id,
                taxer_name=taxer.name,
                taxer_phone=desensitize.phone(taxer.phone),
                auth_at=taxer.auth_at.strftime("%Y-%m-%d %H:%M:%S") if taxer.auth_at else "-",
                runtime_id=int_wrapper(taxer.runtime_id),
                runtime_username=string_wrapper(taxer.runtime_username),
                runtime_session=string_wrapper(taxer.runtime_session),
                runtime_session_created_at=string_wrapper(
                    taxer.runtime_session_created_at.strftime("%Y-%m-%d %H:%M:%S")
                    if taxer.runtime_session_created_at
                    else "-"
                ),
            )
            response.data.accounts.append(pb_account)
        return response

    @catch_error_as_response(FaceRecognitionResponse)
    @org_required
    def FaceRecognition(self, request, context):
        from robot_processor.invoice.tax_bureau.models import Corporate

        org_id = load_from_request().org_id
        response = FaceRecognitionResponse(succeed=True)
        response.data.trace_id = current_trace_id()
        corporate = Corporate.get_and_check_corporate(org_id, request.corporate_id).expect("查询企业信息失败")
        rpa_control.face_recognition(
            response.data.trace_id,
            org_id,
            corporate.credit_id,
            request.account,
            request.debug_mode,
        ).expect("发起人脸识别命令失败")
        FaceRecognitionHelper(response.data.trace_id).init(request.corporate_id, request.account)
        return response

    @catch_error_as_response(FaceRecognitionResultResponse)
    @org_required
    def GetFaceRecognitionResult(self, request, context):
        response = FaceRecognitionResultResponse(succeed=True)
        info = FaceRecognitionHelper(request.trace_id).get()
        response.data.status = info["status"]
        match info["status"]:
            case pb_FaceRecognition.READY:
                response.data.ready_info.qrcode = info["qrcode"]
            case pb_FaceRecognition.FAILED:
                response.data.error_message = info["error_message"]
        return response

    def ReportFaceRecognitionQRCode(self, request, context):
        logger.info(f"[{request.trace_id}]收到客户端上报: {proto_to_json(request)}")
        FaceRecognitionHelper(request.trace_id).set_qrcode(request.qrcode)
        return Empty()

    def ReportFaceRecognitionResult(self, request, context):
        logger.info(f"[{request.trace_id}]收到客户端上报: {proto_to_json(request)}")
        FaceRecognitionHelper(request.trace_id).set_result(request)
        return Empty()

    @catch_error_as_response(GetLoginResultResponse)
    @org_required
    def LoginByQRCode(self, request, context):
        from robot_processor.invoice.tax_bureau.models import Corporate

        org_id = load_from_request().org_id
        response = FaceRecognitionResponse(succeed=True)
        response.data.trace_id = current_trace_id()
        corporate = Corporate.get_and_check_corporate(org_id, request.corporate_id).expect("查询企业信息失败")
        rpa_control.login_by_qr_code(
            response.data.trace_id,
            org_id,
            corporate.competent_tax_bureau.name,
            request.debug_mode,
        ).expect("发起人脸识别命令失败")
        LoginHelper(response.data.trace_id).init()
        return response

    def ReportLoginResult(self, request, context):
        logger.info(f"[{request.trace_id}]收到客户端上报: {proto_to_json(request)}")
        LoginHelper(request.trace_id).set_result(request)
        return Empty()

    @catch_error_as_response(GetLoginResultResponse)
    @org_required
    def GetLoginResult(self, request, context):
        response = GetLoginResultResponse(succeed=True)
        login_result = LoginHelper(request.trace_id).get()
        response.data.MergeFrom(login_result)
        response.data.ClearField("taxer_info")
        return response

    def IssueCallback(self, request, context):
        from robot_processor.invoice.notification import NotificationServicer
        from robot_processor.invoice.workflow.errors import InvoiceRequestNotExistError
        from robot_processor.invoice.workflow.service import IssueBroker
        from robot_processor.invoice.workflow.service import PostBackBroker

        logger.bind(method="IssueCallback", invoice_request_id=request.trace_id).info(
            "收到开票回调: {}", proto_to_json(request)
        )
        if not (invoice_request := db.session.get(InvoiceRequest, int(request.trace_id), with_for_update=True)):
            raise InvoiceRequestNotExistError()
        issue_broker = IssueBroker.by_request(invoice_request)

        match request.WhichOneof("result"):
            case "msg":
                InvoiceIssueQueue.close(invoice_request.org_id, invoice_request.id)
                if invoice_request.invoice_issued_id:
                    issue_broker.fetch_issued_failed(request.msg, user_info=rpa_user_info)
                else:
                    issue_broker.issue_failed(request.msg, user_info=rpa_user_info)
            case "invoice_number":
                invoice_number = request.invoice_number.invoice_number
                issuing_time = arrow.get(request.invoice_number.issuing_time).datetime
                issue_broker.issued(rpa_user_info, invoice_number, issuing_time)
            case "invoice_info":
                InvoiceIssueQueue.close(invoice_request.org_id, invoice_request.id)
                issue_broker.issued_with_detail(request.invoice_info)
        notify_servicer = NotificationServicer.by_org_id(invoice_request.org_id)
        for workflow in issue_broker.invoice_workflows:
            try:
                post_back_broker = PostBackBroker(workflow)
                post_back_broker.need_platform_apply_post_back and post_back_broker.do_post_back()
            except PermissionError:
                pass
            try:
                notify_servicer.auto_notify(workflow)
            except Exception as e:
                logger.opt(exception=e).error(f"自动通知失败 {e}")

        return EmptyResponse(succeed=True)


class RpaControl:
    def __init__(self, config=None):
        from robot_processor.invoice.configs import RpaControlConfig

        self.config: RpaControlConfig = config or rpa_control_config
        self.session = SessionTracing()
        adapter = self.Adapter(config=self.config)
        self.session.mount("https://", adapter)
        self.session.mount("http://", adapter)

    def login_by_qr_code(self, trace_id, org_id, competent_tax_bureau, debug_mode):
        api_path = "/invoice/login-by-qrcode"
        try:
            response = self.session.post(
                self.config.endpoint + api_path,
                json=dict(
                    trace_id=trace_id,
                    debug_mode=debug_mode,
                    org_id=org_id,
                    competent_tax_bureau=competent_tax_bureau,
                    timeout=invoice_config.login_timeout.seconds,
                ),
            )
            if not response.ok:
                return Err(Exception(response_to_log(response)))
        except Exception as e:
            return Err(e)

        response_data = response.json()
        if not response_data["succeed"]:
            return Err(Exception(response_data["msg"]))
        return Ok(response_data["data"])

    def face_recognition(self, trace_id, org_id, credit_id, account, debug_mode):
        from robot_processor.invoice.tax_bureau.models import CorporateTaxer

        api_path = "/invoice/face-recognition"
        try:
            auth_info = CorporateTaxer.get_auth_info(credit_id, account)
            response = self.session.post(
                self.config.endpoint + api_path,
                json=dict(
                    trace_id=trace_id,
                    debug_mode=debug_mode,
                    org_id=org_id,
                    timeout=invoice_config.auth_timeout.seconds,
                    **auth_info,
                ),
            )
            if not response.ok:
                return Err(Exception(response_to_log(response)))
        except Exception as e:
            return Err(e)
        response_data = response.json()
        if not response_data["succeed"]:
            return Err(Exception(response_data["msg"]))
        return Ok(response_data["data"])

    @as_result(Exception)
    def issue_invoice(self, org_id, invoice_request: InvoiceRequest, debug_mode: bool):
        from robot_processor.invoice.tax_bureau.models import CorporateTaxer

        auth_info = CorporateTaxer.get_auth_info(invoice_request.seller_credit_id, invoice_request.account)
        # 开票账号信息：税局、纳税人识别号、登录账号、密码
        payload = dict(
            org_id=org_id,
            trace_id=invoice_request.id,
            debug_mode=debug_mode,
            timeout=invoice_config.issue_timeout.seconds,
            **auth_info,
        )
        if invoice_request.issuing_type is IssuingType.BLUE:
            api_path = "/invoice/issue-blue"
            payload["invoice"] = invoice_request.to_issue_blue()
        else:
            api_path = "/invoice/issue-red"
            payload["invoice"] = invoice_request.to_issue_red()

        response = self.session.post(self.config.endpoint + api_path, json=payload)
        logger.bind(invoice_request_id=invoice_request.id).info(
            f"issue blue invoice: payload: {payload}, response: {response_to_log(response)}"
        )
        response_data = response.json()
        # response 有两层，外层为 rpa-control 调用客户端是否成功，第二次 .data 是客户端返回的数据
        match response_data:
            case {"succeed": True, "data": {"succeed": True}}:
                return response_data["data"]
            case {"succeed": False, "msg": err_msg}:
                raise Exception(err_msg)
            case {"succeed": True, "data": {"succeed": False, "msg": err_msg}}:
                raise Exception(err_msg)
            case _:
                raise ValueError(response_data)

    @as_result(Exception)
    def fetch_issued(self, org_id, invoice_request: InvoiceRequest, debug_mode: bool):
        from robot_processor.invoice.tax_bureau.models import CorporateTaxer

        api_path = "/invoice/fetch-issued"
        auth_info = CorporateTaxer.get_auth_info(invoice_request.seller_credit_id, invoice_request.account)
        if not (invoice_issued := invoice_request.invoice_issued):
            raise PermissionError("仅已开票的发票可以获取发票详情")
        payload = dict(
            org_id=org_id,
            trace_id=invoice_request.id,
            debug_mode=debug_mode,
            invoice_number=invoice_issued.invoice_number,
            issuing_time=invoice_issued.issuing_time.strftime("%Y-%m-%d %H:%M:%S"),
            timeout=invoice_config.issue_timeout.seconds,
            **auth_info,
        )
        response = self.session.post(self.config.endpoint + api_path, json=payload, timeout=5)
        response_data = response.json()
        logger.bind(invoice_request_id=invoice_request.id).info(
            f"fetch issued: payload: {payload}, response: {response_to_log(response)}"
        )
        if not response_data["succeed"]:
            raise Exception(response_data["msg"])
        return response_data["data"]

    class Adapter(HTTPAdapter):
        def __init__(self, *args, config, **kwargs):
            from robot_processor.invoice.configs import RpaControlConfig

            self._client_config: RpaControlConfig = config
            super().__init__(*args, **kwargs)

        def send(self, request, **kwargs):
            kwargs.setdefault("timeout", self._client_config.request_timeout)
            return super().send(request, **kwargs)


class FaceRecognitionHelper:
    def __init__(self, trace_id, cache_manager=None):
        self._cache = cache_manager or cache
        self._trace_id = trace_id

    def init(self, corporate_id: int, account: str):
        data = dict(
            status=pb_FaceRecognition.PENDING,
            corporate_id=corporate_id,
            account=account,
            start_at=datetime.now(),
            qrcode=None,
            error_message=None,
        )
        self._cache.set(self._cache_key, data, timeout=invoice_config.auth_timeout.seconds)

    def get(self):
        info = self._cache.get(self._cache_key)
        if info is None:
            return dict(
                status=pb_FaceRecognition.FAILED,
                corporate_id=None,
                account=None,
                start_at=None,
                qrcode=None,
                error_message="人脸识别请求已失效",
            )

        match info["status"]:
            case pb_FaceRecognition.Status.PENDING:
                # 等待客户端上报二维码的时间为 1 分钟
                if datetime.now() - info["start_at"] > invoice_config.auth_wait_qrcode_timeout:
                    info["status"] = pb_FaceRecognition.FAILED
                    info["error_message"] = "获取人脸识别二维码超时，请检查客户端人脸识别状态，或重新登录"
            case pb_FaceRecognition.READY:
                # 等待人脸识别结果的时间为 5 分钟
                if datetime.now() - info["start_at"] > invoice_config.auth_timeout:
                    info["status"] = pb_FaceRecognition.FAILED
                    info["error_message"] = "获取人脸识别结果超时，请重新登录"
        return info

    def set_qrcode(self, qrcode):
        info = self.get()
        if info["status"] != pb_FaceRecognition.PENDING:
            return
        info["qrcode"] = qrcode
        info["status"] = pb_FaceRecognition.READY
        self._cache.set(self._cache_key, info, timeout=invoice_config.auth_timeout.seconds)

    def set_result(self, result: FaceRecognitionResult):
        from robot_processor.invoice.tax_bureau.models import CorporateTaxer

        info = self.get()
        if info["status"] != pb_FaceRecognition.READY:
            return
        info["status"] = result.status
        if result.WhichOneof("extra_info") == "error_message":
            info["error_message"] = result.error_message
        self._cache.set(self._cache_key, info, timeout=invoice_config.auth_timeout.seconds)
        if result.status == pb_FaceRecognition.SUCCESS:
            taxer = CorporateTaxer.get_by_corporate_account(info["corporate_id"], info["account"])
            taxer.auth(datetime.now())

    def clear(self):
        self._cache.delete(self._cache_key)

    @property
    def _cache_key(self):
        return f"invoice:rpa:face-recognition:{self._trace_id}"


class LoginHelper:
    def __init__(self, trace_id):
        self._trace_id = trace_id

    @property
    def _cache(self):
        import redis

        return cast(redis.Redis, cache.cache._write_client)

    @property
    def _cache_key(self):
        return f"invoice:rpa:login-by-qrcode:{self._trace_id}"

    def init(self):
        login_result = LoginResult(trace_id=self._trace_id, status=LoginResult.PENDING)
        self.set(login_result.SerializeToString())

    def set(self, value: str | bytes):
        self._cache.setex(self._cache_key, invoice_config.login_cache_timeout, value)

    def get(self):
        cached_data = self._cache.get(self._cache_key)
        ttl = self._cache.ttl(self._cache_key)
        if ttl == -2:
            return LoginResult(
                trace_id=self._trace_id, status=LoginResult.FAILED, error_message=string_wrapper("登录请求已失效")
            )
        past = invoice_config.login_cache_timeout - timedelta(seconds=ttl)
        login_result = LoginResult()
        login_result.ParseFromString(cached_data)

        if login_result.status == LoginResult.PENDING and past > invoice_config.login_wait_qrcode_timeout:
            login_result.status = LoginResult.FAILED
            login_result.error_message.value = "获取登录二维码超时，请检查客户端状态并重新登录"
        elif login_result.status == LoginResult.READY and past > invoice_config.login_timeout:
            login_result.status = LoginResult.FAILED
            login_result.error_message.value = "获取登录结果超时，请重新登录"
        return login_result

    def set_result(self, login_result: LoginResult):
        from sqlalchemy import or_ as sql_or
        from sqlalchemy.sql.operators import eq as sql_eq
        from sqlalchemy.sql.operators import ne as sql_ne

        from robot_processor.invoice.tax_bureau.models import Corporate
        from robot_processor.invoice.tax_bureau.models import CorporateTaxer

        if login_result.status == LoginResult.SUCCESS:
            corporate_taxer: CorporateTaxer | None = (
                CorporateTaxer.query.join(Corporate, sql_eq(Corporate.id, CorporateTaxer.corporate_id))
                .filter(
                    sql_eq(Corporate.credit_id, login_result.taxer_info.taxpayer_id),
                    sql_eq(Corporate.org_id, login_result.org_id),
                    sql_ne(Corporate.status, Corporate.Status.DELETED),
                    sql_ne(CorporateTaxer.status, CorporateTaxer.Status.DELETED),
                    sql_or(
                        sql_eq(CorporateTaxer.account, login_result.taxer_info.account),
                        sql_eq(CorporateTaxer.account, login_result.taxer_info.identifier),
                        sql_eq(CorporateTaxer.account, login_result.taxer_info.phone),
                    ),
                )
                .one_or_none()
            )
            if corporate_taxer:
                corporate_taxer.update_runtime_info(login_result.taxer_info)
            else:
                login_result.status = LoginResult.FAILED
                login_result.error_message.value = (
                    f"扫码登录账号 [{login_result.taxer_info.runtime_session}] 未在飞梭后台配置"
                )

        self.set(login_result.SerializeToString())


rpa_control = RpaControl()
