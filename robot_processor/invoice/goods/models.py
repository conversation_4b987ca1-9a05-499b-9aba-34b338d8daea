from decimal import Decimal
from enum import IntEnum
from enum import StrEnum
from typing import TYPE_CHECKING
from typing import Any
from typing import ClassVar
from typing import Optional
from typing import cast

import robot_types.core
import sqlalchemy as sa
from google.protobuf.json_format import ParseDict
from leyan_proto.digismart.robot.invoice.goods_pb2 import GoodsIssuingRule as pb_GoodsIssuingRule
from leyan_proto.digismart.robot.invoice.goods_pb2 import \
    GoodsIssuingRuleEditableView as pb_GoodsIssuingRuleEditableView
from leyan_proto.digismart.robot.invoice.goods_pb2 import GoodsItem as pb_GoodsItem
from leyan_proto.digismart.robot.invoice.goods_pb2 import IssuingItem as pb_IssuingItem
from leyan_proto.digismart.robot.invoice.goods_pb2 import TaxCode as pb_TaxCode
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
from leyan_proto.digismart.robot.symbol_table_pb2 import SimpleFilter as pb_SimpleFilter
from pydantic import BaseModel
from robot_types.helper import deserialize
from robot_types.helper.predefined import BizType
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.sql.operators import eq as sql_eq

from robot_processor.db import DbBaseModel
from robot_processor.db import db
from robot_processor.db import in_transaction
from robot_processor.invoice.common.models import OperationLog
from robot_processor.invoice.goods.schemas import ExemptionType
from robot_processor.invoice.goods.schemas import PreferentialPolicy
from robot_processor.logging import to_log
from robot_processor.symbol_table.filters import FilterContextField
from robot_processor.symbol_table.filters import FilterContextMixin
from robot_processor.utils import ensure_mirror_of_pb_enum
from robot_processor.utils import int_wrapper
from robot_processor.utils import message_to_dict
from robot_processor.utils import pb_value_wrapper
from robot_processor.utils import string_wrapper

if TYPE_CHECKING:
    from robot_processor.invoice.goods.services import GoodsBroker


class TaxCode(DbBaseModel):
    """税务局提供单税务分类编码表"""

    __tablename__ = "invoice_goods_tax_info"
    # 商品税收分类编码
    code = mapped_column("tax_code", sa.String(19), primary_key=True)
    parent = mapped_column(sa.String(19), nullable=True)
    # 货物和劳务名称
    name = mapped_column(sa.String(256))
    # 商品和服务分类简称
    name_abbr = mapped_column(sa.String(64))
    # 说明
    description = mapped_column(sa.Text())
    # 税率
    tax_rate = mapped_column(sa.DECIMAL(3, 2), nullable=True)

    def to_pb(self):
        pb = pb_TaxCode()
        pb.tax_code = self.code
        if self.parent:
            pb.parent.value = self.parent
        pb.name = self.name
        if self.name_abbr:
            pb.name_abbr.value = self.name_abbr
        if self.description:
            pb.description.value = self.description
        if self.tax_rate is not None:
            pb.tax_rate.value = str(self.tax_rate)
        return pb

    @classmethod
    def get_by_parent(cls, parent: str | None):
        stmt = sa.select(cls).where(sql_eq(cls.parent, parent))
        result = db.session.execute(stmt)
        return result.scalars().all()

    @classmethod
    def get_by_keyword(cls, keyword: str | None):
        stmt = sa.select(cls).where(cls.tax_rate.isnot(None))
        if keyword is None:
            stmt = stmt.limit(10)
        else:
            stmt = stmt.where(
                sa.or_(
                    sql_eq(cls.code, keyword),
                    cls.name.like(f"%{keyword}%"),
                    cls.name_abbr.like(f"%{keyword}%"),
                    cls.description.like(f"%{keyword}%"),
                ),
                cls.name_abbr != "",
            )

        result = db.session.execute(stmt)
        return result.scalars().all()

    @classmethod
    def get_name_abbr_by_code(cls, tax_code: str):
        stmt = sa.select(cls.name_abbr).where(sql_eq(cls.code, tax_code))
        return db.session.execute(stmt).scalar_one_or_none()


class IssuingItem(DbBaseModel):
    __tablename__ = "invoice_issuing_item"

    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    org_id: Mapped[int]
    tax_code: Mapped[str] = mapped_column(sa.String(19))

    # 项目名称，用于发票明细行中 “项目名称” 列; 必填
    title: Mapped[str] = mapped_column(sa.String(128), default="")
    title_template: Mapped[list[dict]] = mapped_column(sa.JSON, nullable=True)
    # 规格型号，用于发票明细行中 “规格型号” 列
    description: Mapped[str | None] = mapped_column(sa.String(128))

    # [不可编辑]税收分类简称，用于维护税收规则时的展示，来自 TaxCode.name_abbr
    tax_name_abbr: Mapped[str] = mapped_column(sa.String(64))
    # 税率，用于发票明细行中 “税率” 列; 必填
    tax_rate: Mapped[Decimal] = mapped_column(sa.DECIMAL(3, 2))
    # 小规模纳税人税率，用于发票明细行中 “税率” 列; 必填
    small_taxpayer_tax_rate: Mapped[Decimal] = mapped_column(sa.DECIMAL(3, 2))
    unit: Mapped[str | None] = mapped_column(sa.String(32), nullable=True)
    price: Mapped[Decimal | None] = mapped_column(sa.DECIMAL(12, 2), nullable=True)
    is_taxed: Mapped[bool] = mapped_column(sa.Boolean, nullable=False, default=True)
    exemption_type: Mapped[ExemptionType] = mapped_column(sa.Enum(ExemptionType), default=ExemptionType.NORMAL)
    preferential_policy: Mapped[PreferentialPolicy] = mapped_column(
        sa.Enum(PreferentialPolicy), default=PreferentialPolicy.NO_PREFERENTIAL
    )
    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)

    def to_pb(self):
        tax_rate_str = "" if self.tax_rate is None else str(self.tax_rate)
        small_taxpayer_tax_rate_str = "" if self.small_taxpayer_tax_rate is None else str(self.small_taxpayer_tax_rate)

        return pb_IssuingItem(
            id=self.id,
            title=self.title,
            title_template=pb_value_wrapper(self.title_template),
            tax_name_abbr=self.tax_name_abbr,
            tax_code=self.tax_code,
            tax_rate=tax_rate_str,
            small_taxpayer_tax_rate=small_taxpayer_tax_rate_str,
            description=string_wrapper(self.description),
            unit=string_wrapper(self.unit),
            price=string_wrapper(self.price),
            is_taxed=self.is_taxed,
            tax_exemption_type=self.exemption_type,  # type: ignore[arg-type]
            preferential_tax_policy=self.preferential_policy,  # type: ignore[arg-type]
        )

    def to_workflow_pb(self, *, is_small_taxpayer: bool, goods_item: Optional["GoodsItem"]):
        from robot_processor.invoice.workflow.service.issuing_item_title_broker import IssuingItemTitleBroker

        tax_rate = self.small_taxpayer_tax_rate if is_small_taxpayer else self.tax_rate
        if tax_rate is None:
            tax_rate_str = ""  # type: ignore[unreachable]
        else:
            tax_rate_str = str(tax_rate)
        pb = pb_InvoiceWorkflow.IssuingItem(
            issuing_item_id=self.id,
            title=IssuingItemTitleBroker.render_title_template(self, goods_item),
            tax_name_abbr=self.tax_name_abbr,
            tax_code=self.tax_code,
            tax_rate=tax_rate_str,
            description=string_wrapper(self.description),
            unit=string_wrapper(self.unit),
            price=string_wrapper(self.price),
            tax_exemption_type=self.exemption_type,  # type: ignore[arg-type]
            preferential_tax_policy=self.preferential_policy,  # type: ignore[arg-type]
        )
        if goods_item is not None:
            pb.goods_item.MergeFrom(goods_item.to_pb())
        return pb

    @classmethod
    @in_transaction()
    def create(cls, org_id: int, view: pb_IssuingItem):
        self = cls(org_id=org_id)
        self.update(view)
        db.session.add(self)
        return self

    @in_transaction()
    def update(self, view: pb_IssuingItem):
        self.tax_code = view.tax_code
        self.title_template = cast(list, message_to_dict(view.title_template))
        self.tax_name_abbr = view.tax_name_abbr
        self.tax_rate = Decimal(view.tax_rate)
        if view.small_taxpayer_tax_rate:
            self.small_taxpayer_tax_rate = Decimal(view.small_taxpayer_tax_rate)
        self.description = view.description.value if view.HasField("description") else None
        self.is_taxed = view.is_taxed
        self.price = view.price.value if view.HasField("price") else None  # type: ignore[assignment]
        self.unit = view.unit.value if view.HasField("unit") else None
        self.exemption_type = view.tax_exemption_type  # type: ignore[assignment]
        self.preferential_policy = view.preferential_tax_policy  # type: ignore[assignment]

    @classmethod
    def list_by_filter_and_paginate(cls, org_id: int, filters, paginate):
        stmt = sa.select(cls).select_from(cls).where(sql_eq(cls.org_id, org_id), cls.deleted.is_(False))
        count_stmt = stmt.with_only_columns(sa.func.count())
        paginate.total.value = db.session.execute(count_stmt).scalar()
        if paginate.total.value == 0:
            return []
        if paginate.HasField("order_by"):
            stmt = stmt.order_by(sa.text(paginate.order_by.value))
        page, per_page = paginate.page.value, paginate.per_page.value
        stmt = stmt.limit(per_page).offset(per_page * (page - 1))
        result = db.session.execute(stmt)
        items = result.scalars().all()
        return items

    @classmethod
    def get_by_id(cls, org_id: int, item_id: int):
        stmt = sa.select(cls).where(
            sql_eq(cls.org_id, org_id),
            sql_eq(cls.id, item_id),
        )
        result = db.session.execute(stmt)
        return result.scalar_one()


class GoodsIssuingRule(DbBaseModel):
    """客户自定义的SKU商品-税收分类匹配规则"""

    __tablename__ = "invoice_goods_tax_info_rule"
    __table_args__ = (sa.Index("idx_org_id_sku_id", "org_id"),)

    @ensure_mirror_of_pb_enum(pb_GoodsIssuingRule.Status)
    class Status(IntEnum):
        ENABLED = 0
        DISABLED = 1

    # 非业务唯一标识
    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    # 租户 ID
    # 税收分类匹配规则集在每个租户内配置并生效
    org_id = mapped_column(sa.Integer, nullable=False, comment="租户ID")
    # 税收分类匹配规则的名称，仅用作辅助展示
    name = mapped_column(sa.String(64), nullable=True, comment="规则名称")
    # 规则状态，可以临时禁用规则
    status = mapped_column(sa.Enum(Status), nullable=False, default=Status.ENABLED)
    # 规则优先级，数字越小优先级越高
    # 在租户内可以有多个规则，匹配时按照优先级从高到低匹配
    order = mapped_column(sa.Integer, nullable=False, comment="规则优先级")
    # 配置规则条件
    filter = mapped_column(sa.JSON, comment="配置规则条件")
    # 规则条件匹配后，应用的发票明细
    issuing_item_id = mapped_column(sa.Integer, nullable=False, comment="发票明细商品")

    def to_predicate(self):
        from robot_types.core import Condition
        from robot_types.core import Filter

        return Filter(
            relation="and",
            conditions=[deserialize(condition, Condition) for condition in self.filter["conditions"]],
        )

    def to_pb(self):
        issuing_item = IssuingItem.get_by_id(self.org_id, self.issuing_item_id)
        return pb_GoodsIssuingRule(
            id=self.id,
            name=self.name or "",
            status=self.status,
            filter=ParseDict(self.filter, pb_SimpleFilter()) if self.filter else None,
            issuing_item=issuing_item.to_pb() if issuing_item else None,
        )

    @classmethod
    def log_update(cls, user, org_id, diff):
        OperationLog.create(
            OperationLog.Model.GOODS_CUSTOMIZE_RULESET,
            org_id,
            user,
            changes=dict(diff=diff),
        )

    @in_transaction()
    def edit(self, view: pb_GoodsIssuingRuleEditableView, diff: dict):
        diff["action"] = "update"
        new_rule_name = view.name.value if view.HasField("name") else None
        if self.name != new_rule_name:
            diff["name"] = dict(old=self.name, new=new_rule_name)
            self.name = new_rule_name
        if self.status != view.status:
            diff["status"] = dict(old=self.status, new=view.status)
            self.status = view.status
        new_rule_filter = message_to_dict(view.filter)
        if self.filter != new_rule_filter:
            diff["filter"] = dict(old=self.filter, new=new_rule_filter)
            self.filter = new_rule_filter
        if self.issuing_item_id != view.issuing_item.id:
            diff["issuing_item_id"] = dict(old=self.issuing_item_id, new=view.issuing_item.id)
            self.issuing_item_id = view.issuing_item.id

    @classmethod
    @in_transaction()
    def create(cls, org_id: int, view: pb_GoodsIssuingRuleEditableView, diff: dict):
        self = cls(org_id=org_id, order=999)
        self.edit(view, diff=diff)
        diff["action"] = "create"
        db.session.add(self)
        return self

    @in_transaction()
    def delete(self, diff: dict):
        diff["action"] = "delete"
        db.session.delete(self)

    @classmethod
    @in_transaction()
    def update_sort(cls, org_id: int, sort_ids: list[int], diff: dict):
        diff["action"] = "sort"
        rules = cls.get_by_org_id(org_id)
        rule_mapping: dict[int, GoodsIssuingRule] = {rule.id: rule for rule in rules}
        if set(rule_mapping.keys()).difference(set(sort_ids)):
            raise ValueError("排序的规则 ID 不匹配")
        diff["sort"] = dict(old=[rule.id for rule in rules], new=sort_ids)
        for order, rule_id in enumerate(sort_ids):
            rule_mapping[rule_id].order = order

    @classmethod
    @in_transaction()
    def update(cls, org_id, rules: list[pb_GoodsIssuingRuleEditableView]):
        diff = []
        current_ruleset_mapper = {rule.id: rule for rule in cls.get_by_org_id(org_id)}
        for order, pb_rule_view in enumerate(rules):
            rule_diff: dict[str, Any] = {}
            if pb_rule_view.HasField("id"):
                rule = current_ruleset_mapper.pop(pb_rule_view.id.value)
                rule_diff["action"] = "update"
            else:
                rule = cls(org_id=org_id)
                db.session.add(rule)
                rule_diff["action"] = "create"

            new_rule_name = pb_rule_view.name.value if pb_rule_view.HasField("name") else None
            if rule.name != new_rule_name:
                rule_diff["name"] = dict(old=rule.name, new=new_rule_name)
                rule.name = new_rule_name
            if rule.status != pb_rule_view.status:
                rule_diff["status"] = dict(old=rule.status, new=pb_rule_view.status)
                rule.status = pb_rule_view.status
            if rule.order != order:
                rule_diff["order"] = dict(old=rule.order, new=order)
                rule.order = order
            new_rule_filter = message_to_dict(pb_rule_view.filter)
            if rule.filter != new_rule_filter:
                rule_diff["filter"] = dict(old=rule.filter, new=new_rule_filter)
                rule.filter = new_rule_filter
            if rule.issuing_item_id != pb_rule_view.issuing_item.id:
                rule_diff["issuing_item_id"] = dict(old=rule.issuing_item_id, new=pb_rule_view.issuing_item.id)
                rule.issuing_item_id = pb_rule_view.issuing_item.id
            db.session.flush()
            rule_diff["id"] = rule.id
            diff.append(rule_diff)

        for rule_to_remove in current_ruleset_mapper.values():
            diff.append(dict(action="delete", id=rule_to_remove.id))
            db.session.delete(rule_to_remove)

        return diff

    @classmethod
    def get_by_org_id(cls, org_id):
        stmt = sa.select(cls).where(cls.org_id == org_id).order_by(cls.order.asc())
        result = db.session.execute(stmt)
        return result.scalars().all()

    @classmethod
    def get_by_id(cls, org_id: int, rule_id: int):
        stmt = sa.select(cls).where(cls.org_id == org_id, cls.id == rule_id)
        result = db.session.execute(stmt)
        return result.scalar_one_or_none()

    @classmethod
    def list_by_filter(cls, org_id, filter_: robot_types.core.Filter):
        sql_criteria_resolver = BizType.INVOICE_GOODS_ISSUING_RULE_FILTER_CONTEXT.provide_sql_criteria_resolver()
        stmt = (
            sa.select(GoodsIssuingRule)
            .where(GoodsIssuingRule.org_id == org_id)
            .join(IssuingItem, IssuingItem.id == GoodsIssuingRule.issuing_item_id)
        )
        if filter_.conditions:
            stmt = stmt.where(sql_criteria_resolver.filter_to_sql_criteria(filter_))
        result = db.session.execute(stmt.order_by(GoodsIssuingRule.order.asc()))
        return result.scalars().all()


class GoodsIssuingItem(DbBaseModel):
    __bind_key__ = "dgt_item"
    __tablename__ = "invoice_item_tax_config"
    __table_args__ = (
        sa.Index(
            "uk_sid_datasource_goods_item",
            "sid",
            "datasource",
            "goods_item",
            unique=True,
        ),
    )

    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    datasource = mapped_column(sa.String(64), nullable=False)
    sid = mapped_column(sa.String(64), nullable=False)
    goods_item = mapped_column(sa.String(128), nullable=False)
    issuing_item_id = mapped_column(sa.Integer, nullable=False)

    @classmethod
    @in_transaction()
    def create_or_update(cls, sid, datasource: str, goods_item: str, issuing_item_id: int):
        from sqlalchemy.dialects.mysql import insert as sql_insert

        db.session.execute(
            sql_insert(cls)
            .values(
                sid=sid,
                datasource=datasource,
                goods_item=goods_item,
                issuing_item_id=issuing_item_id,
            )
            .on_duplicate_key_update(issuing_item_id=issuing_item_id)
        )


class _DatasourceType(StrEnum):
    UNKNOWN = "unknown"
    ERP = "erp"
    PLATFORM = "platform"


class GoodsItem(BaseModel):
    """飞梭商品"""

    class Config:
        arbitrary_types_allowed = True

    DatasourceType: ClassVar = _DatasourceType

    @ensure_mirror_of_pb_enum(pb_GoodsItem.Datasource)
    class Datasource(IntEnum):
        DATASOURCE_UNSPECIFIED = 0, _DatasourceType.UNKNOWN
        # erp: 聚水潭
        JST = pb_GoodsItem.JST, _DatasourceType.ERP
        # erp: 旺店通企业版
        WDT = pb_GoodsItem.WDT, _DatasourceType.ERP
        # erp: 旺店通旗舰版
        WDTULTI = pb_GoodsItem.WDTULTI, _DatasourceType.ERP
        # platform: 抖店
        DOUDIAN = pb_GoodsItem.DOUDIAN, _DatasourceType.PLATFORM
        # platform: 淘宝
        TAOBAO = pb_GoodsItem.TAOBAO, _DatasourceType.PLATFORM
        # platform: 天猫
        TMALL = pb_GoodsItem.TMALL, _DatasourceType.PLATFORM
        # platform: 拼多多
        PDD = pb_GoodsItem.PDD, _DatasourceType.PLATFORM
        # platform: 京东
        JD = pb_GoodsItem.JD, _DatasourceType.PLATFORM
        # erp: 万里牛
        WANLINIU = pb_GoodsItem.WANLINIU, _DatasourceType.ERP
        # erp: 管易云
        GUANYIYUN = pb_GoodsItem.GUANYIYUN, _DatasourceType.ERP
        # erp: 吉客云
        JACKYUN = pb_GoodsItem.JACKYUN, _DatasourceType.ERP
        # erp: 快麦
        KUAIMAI = pb_GoodsItem.KUAIMAI, _DatasourceType.ERP
        # platform: 快手
        KUAISHOU = pb_GoodsItem.KUAISHOU, _DatasourceType.PLATFORM

        @property
        def pb_value(self):
            return pb_GoodsItem.Datasource.Value(self.name)

        def __new__(cls, value, _):
            self = int.__new__(cls, value)
            self._value_ = value
            return self

        def __init__(self, _, datasource_type: _DatasourceType):
            self.datasource_type = datasource_type

        @classmethod
        def safe_init(cls, value):
            try:
                return cls(value)  # type: ignore[call-arg]
            except ValueError:
                try:
                    return cls[value]
                except KeyError:
                    return cls.DATASOURCE_UNSPECIFIED

    class CombinedItem(BaseModel):
        title: str
        num: int
        description: str | None = None
        short_title: str | None = None

        sku: str | None = None
        sku_outer: str | None = None
        spu: str | None | None = None
        spu_outer: str | None = None
        picture: str | None = None

        def to_pb(self):
            return pb_GoodsItem.CombinedItem(
                title=string_wrapper(self.title),
                num=int_wrapper(self.num),
                description=string_wrapper(self.description),
                short_title=string_wrapper(self.short_title),
                picture=string_wrapper(self.picture),
                sku=string_wrapper(self.sku),
                sku_outer=string_wrapper(self.sku_outer),
                spu=string_wrapper(self.spu),
                spu_outer=string_wrapper(self.spu_outer),
            )

    id: int = 0
    datasource: Datasource
    datasource_type: DatasourceType
    sid: str
    is_combined: bool = False

    # 商品名称 / 组合装名称
    title: str
    # 商品规格属性 / 组合装规格属性
    description: str | None = None
    # 商品简称 / 组合装简称
    short_title: str | None = None
    # 商品图片
    picture: str | None = None

    sku: str | None = None
    sku_outer: str | None = None
    spu: str | None = None
    spu_outer: str | None = None
    # 如果是组合装商品，则为组合装明细
    details: list[CombinedItem] | None = None
    issuing_item: IssuingItem | None = None

    @classmethod
    def from_pb(cls, pb_goods_item: pb_GoodsItem):
        datasource = GoodsItem.Datasource(pb_goods_item.datasource)  # type: ignore[call-arg]
        return cls(
            datasource=datasource,
            datasource_type=datasource.datasource_type,
            sid=pb_goods_item.sid,
            title=pb_goods_item.title.value,
            short_title=pb_goods_item.short_title.value if pb_goods_item.HasField("short_title") else None,
            description=pb_goods_item.description.value if pb_goods_item.HasField("description") else None,
            picture=pb_goods_item.picture.value if pb_goods_item.HasField("picture") else None,
            sku=pb_goods_item.sku.value if pb_goods_item.HasField("sku") else None,
            sku_outer=pb_goods_item.sku_outer.value if pb_goods_item.HasField("sku_outer") else None,
            spu=pb_goods_item.spu.value if pb_goods_item.HasField("spu") else None,
            spu_outer=pb_goods_item.spu_outer.value if pb_goods_item.HasField("spu_outer") else None,
            is_combined=pb_goods_item.is_combined,
        )

    def to_pb(self):
        return pb_GoodsItem(
            datasource=self.datasource.pb_value,
            sid=self.sid,
            title=string_wrapper(self.title),
            short_title=string_wrapper(self.short_title),
            description=string_wrapper(self.description),
            picture=string_wrapper(self.picture),
            sku=string_wrapper(self.sku),
            sku_outer=string_wrapper(self.sku_outer),
            spu=string_wrapper(self.spu),
            spu_outer=string_wrapper(self.spu_outer),
            is_combined=self.is_combined,
            details=[item.to_pb() for item in self.details] if self.details else None,
            issuing_item=self.issuing_item.to_pb() if self.issuing_item else None,
            is_issuing_item_set=self.issuing_item is not None,
        )

    def to_log(self):
        return "GoodsItem(datasource={datasource}, sku_outer={sku_outer}, spu_outer={spu_outer})".format(
            datasource=self.datasource.name, sku_outer=self.sku_outer, spu_outer=self.spu_outer
        )

    def to_workflow_issuing_item_pb(
        self, goods_mode, goods_broker: "GoodsBroker", is_small_taxpayer, num: str, amount: str
    ):
        from robot_types.model.invoice.config_manager import GoodsMode

        issuing_item = goods_broker.get_issuing_item(
            source="erp" if goods_mode == GoodsMode.ERP else "platform", goods_item=self
        ).unwrap_or(IssuingItem())
        pb_issuing_item = issuing_item.to_workflow_pb(is_small_taxpayer=is_small_taxpayer, goods_item=self)
        pb_issuing_item.num.value = num
        pb_issuing_item.amount = str(amount)
        return pb_issuing_item


class TradeGoodsItem(GoodsItem):
    num: str
    paid: str


@to_log.register
def goods_item_to_log(val: GoodsItem, **kwargs):
    return "GoodsItem(datasource={datasource}, sku_outer={sku_outer}, spu_outer={spu_outer})".format(
        datasource=val.datasource.name, sku_outer=val.sku_outer, spu_outer=val.spu_outer
    )


class GoodsItemFilterContext(FilterContextMixin):
    __name__ = "goods_item"

    title: str = FilterContextField("商品名称")
    short_title: str = FilterContextField("商品简称")
    description: str = FilterContextField("商品规格属性")
    spu: str = FilterContextField("商品SPUID")
    spu_outer: str = FilterContextField("SPU商家编码")
    sku: str = FilterContextField("商品SKUID")
    sku_outer: str = FilterContextField("SKU商家编码")
    is_issuing_item_set: bool = FilterContextField("是否设置税收规则")
