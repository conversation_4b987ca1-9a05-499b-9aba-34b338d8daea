import json
import time
from collections import defaultdict
from contextlib import contextmanager
from copy import deepcopy
from dataclasses import dataclass
from dataclasses import field
from dataclasses import replace
from functools import cached_property
from typing import Any
from typing import Final
from typing import Literal

import objectpath
from beartype import beartype
from flask import current_app
from flask import request
from loguru import logger
from result import Err
from result import Ok
from result import Result
from robot_types.core import Condition
from robot_types.core import Filter
from robot_types.core import Predicate
from robot_types.core import Scope
from robot_types.core import Symbol
from robot_types.core import SymbolTable
from robot_types.core import Value
from robot_types.core.value import PathIndicator
from robot_types.core.value import PredicateIndicator
from robot_types.core.value import Var
from robot_types.helper import ValueResolver
from robot_types.helper import deserialize
from robot_types.helper.form_composer import FormComposer
from robot_types.helper.type_checker import DataTypeError
from robot_types.helper.type_checker import Type<PERSON>hecker
from sqlalchemy.orm.attributes import flag_modified

from robot_metrics import stats
from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.business_order.errors import BusinessOrderError
from robot_processor.business_order.errors import BusinessOrderValidateError
from robot_processor.business_order.errors import ComponentValueUniqueRemind
from robot_processor.business_order.job_action import JobAction
from robot_processor.business_order.job_wrappers.wrapper import execute_one_job
from robot_processor.business_order.mini_app.schema import BuyerBusinessOrderSchema
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import get_form_composer
from robot_processor.business_order.schema import BusinessOrderSchema
from robot_processor.business_order.schema import CreateAction
from robot_processor.business_order.tasks import package_jobs
from robot_processor.business_order.utils.operate_debounce import BusinessOrderOperateLock
from robot_processor.client import action_client
from robot_processor.client import app_config
from robot_processor.client import asgard_client
from robot_processor.client import kiosk_client
from robot_processor.client import risk_control_client
from robot_processor.constants import SHOP_EXPIRE_MSG
from robot_processor.db import in_transaction
from robot_processor.db import no_auto_flush
from robot_processor.enums import AssigneeRule
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import Creator
from robot_processor.enums import FromType
from robot_processor.enums import JobProcessMark
from robot_processor.enums import JobStatus
from robot_processor.enums import ShopStatus
from robot_processor.enums import WidgetValueUniqueCheckType
from robot_processor.error.errors import BusinessOrderValueUniqueConstraintError
from robot_processor.ext import cache
from robot_processor.ext import db
from robot_processor.form.getter import get_non_ref_widgets_by_form_id
from robot_processor.form.models import Form
from robot_processor.form.models import FormSymbol
from robot_processor.form.models import FormVersion
from robot_processor.form.models import Step
from robot_processor.form.schemas import StepValidationRule
from robot_processor.form.symbol_table import FormSymbolScope
from robot_processor.logging import vars as log_vars
from robot_processor.provider.schema import PrefilledReqSchema
from robot_processor.provider.schema import PrefilledRespSchema
from robot_processor.shop.kiosk_models import KioskOrg
from robot_processor.shop.models import Shop
from robot_processor.types.common import BuyerInfo
from robot_processor.utils import ResultUtil
from robot_processor.utils import filter_dict
from robot_processor.utils import normalize_buyer_uid
from robot_processor.utils import unwrap_optional
from robot_processor.widget.payment_method_utils import get_payment_method_keys


class BusinessManager:

    # 通过智能建单的工单数据中需要保留原始的事件数据
    RAW_EVENT_DATA_FIELD: Final = "_raw_event"

    def __init__(
        self,
        form: Form,
        shop: Shop,
        creator: AccountDetailV2,
        from_type: FromType,
        creation_lock: BusinessOrderOperateLock | None = None,
        # 由前端控制提交的表单，不使用组件的默认值配置
        ignore_default_data_config: bool = False,
    ):
        self.form = form
        self.shop = shop
        self.form_shop = Form.Utils.form_wrapper(form, shop)
        self.creator = creator
        self.from_type = from_type
        self.context = dict(
            form=form,
            shop=shop,
            form_shop=self.form_shop,
            creator=creator,
            from_type=from_type,
        )
        if not (form_version := form.versions.first()):
            raise BusinessOrderError("抱歉，当前工单模板未发布，无法创建", **self.context)
        self.form_version: FormVersion = form_version
        self.context["form_version"] = self.form_version
        self.form_composer = get_form_composer(self.form_version.id)
        self.creation_lock = creation_lock
        self.ignore_default_data_config = ignore_default_data_config

    @classmethod
    def from_business_order(cls, business_order: BusinessOrder):
        return cls(
            form=unwrap_optional(business_order.form),
            shop=unwrap_optional(business_order.shop),
            creator=business_order.get_creator_info(),
            from_type=business_order.from_type,
        )

    def with_lock(self, creation_lock: BusinessOrderOperateLock | None):
        return BusinessManager(
            form=self.form,
            shop=self.shop,
            creator=self.creator,
            from_type=self.from_type,
            creation_lock=creation_lock,
        )

    @cached_property
    def first_step(self):
        return Step.query.get(self.form_version.get_first_step())

    @property
    def ui_schema_for_create(self):
        return Step.Utils.raw_ui_schema(self.first_step)

    def get_form_validator(self, step: Step | None = None):
        return FormValidator.init_form_validator(self.shop, self.form_composer, step or self.first_step)

    @beartype
    def pipeline_create_order(
        self,
        data: dict,
        buyer_info: BuyerInfo | None = None,
        action: CreateAction = CreateAction.SUBMIT,
        appoint_assignee: AccountDetailV2 | None = None,  # 当前步骤为人工步骤时，指定下一个步骤的处理人
        ignore_check_type_remind_error: bool = True,
    ) -> Result[BusinessOrder, BusinessOrderError]:
        current_step = self.first_step
        if action == CreateAction.SUBMIT:
            form_validator = self.get_form_validator(current_step)
            validate_result = form_validator.validate(data).or_else(
                form_validator.post_validate(ignore_check_type_remind_error)
            )
            if validate_result.is_err():
                return validate_result

            def after_submit_hook(bo_: BusinessOrder):
                # risk-control 使用的是覆盖式更新，需要使用 bo 级别的 do risk control 来上报信息
                bo_.do_risk_control()
                package_jobs(bo_.id, bo_.current_job_id)

        else:

            def after_submit_hook(bo_: BusinessOrder):
                pass

        if self.creation_lock is not None:
            acquire_result = self.creation_lock.acquire()
            if acquire_result.is_err():
                return Err(acquire_result.unwrap_err())

        if self.ignore_default_data_config:
            default_data = {}
        else:
            try:
                value_resolver = ValueResolver(data.copy())
                default_data = self.prepare_default_data(value_resolver=value_resolver)
            except Exception as e:
                logger.opt(exception=e).error("prepare default data failed")
                default_data = {}
        data = {**default_data, **data}
        try:
            bo = self._create_order(data, buyer_info, action, appoint_assignee)
        except BusinessOrderError as ex:
            return Err(ex)
        if bo not in db.session:
            bo = db.session.merge(bo)  # 重新附加到session
            db.session.refresh(bo)  # 重新加载数据
        action_client.create_action_log_by_kafka(
            dict(
                org_id=str(self.shop.org_id),
                sid=self.shop.sid,
                user=bo.aid,
                platform="",
                label=bo.name,
                operator="create" if action == CreateAction.SUBMIT else "save",
                model="business_orders",
                object_id=str(bo.id),
                operate_ts=int(time.time()),
                raw_json=json.dumps(bo.data, ensure_ascii=True),
            )
        )
        after_submit_hook(bo)
        if self.creation_lock is not None:
            self.creation_lock.after_create()
        return Ok(bo)

    @in_transaction()
    @no_auto_flush()
    def _create_order(
        self,
        data: dict,
        buyer_info: BuyerInfo | None = None,
        action: CreateAction = CreateAction.SUBMIT,
        appoint_assignee: AccountDetailV2 | None = None,
    ):
        if not KioskOrg.is_org_enabled(self.form_shop.org_id):
            raise BusinessOrderError(SHOP_EXPIRE_MSG, **self.context)
        if self.form_shop.deleted:
            raise BusinessOrderError("抱歉，当前工单模板及任务已被删除，无法创建", **self.context)
        if not self.form_shop.enabled:
            raise BusinessOrderError("抱歉，当前工单模板及任务未启用，无法创建", **self.context)
        if not self.form_version:
            raise BusinessOrderError("抱歉，当前工单模板未发布，无法创建", **self.context)
        order = BusinessOrder()
        db.session.add(order)
        db.session.flush()
        log_vars.BusinessOrderId.set(order.id)

        order.form_id = self.form.id
        order.form_version_id = self.form_version.id
        order.sid = self.shop.sid
        order.from_type = self.from_type
        order.set_creator_info(self.creator)
        order.aid = self.creator.user_nick
        order.set_updator(self.creator)
        if buyer_info:
            order.buyer_open_uid = buyer_info.open_uid
            order.uid = buyer_info.nick
        db.session.flush()

        # 不使用 execute_one_job 和 get_job_executor 和 JobController.execute 的原因是
        # create_order 方法在一个事务内，以上方法内部会主动 commit 事务，后续操作就会出现 InvalidRequestError
        from robot_processor.job.begin_job import BeginJobController

        begin_job = order.init_bo_jobs()
        assert begin_job is not None
        order.set_current_execute_job(begin_job)
        BeginJobController(begin_job).run_job()

        # 这一步才到入参里 data 提交的表单数据所在的步骤
        job = begin_job.next
        assert job is not None
        job_action = JobAction(job_id=job.id, job=job)
        order.set_current_execute_job(job)
        # 第一个人工步骤进行处理时，则也开启计时。
        job.start_timing()

        # 如果没有传 flag 字段，则以“无旗帜”进行传入。
        if data.get("flag") is None:
            data.update({"flag": ""})

        if action == CreateAction.SUBMIT:
            job_action.do_accept(data, appoint_assignee, self.creator, "创建工单并提交")
        else:
            job_action.do_save(data, self.creator, "创建工单仅保存")

        return order

    def type_check_by_step(self, step: Step, data: dict, extra: Literal["ignore", "forbid"] = "forbid"):
        """校验提交的数据类型是否准确"""
        form_composer = deepcopy(self.form_composer)
        form_composer.set_current(step.id)
        symbol_table = form_composer.symbol_table_wrapper
        scope = symbol_table.current_scope
        return TypeChecker.check_step_order_data(data, symbol_table=symbol_table, scope=scope, extra=extra)

    def type_check(self, data: dict, extra: Literal["ignore", "forbid"] = "forbid"):
        """校验提交的数据类型是否准确"""
        form_composer = deepcopy(self.form_composer)
        symbol_table = form_composer.symbol_table_wrapper
        return TypeChecker.check_order_data(data, symbol_table=symbol_table, extra=extra)

    def reveal_data_type_error_symbol(self, step: Step, error: DataTypeError):
        indicators = []
        for loc in error.loc:
            if isinstance(loc, str):
                indicator = PathIndicator(loc, False, False)
                indicators.append(indicator)
            elif isinstance(loc, int):
                indicators[-1].array_flag = True
        var = Var.init_by_indicators(indicators)
        form_composer = deepcopy(self.form_composer)
        form_composer.set_current(step.id)
        symbol_table = form_composer.symbol_table_wrapper
        return var.resolve_symbol(symbol_table=symbol_table, scope=symbol_table.current_scope)

    @staticmethod
    @contextmanager
    def create_or_clean(body: BusinessOrderSchema):
        bo = None
        delete_flag = False

        def delete():
            nonlocal delete_flag
            delete_flag = True

        def set_trace(trace_bo):
            nonlocal bo
            bo = trace_bo

        try:
            yield set_trace, delete
        except Exception:
            delete()
            raise
        finally:
            if delete_flag and bo and (bo_id := getattr(bo, "id")):
                db.session.rollback()
                Job.query.filter_by(business_order_id=bo_id).delete()
                BusinessOrder.query.filter(BusinessOrder.id == bo_id).delete()
                db.session.commit()
                BusinessManager.error_event(body)

    @staticmethod
    def update_business_order_trade(data_for_upgrade: dict, bo):
        tid_list = list(objectpath.Tree(data_for_upgrade).execute("$..tid"))

        tid = tid_list.pop(0) if tid_list else None
        if tid:
            bo.data["tid"] = tid
        oid_list = list(objectpath.Tree(data_for_upgrade).execute("$..oid"))
        oid = oid_list.pop(0) if oid_list else None
        if oid:
            bo.data["oid"] = oid

    @staticmethod
    def merge_tid_oid_uid_from_data(bo) -> Result[None, str]:
        """
        从keymap以及电商组件中获取tid, oid, uid以支持检索
        理论上keymap可以支持通用组件，但实际实现上keymap都只按照电商组件获取了结构化信息，
        所以目前可以仅使用电商组建来获取tid, oid ,uid

        """
        data = deepcopy(bo.data)
        data.pop("tid", None)  # 前端提交的tid 及 oid 数据可能为空 但是订单组件里的值是全的 这里我们信赖组件的值
        data.pop("oid", None)
        # 避免 event_data 中的数据污染 objectpath
        data.pop(BusinessManager.RAW_EVENT_DATA_FIELD, None)
        widgets = get_non_ref_widgets_by_form_id(bo.form_id)
        for key, widget in widgets.items():
            if widget.get("type") == "product":
                data.pop(key, None)

        tid_or_oids = set()
        tid_list = list(filter(lambda v: bool(v), objectpath.Tree(data).execute("$..tid")))
        tid_or_oids.update(tid_list)

        tid = tid_list.pop(0) if tid_list else None
        if tid:
            bo.data["tid"] = tid
        oid_list = list(filter(lambda v: bool(v), objectpath.Tree(data).execute("$..oid")))
        tid_or_oids.update(oid_list)
        oid = oid_list.pop(0) if oid_list else None
        if oid:
            bo.data["oid"] = oid
        if (tid and len(tid) > 32) or (oid and len(oid) > 32):
            return Err("订单或子订单长度不能超过32位")
        flag_modified(bo, "data")
        if bo.uid is None:
            usernick_list = list(objectpath.Tree(bo.data).execute("$..usernick"))
            bo.uid = usernick_list.pop(0) if usernick_list else None
        bo.create_business_order_trade_map(tid_or_oids)
        # 多订单修复 修复侧边关联多订单是其他订单无法查询到工单
        # https://www.teambition.com/project/60c84b990d1fe3ddf6e122e9/bug/section/all/task/6136d9b003dfed005adbad85
        return Ok(None)

    @staticmethod
    def fill_business_order(
        body: BusinessOrderSchema | BuyerBusinessOrderSchema,
    ) -> tuple[dict | None, BusinessOrder | None]:  # noqa
        """
        基于创建工单的请求参数创建对应的 orm 对象。

        :param body: 创建工单的请求参数
        :return: 创建结果, 如果创建失败，将返回 tuple (error_result_dict, None), 如果创建成功，将返回 tuple (None, BusinessOrder)
        """
        assert body.creator_type in [
            Creator.ASSISTANT,
            Creator.LEYAN,
            Creator.RPA,
            Creator.USER,
        ], "不支持的创建人用户类型"

        body.modify_data()
        is_ok, error = body.check()
        if not is_ok:
            return ResultUtil.build_dict(400, None, error), None
        org_id = int(body.form.org_id)
        if not KioskOrg.is_org_enabled(org_id):
            return ResultUtil.build_dict(400, None, SHOP_EXPIRE_MSG), None
        if body.form.deleted:
            return ResultUtil.build_dict(400, None, "该工单已失效,建议您咨询客服"), None
        if not body.form.enabled:
            return ResultUtil.build_dict(400, None, "该类型工单未启用"), None

        # 如果没有传 flag 字段，则以“无旗帜”进行传入。
        if body.data.get("flag") is None:
            body.data.update({"flag": ""})

        if body.creator_type in [Creator.ASSISTANT, Creator.LEYAN]:
            # 如果是这两种用户类型，说明该工单是由客服手动创建的。
            # 将调用 kiosk 的 API 对用户状态进行检查，如果用户状态异常，则不允许创建工单。
            assert body.creator_user_id is not None, "creator_user_id should not be None"
            detail = kiosk_client.get_user_by_id(user_type=body.creator_type, user_id=body.creator_user_id)
            if detail is None:
                return ResultUtil.build_dict(400, None, "获取用户名失败"), None
            bo = BusinessOrder(
                sid=body.sid,
                aid=detail.user_nick,  # NOTE: 为啥字段名是 id，而值是 nick?
                form_id=body.form_id,
                buyer_open_uid=body.buyer_open_uid,
                data=body.data,
                from_type=body.from_type,
                uid=normalize_buyer_uid(body.uid),
            )
            db.session.add(bo)
            db.session.flush()
            bo.set_creator_info(detail)
        else:
            # 其他用户类型时，工单是由买家通过淘宝小程序创建，或者由系统自动创建。
            # 自动创建的情况包括：
            # - n8n 通过 webhook 创建工单
            # - robot_processor/customize package 下的脚本在 cron job 中创建工单
            bo = BusinessOrder(
                from_type=body.from_type,
                sid=body.sid,
                # aid 是指 assistant user id, 即客服用户 id，
                # 如果是买家创建的工单，则将 aid 设置为 买家的 user id，否则设置为 user_nick
                aid=body.uid if body.creator_type == Creator.USER else body.user_nick,
                mid=body.mid,
                form_id=body.form_id,
                buyer_open_uid=body.buyer_open_uid,
                creator_type=body.creator_type,
                creator_user_id=body.creator_user_id,
                data=body.data,
                uid=normalize_buyer_uid(body.uid),
            )
            db.session.add(bo)
            db.session.flush()

        log_vars.BusinessOrderId.set(bo.id)
        res = BusinessManager.merge_tid_oid_uid_from_data(bo)
        if res.is_err():
            ResultUtil.build_dict(400, None, res.unwrap_err()), None

        return None, bo

    @staticmethod
    def create_buyer_business_order(org_id, body: BuyerBusinessOrderSchema):
        """
        买家通过淘宝小程序入口创建工单。此类工单的 creator_type 将为 USER，aid 为 null。

        :param org_id: 租户 id
        :param body: 需要往工单上填充的数据
        :return: 创建结果
        """
        with BusinessManager.create_or_clean(body) as (set_trace, delete):
            error, bo = BusinessManager.fill_business_order(body)
            set_trace(bo)
            if error:
                delete()
                return error
            res = BusinessManager.init_new_business_order(
                org_id=org_id,
                bo=unwrap_optional(bo),
                body=body,
                appoint_next_assignee=False,
                seed=request.args.get("seed"),
            )
            if ResultUtil.not_success(res):
                delete()
            return res

    @staticmethod
    def create_n8n_business_order(org_id, body: BusinessOrderSchema) -> dict:
        """
        n8n 通过 webhook 创建工单。此类工单的 creator_type 将为 RPA，aid 为 飞梭机器人。

        :param org_id: 租户 id
        :param body: 需要往工单上填充的数据
        :return: 创建结果
        """
        with BusinessManager.create_or_clean(body) as (set_trace, delete):
            # n8n 传过来的部分组件json schema需要做转换
            is_ok, error = body.n8n_convert_data()
            if not is_ok:
                db.session.rollback()
                return ResultUtil.build_dict(400, None, error)

            logger.debug(f"auto create -- bo data@{body.data}")
            error, nullable_bo = BusinessManager.fill_business_order(body)
            set_trace(nullable_bo)
            if error:
                delete()
                return error
            bo = unwrap_optional(nullable_bo)
            if current_app.config.get("ENABLE_AUTO_FILLED", True):
                if prefilled_data := BusinessManager.fill_by_providers(body):
                    bo.data.update(**prefilled_data)
                    flag_modified(bo, "data")
                    db.session.add(bo)
            res = BusinessManager.init_new_business_order(
                org_id=org_id,
                bo=bo,
                body=body,
                appoint_next_assignee=False,
                seed=request.args.get("seed"),
            )
            if ResultUtil.not_success(res):
                delete()
            return res

    @staticmethod
    def fill_by_providers(body: BusinessOrderSchema):
        provider_configs = []
        if not (provider_widget := body.filter_widget()):
            return None

        if not body.data.get(provider_widget["key"]):
            logger.debug(f"skip bo...@widget_uuid{provider_widget['key']}")
            return None

        for widget in body.widgets():
            if not (ref_config := widget.get("ref_config")):
                continue
            for config in ref_config:
                config.update({"key": widget["key"]})
            provider_configs.extend(ref_config)

        if not provider_configs:
            return None

        param = PrefilledReqSchema(
            form_id=body.form.id,
            data_keys=[
                {"sku_id": item.get("sku"), "spu_id": item.get("spu")}
                for item in body.data.get(provider_widget["key"]) or []
            ],
        )
        result = asgard_client.prefilled(body.form.sid, provider_configs, param.dict())
        if not result:
            return None

        return {item.key: item.value for item in PrefilledRespSchema(**result).data}

    @staticmethod
    def create_customize_business_order(org_id, body: BusinessOrderSchema) -> dict:
        """针对商家定制场景的工单创建函数."""
        error, bo = BusinessManager.fill_business_order(body)
        if error:
            logger.error("填充订单数据失败 {}", error)
            return error
        return BusinessManager.init_new_business_order(  # NOQA
            org_id=org_id,
            bo=unwrap_optional(bo),
            body=body,
            appoint_next_assignee=False,
            seed=None,
        )

    @staticmethod
    def init_new_business_order(
        org_id,
        bo: BusinessOrder,
        body: BusinessOrderSchema,
        appoint_next_assignee: bool,
        seed: str | None = None,
    ) -> dict:
        """
        完成新工单的初始化工作, 包括：初始化 job，处理自动编号组件，预分配执行人，创建操作日志，执行工单的第一个 job, 风控上报

        :param org_id: 工单所属的租户 id
        :param bo: 新工单
        :param body: 需要往工单上填充的数据
        :param appoint_next_assignee: 是否预分配下一个执行人
        :param seed: 不知道是啥
        """
        assert isinstance(body, (BusinessOrderSchema, BuyerBusinessOrderSchema))
        from robot_processor.form.models import WidgetAutoNumber

        form_id = body.form_id
        sid = body.sid
        if not (version := bo.form.versions.first()):  # type: ignore[union-attr]
            version = bo.form.snapshot()  # type: ignore[union-attr]
        bo.form_version_id = version.id
        db.session.flush()
        first_job = bo.init_bo_jobs()
        assert first_job is not None
        jobs = bo.jobs.order_by(Job.id).all()
        assert len(jobs) > 0
        assert first_job.id == jobs[0].id
        first_human_job = jobs[1] if len(jobs) > 1 else None
        if first_human_job is None:
            logger.error("第一个人工 job 创建失败")
        third_job = jobs[2] if len(jobs) > 2 else None

        # 自动编号组件值处理
        # 由于内部使用到了 get_business_order_widgets 方法，而该方法需要基于工单已有的任务来进行数据获取。
        # 因此只有在工单的任务生成之后，执行才会有意义。
        WidgetAutoNumber.handle_widget_auto_number(bo)

        if body.next_job_assistant:
            next_operator = body.next_job_assistant.to_account_detail()
        else:
            next_operator = None
        # 预分配执行人
        if appoint_next_assignee and third_job and third_job.is_human():
            success, error = BusinessManager.appoint_assignee_to_job(third_job, next_operator)
            if not success:
                db.session.rollback()
                return ResultUtil.build_dict(400, None, error)

        # 进行唯一值校验
        try:
            risk_control_client.check_widget_value_unique(
                str(org_id), sid, form_id, None, bo.data, bo.get_non_ref_widgets(), True
            )
        except BusinessOrderValueUniqueConstraintError as e:
            return ResultUtil.build_dict(400, None, e.detail)

        # FIXME 需要把新建 也移动到 工单的操作上
        action_client.create_action_log_by_kafka(
            dict(
                org_id=str(org_id),
                sid=str(sid),
                user=bo.aid,
                platform="",
                label=bo.name,
                operator=("create" if body.create_action == CreateAction.SUBMIT else "save"),
                model="business_orders",
                object_id=str(bo.id),
                operate_ts=int(time.time()),
                raw_json=json.dumps(bo.data, ensure_ascii=True),
            )
        )

        if body.create_action == CreateAction.SUBMIT:
            # 提交：保存工单，并自动执行工单上的 job
            package_jobs.send(bo.id)
        else:
            # 保存: 保存工单，自动执行工单的 begin job，然后将首个 人工 job 设置为待处理状态。
            # 只有工单创建人手动 accept 了首个人工job 后，工单的后续job 才会继续执行。
            # SUBMIT 和 SAVE 的区别：
            # - SUBMIT 的话，至少会执行到 first_human_job 的下个 job
            # - SAVE 的话，只会执行到 first_human_job, 且 first_human_job 将暂停在等待客服确认的状态
            execute_one_job(job=first_job)
            db.session.flush()
            if first_human_job:
                bo.set_current_execute_job(first_human_job)
                first_human_job.set_status(JobStatus.PENDING)
                first_human_job.process_mark = JobProcessMark.SAVE
                first_human_job.start_timing()
                first_human_job.set_assignee_assistant(
                    AccountDetailV2(
                        user_type=bo.creator_type,
                        user_id=bo.creator_user_id,
                        user_nick=bo.aid,
                    )
                )
                bo.set_status(BusinessOrderStatus.TO_BO_SUBMITTED)
                db.session.add(first_job)
                db.session.add(bo)
                db.session.flush()

        flag_modified(bo, "data")
        if seed:
            cache.delete(seed)

        # 工单创建失败, 可能导致无效的风控数据上报: https://redmine.leyantech.com/issues/568355
        # 简单起见, 将把风控上报放到最后面来做
        bo.do_risk_control()

        logger.info("created business order")
        return ResultUtil.build_dict(200, bo.id, "")

    @classmethod
    def appoint_assignee_to_job(cls, job: Job, next_operator: AccountDetailV2 | None):
        """预分配人工步骤的客服"""
        if (
            not next_operator and job.is_human() and job.raw_step_v2["assignee_rule"] == AssigneeRule.MANUAL.value
        ):  # noqa: E501
            return False, "需指派下一步执行人"
        if next_operator:
            success, error = job.appoint_reserved_assignee(next_operator)
            return success, error
        return True, ""

    @staticmethod
    def replace_upload_widget(bo):
        from robot_processor.client.conf import oss_config

        data = bo.data or {}
        need_replace_widgets = {}
        for key, value in data.items():
            if key.startswith("system_") or not value or not isinstance(value, list):
                continue
            need_replace = False
            for item in value:
                if not isinstance(item, dict):
                    continue
                url = item.get("url")
                if (
                    url
                    and url.startswith("http")
                    and not url.startswith(oss_config.VIDEO_OSS_URL_BASE)
                    and not url.startswith(oss_config.IMAGE_OSS_URL_BASE)
                ):
                    need_replace = True
                    break
            if need_replace:
                need_replace_widgets[key] = ""

        for job in bo.jobs:
            ui_schema = job.raw_step_v2.get("ui_schema", []) or []
            for schema in ui_schema:
                key, widget_type = schema.get("key"), schema.get("type")
                if not key or key not in need_replace_widgets:
                    continue
                upload_type = schema.get("uploadType")  # video/image需要 而file会提前处理，不走这个逻辑
                if widget_type != "upload" or upload_type not in ["video", "image"]:
                    need_replace_widgets.pop(key)
                else:
                    need_replace_widgets[key] = upload_type
        to_replace_widgets = list(need_replace_widgets.items())
        to_replace_widgets.sort(key=lambda x: x[0])  # 后面希望用这个列表的值来检查重复 task，所以做一次排序
        return to_replace_widgets

    @staticmethod
    def error_event(data: BusinessOrderSchema):
        from robot_processor.ext import bo_create_error_event_producer

        try:
            bo_create_error_event_producer(
                dict(
                    form_id=data.form_id,
                    from_type=data.from_type.name if data.from_type else "",
                    creator_type=data.creator_type.name if data.creator_type else "",
                    creator_id=data.creator_user_id if data.creator_user_id else 0,
                    data=json.dumps((data.data or {}), ensure_ascii=False),
                    extra=data.json(
                        exclude={
                            "form_id",
                            "from_type",
                            "creator_type",
                            "creator_user_id",
                            "data",
                        },
                        ensure_ascii=False,
                    ),
                )
            )
        except Exception as e:  # noqa
            logger.error(f"create bo error event failed. error: {e}, data: {data}")

    def event_to_bo_data(self, event, data):
        from typing import cast

        from robot_processor.form.event.models import EventConfig
        from robot_processor.form.models import FormSymbol
        from robot_processor.symbol_table.data_converter import convert_to_label_map
        from robot_processor.symbol_table.data_converter import convert_to_name_map
        from robot_processor.symbol_table.models import Namespace

        event = cast(EventConfig, event)
        step_id = self.form_version.get_first_step()
        temp = convert_to_label_map(data, Namespace(symbols=event.get_deprecated_symbols()))
        form_namespace = FormSymbol.get_namespace(self.form.id, step_id).unwrap()
        bo_data = convert_to_name_map(temp, form_namespace)
        bo_data[self.RAW_EVENT_DATA_FIELD] = data
        return bo_data

    @classmethod
    def create_bo_by_event(cls, event, shop: Shop, data: dict, buyer_info: BuyerInfo | None = None):
        """通过定义的事件来创建工单"""
        from typing import cast

        from robot_processor.form.event.models import EventConfig

        log_vars.OrgId.set(shop.org_id)
        log_vars.Sid.set(shop.sid)
        log_vars.Event.set(event.id)
        creator = AccountDetailV2(user_type=Creator.RPA, user_id=0, user_nick="RPA")
        from_type = FromType.KAFKA_TRADE_EVENT
        event = cast(EventConfig, event)

        logger.info(f"create bo by event data: {data}")

        created = []

        if shop.deleted or shop.status != ShopStatus.ENABLE:
            logger.info(f"shop {shop.id} is deleted or disabled")
            return

        for form in event.get_form_by_shop(shop):
            log_vars.FormId.set(form.id)
            self = cls(form, shop, creator, from_type)
            form_validator = FormValidator.init_event_filter(shop, self.first_step, self.form_version.id)
            match form_validator.validate(data).or_else(form_validator.process_errors_for_create):
                case Err(BusinessOrderValidateError() as err):
                    logger.info("event filters not match. " + err.to_log(form_validator))
                case Ok():
                    bo_data = self.event_to_bo_data(event, data)
                    create_res = self.pipeline_create_order(bo_data, buyer_info)
                    created.append(create_res)
                    if create_res.is_ok():
                        bo = create_res.unwrap()
                        bo.do_risk_control()
                        stats.incr(f"{event.id.name}.created", count=1)
                    log_vars.context.clear("business_order_id")
        return created

    def prepare_default_data(self, step_uuid: str | None = None, value_resolver: ValueResolver | None = None):
        """根据表单配置，准备一份默认值数据"""
        if step_uuid is None:
            step = unwrap_optional(db.session.get(Step, unwrap_optional(self.form_version).get_first_step()))
            scope = FormSymbolScope.from_step(step)
        else:
            step_id = unwrap_optional(self.form_version).get_current_step(step_uuid)
            scope = FormSymbolScope(form_id=self.form.id, step_uuid=step_uuid, step_id=step_id)
        symbols_res = FormSymbol.query_symbol_table_editable_view_by_scope(scope)
        if symbols_res.is_err():
            return {}
        symbols = symbols_res.unwrap()

        default = {}
        for symbol in symbols:
            if symbol.component_id in app_config.component_use_default_value_blacklist:
                continue
            default_value = symbol.render_config.get("defaultValue")
            if (default_value is None) or (default_value == []):
                continue
            match default_value:
                case [{"qualifier": str()}, *_]:
                    if value_resolver is not None:  # type: ignore[unreachable]
                        try:
                            default[symbol.name] = "".join(
                                [
                                    val.with_resolver(value_resolver).resolve().unwrap()
                                    for val in deserialize(default_value, list[Value])
                                ]
                            )
                        except:  # noqa
                            pass
                case _:
                    default[symbol.name] = default_value
        return default


@dataclass
class FormValidator:
    """步骤级别的表单校验

    对于表单校验，规则满足=错误
    对于过滤器，规则满足=正确
    """

    mode: Literal["filter", "doctor"]
    shop: Shop
    form_id: int
    form_version_id: int
    symbol_table: SymbolTable
    current_scope: Scope
    enabled: bool
    validations: list[StepValidationRule]

    org_id: int = field(init=False)
    sid: str = field(init=False)

    def __post_init__(self):
        self.org_id = int(self.shop.org_id)  # type: ignore[arg-type]
        self.sid = self.shop.sid

    @classmethod
    def init_form_validator(cls, shop: Shop, form_composer: FormComposer, step: Step):
        """初始化为一个表单校验规则"""
        symbol_table = form_composer.symbol_table_wrapper
        return cls(
            mode="doctor",
            shop=shop,
            form_id=form_composer.meta.id,
            form_version_id=form_composer.meta.version_id,
            symbol_table=symbol_table,
            current_scope=symbol_table.current_scope,
            enabled=step.validation_config.enabled,
            validations=step.validation_config.validations or [],
        )

    @classmethod
    def init_event_filter(cls, shop: Shop, step: Step, form_version_id: int):
        """初始化为一个智能事件的分支判断规则"""
        from robot_processor.form.event.models import EventConfig

        if not step.event:
            return cls(
                mode="filter",
                shop=shop,
                form_id=unwrap_optional(step.form_id),
                form_version_id=form_version_id,
                current_scope=Scope("root", None),
                symbol_table=SymbolTable(scope_symbols=[]),
                enabled=False,
                validations=[],
            )

        validations = []
        event = EventConfig.get_by_id(step.event)
        event_shortcuts = event.get_shortcuts()
        for shortcut_value in step.event_shortcuts or []:
            if not (shortcut := event_shortcuts.get(shortcut_value)):
                continue
            if shortcut.mode != shortcut.Mode.FILTER:
                continue
            rule = StepValidationRule(
                name=shortcut_value,
                check_type=WidgetValueUniqueCheckType.CREATE_FORBIDDEN,
                relation=shortcut.filters.get("relation", "and"),
                conditions=shortcut.filters.get("conditions", []),
            )
            validations.append(rule)
        if step.event_filters:
            rule = StepValidationRule(
                name="自定义筛选",
                check_type=WidgetValueUniqueCheckType.CREATE_FORBIDDEN,
                relation=step.event_filters.get("relation", "and"),
                conditions=step.event_filters.get("conditions", []),
            )
            validations.append(rule)
        return cls(
            mode="filter",
            shop=shop,
            form_id=unwrap_optional(step.form_id),
            form_version_id=form_version_id,
            symbol_table=event.get_symbol_table(),
            current_scope=event.get_scope(),
            enabled=True,
            validations=validations,
        )

    def validate(self, data: dict, exclude_bo_id: int | None = None) -> Ok[None] | Err[BusinessOrderValidateError]:
        if not self.enabled:
            return Ok(None)
        errors: list[BusinessOrderValidateError.ValidateError] = []
        value_resolver = self.get_value_resolver(data, exclude_bo_id=exclude_bo_id)
        # 规则之间是任一满足的关系
        for validation in self.validations:
            predicate_value = validation.to_value()
            predicate_indicator = predicate_value.with_resolver(value_resolver).resolve_predicate_with_detail()
            match self.mode, predicate_indicator.res:
                case ("doctor", True as expect) | ("filter", False as expect):
                    not_satisfied = self.collect_not_satisfied(predicate_indicator, expect)
                    errors.append(BusinessOrderValidateError.ValidateError(validation.name, not_satisfied))

        if not errors:
            return Ok(None)
        else:
            return Err(BusinessOrderValidateError(errors, self))

    def component_value_unique_check(
        self, data: dict, exclude_bo_id: int | None = None
    ) -> Ok[None] | Err[ComponentValueUniqueRemind]:
        """组件级的值唯一校验"""
        form_version = db.session.get_one(FormVersion, self.form_version_id)
        steps = form_version.query_steps_with_entities()
        widgets: dict[str, set[str]] = defaultdict(set)
        for step in steps:
            for dimension in step.get_merged_value_unique_dimensions(
                data=data, shop=self.shop, form_version_id=self.form_version_id
            ):
                check_res = risk_control_client.query_risk(
                    org_id=str(self.org_id),
                    sid=self.sid,
                    model="business_order",
                    scope=str(self.form_id),
                    dimensions=[dimension],
                    exclude_bo_id=exclude_bo_id,
                )
                if check_res:
                    widgets[dimension["dimension"]].update(check_res)
        if widgets:
            return Err(ComponentValueUniqueRemind({key: list(value) for key, value in widgets.items()}))
        return Ok(None)

    def post_validate(self, ignore_check_type_remind_error=True):
        """是否过滤掉校验方式=仅提示的错误"""

        def process_error(error: BusinessOrderError):
            if isinstance(error, BusinessOrderValidateError) and ignore_check_type_remind_error:
                return self.process_errors_for_create(error)
            else:
                return Err(error)

        return process_error

    @classmethod
    def collect_not_satisfied(cls, predicate_indicator: PredicateIndicator, res: bool):
        not_satisfied: list[BusinessOrderValidateError.NotSatisfied] = []

        def collect(indicator: PredicateIndicator, prefix: list[int]):
            if indicator.res != res:
                return
            if indicator.children:
                for child in indicator.children:
                    collect(child, prefix + [indicator.loc])
            else:
                not_satisfied.append(
                    BusinessOrderValidateError.NotSatisfied(loc=prefix + [indicator.loc], extra=indicator.extra)
                )

        collect(predicate_indicator, [])
        return not_satisfied

    def get_value_resolver(self, data: dict, exclude_bo_id: int | None = None):
        from robot_types.helper.value.predicate import Resolver

        value_resolver = ValueResolver(data.copy())

        # 处理 value unique 的 validator
        # value unique 的判断是基于 value unique resource 的结果来判断的，无法提供固定的 evaluate func
        value_unique_resource = self.query_value_unique(data, exclude_bo_id=exclude_bo_id)
        value_resolver.context.update({"_value_unique_resource": value_unique_resource})
        for check_key, values in value_unique_resource.items():
            value_resolver.evaluate_registry.register_name(
                check_key,
                Resolver(
                    value_unique=lambda a, b: len(values) == 0,
                    value_recorded=lambda a, b: len(values) > 0,
                ),
            )
        # 为 data 中没有提交的字段注册一个 always false 的 resolver (None 被认为是不满足值唯一)
        for check_key in self.value_unique_conditions:
            if check_key in value_unique_resource:
                continue
            value_resolver.evaluate_registry.register_name(
                check_key,
                Resolver(
                    value_unique=lambda a, b: True,
                    value_recorded=lambda a, b: False,
                ),
            )

        return value_resolver

    def query_value_unique(self, data: dict, exclude_bo_id: int | None = None):
        value_unique: dict[str, set[str]] = dict()
        for check_key, check_values in self.prepare_dimensions(data).items():
            dimensions = [dict(dimension=check_key, dimension_value=value) for value in check_values]

            check_res = risk_control_client.query_risk(
                str(self.org_id),
                self.sid,
                "business_order",
                str(self.form_id),
                dimensions,
                exclude_bo_id=exclude_bo_id,
            )

            logger.info(
                "org_id: {org_id}, sid: {sid}, form_id: {form_id}, dimensions: {dimensions}, res: {check_res}".format(
                    org_id=self.org_id,
                    sid=self.sid,
                    form_id=self.form_id,
                    dimensions=dimensions,
                    check_res=check_res,
                )
            )
            value_unique[check_key] = check_res
        return value_unique

    @property
    def value_unique_conditions(self):
        if not self.enabled:
            return dict()
        _value_unique_conditions: dict[str, Value] = dict()

        def find_value_unique_values(predicate: Predicate):
            match predicate:
                case Condition(
                    a=Value(qualifier="var"),
                    o=["value_unique" | "value_recorded" | "VALUE_UNIQUE" | "VALUE_RECORDED"],
                ):
                    _value_unique_conditions[predicate.a.var.path] = predicate.a  # type: ignore[unreachable]
                case Filter():
                    for condition in predicate.conditions:
                        find_value_unique_values(condition)

        for validation in self.validations:
            predicate_value = validation.to_value()
            find_value_unique_values(predicate_value.predicate)

        return _value_unique_conditions

    @property
    def value_unique_values(self):
        return list(self.value_unique_conditions.values())

    def prepare_dimensions(self, data: dict):
        dimensions: dict[str, set[str]] = defaultdict(set)
        for check_item in self.value_unique_values:
            check_key = check_item.var.path
            form_symbol = check_item.var.resolve_symbol(self.symbol_table, self.current_scope)
            try:
                raw_value = check_item.var.resolve_data(data)
            except KeyError:
                raw_value = None
            # 订单是特殊处理的
            if form_symbol.component_id == "order" and isinstance(raw_value, list):
                for item in raw_value:
                    if dimension_value := self.normalize_value(form_symbol, item):
                        dimensions[check_key].add(dimension_value)
            else:
                if dimension_value := self.normalize_value(form_symbol, raw_value):
                    dimensions[check_key].add(dimension_value)
        return dict(dimensions)

    @classmethod
    def normalize_value(cls, form_symbol: Symbol, raw_value: Any) -> str | None:
        def normalize(norm_form_symbol: Symbol, norm_raw_value: Any):
            if norm_raw_value is None:
                return None
            if norm_raw_value == "":
                return None
            match norm_form_symbol.component_id, norm_raw_value:
                case "product", [dict(), *_]:
                    if not norm_form_symbol.children:
                        return None
                    item_form_symbol = norm_form_symbol.children[0]
                    if norm_form_symbol.render_config.get("dataSource") == "platform":
                        key_sets = {"SPU", "SKU"}
                    else:
                        key_sets = {"SKU_OUTER"}
                    return [normalize(item_form_symbol, filter_dict(item, key_sets)) for item in norm_raw_value]
                case "payment-method", _:
                    if not (isinstance(norm_raw_value, dict) and norm_raw_value):
                        return None
                    norm_raw_value = norm_raw_value.copy()
                    if "pay_tid" in norm_raw_value:
                        norm_raw_value["tid"] = norm_raw_value["pay_tid"]
                    keys = get_payment_method_keys(norm_raw_value["payment_method"])
                    return "-".join([str(value) for key in keys if (value := norm_raw_value.get(key))])
                case "usernick" | "alipay" | "string" | "number" | "textarea", _:
                    return str(norm_raw_value)
                case "order", _:
                    if isinstance(norm_raw_value, dict):
                        return "{tid}{oid}".format(
                            tid=norm_raw_value.get("tid"),
                            oid=norm_raw_value.get("oid", ""),
                        )
                    elif isinstance(norm_raw_value, list):
                        raise ValueError("为了兼容订单组件值唯一校验，请将多个订单分别 normalize")
                    elif isinstance(norm_raw_value, str):
                        # 选中了 56f41c16b34e42f6b8d042751e608ae4[*].tid 这样的信息
                        return norm_raw_value
                    else:
                        return str(norm_raw_value)

            match norm_form_symbol.type_spec.type:
                case "collection":
                    return {
                        key: value
                        for key, raw in norm_raw_value.items()
                        if (
                            key_symbol := next(
                                filter(
                                    lambda x: x.name == key,
                                    norm_form_symbol.children or [],
                                ),
                                None,
                            )
                        )
                        if (value := normalize(key_symbol, raw))
                    } or None
                case "array":
                    if not norm_form_symbol.children:
                        return None
                    return [normalize(norm_form_symbol.children[0], item) for item in norm_raw_value]

            return norm_raw_value

        def dumps(normalized_value):
            match normalized_value:
                case None:
                    return None
                case dict() | list():
                    return json.dumps(normalized_value, ensure_ascii=False, sort_keys=True)
                case _:
                    return str(normalized_value)

        return dumps(normalize(form_symbol, raw_value))

    def process_errors_for_create(self, validate_error: BusinessOrderValidateError):
        """表单校验用于创建，忽略校验方式=仅提示的不满足规则

        References:
            https://leyan.yuque.com/digismart/manual/kfsqd04gy9f3lu82#l9sKT
        """
        create_forbidden_errors = [
            error
            for error in validate_error.errors
            if self.get_validation_rule(error.name).check_type == WidgetValueUniqueCheckType.CREATE_FORBIDDEN
        ]
        if create_forbidden_errors:
            return Err(BusinessOrderValidateError(create_forbidden_errors, self))
        else:
            return Ok(None)

    def process_errors_only_remind(self, validate_error: BusinessOrderValidateError):
        remind_errors = [
            error
            for error in validate_error.errors
            if self.get_validation_rule(error.name).check_type == WidgetValueUniqueCheckType.REMIND
        ]
        if remind_errors:
            return Err(BusinessOrderValidateError(remind_errors, self))
        else:
            return Ok(None)

    def get_validation_rule(self, name: str) -> StepValidationRule:
        return next(filter(lambda x: x.name == name, self.validations))

    def copy(self):
        return replace(self, symbol_table=replace(self.symbol_table))
