import copy
import mimetypes
import re
from io import Bytes<PERSON>
from typing import Annotated

import requests
from loguru import logger
from sqlalchemy.orm.attributes import flag_modified

import robot_processor.customize.create_bo_by_duplicate_l_id  # noqa
from robot_processor.assistant.schema import AssistantV2
from robot_processor.business_order.job_wrappers.wrapper import execute_all_possible_jobs_in_bo
from robot_processor.business_order.job_wrappers.wrapper import execute_one_job
from robot_processor.business_order.job_wrappers.wrapper import set_business_order_status_against_current_job
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import JobStatus
from robot_processor.client import image_oss_client
from robot_processor.client import kiosk_client
from robot_processor.client import robot_transfer
from robot_processor.client import video_oss_client
from robot_processor.client.conf import oss_config
from robot_processor.constants import TASK_QUEUE_BACKGROUND_TASK
from robot_processor.constants import TASK_QUEUE_JOB
from robot_processor.constants import <PERSON><PERSON><PERSON>rity
from robot_processor.enums import Action
from robot_processor.enums import Business<PERSON>rder<PERSON>tatus
from robot_processor.enums import Creator
from robot_processor.enums import PlanWhenAssignException
from robot_processor.ext import db
from robot_processor.ext import task_queue
from robot_processor.job.human_job import HumanJobController
from robot_processor.kafka_event.exception_event import ExceptionEventHelper
from robot_processor.kafka_event.exception_event import NotifyTrigger
from robot_processor.kafka_event.exception_event import StepNotifyConfig
from robot_processor.logging import vars as log_vars
from robot_processor.utils import unwrap_optional


@task_queue.actor(queue_name=TASK_QUEUE_JOB, time_limit=1000 * 60 * 30)
@log_vars.bind_logger_info
def execute_job(
    job_id: Annotated[int, log_vars.JobId],
    chain=False,
    is_auto_retry: bool = False,
    need_record_job_transition: bool = False,
    is_jump: bool = False,
):
    from robot_processor.assistant.schema import AccountDetailV2
    from robot_processor.assistant.schema import Creator
    from robot_processor.enums import JobProcessMark
    from robot_processor.enums import StepType

    logger.info(f"即将执行任务 {job_id} {is_auto_retry=} {is_jump=} {need_record_job_transition=}")

    # 修改此函数名称时要同步对SyncJobMessageMiddleware进行修改！！
    # 先行查找任务，并判断是否需要先记录一条标识为"重试"的操作日志。
    # 应该在更早的阶段过滤掉已删除的任务。
    job = Job.query.join(Job.business_order).filter(BusinessOrder.deleted.isnot(True)).filter(Job.id == job_id).first()
    if not job:
        logger.warning(f"找不到任务: {job_id}")
        return None
    # 当该任务被标记为已经完成，且无后续任务，则记录该工单的完结日志。
    if job.step_type != StepType.jump and job.is_completed() and not job.has_next:
        job.business_order.complete_business_order_and_record_log()
        return None
    # 在预期的设计中，如果一个 job 执行成功，会通过 dramatiq_abort 来终止自动重试任务
    # 但是在目前的使用中，发现 dramatiq_abort 会出现失败的情况，导致重试的任务没有被终止
    # 在这里需要再加一层判断，避免重复执行
    if is_auto_retry and job.is_completed():
        logger.warning("任务已经完成，拦截自动重试的任务。")
        return
    if is_auto_retry:
        # 当判定为自动重试时，则需要判断任务类型是否为 "自动化任务"，如果是，则进行日志记录。
        # 此时，Job 的 process_mark 应当被记录为 RETRY。
        if job.step_type == StepType.auto.value:
            job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.RETRY,
                assistant=AccountDetailV2(user_id=0, user_type=Creator.RPA, user_nick="RPA应用"),
                operate_reason="",
            )
    if is_jump:
        job.business_order.set_current_execute_job(job)
        job.business_order.job_transition_wrapper.record(current_job=job.prev, next_job=job)
        job.update_process_mark_and_record_action(
            process_mark=JobProcessMark.JUMP,
            assistant=AccountDetailV2(user_id=0, user_type=Creator.SYSTEM, user_nick="系统"),
            operate_reason="",
        )
    if chain:
        logger.info(f"任务 {job_id} 载入执行链路")
        execute_all_possible_jobs_in_bo(
            job_id,
            is_auto_retry=is_auto_retry,
            need_record_job_transition=need_record_job_transition,
        )
    else:
        execute_one_job(job_id, is_auto_retry=is_auto_retry)


@task_queue.actor(queue_name=TASK_QUEUE_JOB)
@log_vars.bind_logger_info
def package_jobs(
    business_order_id: Annotated[int, log_vars.BusinessOrderId],
    current_job_id: Annotated[int | None, log_vars.JobId] = None,
    need_record_job_transition: bool = False,
):
    bo: BusinessOrder | None = BusinessOrder.query.get(business_order_id)
    if not bo:
        return
    log_vars.Sid.set(bo.sid)
    if not current_job_id:
        current_job_id = bo.job_history[0]
    job = Job.query.get(current_job_id)
    execute_job.send_with_options(
        args=(current_job_id,),
        kwargs={"chain": True, "need_record_job_transition": need_record_job_transition},
        task_type=job.task_type,  # type: ignore[union-attr]
    )  # noqa: E501


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.NORMAL)
@log_vars.bind_logger_info
def patch_transfer(sid: Annotated[str, log_vars.Sid], bo_id: Annotated[int, log_vars.BusinessOrderId], _):
    business_order: BusinessOrder | None = BusinessOrder.query.filter(BusinessOrder.id == bo_id).first()  # noqa: E501
    if not business_order:
        logger.warning("找不到工单")
        return
    extra_data = business_order.extra_data
    if extra_data:
        operate_reason = extra_data.get("operate_reason")
    else:
        operate_reason = ""
    if business_order.is_end():
        action = "close"
    else:
        action = "recover"
    kwargs = {
        "operate_reason": operate_reason,
        "org_id": business_order.shop.org_id,  # type: ignore[union-attr]
        "platform": business_order.shop.platform,  # type: ignore[union-attr]
        "operator": business_order.update_user,
    }
    robot_transfer.put_transfer(sid, bo_id, action=action, **kwargs)


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.HIGH)
def handle_circulation_notification(job_id: int, action: Action) -> None:
    """
    工单流转时，去生成通知信息。
    :param job_id:
    :param action:
    :return:
    """
    job: Job | None = Job.query.filter(Job.id == job_id).first()
    if job is None:
        return
    if job.step_id is None:
        logger.warning(f"任务 {job_id} 没有对应的步骤信息。")
        return
    notify_configs = ExceptionEventHelper.get_enabled_step_notify_configs(job.step_id)
    for notify_config in notify_configs:
        config = StepNotifyConfig(**notify_config.config)
        if config.notify_trigger != NotifyTrigger.circulation:
            continue
        if action not in config.order_circulation:
            continue
        ExceptionEventHelper.process_by_step_notify_config(job, config)
        db.session.commit()


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.HIGH)
def update_approvers(step_id: int):
    """
    根据 step 上所绑定的审批人信息，去更新所有待审批的任务的审批人。
    :param step_id:
    :return:
    """
    from robot_processor.assistant.schema import AccountDetailV2
    from robot_processor.business_order.models import JobApprover
    from robot_processor.enums import JobProcessMark
    from robot_processor.enums import StepType
    from robot_processor.form.models import Step
    from robot_processor.job.approve import ApproveJobController
    from robot_processor.shop.models import Shop

    step: Step | None = Step.query.filter(Step.id == step_id, Step.step_type == StepType.approve).first()

    if step is None:
        return

    assistant: AssistantV2 = step.get_assistants_v2()

    # 查找所有与该步骤的 step_uuid 一致的、待审批的任务的 id。
    job_ids: list[int] = [
        job_id
        for (job_id,) in db.session.query(JobApprover.job_id.distinct())
        .filter(
            JobApprover.step_uuid == step.step_uuid,
        )
        .all()
    ]

    latest_users_mapping: dict[str, set[int]] = {}

    operate_assistant = AccountDetailV2(user_type=Creator.SYSTEM, user_id=0, user_nick="系统")
    operate_reason = "审批人账号变动，已满足审批条件，自动通过审批。"

    for job_id in job_ids:
        job: Job | None = Job.query.filter(Job.id == job_id).first()
        if job is None:
            continue
        if job.step is None:
            continue
        if job.step.get_assistants_v2() == assistant:
            continue
        shop: Shop | None = job.shop
        if not shop:
            continue
        job_approvers: list[JobApprover] = JobApprover.query.filter(JobApprover.job_id == job_id).all()

        job_approver_account_ids = {job_approver.user_id for job_approver in job_approvers}

        job_approver_account_mapping = {job_approver.user_id: job_approver for job_approver in job_approvers}

        user_ids = latest_users_mapping.get(job.sid)
        if user_ids is None:
            user_ids = {
                u.feisuo_user_id
                for u in assistant.get_latest_assignee_account_details(shop)
                if u.feisuo_user_id is not None
            }
            latest_users_mapping.update({shop.sid: user_ids})

        # 将不在 job_approver 中的审批人信息加入进去。
        for user_id in user_ids.difference(job_approver_account_ids):
            # add
            job_approver = JobApprover(
                job_id=job.id,
                step_uuid=job.step_uuid,
                user_id=user_id,
            )
            db.session.add(job_approver)

        # 删除已经不在最新的审批人列表中的审批人。
        for user_id in job_approver_account_ids.difference(user_ids):
            # delete
            if (ja := job_approver_account_mapping.get(user_id)) is not None:
                db.session.delete(ja)

        db.session.flush()

        # 检测当前处在审批节点、并且工单为待受理状态的工单，其能否构成审批通过的条件。
        if job.business_order.current_job_id != job.id:
            continue
        if job.business_order.status != BusinessOrderStatus.PENDING:
            continue
        if ApproveJobController(job).check_multi_to_sign_is_approved():
            # 将当前任务状态设置为 SUCCEED。
            job.set_status(JobStatus.SUCCEED)
            job.clear_visit()
            JobApprover.clean_by_job_id(job.id)
            job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.ENDORSE,
                assistant=operate_assistant,
                operate_reason=operate_reason,
            )
            handle_circulation_notification.send(job.id, Action.approve)
            package_jobs.send(job.business_order_id, job.id, True)
            db.session.commit()


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.HIGH)
def handle_approver_status(sid, action, users):
    from robot_processor.assistant.schema import AccountDetailV2
    from robot_processor.business_order.models import JobApprover
    from robot_processor.enums import JobProcessMark
    from robot_processor.form.models import Step
    from robot_processor.form.models import StepType
    from robot_processor.job.approve import ApproveJobController

    if action not in ("DISABLE", "DELETE", "ENABLE"):
        return
    if not (sid and users):
        return
    leyan_user_ids = set()
    for user in users:
        user_id = user.get("accessor_user_id")
        user_type = user.get("accessor_user_type")
        if user_type == Creator.LEYAN and user_id is not None:
            leyan_user_ids.add(user_id)

    if action == "ENABLE":
        # 获取所有符合账号类型及 ID 的被禁用的审批人。
        job_approvers = (
            JobApprover.query.filter(
                JobApprover.user_id.in_(leyan_user_ids),
            )
            .filter(JobApprover.is_valid.is_(False))
            .all()
        )
        for job_approver in job_approvers:
            job_approver.is_valid = True
        db.session.commit()
    else:
        # 获取所有符合账号类型及 ID 的被启用的审批人。
        job_approvers = (
            JobApprover.query.filter(JobApprover.user_id.in_(leyan_user_ids))
            .filter(JobApprover.is_valid.is_(True))
            .all()
        )
        # 更新任务审批人的有效性。
        for job_approver in job_approvers:
            job_approver.is_valid = False
        db.session.commit()

        business_orders = (
            BusinessOrder.query.join(Job, Job.id == BusinessOrder.current_job_id)
            .join(Step, Step.id == Job.step_id)
            .filter(Job.id.in_([job_approver.job_id for job_approver in job_approvers]))
            .filter(
                BusinessOrder.status == BusinessOrderStatus.PENDING,
            )
            .filter(Step.step_type == StepType.approve)
            .all()
        )
        assistant = AccountDetailV2(user_type=Creator.SYSTEM, user_id=0, user_nick="系统")
        operate_reason = "部分审批人账号被禁用，已满足审批条件，自动通过审批。"

        # 遍历工单，检测是否构成审批通过。
        for business_order in business_orders:
            job = business_order.current_job
            if job is None:
                continue
            if ApproveJobController(job).check_multi_to_sign_is_approved():
                # 将当前任务状态设置为 SUCCEED。
                job.set_status(JobStatus.SUCCEED)
                job.clear_visit()
                JobApprover.clean_by_job_id(job.id)
                job.update_process_mark_and_record_action(
                    process_mark=JobProcessMark.ENDORSE,
                    assistant=assistant,
                    operate_reason=operate_reason,
                )
                handle_circulation_notification.send(job.id, Action.approve)
                package_jobs.send(job.business_order_id, job.id, True)

                db.session.commit()


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.HIGH)
def handle_assignee_status(sid, action: str, users):
    from robot_processor.assistant.constants import ASSISTANT_DELETED
    from robot_processor.assistant.constants import ASSISTANT_DISABLED
    from robot_processor.assistant.constants import ASSISTANT_INVALID
    from robot_processor.business_order.exception_rule import ExceptionBusinessOrder

    if action not in ("DISABLE", "DELETE", "ENABLE"):
        return
    if not (sid and users):
        return
    leyan_user_ids = set()
    for user in users:
        user_id = user.get("accessor_user_id")
        if not user_id:
            continue
        user_type = user.get("accessor_user_type")
        if user_type == Creator.LEYAN:
            leyan_user_ids.add(user_id)
    query = (
        BusinessOrder.query.join(Job)
        .filter(BusinessOrder.sid == sid)
        .filter(Job.id == BusinessOrder.current_job_id)
        .filter(Job.feisuo_assignee_user_id.in_(leyan_user_ids))
        .filter(BusinessOrder.deleted.is_(False))
    )

    business_orders: list[BusinessOrder]
    if action == "ENABLE":
        # 将因为分派人失效而进入异常任务池的任务移出异常任务池
        business_orders = (
            query.filter(BusinessOrder.status == BusinessOrderStatus.IN_EXCEPTION)
            .filter(Job.status == JobStatus.FAILED)
            .all()
        )
        for business_order in business_orders:
            if ExceptionBusinessOrder.Utils.if_exists(
                business_order.id,
                business_order.current_job_id,  # type: ignore[arg-type]
                [ASSISTANT_INVALID, ASSISTANT_DELETED, ASSISTANT_DISABLED],
            ):
                job: Job = business_order.current_job  # type: ignore[assignment]
                logger.bind(business_order_id=business_order.id, job_id=job.id).info(
                    "客服账号 {} 被恢复，任务移出异常池", leyan_user_ids
                )
                job.set_status(JobStatus.PENDING)
                set_business_order_status_against_current_job(job)
    else:  # DISABLE or DELETE
        business_orders = query.filter(
            BusinessOrder.status.in_(
                [
                    BusinessOrderStatus.RUNNING,
                    BusinessOrderStatus.PENDING,
                    BusinessOrderStatus.TO_BO_SUBMITTED,
                    BusinessOrderStatus.TO_BE_COLLECTED,
                ]
            )
        ).all()
        for business_order in business_orders:
            job = business_order.current_job  # type: ignore[assignment]
            assign_client = HumanJobController(job=job)
            if assign_client.current_assignee is None or not assign_client.current_assignee.is_valid():
                logger.info("job: {} 当前客服不可用，按需进行重新分配。".format(job.id))
                # 找到该任务的对应步骤的最新版本
                latest_version_step = job.current_step()
                if latest_version_step is None:
                    Job.Utils.mark_failed(job, "指派的客服被禁用或删除。")
                    continue
                # 如果分派账号被禁用或删除时，工单分派规则不为“重新分派”，则进入异常池
                if (
                    latest_version_step.get_assistants_v2().assign_account_exception
                    != PlanWhenAssignException.RE_ASSIGN
                ):
                    Job.Utils.mark_failed(job, "指派的客服被禁用或删除。")
                    continue
                # 尝试重新分派
                assign_client.job.set_assignee_assistant(None)
                result = assign_client.execute()
                # 分派失败则也进入异常池
                if result.status == JobStatus.FAILED:
                    Job.Utils.mark_failed(job, result.exc_info or "客服重新分派失败")
                    continue
            set_business_order_status_against_current_job(job)

    db.session.commit()


@task_queue.actor(priority=TaskPriority.NORMAL, queue_name=TASK_QUEUE_BACKGROUND_TASK)
@log_vars.bind_logger_info
def replace_file_with_oss(bo_id: Annotated[int, log_vars.BusinessOrderId], widget_keys, force=False):
    """
    侧边栏上传的图片及视频 淘宝只保留一段时间，造成一部分商家的困扰， 需要及时将淘宝的文件地址替换成oss地址
    淘宝的视频有效期只有24小时，存在过期风险，需要定时任务保证及时替换
    """
    mimetypes.add_type("application/x-matroska", ".mkv")
    bo = BusinessOrder.query.get(bo_id)
    if not bo:
        return
    if not (bo.current_job.is_human() or bo.is_end()) and not force:  # type: ignore[union-attr]
        # 为了避免并发导致覆盖更新data 只在当前任务是人工任务或者工单完结（关闭、删除、完成）时实际执行替换
        # 但是千牛上传的视频文件有24小时有效期，需要在有效期内替换
        replace_file_with_oss.send_with_options(args=(bo.id, widget_keys), kwargs={"force": True}, delay=3600 * 1000)
        return

    log_vars.Sid.set(bo.sid)

    def replace(upload_type, src_url, filename):
        if upload_type not in ("video", "image"):
            return
        oss_url_base = {
            "video": oss_config.VIDEO_OSS_URL_BASE,
            "image": oss_config.IMAGE_OSS_URL_BASE,
        }
        if src_url.startswith(oss_url_base.get(upload_type)):
            return
        # 移除过多的斜杠
        src_url = re.sub(r"(?<!:)/+", "/", src_url)
        #  执行替换
        try:
            resp = requests.get(
                src_url,
                headers={
                    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 "
                    "(KHTML, like Gecko) Chrome/99.0.4844.83 Safari/537.36"
                },
            )
            resp.raise_for_status()
        except requests.exceptions.HTTPError as e:
            logger.error(e)
            return
        file = BytesIO()
        content_type_name = resp.headers.get("content-type")
        if content_type_name:
            content_type = mimetypes.guess_extension(content_type_name)
        else:
            content_type = ""
        file.write(resp.content)
        file.seek(0)
        if upload_type == "video":
            return video_oss_client.upload_video(bo.shop.org_id, file, origin_name=f"{filename}{content_type}")  # type: ignore[union-attr]  # noqa: E501
        elif upload_type == "image":
            return image_oss_client.upload_image(bo.shop.org_id, file, origin_name=f"{filename}{content_type}")  # type: ignore[union-attr]  # noqa: E501
        else:
            return None

    widget_to_update = {}
    data = copy.deepcopy(bo.data)
    db.session.commit()
    for key, upload_type in widget_keys:
        value = data.get(key)
        if not value:
            continue
        if not isinstance(value, list):
            continue
        modified = False
        for idx, single in enumerate(value):
            filename = single.get("fileName")
            src_url = single.get("url")
            target_url = replace(upload_type, src_url, filename)
            if target_url:
                modified = True
                single["url"] = target_url
                value[idx] = single
                logger.info(f"Replace business order {bo.id} {filename} from {src_url} to {target_url}")
        if modified:
            widget_to_update[key] = value
    if widget_to_update:
        bo = BusinessOrder.query.filter_by(id=bo_id).with_for_update().first()  # 写入数据前加行锁 避免写入冲突
        if bo and (bo.current_job.is_human() or bo.is_end() or force):  # type: ignore[union-attr]
            # 为了避免并发导致覆盖更新data 只在当前任务是人工任务或者工单完结（关闭、删除、完成）时实际执行替换
            bo.data.update(widget_to_update)
            # bo.updated_at = bo.updated_at
            # 使用上面的方式会触发 SQLAlchemy 的 onupdate 事件，导致更新时间被更新，所以使用下面的方式更新
            # ut: business_order.test_tasks.test_bo_updated_at
            BusinessOrder.query.filter_by(id=bo_id).update(
                {BusinessOrder.updated_at: bo.updated_at, BusinessOrder.data: bo.data}, synchronize_session=False
            )
        db.session.commit()


@task_queue.actor(queue_name=TASK_QUEUE_JOB)
@log_vars.bind_logger_info
def async_n8n_callback(business_order_id: Annotated[int, log_vars.BusinessOrderId], body: dict):
    bo = BusinessOrder.query.get(business_order_id)
    if not bo:
        logger.warning("找不到 n8n job 对应工单, body={}", body)
        return
    log_vars.Sid.set(bo.sid)
    step = body.get("business_order", {}).get("step")
    if not step:
        logger.warning("找不到 n8n job 对应步骤, body={}", body)
        return

    step_uuid: str = step.get("step_uuid")
    job = bo.jobs.filter_by(step_uuid=step_uuid).first()
    if not job:
        logger.warning("找不到 n8n job 对应工单 job, body={}", body)
        return

    log_vars.Job.set(job)
    logger.info("收到 n8n callback, body={}", body)

    job.extra_data = step.get("data", {})
    job.set_status(JobStatus.SUCCEED)
    bo.data.update(job.extra_data)

    flag_modified(bo, "data")
    db.session.add(bo)
    db.session.commit()
    # todo: 确认是否需要追加 job_transition？
    package_jobs.send(business_order_id, job.id)


@task_queue.actor(queue_name=TASK_QUEUE_JOB)
def short_url_expire(form_id: int, channel_id: int, expire_ts: int):
    import arrow

    from robot_processor.constants import TIME_ZONE
    from robot_processor.form.models import Form
    from robot_processor.notify.services import create_short_link_expire_notify
    from robot_processor.shop.models import Shop

    logger.info(f"n8n shot_url_expire for {form_id} expire_at:{expire_ts}")
    form = Form.query.get(form_id)
    shop = Shop.query.filter_by(channel_id=channel_id).first()
    if not form:
        return
    form = form.wraps(shop)  # type: ignore[arg-type]

    users = kiosk_client.has_function_users_by_sid(
        org_id=form.org_id, sid=form.sid, function_code="TEMPLATE_CREATE_AND_EDIT"
    )
    if not users:
        return

    org_id = form.get_org_id()
    for user in users:
        user_nick = unwrap_optional(user.user_nick)
        # 注册两个通知: 一个是提前3天提醒即将到期，一个是过期后提醒已经过期
        expire_datetime = arrow.get(expire_ts, tzinfo=TIME_ZONE).format("YYYY-MM-DD HH:mm:ss")
        create_short_link_expire_notify(
            to_user_nick=user_nick,
            content=f"您的工单【{form.name}】短链接将于 {expire_datetime} 到期，请及时在后台重新生成后配置",
            send_ts=expire_ts - 3 * 24 * 60 * 60,  # 提前3天提醒短链接即将过期
            org_id=org_id,
        )
        create_short_link_expire_notify(
            to_user_nick=user_nick, content=f"您的工单【{form.name}】短链接已到期，请及时在后台重新生成后配置", send_ts=expire_ts, org_id=org_id
        )
