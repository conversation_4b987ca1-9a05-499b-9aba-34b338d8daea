import copy
import enum
import json
import re
import time
from datetime import datetime
from enum import StrEnum
from functools import cached_property
from functools import lru_cache
from itertools import chain
from typing import Dict
from typing import Iterable
from typing import List
from typing import Literal
from typing import Optional
from typing import Union

import arrow
import sqlalchemy as sa
from flask import current_app
from loguru import logger
from more_itertools import first
from pydantic import BaseModel
from pydantic import Field
from pydantic import parse_obj_as
from pydantic import validator
from result import Err
from result import Ok
from result import Result
from robot_types.helper.sql import FieldResolverRegistry
from robot_types.helper.sql import TableFieldRegistry
from sqlalchemy import Computed
from sqlalchemy import and_
from sqlalchemy import or_
from sqlalchemy import true
from sqlalchemy.ext.associationproxy import AssociationProxy
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.orm import DynamicMapped
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy.orm.attributes import flag_modified
from typing_extensions import TypedDict

from robot_processor.assistant.constants import ASSISTANT_DELETED
from robot_processor.assistant.constants import ASSISTANT_INVALID
from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.client import action_client
from robot_processor.client import kiosk_client
from robot_processor.client.logistics_clients.schema import JTWaybillIntercept
from robot_processor.constants import MISSING
from robot_processor.constants import PLATFORM_MAP
from robot_processor.db import BasicMixin
from robot_processor.db import DbBaseModel
from robot_processor.db import in_transaction
from robot_processor.db import no_auto_flush
from robot_processor.enums import AssigneeRule
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import Creator
from robot_processor.enums import FormMold
from robot_processor.enums import FromType
from robot_processor.enums import JobProcessMark
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.enums import StepType
from robot_processor.enums import UserStatus
from robot_processor.ext import db
from robot_processor.form.api.widget_schema import WidgetInfoDict
from robot_processor.form.models import Form
from robot_processor.form.models import FormVersion
from robot_processor.form.models import Step
from robot_processor.form.models import WidgetInfo
from robot_processor.form.models import WidgetRef
from robot_processor.shop.models import Shop
from robot_processor.utils import get_nonce
from robot_processor.utils import ts2date
from robot_processor.utils import unwrap_optional


class Job(DbBaseModel, BasicMixin):
    status: Mapped[JobStatus] = mapped_column(sa.Enum(JobStatus), default=JobStatus.INIT, comment="job 的状态")
    step_id: Mapped[int | None] = mapped_column(sa.Integer, comment="关联步骤")
    step: Mapped[Step | None] = relationship(Step, primaryjoin="foreign(Job.step_id)==Step.id")
    business_order_id: Mapped[int | None] = mapped_column(sa.ForeignKey("business_order.id"), comment="关联工单")
    business_order: Mapped["BusinessOrder"] = relationship(lambda: BusinessOrder, back_populates="jobs")
    # 执行时填充
    message_id: Mapped[str] = mapped_column(sa.String(128), default=get_nonce, comment="队列里的job_id")
    # 异常信息
    exc_info: Mapped[str | None] = mapped_column(sa.Text)

    data: Mapped[dict | None] = mapped_column(sa.JSON)
    _raw_step_v2: Mapped[dict] = mapped_column("raw_step_v2", sa.JSON, default=dict, comment="步骤信息")

    # 步骤唯一ID
    step_uuid: Mapped[str | None] = mapped_column(sa.String(32))

    process_mark: Mapped[JobProcessMark] = mapped_column(
        sa.Enum(JobProcessMark), default=JobProcessMark.UNPROCESSED, comment="处理标记"
    )

    assignee: Mapped[str | None] = mapped_column(sa.String(128), nullable=True, comment="代理人")
    assignee_user_id: Mapped[int | None] = mapped_column(sa.Integer, comment="assignee user id")
    assignee_group: Mapped[list] = mapped_column(sa.JSON, default=list, comment="assignee归属组")
    assignee_type: Mapped[Creator | None] = mapped_column(
        sa.Enum(Creator), default=Creator.ASSISTANT, comment="assignee类型"
    )  # noqa: E501
    feisuo_assignee: Mapped[str | None] = mapped_column(sa.String(128), nullable=True, comment="assignee飞梭账号昵称")
    feisuo_assignee_user_id: Mapped[int | None] = mapped_column(sa.Integer, comment="assignee飞梭账号user id")
    feisuo_assignee_group: Mapped[list] = mapped_column(sa.JSON, default=list, comment="assignee飞梭账号归属组")

    shop: AssociationProxy[Shop] = association_proxy("business_order", "shop")
    form: AssociationProxy[Form] = association_proxy("step", "form")
    sid: AssociationProxy[str] = association_proxy("business_order", "sid")

    @property
    def org_id(self):
        return self.shop.org_id

    @property
    def shop_platform(self):
        return self.shop.platform

    def get_candidate_assistants(self) -> List[AccountDetailV2]:
        """从step中获取可以所有的可以指派的用户列表"""
        if self.step is None:
            logger.warning(f"job {self.id} has no step({self.step_id}@{self.step_uuid})")
            return []
        try:
            assistants_v2 = self.step.get_assistants_v2()
            return assistants_v2.get_latest_assignee_account_details(self.shop)
        except Exception as e:  # noqa
            logger.exception("get candidate assistant error.")
            return []

    # 处理失效信息: types.notifier.DeadlineInfo
    deadline_info: Mapped[dict] = mapped_column(sa.JSON, default=dict)  # 根据step生成的截止时间

    # extra data  显示一些额外信息（N8N的回调结果、预分派的执行人）
    _extra_data: Mapped[dict] = mapped_column("extra_data", sa.JSON, default=dict)
    archived: Mapped[bool | None] = mapped_column(
        sa.Boolean,
        comment="标记该条数据是否需要归档，否则bi监听binlog的delete event之后会直接删除",
    )

    @property
    def extra_data_wrapper(self):
        """目前没有需要 record iteration 的场景, 所以暂时不开放"""
        return Job.Wrapper.ExtraData(self)

    @property
    def extra_data(self):
        return self._extra_data

    @extra_data.setter
    def extra_data(self, value):
        self._extra_data = value

    @property
    def raw_step_v2(self):
        if self._raw_step_v2:
            return self._raw_step_v2
        if not self.step:
            return {}
        # raw_step 异常则走实时接口，不在这里做兜底修复；
        # 需要人工修复，并排查raw_step异常原因
        if not self.step.raw_step:
            self.step.update_raw_step()

        if self.step.is_auto() and self.step.data.get("rpa_id") and not self.step.raw_step.get("task"):  # noqa: E501
            # related: https://git.leyantech.com/digismart/robot-processor/-/issues/147
            logger.error(f"empty task of step:{self.step.id}! created_at:{ts2date(self.step.created_at)}")
        return self.step.raw_step

    @property
    def raw_ui_schema(self):
        return self.raw_step_v2.get("ui_schema", [])

    @property
    def raw_task_id(self):
        return self.raw_step_v2.get("data", {}).get("rpa_id", None)

    @property
    def raw_task_info(self):
        return self.raw_step_v2.get("task") or {}

    @property
    def widget_ref(self):
        """step引用的组件"""
        return WidgetRef.parse(self.raw_step_v2.get("data", {}).get("widget_ref"), force_parse_str=True)

    @property
    def display_rule(self):
        return self.raw_step_v2.get("display_rule", [])

    def new_task_notify(self):
        """新买家工单提醒"""
        from robot_processor.notify.services import create_new_buyer_order_notify

        if not self.is_human():
            logger.info("非人工任务，不提醒")
            return

        if not self.enable_force_task_notifier:
            logger.info("未开启强制提醒")
            return

        _expect_status = [
            BusinessOrderStatus.RUNNING,
            BusinessOrderStatus.FAILED,
            BusinessOrderStatus.PAUSED,
            BusinessOrderStatus.PENDING,
            BusinessOrderStatus.IN_EXCEPTION,
            BusinessOrderStatus.TO_BE_COLLECTED,
        ]
        business_order = self.business_order
        if business_order.status not in _expect_status:
            logger.info("工单状态 {} 不在 {} 中，不提醒", business_order.status, _expect_status)
            return

        step = self.step
        form = step.form  # type: ignore[union-attr]
        shop: Shop | None = business_order.shop

        if form.form_mold != FormMold.BUYER:
            logger.info("非买家工单不提醒")
            return
        if self.is_assignee_valid():  # 客服有效
            assistants = [self.get_assignee_assistant()]
        elif self.need_pick():  # 自由领取
            assistants = self.get_candidate_assistants()
        else:
            logger.error(f"当前步骤无执行客服，job_id: {self.id}")
            return
        if len(assistants) == 0:
            logger.error(f"任务缺少有效的执行客服账号, job id: {self.id}")
            return
        if not shop:
            logger.warning(f"未找到工单实例店铺信息，business_order_id: {self.business_order_id}")
            return

        notified = []
        data: JobRemindRecord.Data = dict(
            business_order_id=self.business_order_id,  # type: ignore[typeddict-item]
            job_id=self.id,
            form_name=form.name,  # type: ignore[typeddict-item]
            step_name=step.name,  # type: ignore[union-attr,typeddict-item]
            sid=shop.sid,
            org_id=str(shop.org_id) if shop.org_id is not None else "",
        )
        try:
            for assistant in assistants:
                create_new_buyer_order_notify(job=self, to_user_nick=assistant.user_nick, context=data)
                notified.append(assistant.user_nick)
        except Exception as e:
            logger.opt(exception=e).error("提醒新任务时出现异常")
        logger.info(
            "提醒新买家工单: assistants={} notified={}",
            [a.user_nick for a in assistants],
            notified,
        )

    def _get_step_notifier(self):
        """获取步骤超时提醒设置"""
        notifier_settings_data = self.raw_step_v2.get("notifier")
        if not notifier_settings_data:
            return None
        return Step.Schema.Notifier(**notifier_settings_data)

    @logger.catch
    def start_timing(self):
        """开始计时"""
        from robot_processor.notify.services import create_exceed_job_deadline_notify
        from robot_processor.notify.services import create_near_job_deadline_notify
        from robot_processor.types.job_deadline import DeadlineInfo

        notifier_settings = self._get_step_notifier()
        if not notifier_settings or not notifier_settings.enabled:
            logger.info("step 没有设置超时提醒: notifier_settings={}", notifier_settings)
            return

        if not self.deadline_info:
            deadline_info = DeadlineInfo.from_step_notifier_settings(notifier_settings)
            self.deadline_info = deadline_info.dict()
            flag_modified(self, "deadline_info")
            db.session.add(self)
            self.business_order.message_ts = deadline_info.job_ts
        else:
            deadline_info = DeadlineInfo.parse_obj(self.deadline_info)

        # 注意：下面的两个通知，其实际发送时间分别是 deadline_ts 和 timeout_ts
        # 如果 job 实际上在这两个时间点之前就完成了，那么这两个通知将在 end_timing 中通过 CancelNotification 取消发送
        if notifier_settings.deadline_enabled and deadline_info.job_ts > deadline_info.deadline_ts:
            create_near_job_deadline_notify(self, deadline_info)
        if notifier_settings.timeout_enabled and deadline_info.job_ts < deadline_info.timeout_ts:
            create_exceed_job_deadline_notify(self, deadline_info)

    @logger.catch
    def end_timing(self):
        """结算计时"""
        from robot_processor.enums import BusinessOrderTimeoutType
        from robot_processor.notify.services import cancel_notify
        from robot_processor.types.job_deadline import DeadlineInfo

        notifier_settings = self._get_step_notifier()

        if not notifier_settings or not notifier_settings.enabled:
            self.business_order.is_timeout = BusinessOrderTimeoutType.NOT_SET.value
        else:
            now = int(time.time())
            if not self.deadline_info:
                # 降级成 WARNING 级别。
                logger.warning("还没有开启计时，代码中可能存在 bug")
            else:
                deadline_info = DeadlineInfo.parse_obj(self.deadline_info)
                if now > deadline_info.job_ts:
                    self.business_order.is_timeout = BusinessOrderTimeoutType.TIMEOUT.value
                elif now < deadline_info.job_ts:
                    self.business_order.is_timeout = BusinessOrderTimeoutType.NOT_TIMEOUT.value

        logger.info("结束计时, 取消所有尚未生效的通知: {}", cancel_notify(self.message_id))

    @property
    def prev(self) -> Optional["Job"]:
        """
        以最近的执行记录为标准，获取前一步任务。

        warning: 不得用于类似 while job.prev; doxxx; job=job.prev 这类条件循环。
                 因为现在 job_history 是记录任务执行的记录列表，可能会存在有任务重复执行的情况。
                 介于该方法内部使用 list.index 来在列表里定位，可能造成死循环。
        :return:
        """

        reversed_job_history = self.business_order.get_reversed_job_history()
        if self.id not in reversed_job_history:
            # 不在 job_history 中，则表明可能还没执行到。
            return None
        cur_job_index = reversed_job_history.index(self.id)
        prev_job_index = cur_job_index + 1
        if prev_job_index < len(reversed_job_history):
            prev_job_id = reversed_job_history[prev_job_index]
            return Job.query.get(prev_job_id)
        return None

    def get_prev_in_first_run(self) -> Optional["Job"]:
        """
        获取第一次执行时的前序任务。
        :return:
        """
        job_history = self.business_order.job_history
        if self.id not in job_history:
            # 不在 job_history 中，则表明可能还没执行到。
            return None
        cur_job_index = job_history.index(self.id)
        prev_job_index = cur_job_index - 1
        if prev_job_index >= 0:
            prev_job_id = job_history[prev_job_index]
            return Job.query.get(prev_job_id)
        return None

    def get_prev_jobs(self) -> list:
        """
        获取当前工单下，该任务后已经执行过的所有任务。
        :return:
        """
        reversed_job_history = self.business_order.get_reversed_job_history()
        if self.id not in reversed_job_history:
            return []
        cur_job_index = reversed_job_history.index(self.id)
        prev_job_index = cur_job_index + 1
        if prev_job_index >= len(reversed_job_history):
            return []
        jobs = Job.query.filter(
            Job.business_order_id == self.business_order.id,
            Job.id.in_(reversed_job_history),
        ).all()
        job_mapping = {job.id: job for job in jobs}
        return [job_mapping.get(prev_job_id) for prev_job_id in reversed_job_history[prev_job_index:]]

    @property
    def next(self) -> Optional["Job"]:
        """
        首先以最近的执行记录为标准，获取下一步任务。
        如果没有出现在执行记录中，则说明还没有执行到，基于 step_id 去找。

        warning: 不得用于类似 while job.next; doxxx; job=job.next 这类条件循环。
                 因为现在 job_history 是记录任务执行的记录列表，可能会存在有任务重复执行的情况。
                 介于该方法内部使用 list.index 来在列表里定位，可能造成死循环。
        :return:
        """
        reversed_job_history = self.business_order.get_reversed_job_history()
        if self.id not in reversed_job_history:
            next_job = None
        else:
            cur_job_index = reversed_job_history.index(self.id)
            next_job_index = cur_job_index - 1
            if next_job_index < 0:
                next_job = None
            else:
                next_job = Job.query.get(reversed_job_history[next_job_index])

        if next_job is not None:
            return next_job

        next_step_id = self.business_order.get_next_step_id(self.id, None)
        if next_step_id is None or next_step_id in ["BRANCH", "END"]:
            return None

        return Job.query.filter_by(
            step_id=next_step_id,
            business_order_id=self.business_order_id,
        ).one_or_none()

    def get_next_jobs(self) -> list:
        """
        获取当前工单下，该任务后已经执行过的所有任务。
        :return:
        """
        reversed_job_history = self.business_order.get_reversed_job_history()
        if self.id not in reversed_job_history:
            return []
        cur_job_index = reversed_job_history.index(self.id)
        next_job_index = cur_job_index - 1
        if next_job_index < 0:
            return []
        jobs = Job.query.filter(
            Job.business_order_id == self.business_order.id,
            Job.id.in_(reversed_job_history),
        ).all()
        job_mapping = {job.id: job for job in jobs}
        return [job_mapping.get(next_job_id) for next_job_id in reversed_job_history[: next_job_index + 1][::-1]]

    @property
    def enable_force_task_notifier(self):
        return (self.raw_step_v2 or {}).get("notifier", {}).get("force_task_notifier", {}).get("enabled", False)

    @property
    def has_next(self):
        return bool(self.step.next_step_ids)  # type: ignore[union-attr]

    @property
    def task_type(self):
        return self.raw_step_v2.get("task", {}).get("task_type")

    @property
    def job_name(self):
        return self.raw_step_v2.get("name")

    def set_next_assignee(self, next_job_assignee_assistant):
        next_job = self.next
        if (
            next_job is not None
            and next_job.is_human()
            and next_job.raw_step_v2.get("assignee_rule", AssigneeRule.RANDOM) == AssigneeRule.MANUAL
            and next_job_assignee_assistant is not None
        ):
            next_job_assignee_assistant = unwrap_optional(next_job_assignee_assistant.get_bound_leyan_user())
            next_job.extra_data = dict(
                reserved_assignee=dict(
                    account=next_job_assignee_assistant.user_nick,
                    assignee_type=next_job_assignee_assistant.user_type,
                    assignee_user_id=next_job_assignee_assistant.user_id,
                )
            )

    @property
    def raw_step_type(self):
        _step_type = self.raw_step_v2.get("step_type", StepType.auto)

        try:
            return StepType(_step_type)
        except ValueError:
            return StepType.auto

    @property
    def raw_step_assignee_rule(self):
        _step_assignee_rule = self.raw_step_v2.get("assignee_rule", AssigneeRule.RANDOM)

        try:
            return AssigneeRule(_step_assignee_rule)
        except ValueError:
            return AssigneeRule.RANDOM

    def is_auto(self):
        return self.raw_step_v2.get("step_type") in [
            StepType.auto.value,
            StepType.exclusive_gateway.value,
            StepType.iterate_gw_begin.value,
            StepType.iterate_gw_end.value,
        ]

    def is_buyer_create_job(self):
        form_id = self.raw_step_v2.get("form_id")
        form: Form | None = Form.query.get(form_id) if form_id else None
        # 是买家自助填写工单且是第一个步骤  form.enable_service_count 是为了兼容历史数据
        if (
            form.enable_service_count or form.form_mold == FormMold.BUYER  # type: ignore[union-attr]
        ) and not self.raw_step_v2.get("prev_step_ids"):
            return True
        return False

    def is_human(self):
        return self.raw_step_v2.get("step_type") == StepType.human.value

    def is_exclusive_gateway(self):
        return self.raw_step_v2.get("step_type") == StepType.exclusive_gateway.value

    def is_gateway(self):
        return StepType(self.raw_step_v2.get("step_type")) in (
            StepType.exclusive_gateway,
            StepType.iterate_gw_begin,
            StepType.iterate_gw_end,
        )

    def need_pick(self):
        step = self.step
        if step is None:
            return False
        if step.assignee_rule != AssigneeRule.FREE_PICK:
            return False
        if step.step_type != StepType.human:
            return False
        if self.assignee_user_id and self.assignee_type:
            return False
        if self.status == JobStatus.INIT:
            return False

        return True

    @property
    def format(self):
        return f"Job({self.id} {self.status.name})"

    def is_completed(self):
        """job已经完结"""
        return self.status == JobStatus.SUCCEED

    def is_success(self):
        """job已执行成功"""
        return self.status == JobStatus.SUCCEED or self.__iterator_gateway_success()

    def __iterator_gateway_success(self):
        """遍历网关在迭代期间会一直处于running状态"""
        return self.status == JobStatus.RUNNING and StepType(self.step_type) in (
            StepType.iterate_gw_begin,
            StepType.iterate_gw_end,
        )

    def is_failed(self):
        return self.status == JobStatus.FAILED

    def is_paused(self):
        return self.status in [JobStatus.RUNNING, JobStatus.FAILED, JobStatus.PENDING]

    def is_unprocessed(self):
        """待处理"""
        return self.status == JobStatus.PENDING and self.get_process_mark() in [
            JobProcessMark.UNPROCESSED,
            JobProcessMark.REOPEN,
            JobProcessMark.RECALL,
            JobProcessMark.REJECT,
            JobProcessMark.SAVE,
            JobProcessMark.PICK,
            JobProcessMark.DELIVER,
            JobProcessMark.ASSIGN,
            JobProcessMark.PAUSE,
            JobProcessMark.UNPAUSE,
        ]

    def is_processed(self):
        """已处理"""
        return (
            self.status not in [JobStatus.INIT, JobStatus.PENDING]
            or (self.status == JobStatus.PENDING and self.get_process_mark() == JobProcessMark.CLOSE)  # 关闭
            or (self.status == JobStatus.INIT and self.get_process_mark() == JobProcessMark.REJECT)  # 退回
        )

    def appoint_reserved_assignee(self, next_operator: AccountDetailV2):
        """为当前job预先分配处理人"""
        raw_step_v2 = self.raw_step_v2
        assignee_rule = raw_step_v2.get("assignee_rule")
        if not self.is_human():
            logger.error(f"当前为非人工步骤，不能指派执行人 job_id: {self.id}")
            return False, "当前为非人工步骤，不能指派执行人"

        if AssigneeRule.MANUAL.value == assignee_rule:
            next_operator = unwrap_optional(next_operator.get_bound_leyan_user())
            if next_operator not in self.get_candidate_assistants():
                logger.info(f"wzy{next_operator}  |||  {self.get_candidate_assistants()}")
                logger.error(f"指定执行人不在当前步骤候选人中 job_id: {self.id}")
                return False, "指定执行人不在当前步骤候选人中"
            self.extra_data = {
                "reserved_assignee": {
                    "account": next_operator.user_nick,
                    "assignee_type": next_operator.user_type,
                    "assignee_user_id": next_operator.user_id,
                }
            }
        return True, ""

    @classmethod
    def create_by_step(cls, step, business_order_id):
        job = cls()
        job.step_id = step.id
        job.business_order_id = business_order_id
        job.step_uuid = step.step_uuid
        return job

    def to_dict(self, with_schema=False, from_plugin=False):
        raw_step_v2 = self.raw_step_v2
        info = {
            "status": self.status.value,
            "step_name": raw_step_v2.get("name"),
            "step_type": raw_step_v2.get("step_type", StepType.auto.value),
            "task_id": self.raw_task_id,
            "step_uuid": raw_step_v2.get("step_uuid"),
            "step_id": raw_step_v2.get("id"),
            "form_id": raw_step_v2.get("form_id"),
            "job_id": self.id,
            "can_reject": raw_step_v2.get("can_reject"),
            "next_step_ids": raw_step_v2.get("next_step_ids"),
            "prev_step_ids": raw_step_v2.get("prev_step_ids"),
            "step_status": raw_step_v2.get("status"),
            "assignee": self.assignee,
            "data": {},
            "has_tracing_remind": False,
            "process_mark": self.get_process_mark().value,
        }
        if with_schema:
            info["ui_schema"] = copy.deepcopy(raw_step_v2.get("ui_schema", []) or [])
            if from_plugin:
                for widget in info["ui_schema"]:
                    # 不在打平，前端确认后可以这段代码移除
                    value = widget.get("option_value", {})
                    widget.update(value)
        return info

    @no_auto_flush()
    def set_status(self, status: JobStatus):
        from robot_metrics import Stats

        is_auto_step = bool(self.step and self.step.is_auto())
        is_final = bool(status in [JobStatus.SUCCEED, JobStatus.FAILED])
        is_inited = bool(status == JobStatus.INIT)
        is_succeed = bool(status == JobStatus.SUCCEED)
        is_rpa_client_job_history = bool("execution_id" in (self._extra_data or {}))

        old_status = self.status
        self.status = status

        if is_succeed and is_auto_step:
            # FIXME: 为什么要将最近更新人清空
            self.business_order.set_updator(None)

        # 当前步骤任务完成，执行清理逻辑
        if is_final:
            self.process_mark = JobProcessMark.ACCEPT
            self.remove_from_pool()
        # 通常撤回/退回动作会将 Job 初始化 robot_processor.business_order.job_action.JobAction.do_reject
        elif is_inited:
            if is_auto_step:
                self.remove_job_task()
                if is_rpa_client_job_history:
                    self.remove_rpa_client_execute_history()
            else:
                self.remove_from_pool()
        else:
            pass

        if old_status != status and is_final:
            if self.step:
                try:
                    step_type = self.step.step_type.name
                except:  # noqa
                    step_type = self.step.step_type  # type: ignore[assignment]
                auto_job_type = self.raw_step_v2.get("task", {}).get("task_type")
            else:
                step_type = "unknown"
                auto_job_type = "unknown"
            Stats.Job.mark_finished(f"{step_type}:{auto_job_type}", elapsed=time.time() - self.created_at)

    def get_process_mark(self) -> JobProcessMark:
        """枚举值"""
        if self.process_mark:
            return self.process_mark
        elif self.is_completed():
            # 兼容旧数据
            return JobProcessMark.ACCEPT
        return JobProcessMark.UNPROCESSED

    @classmethod
    def unprocessed_condition(cls):
        """
        待处理条件

        status = pending
        """
        return cls.status == JobStatus.PENDING

    @classmethod
    def processed_condition(cls):
        """
        已处理条件
        status != pending
        或者
        status == pending && mark in(REJECT,CLOSE)
        """
        return or_(
            cls.status.not_in([JobStatus.INIT, JobStatus.PENDING]),
            and_(
                cls.status == JobStatus.PENDING,
                cls.process_mark == JobProcessMark.CLOSE,
            ),
            and_(cls.status == JobStatus.INIT, cls.process_mark == JobProcessMark.REJECT),
        )

    def brief(self):
        status = self.status.value if self.status else JobStatus.INIT.value
        return {"id": self.id, "status": status, "task_type": self.task_type}

    def remove_rpa_client_execute_history(self):
        prev_execution_id = self._extra_data.pop("execution_id")
        self.extra_data = {**self._extra_data, "prev_execution_id": prev_execution_id}

    def remove_job_task(self):
        JobTask.query.filter(JobTask.job_id == self.id).delete()

    def remove_from_pool(self):
        return JobPool.remove_job(self.id)

    def pick_from_pool(self, user):
        return JobPool.pick_job(self.id, user)

    def in_pool(self, user: AccountDetailV2):
        user_id, user_type = user.user_id, user.user_type
        if not user_id or not user_type:
            return False
        return bool(
            JobPool.query.filter_by(assignee_user_id=user_id, assignee_user_type=user_type, job_id=self.id).first()
        )

    def get_assignee_assistant(self):
        """这里使用实时查询而不是使用 Job 上冗余的客服信息，是因为很多业务需要查询客服的禁用状态"""
        from robot_processor.error.errors import ScenarioError

        try:
            if not self.assignee_user_id or not self.assignee_type:
                return None
            return kiosk_client.get_user_by_id(self.assignee_type, self.assignee_user_id)
        except ScenarioError:
            return None
        except AssertionError as e:
            logger.debug(
                f"get job assignee assistant error: {e}, "
                f"job: {self.id}, "
                f"assistant: {(self.assignee_user_id, self.assignee_type)}"
            )
            return AccountDetailV2(
                user_type=self.assignee_type,
                user_id=self.assignee_user_id,
                user_nick=self.assignee,
            )

    def set_assignee_assistant(self, new_assignee: Optional[AccountDetailV2], remove_from_pool=True):
        if new_assignee is None:
            self.assignee_user_id = self.assignee = self.feisuo_assignee_user_id = self.feisuo_assignee = None
            self.assignee_type = Creator.ASSISTANT
            self.assignee_group = self.feisuo_assignee_group = []
        else:
            self.assignee_user_id = new_assignee.user_id
            self.assignee = new_assignee.user_nick
            self.assignee_type = Creator(unwrap_optional(new_assignee.user_type))
            self.assignee_group = new_assignee.groups
            self.feisuo_assignee_user_id = new_assignee.feisuo_user_id
            self.feisuo_assignee = new_assignee.feisuo_user_nick
            self.feisuo_assignee_group = new_assignee.feisuo_groups
            if remove_from_pool:
                self.remove_from_pool()
                self.clear_visit()

    def is_assignee_valid(self):
        assignee = self.get_assignee_assistant()
        return assignee is not None and assignee.is_valid()

    def check_assignee(self) -> Result[None, str]:
        # 买家工单，且是第一步，不需要校验
        if self.is_buyer_create_job():
            return Ok(None)
        if self.is_auto():
            return Ok(None)

        assignee_user = self.get_assignee_assistant()
        if assignee_user:
            # 候选客服的状态为非有效
            if assignee_user.status == UserStatus.DISABLE.value:
                return Err(f"{ASSISTANT_INVALID} {assignee_user=}")
            # 分派人删除 针对已分派的任务
            elif assignee_user.status == UserStatus.DELETED.value:
                return Err(f"{ASSISTANT_DELETED} {assignee_user=}")
            # 校验通过
            else:
                return Ok(None)
        # 没有指派客服信息，肯定是有问题的
        else:
            # 工单模板当前步骤被删除 导致无法获取步骤可执行客服
            if not self.current_step():
                return Err("当前步骤已被删除")
            # 无可用分派人 针对还未分派的任务（自由领取待领取， 随机分派候选人列表为空）
            elif not self.get_candidate_assistants():
                return Err("缺少有效的执行客服账号")
            # 没有选择下一个人工步骤的分派人 针对批量导入创建的工单
            else:
                return Err("需要手动分派执行客服")

    def current_step(self) -> Optional[Step]:
        step = (
            Step.query.filter(
                Step.step_uuid == self.step_uuid,
                Step.deleted.isnot(True),
                Step.is_dirty.is_(False),
            )
            .order_by(Step.id.desc())
            .first()
        )
        return step

    def is_visited(self, user_id):
        # 上线时间点前的默认已visit
        exclude = arrow.get(current_app.config["RED_DOT_EXCLUDE_DATE"])
        if self.updated_at <= exclude.int_timestamp:
            return True
        return db.session.query(JobVisitHistory.query.filter_by(job_id=self.id, user_id=user_id).exists()).scalar()

    def visit(self, user_id):
        if self.is_visited(user_id):
            return
        else:
            obj = JobVisitHistory(user_id=user_id, job_id=self.id)
            db.session.add(obj)

    def clear_visit(self, user_ids=None):
        query = JobVisitHistory.query.filter_by(job_id=self.id)
        if user_ids:
            query.filter(JobVisitHistory.user_id.in_(user_ids))
        return query.delete()

    @property
    def step_type(self) -> StepType:
        if self.step is None:
            logger.error(f"Job {self.id} 缺少步骤信息。")
            return self.raw_step_type
        else:
            return self.step.step_type

    def is_alipay(self):
        task_type = self.raw_step_v2.get("task", {}).get("task_type")
        return task_type and JobType.has(task_type) and JobType(task_type) in (JobType.ALIPAY, JobType.CONFIRM_ALIPAY)

    def update_process_mark_and_record_action(
        self,
        process_mark: JobProcessMark,
        assistant: AccountDetailV2,
        operate_reason: str,
        is_keep_business_order_updator_info: bool = False,
    ) -> None:
        """
        调用该方法，会在修改 job 的 process_mark 后，进而修改到对应工单的信息。
        并记录操作日志。

        :param process_mark:                            Job 处理标志。
        :param assistant:                               执行者的相关信息。
        :param operate_reason:                          操作理由。
        :param is_keep_business_order_updator_info:     是否要保持 business_order 的 updator_info 不变。
        :return:
        """
        # 查找对应的工单。
        business_order: Optional[BusinessOrder] = self.business_order
        if business_order is None:
            logger.exception("查询不到该任务: {} 对应的工单: {}".format(self.id, self.business_order_id))
            return

        action = process_mark
        operate_action_name = process_mark.name
        operate_ts = int(time.time())

        # 更新任务的处理标识
        if process_mark not in [
            JobProcessMark.ASSIGN,
            JobProcessMark.UPGRADE,
            JobProcessMark.FINISH,
            JobProcessMark.JUMP,
            JobProcessMark.ENDORSE,
            JobProcessMark.OVERRULE,
        ]:
            self.process_mark = process_mark
        # 由于数据库中 Job 的 process_mark 枚举已经被限定。
        # 如果任务被标记为 finish，则 process_mark 修改为 accept。
        # 但是 operate_action 依旧使用 finish。（因为 nevermore 端有设定该枚举）
        if process_mark in [
            JobProcessMark.FINISH,
            JobProcessMark.ENDORSE,
            JobProcessMark.JUMP,
        ]:
            self.process_mark = JobProcessMark.ACCEPT
        if process_mark in [JobProcessMark.OVERRULE]:
            self.process_mark = JobProcessMark.REJECT

        if not is_keep_business_order_updator_info:
            business_order.set_updator(assistant)

        # 记录操作日志到 robot-processor 的数据库
        business_order.record_job_action(
            action=action,
            operate_reason=operate_reason,
            operate_ts=operate_ts,
        )
        # 记录操作日志到 nevermore
        try:
            operate_action = JobProcessMark[operate_action_name]

            raw_data = business_order.data.copy()
            raw_data["comment"] = operate_reason
            raw_data["job"] = self.raw_step_v2["name"]
            assignee = self.get_assignee_assistant()
            raw_data["assign_target_user"] = assignee.user_nick if assignee else ""

            if operate_action in (JobProcessMark.UPGRADE, JobProcessMark.SAVE):
                operator = "update"
            else:
                operator = operate_action.name.lower()
            # 产品要求: 重试时显示步骤名称
            if operate_action in (JobProcessMark.RETRY,):
                label = self.raw_step_v2.get("name")
            else:
                form_version = business_order.get_form_version()
                label = (form_version.meta or {}).get("name")
            with db.session.no_autoflush:
                log = dict(
                    sid=str(business_order.sid),
                    platform="",
                    model="business_orders",
                    label=label,
                    object_id=str(business_order.id),
                    operator=operator,
                    operate_ts=operate_ts,
                    user=assistant.user_nick,
                    raw_json=json.dumps(raw_data, ensure_ascii=True),
                )
            action_client.create_action_log_by_kafka(log)
        except Exception as e:
            logger.warning(f"action log error: {e}")

    def clear_tips(self) -> None:
        """
        清除指派记录。
        :return:
        """
        ed = self.extra_data.copy()
        ed.update({"tips": None})
        self.extra_data = dict(ed)

    def record_tips(self, operator: str, assignee: str, reason: str) -> None:
        """
        操作记录。
        :param operator:
        :param assignee:
        :param reason:
        :return:
        """
        now_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        tips = "[{}] 于 {} 指派工单给 [{}]\n原因：{}".format(operator, now_time, assignee, reason)
        logger.info("assign tips: {}".format(tips))
        ed = self.extra_data.copy()
        ed.update({"tips": tips})
        self.extra_data = dict(ed)

    def get_tips(self) -> str:
        """
        获取操作记录。
        :return:
        """
        return self.extra_data.get("tips")

    def need_manual_assign(self) -> bool:
        """任务是否需要手动分派执行客服

        任务为'人工步骤'且执行客服分派规则为'手动选择'
        """
        return (
            self.raw_step_v2["step_type"] == StepType.human
            and self.raw_step_v2.get("assignee_rule", AssigneeRule.RANDOM) == AssigneeRule.MANUAL
        )

    def get_form_validator(self):
        return unwrap_optional(self.step).get_form_validator(
            shop=self.shop,
            form_composer=get_form_composer(self.business_order.form_version_id),
        )

    def get_event_validator(self):
        return unwrap_optional(self.step).get_event_validator(
            shop=self.shop,
            form_version_id=unwrap_optional(self.business_order.form_version_id),
        )

    def get_value_unique_dimensions(self):
        step = unwrap_optional(self.step)
        form_version_id = unwrap_optional(self.business_order.form_version_id)
        data = self.business_order.data.copy()
        dimensions = step.get_merged_value_unique_dimensions(data=data, shop=self.shop, form_version_id=form_version_id)
        return dimensions

    class Utils:
        """一般需要 pipeline 做的工具方法，维护在 Utils 中

        Methods:
            mark_failed(job, exc_info)
                标记 job 执行失败
            job_transition_action(job)
                可读的工单流转操作
        """

        @staticmethod
        @in_transaction()
        def mark_failed(job: "Job", exc_info: Union[Exception, str]):
            from robot_processor.business_order.job_wrappers.wrapper import \
                set_business_order_status_against_current_job

            job.exc_info = str(exc_info)
            job.set_status(JobStatus.FAILED)
            set_business_order_status_against_current_job(job)

        @staticmethod
        def job_transition_action(job: "Job"):
            readable_process_mark_map = {
                JobProcessMark.ACCEPT: "提交",
                JobProcessMark.REJECT: "退回",
                JobProcessMark.RECALL: "撤回",
                JobProcessMark.SKIP: "跳过",
            }
            if job.process_mark not in readable_process_mark_map:
                with logger.contextualize(job_id=job.id):
                    logger.warning(f"unrecognized process mark: {job.process_mark}")
                return "未知"
            return readable_process_mark_map[job.process_mark]

    class Schema:
        class ExtraData(BaseModel):
            class Config:
                validate_assignment = True

            class Iteration(BaseModel):
                widget_ref: WidgetRef
                values: list = Field(default_factory=list)

            reserved_assignee: Optional[dict] = Field(default=None, description="预分配执行人信息")
            iterations: Optional[List[Iteration]] = Field(default=None, description="遍历网关迭代器")
            archived_operated_approver_ids: list[int] | None = Field(
                default=None, description="执行过审批操作的审批人的归档信息"
            )
            jt_intercept_callback_results: list[JTWaybillIntercept] | None = Field(
                default=None, description="存储极兔拦截下发回调的信息列表"
            )

    class Wrapper:
        __slots__ = ()

        class ExtraData:
            __slots__ = ("_job", "_extra_data")
            _job: "Job"
            _extra_data: "Job.Schema.ExtraData"

            def __init__(self, job):
                self._job = job
                self._extra_data = Job.Schema.ExtraData(**(job._extra_data or {}))

            def _init_iteration(self, widget_ref: "WidgetRef"):
                if self._extra_data.iterations is None:
                    self._extra_data.iterations = []
                self._extra_data.iterations.append(self._extra_data.Iteration(widget_ref=widget_ref))
                self._save()

            def record_iteration(self, widget_ref: "WidgetRef", value):
                for iteration in self._extra_data.iterations or []:
                    if iteration.widget_ref == widget_ref:
                        iteration.values.append(value)
                        self._save()
                        break
                else:
                    self._init_iteration(widget_ref)
                    self.record_iteration(widget_ref, value)

            def get_archived_operated_approver_ids(self) -> set[int]:
                """
                获取已经归档的、进行过审批操作的审批人。
                :return:
                """
                archived_operated_approvers = self._extra_data.archived_operated_approver_ids or []
                return set(archived_operated_approvers)

            def extend_archived_operated_approver_ids(self, operated_approver_ids: list[int]) -> None:
                """
                追加审批人到归档信息中。
                :param operated_approver_ids:
                :return:
                """
                archived_operated_approver_ids = self.get_archived_operated_approver_ids()
                self._extra_data.archived_operated_approver_ids = list(
                    archived_operated_approver_ids | set(operated_approver_ids)
                )
                self._save()

            def clean_archived_operated_approvers(self) -> None:
                """
                清空已归档的、进行过审批操作的审批人。
                :return:
                """
                self._extra_data.archived_operated_approver_ids = []
                self._save()

            def get_jt_intercept_results(self) -> list[JTWaybillIntercept]:
                """
                获取极兔拦截下发回调的结果存储列表。
                :return:
                """
                return self._extra_data.jt_intercept_callback_results or []

            def append_jt_intercept_result(self, result: JTWaybillIntercept) -> None:
                """
                追加极兔拦截下发回调的结果。
                :return:
                """
                intercept_callback_results = self.get_jt_intercept_results()
                intercept_callback_results.append(result)
                self._extra_data.jt_intercept_callback_results = intercept_callback_results
                self._save()

            def _save(self):
                self._job._extra_data = self._extra_data.dict(exclude_none=True)

            def readable(self):
                return f"ExtraDataWrapper(job={self._job}, extra_data={self._extra_data})"

            __str__ = readable
            __repr__ = readable


class JobSystemAttributes:
    system_key_pattern = re.compile(r"^system_job_(?P<step_uuid>[a-zA-Z0-9]+)_\w+$")

    @classmethod
    def replace_temp_symbol_name(cls, symbol_name: str, step_uuid: str):
        def replacer(match: re.Match):
            if match.group("step_uuid") == "temp":
                return symbol_name.replace("system_job_temp", f"system_job_{step_uuid}")
            return symbol_name

        return cls.system_key_pattern.sub(replacer, symbol_name)


class BusinessOrderSystemAttributes:
    """供系统字段读的属性"""

    def __init__(self, bo: "BusinessOrder"):
        from sqlalchemy.orm import load_only

        from robot_processor.ext import db
        from robot_processor.form.models import Form

        self.bo = bo
        self.form = db.session.get_one(Form, bo.form_id, options=[load_only(Form.id, Form.name, Form.expect_duration)])

    @property
    def updated_at(self):
        return self.bo.updated_at

    @property
    def update_user(self):
        return self.updator

    @property
    def feisuo_creator(self):
        return self.bo.feisuo_creator

    @property
    def feisuo_updator(self):
        return self.bo.feisuo_updator

    @property
    def status(self):
        # 为什么不使用 __getattr__ 代理 bo 的所有 attribute？
        # 因为希望显式列出到底有哪些系统字段
        return self.bo.status

    @property
    def created_at(self):
        return self.bo.created_at

    @property
    def end_at(self):
        return self.bo.end_at

    @property
    def creator_group_string(self):
        return ",".join([i.get("name", "") for i in self.bo.creator_group])

    @property
    def feisuo_creator_group_string(self):
        return ",".join([i.get("name", "") for i in self.bo.feisuo_creator_group])

    @property
    def updator_group_string(self):
        return ",".join([i.get("name", "") for i in self.bo.updator_group])

    @property
    def feisuo_updator_group_string(self):
        return ",".join([i.get("name", "") for i in self.bo.feisuo_updator_group])

    @property
    def current_job_assignee_group_platform_string(self):
        current_job = self.bo.current_job
        if not current_job:
            return ""
        return ",".join([i.get("name", "") for i in current_job.assignee_group])

    @property
    def current_job_assignee_group_feisuo_string(self):
        current_job = self.bo.current_job
        if not current_job:
            return ""
        return ",".join([i.get("name", "") for i in current_job.feisuo_assignee_group])

    @property
    def current_job_assignee_feisuo(self):
        current_job = self.bo.current_job
        if not current_job:
            return ""
        return current_job.feisuo_assignee  # type: ignore[union-attr]

    @property
    def current_job_assignee_platform(self):
        current_job = self.bo.current_job
        if not current_job:
            return ""
        return current_job.assignee

    @property
    def shop_nick(self):
        # 要求返回title
        shop = self.bo.shop
        if shop:
            return shop.title or ""
        return ""

    @property
    def updator(self):
        """
        最近更新人。
        :return:
        """
        user = self.bo.aid
        job_ids = self.bo.pre_job_ids(self.bo.current_job.id)  # type: ignore[union-attr]
        if job_ids:
            jobs = {j.id: j for j in Job.query.filter(Job.id.in_(job_ids)).all()}
            for job_id in job_ids:
                job_ = jobs.get(job_id)
                if job_ and not job_.raw_step_v2.get("step_type") == StepType.human:
                    user = job_.assignee
                    break
        # 去除店铺名 davebella旗舰店:小凤 -> 小凤
        if user:
            if shop := self.bo.shop:
                user = user.replace(f"{shop.nick}:", "", 1)
        return user or ""

    @property
    def current_step_name(self):
        current_job = self.bo.current_job
        if current_job:
            return current_job.step.name  # type: ignore[union-attr]
        return ""

    @property
    def deadline(self) -> int:
        """根据模板form生成的截止时间"""
        return (self.form.expect_duration or 0) + self.bo.created_at  # type: ignore[union-attr]

    @property
    def receipt_url(self):
        return self.bo.data.get("receipt_url") or ""

    @property
    def current_job_exception_reason(self):
        if self.bo.status != BusinessOrderStatus.IN_EXCEPTION:
            return ""
        from robot_processor.business_order.exception_rule.models import ExceptionBusinessOrder

        eo = ExceptionBusinessOrder.query.filter_by(business_order_id=str(self.bo.id)).first()
        if not eo:
            return ""
        return eo.reason

    @property
    def creator_platform(self):
        if not self.bo.id:
            return ""
        return self.bo.creator_nick()

    @property
    def shop_platform_chinese(self):
        # 店铺平台 中文
        shop = self.bo.shop
        if shop:
            return PLATFORM_MAP.get(shop.platform, "未知平台")
        return ""

    @property
    def business_order_id(self):
        return str(self.bo.id)

    @property
    def form_name(self):
        return self.form.name

    @property
    def exception_reason(self):
        return self.bo.current_job.exc_info if self.bo.current_job else ""

    @property
    def id(self):
        return self.bo.id


class BusinessOrderFlag(enum.StrEnum):
    EMPTY = ""  # 表示旗帜无颜色，工单未进行插旗
    RED = "red"
    YELLOW = "yellow"
    GREEN = "green"
    BLUE = "blue"
    PURPLE = "purple"


class BusinessOrder(DbBaseModel, BasicMixin):
    __ignore_columns__ = ["creator_type", "is_timeout", "message_ts", "current_job_id"]

    sid: Mapped[str | None] = mapped_column(sa.String(32), index=True, comment="店铺 id")
    aid: Mapped[str | None] = mapped_column(sa.String(128), comment="客服 id")
    uid: Mapped[str | None] = mapped_column(sa.String(128), index=True, comment="买家 id")
    buyer_open_uid: Mapped[str | None] = mapped_column(sa.String(128), comment="买家淘宝open id")
    mid: Mapped[str | None] = mapped_column(sa.String(128), index=True, comment="飞梭帐号")
    form_id: Mapped[int | None] = mapped_column(sa.Integer, comment="关联的后台工单 id")
    form: Mapped[Form | None] = relationship(lambda: Form, primaryjoin="foreign(BusinessOrder.form_id)==Form.id")
    shop: Mapped[Shop | None] = relationship("Shop", viewonly=True, primaryjoin="foreign(BusinessOrder.sid)==Shop.sid")
    # 步骤所需数据 {sku: {"颜色": "红色", "尺码": "170"}, "oid": oid, "tid": tid}
    data: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="表单数据")

    jobs: DynamicMapped[Job] = relationship(Job, lazy="dynamic", back_populates="business_order")

    # job_history 用来保存明确的执行路径，遇到分支时停止，假设有如下流程图
    #          / 4 - 5
    # 1 - 2 - 3                 \ 9
    #          \  6 -7 -8  /
    # 初始时job_history=[1,2,3]
    # 执行完分支3后假设走了下面的路径，则job_history=[1,2,3,6,7,8,9]
    # 反之如果走了上面的路径，则则job_history=[1,2,3,4,5,9]
    # 即在下一个分支前截断, 更详细的信息参见test case
    job_history: Mapped[list] = mapped_column(sa.JSON, default=list)

    # 跟job_history不同，job_road记录所有可能的路径，并在分支后面填-1
    # 目的是方便根据条件动态生成job_history且无需再回访step结构（可能已经更新了）
    # 上面流程图对应的job_road=[[1,2,3,-1,4,5,9], [1,2,3,-1,6,7,8,9]]
    job_road: Mapped[list] = mapped_column(sa.JSON, default=list)

    # job transition 会和 job_history 配合使用
    # job transition 专注于记录工单流转的方向
    job_transition: Mapped[list["JobTransition"]] = relationship(lambda: JobTransition, back_populates="business_order")

    status: Mapped[BusinessOrderStatus] = mapped_column(
        sa.Enum(BusinessOrderStatus),
        default=BusinessOrderStatus.RUNNING,
        comment="工单的状态",
    )

    from_type: Mapped[FromType] = mapped_column(sa.Enum(FromType), default=FromType.ASSISTANT)

    # 虚拟字段oid -> data.oid
    v_oid: Mapped[str | None] = mapped_column(sa.String(32), Computed("json_unquote(data -> '$.oid')"), index=True)
    # 虚拟字段tid -> data.tid
    v_tid: Mapped[str | None] = mapped_column(sa.String(32), Computed("json_unquote(data -> '$.tid')"), index=True)
    # {"operate_action":"REJECT", "operate_reason": "退回原因"}
    # (退回/关闭/开启及评论)
    extra_data: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="表单扩展数据")
    # 当前任务是否超时
    is_timeout: Mapped[int] = mapped_column(sa.Integer, index=True, default=0)
    # 当前任务到期时间戳
    message_ts: Mapped[int] = mapped_column(sa.Integer, default=0)
    # 当前正在执行的任务id
    current_job_id: Mapped[int | None] = mapped_column(sa.Integer)
    trades: Mapped[list["BusinessOrderTradeMap"]] = relationship(
        "BusinessOrderTradeMap",
        primaryjoin="foreign(BusinessOrderTradeMap.business_order_id)==BusinessOrder.id",
        backref="business_order",
        cascade="delete, delete-orphan, save-update",
    )
    archived: Mapped[bool | None] = mapped_column(
        sa.Boolean,
        comment="标记该条数据是否需要归档，否则bi监听binlog的delete event之后会直接删除",
    )
    form_version_id: Mapped[int | None] = mapped_column(sa.Integer, comment="工单使用的模板版本号", index=True)
    form_version: Mapped[FormVersion] = relationship(
        "FormVersion",
        primaryjoin="foreign(BusinessOrder.form_version_id)==FormVersion.id",
        backref="business_orders",
    )

    creator_user_id: Mapped[int | None] = mapped_column(sa.Integer, comment="客服 user id")
    creator_group: Mapped[list] = mapped_column(sa.JSON, default=list, comment="创建人归属组")
    creator_type: Mapped[Creator] = mapped_column(sa.Enum(Creator), default=Creator.ASSISTANT, comment="创建人类别")
    feisuo_creator: Mapped[str | None] = mapped_column(sa.String(128), comment="创建人飞梭账号昵称")
    feisuo_creator_user_id: Mapped[int | None] = mapped_column(sa.Integer, comment="创建人飞梭账号user id")
    feisuo_creator_group: Mapped[list] = mapped_column(sa.JSON, default=list, comment="创建人飞梭账号归属组")

    update_user: Mapped[str | None] = mapped_column(sa.String(128), comment="最新更新人")
    updator_id: Mapped[int | None] = mapped_column(sa.Integer, comment="最新更新人user id")
    updator_group: Mapped[list] = mapped_column(sa.JSON, default=list, comment="最新更新人归属组")
    updator_type: Mapped[Creator] = mapped_column(sa.Enum(Creator), default=Creator.ASSISTANT, comment="最新更新人类别")
    feisuo_updator: Mapped[str | None] = mapped_column(sa.String(128), comment="最新更新人飞梭账号昵称")
    feisuo_updator_id: Mapped[int | None] = mapped_column(sa.Integer, comment="最新更新人飞梭账号user id")
    feisuo_updator_group: Mapped[list] = mapped_column(sa.JSON, default=list, comment="最新更新人飞梭账号归属组")

    # 父类 BasicMixin 提供的 updated_at 属性进行覆写。
    updated_at: Mapped[int] = mapped_column(
        sa.Integer,
        default=time.time,
        nullable=False,
        comment="最近更新时间，当有操作步骤执行时，会触发更新该字段",
    )

    memo: Mapped[Optional["BusinessOrderMemo"]] = relationship(
        lambda: BusinessOrderMemo,
        uselist=False,
        back_populates="business_order",
        cascade="all, delete-orphan",
    )

    end_at: Mapped[int | None] = mapped_column(sa.Integer, comment="工单结束时间")
    current_job: Mapped[Job | None] = relationship(
        Job,
        viewonly=True,
        uselist=False,
        primaryjoin="and_(BusinessOrder.id==foreign(Job.business_order_id),BusinessOrder.current_job_id==Job.id)",
    )

    # 工单插旗，支持索引
    flag: Mapped[str] = mapped_column(
        sa.String(32),
        Computed("coalesce(json_unquote(data -> '$.flag'), '')"),
        index=True,
    )

    @property
    def form_wrapper(self):
        return Form.Utils.form_wrapper(self.form, self.shop)  # type: ignore[arg-type]

    @property
    def data_wrapper(self):
        """
        bo.data wrapper
        支持读取table组件的值, 保持与dict一致的读取方式
        ref={
            "key":"uuid",
            "field":"uuid"
        }
        data_reader[key] or data_reader.get(key)
        """
        return BusinessOrder.Wrapper.Data(self)

    @property
    def extra_data_wrapper(self):
        return BusinessOrder.Wrapper.ExtraData(self)

    @cached_property
    def widget_form_wrapper(self):
        return BusinessOrder.Wrapper.WidgetForm(self)

    @property
    def job_transition_wrapper(self):
        return BusinessOrder.Wrapper.JobTransition(self)

    @cached_property
    def org_id(self):
        shop = self.shop
        return shop.org_id if shop else None

    @cached_property
    def shop_platform(self):
        shop = self.shop
        return shop.platform if shop else None

    def set_creator_info(self, new_creator: Optional[AccountDetailV2]):
        """创建人有些特殊，仅维护 user_id 和 user_type"""
        if new_creator is None:
            self.creator_user_id = self.feisuo_creator_user_id = self.feisuo_creator = None
            self.creator_type = Creator.ASSISTANT
            self.creator_group = self.feisuo_creator_group = []
        else:
            self.creator_user_id = new_creator.user_id
            self.creator_type = Creator(unwrap_optional(new_creator.user_type))
            self.creator_group = new_creator.groups
            self.mid = new_creator.feisuo_user_phone
            self.feisuo_creator_user_id = new_creator.feisuo_user_id
            self.feisuo_creator = new_creator.feisuo_user_nick
            self.feisuo_creator_group = new_creator.feisuo_groups

    def get_creator_info(self):
        return AccountDetailV2(user_type=self.creator_type, user_id=self.creator_user_id, user_nick=self.creator_nick())

    def update_extra_data(self, **kw):
        self.extra_data.update(kw)
        flag_modified(self, "extra_data")

    def get_updator_info(self):
        if not self.update_user:
            return None
        return AccountDetailV2(
            user_type=self.updator_type or Creator.ASSISTANT.value,
            user_id=self.updator_id,
            user_nick=self.update_user,
        )

    def set_updator_by_nick(self, nick):
        if not nick:
            return
        self.set_updator(kiosk_client.get_user_by_nick(self.sid, self.shop.platform, nick))  # type: ignore[arg-type,union-attr]  # noqa: E501

    def set_updator(self, new_updator: Optional[AccountDetailV2]):
        """设置工单最新修改人信息"""
        if new_updator is None:
            # 当 update_user 是非飞梭/平台客服账号时，会走进这个分支
            # 所以仅清空 updator_id 等信息，update_user 还是需要保留的
            logger.info("清空工单 updator 信息")
            self.updator_id = None
            self.updator_group = []
            self.updator_type = Creator.ASSISTANT  # default
            self.feisuo_updator_id = None
            self.feisuo_updator = None
            self.feisuo_updator_group = []
        else:
            logger.info(f"设置工单 updator 信息为 {new_updator}")
            self.update_user = new_updator.user_nick
            self.updator_id = new_updator.user_id
            self.updator_type = new_updator.user_type  # type: ignore[assignment]
            self.updator_group = new_updator.groups
            self.feisuo_updator = new_updator.feisuo_user_nick
            self.feisuo_updator_id = new_updator.feisuo_user_id
            self.feisuo_updator_group = new_updator.feisuo_groups

    @property
    def deadline_info(self):
        return {
            "message_ts": self.message_ts,
            "is_timeout": self.is_timeout,
            "current_job_id": self.current_job_id,
        }

    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)

    def creator_nick(self) -> str:
        import logging

        creator_user_id = self.creator_user_id
        creator_type = self.creator_type
        creator_name = ""
        if creator_user_id and (creator_type == Creator.LEYAN or creator_type == Creator.ASSISTANT):
            try:
                user_info = kiosk_client.get_user_by_id(creator_type, creator_user_id)
                creator_name = user_info and user_info.user_nick or ""
            except Exception as e:
                logging.error(
                    f"选择的指派客服信息获取失败. [creator_type={creator_type},"
                    f" creator_user_id={creator_user_id}], {e}"
                )
        elif creator_type == Creator.LDAP:
            creator_name = "乐言员工"
        elif creator_type == Creator.RPA:
            creator_name = "机器人"
        elif creator_type == Creator.USER:
            creator_name = self.uid  # type: ignore[assignment]
        return creator_name

    def __str__(self):
        pair = f"{self.aid}@{self.uid}"
        return f"{self.id}: {pair} -> {self.form_id}"

    @cached_property
    def name(self):
        return self.form.name if self.form else ""

    def rollback_jobs_execute_records(self, next_execute_job: Job) -> None:
        """
        该方法用于重置工单的当前任务，到传入的 next_execute_job 这一条链路上的任务的 status（设为 INIT）。

        举个例子，当前工单的 job_history 为 [1, 2, 3, 4, 5]，对应的执行链路便是：
        1 -> 2 -> 3 -> 4 -> 5

        当前任务 为 5，如果 5 跳转到 2，则 next_execute_job 为 2，需要重置 2、3、4、5 的执行情况。

        如果没有将 status 重置为 init 的话，则任务不会重新执行。
        """
        reversed_job_history = self.get_reversed_job_history()
        if self.current_job_id not in reversed_job_history:
            return None

        all_jobs = Job.query.filter(Job.business_order_id == self.id).all()
        jobs_mapping = {job.id: job for job in all_jobs}
        if next_execute_job.id not in jobs_mapping:
            return None

        if next_execute_job.id not in reversed_job_history:
            next_execute_job.set_status(JobStatus.INIT)
            return None

        for rollback_job_id in reversed_job_history:
            rollback_job = jobs_mapping.get(rollback_job_id)
            if rollback_job_id == next_execute_job.id and rollback_job is not None:
                rollback_job.set_status(JobStatus.INIT)
                break
            elif rollback_job_id == self.job_history[0]:
                # 如果该任务为起始步骤任务，则不处理。因为起始步骤对于操作人难以触及，减少一些错误。
                pass
            else:
                if rollback_job is not None:
                    rollback_job.set_status(JobStatus.INIT)
        db.session.flush()

    def set_current_execute_job(self, job: Job) -> None:
        """
        设置工单的当前执行任务。
        """
        self.current_job_id = job.id
        reversed_job_history = self.get_reversed_job_history()
        if len(reversed_job_history) == 0 or job.id != reversed_job_history[0]:
            self.job_history.append(job.id)
            flag_modified(self, "job_history")
            db.session.flush()

    def get_next_step_id(
        self, current_job_id: int | None = None, next_step_uuid: str | None = None
    ) -> int | Literal["BRANCH", "END"] | None:
        cur_step: Step | None = None
        if current_job_id:
            current_job: Job | None = Job.query.get(current_job_id)
            if current_job is None:
                return None
            cur_step = current_job.step

        # 无版本时先创建版本（很久未发布的工单模板）?
        # fixme: 没看到有创建版本的操作。
        if not self.form_version_id:
            if not cur_step:
                if self.form is None:
                    return None
                elif (startup_step := self.form.get_startup_step()) is None:
                    return None
                else:
                    return startup_step.id
            # 老数据无版本时的处理
            # 有传递 next_step_uuid 则优先使用，
            if next_step_uuid is not None:
                next_job = self.jobs.filter_by(step_uuid=next_step_uuid).first()
                return next_job.step_id if next_job else None
            else:
                if cur_step.next_step_ids:
                    next_job = self.jobs.filter_by(step_uuid=cur_step.next_step_ids[0]).first()
                    return next_job.step_id if next_job else None
                else:
                    return None

        else:
            version = self.form_version
            if next_step_uuid is None:
                return version.get_next_step(cur_step.step_uuid if cur_step else None)
            else:
                return version.get_current_step(next_step_uuid)

    def init_bo_jobs(self) -> Job | None:
        """为新工单初始化job.

        初始化逻辑：根据工单模板中的步骤列表，依次创建 job，直到所有 step 都遍历完，或者创建了第一个非 begin/human 步骤的 job。

        - 如果工单模板中只含有起始步骤和人工步骤，则len(bo.jobs) == 2; 如果工单模板中除了起始步骤和人工步骤，还有其他步骤, 则 len(bo.jobs) >= 3
        - bo.jobs[0].step_type == StepType.begin 第一个 job 的类型是起始步骤
        - bo.jobs[1].step_type == StepType.human 第二个 job 的类型是人工步骤，用于填充该工单的表单数据
        """
        first_job: Job | None = None
        step_id = self.get_next_step_id(None)
        step = Step.query.get(step_id)
        if step is None:
            logger.error("未找到起始步骤: {}", step_id)
            return None
        while True:
            job = Job.query.filter_by(step_id=step_id, business_order_id=self.id).one_or_none()
            if job is None:
                job = Job.create_by_step(step, business_order_id=self.id)
                db.session.add(job)
                db.session.flush()
            if first_job is None:
                first_job = job
            if not step.is_human() and not step.is_begin():
                break
            step_id = self.get_next_step_id(job.id)
            if step_id in [None, "BRANCH", "END"]:
                break
            step = Step.query.get(step_id)
            if step is None:
                logger.error("未找到步骤: {}", step_id)
                break
        assert first_job is not None
        self.set_current_execute_job(first_job)
        self.set_updator_by_nick(self.aid or self.uid)
        return first_job

    def get_next_job_to_execute(self, cur_job: Job, next_step_uuid: str | None) -> Job | None:
        """获取 cur_job 的下一个 job"""

        # 优先通过next_step 查找已存在的未完成的job作为下一步
        if next_step_uuid:
            existed_next_job = self.get_job_by_step_uuid(next_step_uuid)
            if existed_next_job is not None:
                return existed_next_job

        next_step_id = self.get_next_step_id(cur_job.id, next_step_uuid)
        if next_step_id in [None, "BRANCH", "END"]:
            return None
        next_step: Step | None = Step.query.get(next_step_id)
        if next_step is None:
            logger.exception(f"未找到步骤: {next_step_id}")
            return None

        next_job: Job | None = Job.query.filter_by(step_id=next_step_id, business_order_id=self.id).one_or_none()
        if next_job is None:
            next_job = Job.create_by_step(next_step, business_order_id=self.id)
            db.session.add(next_job)
            db.session.flush()
            logger.info("创建下个job: {}", next_job.id)
            self.jobs.append(next_job)
        else:
            logger.warning("找到已存在的下个job: {}", next_job.id)
        assert next_job is not None
        if next_step.is_human():
            # 分派规则里有一个上一步指派需要创建出一个步骤
            next_next_step_id = self.get_next_step_id(next_job.id)
            if next_next_step_id not in [None, "BRANCH", "END"]:
                next_next_step = Step.query.get(next_next_step_id)
                if next_next_step is not None:
                    next_next_job = Job.query.filter_by(
                        step_id=next_next_step_id, business_order_id=self.id
                    ).one_or_none()
                    if next_next_job is None:
                        next_next_job = Job.create_by_step(next_next_step, business_order_id=self.id)
                        db.session.add(next_next_job)
                        db.session.flush()
                        logger.info("创建新的下下个 next_job: {}", next_next_job.id)
                        self.jobs.append(next_next_job)
                    else:
                        logger.warning("找到已存在的下下个job: {}", next_next_job.id)
                else:
                    logger.exception("未找到步骤: {}", next_next_step_id)

        # 更新工单的最近更新人信息。
        if cur_job.step_type == StepType.auto:
            update_user: str | None = "RPA应用"
        else:
            update_user = self.feisuo_updator
        self.set_updator_by_nick(update_user)
        return next_job

    def to_dict(self, brief=False, from_plugin=False):
        form = self.form.wraps(self.shop)  # type: ignore[union-attr,arg-type]
        info = super().to_dict(nested=False)
        info["name"] = form.name
        info["description"] = form.description
        info["form_category"] = form.category
        info["form_deleted"] = bool(form.deleted)
        info["updated_at"] = ts2date(self.updated_at)
        info["created_at"] = ts2date(self.created_at)
        info["shop"] = self.shop.brief()  # type: ignore[union-attr]
        if brief:
            return info
        jobs = []
        for job in self.all_jobs():
            job_info = job.to_dict(with_schema=True, from_plugin=from_plugin)
            can_recall = False
            """判断是否可以撤回，待重构"""
            if self.update_user == job.assignee and job.status == JobStatus.SUCCEED:
                raw_step_v2 = self.current_job.raw_step_v2  # type: ignore[union-attr]
                pre_step_ids = raw_step_v2.get("prev_step_ids") if self.current_job else []
                if job.step_uuid in pre_step_ids:
                    """确定job是current_job的前一个"""
                    if self.current_job.status == JobStatus.PENDING:  # type: ignore[union-attr]
                        if raw_step_v2.get("can_reject"):
                            can_recall = True
            job_info.update({"can_recall": can_recall})
            jobs.append(job_info)

        if self.current_job:
            job = self.current_job.to_dict(with_schema=True, from_plugin=from_plugin)
            if _job_deadlined_at := (self.current_job.deadline_info or {}).get("deadline_ts", None):
                _job_deadlined_at = ts2date(_job_deadlined_at)
            job.update(dict(is_timeout=self.is_timeout, deadlined_at=_job_deadlined_at))
        else:
            job = {}
        info.update({"current_jobs": [job]})
        info.update({"job_history": jobs})
        info["deadline_info"] = self.deadline_info

        info.update(
            dict(
                update_reason=(self.extra_data or {}).get("operate_reason", None),
                update_action=(self.extra_data or {}).get("operate_action", "UNPROCESSED"),
            )
        )
        return info

    def is_completed(self):
        return self.status == BusinessOrderStatus.SUCCEED

    def is_to_be_collected(self):
        return self.status == BusinessOrderStatus.TO_BE_COLLECTED

    def all_jobs(self):
        """所有已执行了的job(不包含未确定分支中的job)"""
        if not self.job_history:
            return []
        all_executed_job_ids = list(set(self.job_history))
        return (
            self.jobs.filter(Job.id.in_(all_executed_job_ids)).order_by(sa.func.field(Job.id, *self.job_history)).all()
        )

    def get_job_by_step_uuid(self, step_uuid) -> Optional[Job]:
        """通过step_uuid查询job"""
        if step_uuid:
            return self.jobs.filter(Job.step_uuid == step_uuid).one_or_none()
        return None

    def is_close(self):
        return self.status == BusinessOrderStatus.CLOSE

    def is_running(self) -> bool:
        return self.status == BusinessOrderStatus.RUNNING

    def is_in_exception(self):
        return self.status == BusinessOrderStatus.IN_EXCEPTION

    def pre_job_ids(self, specific_job_id: int) -> List[int]:
        """
        返回从指定 job 开始截断的逆序排列的 job_history。
        :param specific_job_id:
        :return:
        """
        reversed_job_history = self.get_reversed_job_history()
        if specific_job_id in reversed_job_history:
            idx = reversed_job_history.index(specific_job_id)
            return reversed_job_history[idx + 1 :]
        return []

    def brief(self):
        return {
            "sid": self.sid,
            "id": self.id,
            "aid": self.aid,
            "uid": self.uid,
            "form_id": self.form_id,
            "form_name": self.form.name if self.form else "",
            "status": self.status.value,
            "history": self.job_history,
            "jobs": sorted(
                [job.brief() for job in self.jobs if job.id in self.job_history],
                key=lambda q: list(dict.fromkeys(self.job_history)).index(q["id"]),
            ),
            "flag": self.flag,
        }

    def get_kafka_schema(
        self,
        operator: str,
        operate_nick: str,
        org_id: str = "",
        platform: str = "",
        operate_ts: Optional[int] = None,
    ):
        return {
            "sid": str(self.sid),
            "org_id": org_id,
            "platform": platform,
            "model": "business_orders",
            "object_id": str(self.id),
            "label": self.name,
            "operator": operator,
            "operate_ts": (operate_ts or int(time.time())),
            "user": operate_nick,
            "raw_json": json.dumps(self.data, ensure_ascii=True),
        }

    def delete(self):
        # 2.标记删除
        self.deleted = True
        if current_job := self.current_job:
            current_job.process_mark = JobProcessMark.DELETE
        self.modify_transfer("delete", "系统自动操作", "删除工单时删除打款单")
        return True

    def is_end(self):
        # 工单是否是完结态
        return self.deleted is True or self.is_completed() or self.is_close()

    def create_business_order_trade_map(self, tid_or_oids):
        db.session.add(self)
        db.session.flush()
        self.trades = [BusinessOrderTradeMap(business_order_id=self.id, tid_or_oid=tid) for tid in tid_or_oids]

    def complete_business_order_and_record_log(self):
        """
        将工单标记已完结，并记录工单的完结日志。

        :return:
        """
        from robot_processor.enums import BusinessOrderStatus

        operate_ts = int(time.time())

        with db.session.no_autoflush:
            # 工单完结，则其状态需要变更为已完成。
            self.set_status(BusinessOrderStatus.SUCCEED)

        # 记录工单完结的日志。
        try:
            action_client.create_action_log_by_kafka(
                self.get_kafka_schema(
                    "complete",
                    self.name,
                    operate_ts=operate_ts,
                )
            )
        except BaseException as e:
            logger.warning(f"succeed bo action log error: {str(e)}")

    def set_status(self, status: BusinessOrderStatus):
        if self.status == status:
            return
        self.status = status
        now = int(time.time())
        self.updated_at = now
        if self.status == BusinessOrderStatus.SUCCEED:
            self.end_at = now

    def record_job_action(
        self,
        action: JobProcessMark,
        operate_reason: str,
        operate_ts: int,
    ):
        """
        该函数实现了以下两类功能。
        1. 记录操作日志
        2. 更新最近更新时间
        """
        extra_data = (self.extra_data or {}).copy()
        extra_data.update({"operate_action": action.name, "operate_reason": operate_reason})
        self.extra_data = extra_data

        self.set_updated_at(operate_ts=operate_ts)

    def set_updated_at(self, operate_ts: int):
        """
        对 business_order 实例的 updated_at 字段进行处理。

        :param business_order:  需要更新的工单实例。
        :param operate_ts:      操作时间，如果有传递数据，则使用该操作时间作为最近更新时间，否则则使用当前时间。
        """
        if not self.is_completed():
            self.updated_at = operate_ts

    def get_reversed_job_history(self) -> list[int]:
        """
        获取反转后的 job_history。
        便于推算最近的执行记录。
        :return:
        """
        job_history = self.job_history
        reversed_job_history = job_history[::-1]
        return reversed_job_history

    def first_human_job(self):
        jobs = self.all_jobs()
        if len(jobs) > 1:
            return jobs[1]
        else:
            logger.error("此时不应该来取第一个人工job")

    @no_auto_flush()
    def get_form_version(self):
        from robot_processor.form.models import FormVersion

        if self.form_version:
            return self.form_version

        # 没有 form version
        else:
            blank_version = FormVersion.new(self.form)  # type: ignore[arg-type]
            # 实例支持版本前 所有的步骤都会被创建出来
            blank_version.step_id = list({job.step_id for job in self.jobs})
            blank_version.job_road = {}
            blank_version.version_no = ""
            blank_version.updator = ""
            blank_version.version_descriptor = ""
            blank_version.created_at = self.created_at

            return blank_version

    def get_non_ref_widgets(self) -> Dict[str, WidgetInfoDict]:
        """获取该工单内所有步骤上的非引用类型 widget.

        :see: robot_processor.form.getter.get_non_ref_widgets_by_form_id, 通过 form_id 获取相同业务意义的数据
        """
        widgets = {}
        for job in self.jobs:
            for widget in job.raw_ui_schema:
                if not widget.get("before", False):
                    widgets[widget["key"]] = WidgetInfoDict(**widget)  # type: ignore[typeddict-item]
        return widgets

    def get_risk_control_dimensions(self):
        dimensions: list[dict[str, str]] = []
        for job in self.jobs:
            dimensions.extend(job.get_value_unique_dimensions())

        return dimensions

    def do_risk_control(self, action="REPORT", report_channel: Literal["grpc", "kafka"] = "grpc"):
        """往 封控服务(dgt-risk-control) 上报当前工单的最新信息."""
        from robot_processor.client import risk_control_client
        from robot_processor.ext import risk_control_producer

        dimensions = self.get_risk_control_dimensions() if action == "REPORT" else []
        shop = unwrap_optional(self.shop)
        try:
            if self.deleted:
                action = "DELETE"
            if report_channel == "grpc":
                if action == "REPORT":
                    if not dimensions:
                        logger.warning("数据为空，放弃 risk report")
                        return
                    risk_control_client.report_bo_risk(
                        org_id=shop.org_id,
                        sid=self.sid,
                        model="business_order",
                        scope=str(self.form_id),
                        bo_id=self.id,
                        dimensions=dimensions,
                    )
                elif action == "DELETE":
                    risk_control_client.cancel_risk(
                        org_id=shop.org_id,
                        sid=self.sid,
                        model="business_order",
                        scope=str(self.form_id),
                        bo_id=str(self.id),
                    )
                else:
                    logger.warning("未知的 risk action {}", action)
            else:
                record = {
                    "action": action,
                    "org_id": shop.org_id,
                    "sid": self.sid,
                    "model": "business_order",
                    "scope": str(self.form_id),
                    "object_id": str(self.id),
                    "dimensions": dimensions,
                }
                risk_control_producer(record)
        except Exception as e:
            logger.warning(f"action log error: {e}")

    def modify_transfer(
        self,
        action: Literal["recall", "delete", "regain", "recover", "close"],
        operator: str,
        operate_reason: str,
    ):
        from robot_processor.client import robot_transfer

        robot_transfer.put_transfer(
            self.sid,
            self.id,
            action=action,
            operate_reason=operate_reason,
            org_id=self.shop.org_id,  # type: ignore[union-attr]
            platform=self.shop.platform,  # type: ignore[union-attr]
            operator=operator,
        )

    @classmethod
    def creator_condition(cls, platform_users: set[int], leyan_users: set[int]):
        if platform_users and leyan_users:
            return or_(
                and_(
                    BusinessOrder.creator_type == Creator.ASSISTANT,
                    BusinessOrder.creator_user_id.in_(platform_users),
                ),
                and_(
                    BusinessOrder.creator_type == Creator.LEYAN,
                    BusinessOrder.creator_user_id.in_(leyan_users),
                ),
            )
        if platform_users:
            return and_(
                BusinessOrder.creator_type == Creator.ASSISTANT,
                BusinessOrder.creator_user_id.in_(platform_users),
            )
        if leyan_users:
            return and_(
                BusinessOrder.creator_type == Creator.LEYAN,
                BusinessOrder.creator_user_id.in_(leyan_users),
            )
        return true()

    @in_transaction()
    def clear_iterator(self, specified_scope=None):
        """
        清空指定作用域。如果没有传入指定作用域，则清空所有作用域。
        :param specified_scope:
        :return:
        """
        form_version: FormVersion = self.form_version
        try:
            scopes = form_version.get_step_scope(self.current_job.step_uuid)  # type: ignore[union-attr,arg-type]
        except StopIteration:
            return
        for scope in scopes:
            if specified_scope is not None and scope != specified_scope:
                continue
            namespace = self.data.get(scope.step_uuid) or {}
            namespace.pop("iterator", None)
            namespace.pop("item", None)
            self.data[scope.step_uuid] = namespace
        flag_modified(self, "data")

    class Schema:
        class CreatorGroup(BaseModel):
            uuid: Optional[str] = Field(default=None)
            type: Optional[str] = Field(default=None)
            name: Optional[str] = Field(default=None)

        class Table(dict):
            """支持(且仅支持) WidgetRef 查询的工单数据信息"""

            def __getitem__(self, widget_ref: WidgetRef | str):
                Table = self.__class__

                if not isinstance(widget_ref, WidgetRef):
                    logger.debug(
                        "仅支持 WidgetRef 查询，" f"传入的 key 的类型为 {type(widget_ref)}，值为 {widget_ref}",
                    )
                    if widget_ref not in self:
                        raise KeyError(f"工单实例没有 {widget_ref} 的数据")
                    return super().__getitem__(widget_ref)

                if widget_ref.key not in self:
                    raise KeyError(f"工单实例没有 {widget_ref} 的数据")

                # 根据 widget_ref 对 raw_value 进行加工后返回
                raw_value = super().__getitem__(widget_ref.key)

                def single_widget():
                    """简单组件，不做处理直接返回"""
                    return raw_value

                def table_widget():
                    """table 组件，且只有一行数据，直接返回结果"""
                    row_length = len(raw_value)
                    # table 组件已设置，但是没有数据
                    if row_length == 0:
                        raise KeyError(f"工单实例有 {widget_ref} 的数据, 但是 table 组件的值为空或 field 的数据为空")
                    # 仅提示
                    if row_length > 1:
                        msg = f"table 组件配置为单行数据，但是返回了多行, {widget_ref}, {raw_value}"
                        logger.warning(msg)

                    row = first(raw_value)
                    # 指定了需要返回 table 某一列的数据
                    if widget_ref.field:
                        # 因为 widget_ref.field 也是 WidgetRef 类型
                        # 所以要转换成 Table 类型，提供 WidgetRef 的 query 能力
                        return Table(row)[widget_ref.field]
                    else:
                        return row

                def multi_row_table_widget():
                    """table 组件，且支持多行数据"""
                    # 如果有 field，则返回所有行的 field 字段
                    if widget_ref.field:
                        return [Table(row).get(widget_ref.field, MISSING) for row in raw_value]
                    # 如果没有 field，则返回所有行
                    else:
                        return raw_value

                if not widget_ref.is_table:
                    value = single_widget()
                elif widget_ref.multi_row:
                    value = multi_row_table_widget()
                else:
                    value = table_widget()

                return value

            def get(self, key, default=None):
                try:
                    return self[key]
                except KeyError:
                    return default

            def __eq__(self, other):
                self_to_compare = {
                    key: dict.__getitem__(self, key)
                    for key in self
                    if not isinstance(key, str) or not key.startswith("_")
                }
                return self_to_compare == other

        class ExtraData(BaseModel):
            class Config:
                validate_assignment = True
                use_enum_values = True

            # 工单流转历史信息
            operate_action: Optional[str] = Field(default=None, description="工单最近一次 action")
            operate_reason: Optional[str] = Field(default=None, description="工单最近一次操作原因")

            @validator("operate_action", pre=True)
            def convert_enum(cls, operate_action):
                if isinstance(operate_action, JobProcessMark):
                    operate_action = operate_action.name
                return operate_action

            class JumpRecord(BaseModel):
                """
                自动跳转的记录。
                """

                job_id: int = Field(description="Job ID")
                first_run_at: int = Field(description="初次执行时间，可以视情况被手动重试重置")
                can_jump: bool = Field(description="是否可以跳转")
                jump_times: int = Field(description="已经自动跳转过的次数，可以视情况被手动重试重置")

            jump_records: dict[int, JumpRecord] = Field(default={}, description="自动跳转的记录")

            class ExecuteResult(BaseModel):
                step_uuid: str = Field(description="步骤的 UUID")
                is_accept: bool = Field(description="条件是否符合")
                is_executed: bool = Field(False, description="是否已经执行过")

            execute_results: dict[str, ExecuteResult] = Field(default={})

    class Wrapper:
        class ExtraData:
            __slots__ = ("_business_order", "_extra_data")
            _business_order: "BusinessOrder"
            _extra_data: "BusinessOrder.Schema.ExtraData"

            def __init__(self, business_order):
                self._business_order = business_order
                self._extra_data = BusinessOrder.Schema.ExtraData(**(business_order.extra_data or {}))

            def get_all_jump_records(
                self,
            ) -> dict[int, "BoSchema.ExtraData.JumpRecord"]:
                """
                获取所有的跳转记录。
                """
                return self._extra_data.jump_records

            def get_jump_record_by_job_id(self, job_id: int) -> Union["BoSchema.ExtraData.JumpRecord", None]:
                """
                通过 job id 获取跳转记录。
                """
                return self._extra_data.jump_records.get(job_id)

            def get_or_create_jump_record_by_job_id(self, job_id: int) -> "BoSchema.ExtraData.JumpRecord":
                """
                通过 job id 获取跳转记录，如果没有找到，则直接创建一条新记录。
                """
                jump_record = self.get_jump_record_by_job_id(job_id)
                if jump_record is None:
                    # 追加跳转记录。
                    now_time = int(time.time())
                    jump_record = BoSchema.ExtraData.JumpRecord(
                        job_id=job_id,
                        first_run_at=now_time,
                        can_jump=True,
                        jump_times=0,
                    )
                    self.add_jump_record(jump_record)
                return jump_record

            def remove_jump_record_by_job_id(self, job_id: int) -> None:
                """
                移除一项跳转记录。
                """
                jump_records = self.get_all_jump_records()
                if job_id in jump_records:
                    jump_records.pop(job_id)
                    self._extra_data.jump_records = jump_records
                    self._save()

            def add_jump_record(self, jump_record: "BoSchema.ExtraData.JumpRecord") -> None:
                """
                更新跳转记录。
                """
                jump_records = self.get_all_jump_records()
                jump_records.update({jump_record.job_id: jump_record})
                self._extra_data.jump_records = jump_records
                self._save()

            def _save(self):
                self._business_order.extra_data = self._extra_data.dict(exclude_none=True)

            def readable(self):
                return f"ExtraDataWrapper(bo={self._business_order}, extra_data={self._extra_data})"

            __str__ = readable
            __repr__ = readable

        class WidgetForm:
            """
            从工单实例中抽取所有组件信息
            提供
                根据 widget_key 获取 widget_ref 的能力
                根据 widget_ref_obj 获取 widget_ref 的能力
            """

            __slots__ = ("_widget_map", "all_widget_info_list")
            _widget_map: Dict[str, WidgetInfo.View.RawStep]
            all_widget_info_list: list[WidgetInfo.View.RawStep]

            def __init__(self, business_order):
                self._widget_map = self._extract_widget(
                    list(chain(raw for job in business_order.jobs for raw in job.raw_ui_schema))
                )
                self.all_widget_info_list = self._extract_widget_to_list(
                    list(chain(raw for job in business_order.jobs for raw in job.raw_ui_schema))
                )

            def __getitem__(self, widget_ref):
                if isinstance(widget_ref, (dict, WidgetRef)):
                    return WidgetRef.parse(widget_ref)

                if not isinstance(widget_ref, str):
                    return widget_ref

                # 走到这里，说明传入的是 widget_key
                if widget_ref in self._widget_map:
                    widget_info = self._widget_map[widget_ref]
                    widget_ref_obj = dict(
                        key=widget_info.key,
                        type=widget_info.type,
                        # fixme 线上 step.raw_step 中的 widget_type 是错误的
                        # widget_type=widget_info.widget_type,
                        multi_row=widget_info.data_schema.multi_row,
                    )
                    return WidgetRef.parse(widget_ref_obj)
                # 没有这个 widget key 的信息，丢给 WidgetRef 走兜底方案
                else:
                    return WidgetRef.parse(widget_ref)

            def _extract_widget(self, ui_schema: Iterable):
                ui_schema = parse_obj_as(List[WidgetInfo.View.RawStep], ui_schema)
                widget_map = {}
                for widget_info in ui_schema:
                    widget_map[widget_info.key] = widget_info
                    if widget_info.is_table:
                        widget_map.update(self._extract_widget(widget_info.fields))
                return widget_map

            def _extract_widget_to_list(self, ui_schema: Iterable) -> list[WidgetInfo.View.RawStep]:
                """
                拆包出所有的组件，并将其放入一个列表中。
                不同于 map 的好处是，不会因为不同内容、但是同名的 key 导致数据覆盖。
                比如：
                    引用前序组件，传递的 key 是一样的，但是 before 为 true 的可能会覆盖掉之前 before 为 false 的。
                """
                ui_schema = parse_obj_as(List[WidgetInfo.View.RawStep], ui_schema)
                widget_list = []
                for widget_info in ui_schema:
                    widget_list.append(widget_info)
                    if widget_info.is_table:
                        widget_list.extend(self._extract_widget_to_list(widget_info.fields))
                return widget_list

            def fields(self, widget_ref):
                widget_ref = self[widget_ref]
                if widget_ref and widget_ref.is_table:
                    widget_info = self._widget_map[widget_ref.key]
                    return [self[field["key"]] for field in widget_info.fields]

                return []

        class Data:
            """工单数据"""

            __slots__ = ("_business_order", "_widget_form")
            _business_order: "BusinessOrder"
            _widget_form: "BusinessOrder.Wrapper.WidgetForm"

            def __init__(self, business_order):
                self._business_order = business_order
                self._widget_form = business_order.widget_form_wrapper

            def __getitem__(self, widget_ref):
                # 首先将所有的 query 转换为 widget_ref
                widget_ref = self._widget_form[widget_ref]
                # 将 data 转换为 table
                data = BusinessOrder.Schema.Table(self._business_order.data)
                return data[widget_ref]

            def __setitem__(self, key, value):
                self._business_order.data[key] = value

            @property
            def raw_data(self):
                return self._business_order.data

            def __repr__(self):
                return self._business_order.data.__repr__()

            def __str__(self):
                return self._business_order.data.__str__()

            def __contains__(self, item):
                try:
                    _ = self[item]
                    return True
                except KeyError:
                    return False

            def get(self, key, default=None):
                if key is None:
                    return
                try:
                    return self[key]
                except KeyError:
                    return default

            def set(
                self,
                key,
                value,
                *,
                strategy: Literal["append", "update"] = "append",
                index=None,
            ):
                # FIXME 支持局部更新 根据多个 iterator

                widget_ref = WidgetRef.parse(self._widget_form[key], force_parse_str=True)
                if widget_ref.is_table:
                    if strategy == "append":
                        self.insert_table_value(widget_ref, value)
                    elif strategy == "update" and index is not None:
                        self.update_table_value(widget_ref, value, index)
                    else:
                        raise Exception("更新策略为 update 时，请指定需要更新的行")
                else:
                    self.__setitem__(widget_ref.key, value)

            def update_table_value(self, widget_ref, value, index: int):
                """更新 table 组件的值，即使 widget_ref 中有 field ，也只支持整行的数据修改"""
                widget_ref = self._widget_form[widget_ref]
                assert widget_ref.is_table, f"仅支持 table 组件的更新, {widget_ref}"

                # 先找到当前 table 的数据
                table_rows = self[widget_ref.raw_query]
                assert index <= len(table_rows), f"待更新索引无数据 query: {widget_ref}@{index}, {table_rows=}"
                table_rows = [*table_rows[:index], value, *table_rows[index + 1 :]]

                # 更新到 bo data 中
                self.__setitem__(widget_ref.key, table_rows)
                flag_modified(self._business_order, "data")

            def insert_table_value(self, widget_ref, value):
                """向 table 里插入一行数据"""
                widget_ref = self._widget_form[widget_ref]
                assert widget_ref.is_table, f"仅支持 table 组件的更新, {widget_ref}"

                # 先找到当前 table 的数据
                try:
                    table_rows = self[widget_ref.raw_query]
                except KeyError:
                    table_rows = []
                table_rows.append(value)

                # 更新到 bo data 中
                self.__setitem__(widget_ref.key, table_rows)
                flag_modified(self._business_order, "data")

        class JobTransition:
            """

            Methods:
                record(current, next)
                    记录一个工单流转记录
                views()
                    列出工单流转记录
            TODO:
                和 JobRecord 合并
            """

            __slots__ = ("_business_order",)
            _business_order: "BusinessOrder"

            def __init__(self, business_order):
                self._business_order = business_order

            def exists(self):
                return db.session.query(
                    JobTransition.query.filter_by(business_order_id=self._business_order.id).exists()
                ).scalar()

            def get_latest_record(self):
                return (
                    JobTransition.query.filter_by(business_order_id=self._business_order.id)
                    .order_by(JobTransition.id.desc())
                    .first()
                )

            @in_transaction()
            def record(self, current_job, next_job):
                action = Job.Utils.job_transition_action(current_job)

                if self.exists():
                    last_transition = self.get_latest_record()
                    if (
                        last_transition.current == current_job.id
                        and last_transition.next == next_job.id
                        and last_transition.extra.get("action") == action
                    ):
                        return

                # 判断是否有 scope 信息
                form_version: FormVersion | None = self._business_order.form_version
                if form_version is None:
                    transition_type = "主流程"
                    scope = None
                else:
                    try:
                        scopes = form_version.get_step_scope(current_job.step_uuid)
                    except StopIteration:
                        scopes = []
                    if scopes:
                        scope = scopes[0]
                        transition_type = "遍历分支"
                    else:
                        scope = None
                        transition_type = "主流程"

                transition_record = JobTransition()
                transition_record.business_order_id = self._business_order.id
                transition_record.current = current_job.id
                transition_record.current_step_uuid = current_job.step_uuid
                transition_record.next = next_job.id
                transition_record.next_step_uuid = next_job.step_uuid
                transition_record.transition_type = transition_type

                extra = JobTransition.Schema.Extra(action=action)
                if scope:
                    scope_info = self._business_order.data.get(scope.step_uuid) or {}
                    row = scope_info.get("item") or {}
                    iterator_info = scope_info.get("iterator") or {}
                    index = iterator_info.get("index", 0)
                    widget_ref = scope.widget_ref.dict()
                    extra.iterate_gateway = {
                        "row": row,
                        "iterators": [
                            {
                                "iterator_type": "iterate_gateway",
                                "widget_ref": widget_ref,
                                "index": index,
                            }
                        ],
                    }

                transition_record.extra = extra.dict(exclude_none=True)
                db.session.add(transition_record)

            def views(self) -> list["JobTransition.View.Brief"]:
                if self.exists():
                    return [item.brief_view for item in self._business_order.job_transition]

                else:  # 如果没有工单流转记录，尝试从 job_history 中构造一个出来
                    job_transition: list[JobTransition.View.Brief] = []
                    if not self._business_order.job_history:
                        return job_transition

                    job_step_uuid_map: Dict[int, str] = dict(
                        db.session.query(Job.id, Job.step_uuid)  # type: ignore[arg-type]
                        .filter(Job.id.in_(self._business_order.job_history))
                        .all()
                    )
                    for index in range(len(self._business_order.job_history) - 1):
                        current = self._business_order.job_history[index]
                        next_ = self._business_order.job_history[index + 1]
                        dummy = JobTransition.View.Brief(
                            current=current,
                            current_step_uuid=job_step_uuid_map.get(current, ""),
                            next=next_,
                            next_step_uuid=job_step_uuid_map.get(next_, ""),
                        )
                        job_transition.append(dummy)

                    return job_transition

    class Utils:
        @staticmethod
        def get_widget_info(business_order: "BusinessOrder", key: str):
            from sqlalchemy import select

            from robot_processor.form.models import Step
            from robot_processor.form.models import WidgetInfo

            widget_collection_subquery = (
                select(Step.widget_collection_id.label("id"))
                .where(Step.id.in_(business_order.form_version.step_id))
                .where(Step.widget_collection_id.is_not(None))
                .subquery()
            )
            widget_info: Optional[WidgetInfo] = (
                WidgetInfo.query.join(
                    widget_collection_subquery,
                    widget_collection_subquery.c.id == WidgetInfo.widget_collection_id,
                )
                .filter(WidgetInfo.before.is_not(True))
                .filter(WidgetInfo.key == key)
                .first()
            )

            return widget_info

    # 工单查询
    sql_field_resolver_registry = FieldResolverRegistry()
    sql_table_field_registry = TableFieldRegistry()


def _init_business_order_sql_registry():
    from robot_types.helper import OperatorEnum
    from sqlalchemy import func as sql_func

    BusinessOrder.sql_table_field_registry.register(BusinessOrder.__table__)
    BusinessOrder.sql_table_field_registry.register_field("system_business_order_id", BusinessOrder.id)

    # FIXME 升级 MySQL8.0 之前使用 JSON_SEARCH 实现 ANY/EQ
    class AnyEqual:
        def __call__(self, field, value, context, **extra):
            return sql_func.json_search(field, "one", value).isnot(None)

    class AllEqual:
        def __call__(self, field, value, context, **extra):
            return sql_func.json_search(field, "all", value).isnot(None)

    BusinessOrder.sql_field_resolver_registry.types["array"][(OperatorEnum.ANY, OperatorEnum.EQ)] = AnyEqual()
    BusinessOrder.sql_field_resolver_registry.types["array"][(OperatorEnum.ALL, OperatorEnum.EQ)] = AllEqual()


_init_business_order_sql_registry()


class JobApprover(DbBaseModel, BasicMixin):
    """
    审批人信息。
    """

    job_id: Mapped[int] = mapped_column(sa.Integer, comment="任务 ID", index=True)
    user_id: Mapped[int] = mapped_column(sa.Integer, comment="用户执行审批时登陆的飞梭用户 ID")
    # is_valid 这个字段，主要是为了在 step 进行 assistants_v2 这一字段的更新发布后，快捷查询、更新审批人。
    is_valid: Mapped[bool] = mapped_column(sa.Boolean, default=True, comment="用户是否有效")
    is_approved: Mapped[bool] = mapped_column(sa.Boolean, default=False, comment="是否执行过了审批操作")
    step_uuid: Mapped[str] = mapped_column(sa.String(32), comment="关联到的 Step 的 UUID")

    @classmethod
    def clean_by_job_id(cls, job_id: int) -> None:
        """
        删除该 job_id 对应的所有审批人。
        :param job_id:
        :return:
        """
        JobApprover.query.filter(JobApprover.job_id == job_id).delete()


class JobPool(DbBaseModel, BasicMixin):
    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    sid: Mapped[str | None] = mapped_column(sa.String(32), index=True, comment="店铺 id")
    business_order_id: Mapped[int] = mapped_column(sa.Integer, index=True, comment="关联工单id")
    job_id: Mapped[int] = mapped_column(sa.Integer, comment="任务id", index=True)
    assignee_user_id: Mapped[int] = mapped_column(sa.Integer, comment="用户id")
    assignee_user_type: Mapped[Creator] = mapped_column(
        sa.Enum(Creator), default=Creator.ASSISTANT, comment="assignee类型"
    )
    assignee: Mapped[str | None] = mapped_column(
        sa.String(32),
        Computed("CONCAT(job_pool.assignee_user_id,':', job_pool.assignee_user_type)"),
        index=True,
    )

    __table_args__ = (
        sa.UniqueConstraint(
            "job_id",
            "assignee_user_id",
            "assignee_user_type",
            name="job_assignee_user_unique_idx",
        ),
    )

    @classmethod
    def add_job(cls, sid, bo_id, job_id, candidates):
        JobPool.query.filter(JobPool.sid == sid, JobPool.job_id == job_id).delete(synchronize_session=False)
        base_data = {
            "sid": sid,
            "business_order_id": bo_id,
            "job_id": job_id,
        }
        db.session.bulk_insert_mappings(
            JobPool,  # type: ignore[arg-type]
            [
                dict(
                    **base_data,
                    assignee_user_id=candidate.user_id,
                    assignee_user_type=Creator(candidate.user_type),
                )
                for candidate in candidates
            ],
        )
        db.session.commit()

    @classmethod
    def pick_job(cls, job_id, user: AccountDetailV2):
        try:
            user_type = Creator(user.user_type)  # type: ignore[arg-type]
        except Exception:
            logger.error(f"用户的 user_type 不合法：{user.user_type}")
            return
        JobPool.query.filter(
            JobPool.job_id == job_id,
            ~((JobPool.assignee_user_id == user.user_id) & (JobPool.assignee_user_type == user_type)),
        ).delete()

    @classmethod
    def remove_job(cls, job_id):
        # job移出任务池
        JobPool.query.filter(JobPool.job_id == job_id).delete()

    def retry(self):
        # todo 异常工单的重试 适用于自动任务失败的情况及当前人工步骤无可用分派人的情况
        from robot_processor.business_order.tasks import package_jobs

        package_jobs(self.id)


class BusinessOrderTradeMap(DbBaseModel):
    # BusinessOrder 与买家订单的映射表 用于多订单根据订单号查询工单
    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    business_order_id: Mapped[int | None] = mapped_column(sa.Integer, index=True, comment="工单id")
    tid_or_oid: Mapped[str | None] = mapped_column(sa.String(32), index=True, comment="订单号或子订单号")


class JobExecuteCron(DbBaseModel, BasicMixin):
    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    business_order_id: Mapped[int] = mapped_column(sa.Integer, comment="工单id")
    job_id: Mapped[int] = mapped_column(sa.Integer, comment="任务id")
    expect_execute_time: Mapped[int] = mapped_column(sa.Integer, comment="预期执行时间", index=True)
    actual_execute_time: Mapped[int | None] = mapped_column(sa.Integer, comment="实际执行时间")
    exc_info: Mapped[str | None] = mapped_column(sa.String(128))

    status: Mapped[JobStatus] = mapped_column(sa.Enum(JobStatus), default=JobStatus.INIT, comment="job 的状态")

    def complete_job(self):
        from robot_processor.business_order.tasks import package_jobs

        cur_job = Job.query.get(self.job_id)
        cur_job.set_status(JobStatus.SUCCEED)  # type: ignore[union-attr]
        db.session.add(cur_job)
        db.session.commit()
        package_jobs.send(
            self.business_order_id,
            current_job_id=self.job_id,
            need_record_job_transition=True,
        )
        self.status = JobStatus.SUCCEED
        self.actual_execute_time = int(time.time())
        db.session.add(self)
        db.session.commit()


class JobRemindRecord(DbBaseModel):
    __table_args__ = (sa.Index("unique_job_remind", "v_from_user_id", "v_to_user_id", "message_digest"),)

    class User(TypedDict):
        user_id: int
        user_type: int
        user_nick: str

    class Data(TypedDict):
        business_order_id: int
        job_id: int
        form_name: str
        step_name: str
        sid: str
        org_id: str

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    created_at: Mapped[int] = mapped_column(sa.Integer, default=time.time, nullable=False, index=True)
    from_user: Mapped[User] = mapped_column(sa.JSON, default=dict, nullable=False)
    v_from_user_id: Mapped[int] = mapped_column(
        sa.Integer, Computed("cast(json_unquote(from_user -> '$.user_id') as UNSIGNED)")
    )
    to_user: Mapped[User] = mapped_column(sa.JSON, default=dict, nullable=False)
    v_to_user_id: Mapped[int] = mapped_column(
        sa.Integer, Computed("cast(json_unquote(to_user -> '$.user_id') as UNSIGNED)")
    )
    data: Mapped[Data] = mapped_column(sa.JSON, default=dict, nullable=False)
    message_digest: Mapped[str] = mapped_column(sa.String(32), nullable=False)

    @classmethod
    def calculate_digest(cls, data):
        import hashlib
        import json

        return hashlib.md5(json.dumps(data, sort_keys=True).encode("utf-8")).hexdigest()


# 废弃 使用JobAutoRetryRecord, 目前还有CONFIRM_LOGISTICS_CANCEL在使用，trade不再使用
class JobTask(DbBaseModel, BasicMixin):
    job_id: Mapped[int | None] = mapped_column(sa.Integer, comment="job_id")
    run_times: Mapped[int | None] = mapped_column(sa.Integer, comment="执行次数")
    run_status: Mapped[int | None] = mapped_column(sa.Integer, comment="执行状态0未执行1执行中2执行失败3执行成功")
    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)
    data: Mapped[dict | None] = mapped_column(sa.JSON, comment="执行参数")
    job_type: Mapped[str] = mapped_column(
        sa.String(32),
        default=JobType.CONFIRM_LOGISTICS_CANCEL.value,
        comment="执行job的类型",
    )


class JobVisitHistory(DbBaseModel):
    """
    记录用户查看某个job详情的记录 若未查看过 则可在工单卡片处显式红点
    """

    __table_args__ = (sa.Index("unique_job_id_user_id", "job_id", "user_id"),)

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    job_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)
    user_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)


class BusinessOrderMemo(DbBaseModel):
    id: Mapped[int] = mapped_column(sa.ForeignKey("business_order.id"), primary_key=True)
    notes: Mapped[list] = mapped_column(sa.JSON, default=list)

    business_order: Mapped[BusinessOrder] = relationship(BusinessOrder, back_populates="memo")

    class Schema:
        class Attachment(BaseModel):  # type: ignore[no-redef] #
            file_type: str  # 类型: Literal['image', 'video']
            key_in_oss: str
            url: str
            filename: Optional[str] = Field(alias="name")
            name: Optional[str]

        class NoteStatus(StrEnum):
            NORMAL = "正常"
            DELETED = "已删除"

            def need_display(self):
                return self != self.DELETED

        class Note(BaseModel):
            user: AccountDetailV2
            status: "BusinessOrderMemo.Schema.NoteStatus" = Field(
                default_factory=lambda: BusinessOrderMemo.Schema.NoteStatus.NORMAL
            )
            note: Optional[str] = Field(default=None, description="备注内容")
            attachments: Optional[List["BusinessOrderMemo.Schema.Attachment"]] = Field(default_factory=list)
            created_at: str = Field(default_factory=lambda: arrow.now().format("YYYY-MM-DD HH:mm:ss"))
            updated_at: Optional[str] = Field(default=None)

    class View:
        class Base(BaseModel):
            class Config:
                orm_mode = True

            id: int = Field(description="工单实例 ID")
            notes: List["BusinessOrderMemo.Schema.Note"] = Field(description="备注列表")

            @classmethod
            def from_blank(cls, bo_id: int):
                return cls(id=bo_id, notes=[])

        class BusinessOrder(Base):
            """在 business order 中查看/修改"""

            @validator("notes")
            def filter_deleted_note(cls, notes):
                # 过滤掉已删除的备注
                # 按创建时间倒序展示
                return [note for note in notes if note.status.need_display()][::-1]


class JobTransition(DbBaseModel, BasicMixin):
    business_order_id: Mapped[int | None] = mapped_column(sa.ForeignKey("business_order.id"))
    current: Mapped[int | None] = mapped_column(sa.Integer, comment="当前步骤id")
    next: Mapped[int | None] = mapped_column(sa.Integer, comment="下一步骤id")
    current_step_uuid: Mapped[str | None] = mapped_column(sa.String(32), comment="当前步骤uuid")
    next_step_uuid: Mapped[str | None] = mapped_column(sa.String(32), comment="下一步骤uuid")

    transition_type: Mapped[str] = mapped_column(
        sa.String(32),
        default="主流程",
        comment="流转类型；默认为主流程，遍历网关/并发网关为子流程",
    )
    extra: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="额外信息")
    business_order: Mapped[BusinessOrder] = relationship(BusinessOrder, back_populates="job_transition")

    @property
    def brief_view(self) -> "JobTransition.View.Brief":
        return JobTransition.View.Brief.from_orm(self)

    class Schema:
        class Extra(BaseModel):
            iterate_gateway: dict | None = Field(default=None, description="遍历分支任务的数据")
            action: str = Field(default="未知", description="工单流转触发操作")

    class View:
        class Brief(BaseModel):
            class Config:
                validate_assignment = True
                orm_mode = True

            current: int = Field(description="当前步骤 id")
            next: int = Field(description="下一步骤 id")
            current_step_uuid: str = Field(description="当前步骤 step_uuid")
            next_step_uuid: str = Field(description="下一步骤 step_uuid")
            transition_type: Literal["主流程", "遍历分支"] = Field(
                default="主流程",
                description="流转类型；默认为主流程，遍历网关/并发网关为子流程",
            )
            extra: "JobTransition.Schema.Extra" = Field(
                default_factory=lambda: JobTransition.Schema.Extra(),
                description="额外信息",
            )

            def dict(self, **kwargs):
                kwargs.setdefault("exclude_none", True)
                return super().dict(**kwargs)


class WebhookMessages(DbBaseModel, BasicMixin):
    system_name: Mapped[str] = mapped_column(sa.String(128), comment="第三方系统的名称")
    system_unique_id: Mapped[str] = mapped_column(sa.String(128), comment="第三方系统的唯一id")
    system_biz_no: Mapped[str | None] = mapped_column(sa.String(128), comment="第三方系统的业务id，一般给对接方使用")
    event_type: Mapped[str] = mapped_column(sa.String(128), comment="event的类型")
    business_order_id: Mapped[int | None] = mapped_column(sa.Integer, index=True, comment="关联工单id,可能没有")
    payload: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="回调信息")

    @classmethod
    def record_logistics_intercept_event(cls, logistics_company: str, logistics_no: str, payload: dict):
        with in_transaction() as trx:
            message = WebhookMessages(
                system_name=logistics_company,
                event_type="INTERCEPT",
                system_unique_id=logistics_no,
                payload=payload,
            )
            trx.add(message)
        return message


class InterceptEvent(DbBaseModel):
    """物流拦截事件"""

    __table_args__ = (
        sa.PrimaryKeyConstraint("id"),
        sa.Index("idx_logistics", "logistics_no", "logistics_company"),
    )

    class Type(StrEnum):
        REPORT = enum.auto(), "拦截上报"
        CANCEL = enum.auto(), "拦截取消"

        def __new__(cls, value, *args):
            self = str.__new__(cls, value)
            self._value_ = value
            return self

        def __init__(self, _, label):
            self.label = label

    class InterceptStatus(StrEnum):
        WAIT = enum.auto(), "等待拦截结果"
        SUCCESS = enum.auto(), "拦截成功"
        FAILED = enum.auto(), "拦截失败"

        def __new__(cls, value, *args):
            self = str.__new__(cls, value)
            self._value_ = value
            return self

        def __init__(self, _, label):
            self.label = label

        def is_failed(self):
            return self is InterceptEvent.InterceptStatus.FAILED

        def is_success(self):
            return self is InterceptEvent.InterceptStatus.SUCCESS

        def is_wait(self):
            return self is InterceptEvent.InterceptStatus.WAIT

    class EventStatus(StrEnum):
        WAIT_INTERCEPT = enum.auto(), "等待拦截结果"
        WAIT_PROCESS = enum.auto(), "等待事件处理"
        FINISHED = enum.auto(), "已处理"
        CANCELED = enum.auto(), "工单被跳过/关闭"
        TIMEOUT = enum.auto(), "超时"

        def __new__(cls, value, *args):
            self = str.__new__(cls, value)
            self._value_ = value
            return self

        def __init__(self, _, label):
            self.label = label

        @classmethod
        def final_status(cls):
            return [cls.FINISHED, cls.CANCELED, cls.TIMEOUT]

    id: Mapped[int]
    logistics_no: Mapped[str] = mapped_column(sa.String(128))
    logistics_company: Mapped[str] = mapped_column(sa.String(128))
    intercept_at: Mapped[datetime] = mapped_column(sa.DateTime(), server_default=sa.text("now()"))
    result_at: Mapped[datetime | None] = mapped_column(sa.DateTime(), default=None)

    type: Mapped[Type] = mapped_column(sa.Enum(Type))
    intercept_status: Mapped[InterceptStatus] = mapped_column(sa.Enum(InterceptStatus), default=InterceptStatus.WAIT)
    event_status: Mapped[EventStatus] = mapped_column(sa.Enum(EventStatus), default=EventStatus.WAIT_INTERCEPT)

    business_order_id: Mapped[int | None]
    job_id: Mapped[int | None]
    webhook_message_id: Mapped[int | None]

    def get_yto_push_message(self):
        from rpa.yto.schema import InterceptStatusPushMessage

        if not self.webhook_message_id:
            return None

        webhook_message = db.session.get(WebhookMessages, self.webhook_message_id)
        if not webhook_message:
            return None

        return InterceptStatusPushMessage.validate(webhook_message.payload)

    @classmethod
    def record_wait_report_event(cls, logistics_no, logistics_company, business_order_id):
        """记录等待拦截上报结果的事件"""
        with in_transaction() as trx:
            trx.add(
                InterceptEvent(
                    logistics_no=logistics_no,
                    logistics_company=logistics_company,
                    type=InterceptEvent.Type.REPORT,
                    business_order_id=business_order_id,
                )
            )

    @classmethod
    def mark_intercept_report_status(cls, logistics_no, logistics_company, intercept_status, webhook_message_id):
        """找到当前快递等待拦截上报结果的事件

        变更内容:
            拦截结果: WAIT ➡️ SUCCESS / FAILURE
            事件状态: WAIT_INTERCEPT ➡️ WAIT_PROCESS

        Args:
            logistics_no (str): 物流单号
            logistics_company (LogisticsCompany): 物流公司
            intercept_status (InterceptEvent.InterceptStatus): 拦截结果
            webhook_message_id (int | None): webhook 消息 id
        """
        with in_transaction() as trx:
            trx.execute(
                sa.update(InterceptEvent)
                .where(InterceptEvent.logistics_no == logistics_no)
                .where(InterceptEvent.logistics_company == logistics_company)
                .where(InterceptEvent.type == InterceptEvent.Type.REPORT)
                .where(InterceptEvent.intercept_status == InterceptEvent.InterceptStatus.WAIT)
                .where(InterceptEvent.event_status == InterceptEvent.EventStatus.WAIT_INTERCEPT)
                .values(
                    intercept_status=intercept_status,
                    event_status=InterceptEvent.EventStatus.WAIT_PROCESS,
                    webhook_message_id=webhook_message_id,
                    result_at=arrow.now().datetime,
                )
            )

    @classmethod
    def get_latest_intercept_event(cls, logistics_no, logistics_company):
        """查找物流信息最近的一条拦截事件

        Args:
            logistics_no (str): 物流单号
            logistics_company (str): 物流公司 LogisticsType.value

        Returns:
            InterceptEvent | None: 拦截事件
        """
        return (
            cls.query.filter(
                cls.logistics_no == logistics_no,
                cls.logistics_company == logistics_company,
                cls.type == cls.Type.REPORT,
            )
            .order_by(cls.intercept_at.desc())
            .first()
        )

    @classmethod
    def get_or_register_intercept_event(cls, logistics_no, logistics_company, business_order_id, job_id):
        """查找 job 的拦截事件, 或将等待拦截事件的 job 注册到事件上

        Args:
            logistics_no:
            logistics_company:
            business_order_id:
            job_id:

        Returns:
            InterceptEvent | None: 拦截事件

        """
        intercept_event = cls.query.filter(
            cls.logistics_no == logistics_no,
            cls.logistics_company == logistics_company,
            cls.type == cls.Type.REPORT,
            cls.business_order_id == business_order_id,
            cls.job_id == job_id,
        ).first()
        if intercept_event:
            return intercept_event

        intercept_event = cls.query.filter(
            cls.logistics_no == logistics_no,
            cls.logistics_company == logistics_company,
            cls.type == cls.Type.REPORT,
            cls.event_status.not_in(cls.EventStatus.final_status()),
            cls.business_order_id == business_order_id,
            cls.job_id.is_(None),
        ).first()
        if intercept_event:
            intercept_event.job_id = job_id
            db.session.commit()
            return intercept_event

        return None

    @classmethod
    def query_report_wait_process(cls):
        """拦截上报的结果已获取，等待事件被处理"""
        wait_process_events: list[InterceptEvent] = InterceptEvent.query.filter(
            InterceptEvent.event_status == InterceptEvent.EventStatus.WAIT_PROCESS,
        ).all()
        return wait_process_events

    @in_transaction()
    def mark_process_finished(self):
        assert self.event_status == InterceptEvent.EventStatus.WAIT_PROCESS

        self.event_status = InterceptEvent.EventStatus.FINISHED

    @in_transaction()
    def mark_process_canceled(self):
        assert self.event_status in [
            InterceptEvent.EventStatus.WAIT_INTERCEPT,
            InterceptEvent.EventStatus.WAIT_PROCESS,
        ]
        self.event_status = InterceptEvent.EventStatus.CANCELED

    @in_transaction()
    def mark_process_timeout(self):
        assert self.event_status in [
            InterceptEvent.EventStatus.WAIT_INTERCEPT,
            InterceptEvent.EventStatus.WAIT_PROCESS,
        ]
        self.event_status = InterceptEvent.EventStatus.TIMEOUT


@lru_cache(maxsize=1024)
def get_form_composer(form_version_id: int):
    from itertools import groupby

    from robot_types.helper.form_composer import FormComposer
    from robot_types.helper.form_composer import schemas
    from robot_types.helper.symbol import SymbolDeserializer

    from robot_processor.form.models import FormSymbol

    form_version: FormVersion = unwrap_optional(db.session.get(FormVersion, form_version_id))
    composer = FormComposer(
        meta=schemas.FormMeta(
            id=form_version.meta["id"],
            name=form_version.meta["name"],
            version_id=form_version_id,
            version_no=form_version.version_no,
        ),
        steps=[],
    )
    steps = form_version.query_steps_with_entities()
    symbols = FormSymbol.query.filter(
        FormSymbol.form_id == form_version.form_id,
        FormSymbol.step_id.in_([step.id for step in steps]),
    ).all()
    step_symbols = {
        step_id: SymbolDeserializer.deserialize_flatten(list(grouped_symbols))
        for step_id, grouped_symbols in groupby(sorted(symbols, key=lambda x: x.step_id), key=lambda x: x.step_id)
    }
    for step in steps:
        composer_step = schemas.Step(
            id=step.id,
            name=step.name,
            step_uuid=step.step_uuid,
            step_type=schemas.StepType(step.step_type),
            symbols=step_symbols.get(step.id, []),
            key_map=step.key_map,
            branch=step.branch,
            jump=step.jump,
            prev_step_ids=step.prev_step_ids,
            next_step_ids=step.next_step_ids,
        )
        composer.steps.append(composer_step)

    return composer


def update_forward_refs():
    BusinessOrderMemo.Schema.Note.update_forward_refs()
    BusinessOrderMemo.View.Base.update_forward_refs()
    BusinessOrderMemo.View.BusinessOrder.update_forward_refs()

    JobTransition.Schema.Extra.update_forward_refs()
    JobTransition.View.Brief.update_forward_refs()


BoExtraDataWrapper = BusinessOrder.Wrapper.ExtraData
BoWrapper = BusinessOrder.Wrapper
BoSchema = BusinessOrder.Schema

update_forward_refs()
