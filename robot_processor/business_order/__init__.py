"""定义工作流任务的相关逻辑"""

from robot_processor.signals import after_boot
from robot_processor.signals import booting


@booting.connect
def init_app(app):
    from . import archive_task  # noqa
    from . import events  # noqa
    from . import exception_rule
    from . import kafka  # noqa
    from . import models
    from . import tasks
    from .api import function_api
    from .api.business_order_api import api as business_order_api
    from .api.memo_api import memo_api
    from .autofill import api as autofill_api
    from .binlog import handler  # noqa 注册 kafka consumer
    from .binlog.etl import ck_transform  # noqa
    from .mini_app.api import api as mini_app_api
    from .mini_app.backend_api import api as mini_app_backend_api
    from .notice.api import api as notice_api
    from .page_filters.api import api as page_filter_api
    from .page_titles.api import api as page_title_api
    from .seller.api import api as seller_api
    from .struct.export_api import api as export_api
    from .struct.import_api import api as import_api

    app.register_blueprint(business_order_api, url_prefix="/v1")
    app.register_blueprint(memo_api, url_prefix="/v1")
    app.register_blueprint(notice_api, url_prefix="/v1")
    app.register_blueprint(page_filter_api, url_prefix="/v1")
    app.register_blueprint(page_title_api, url_prefix="/v1")
    app.register_blueprint(mini_app_api, url_prefix="/v1/mini-app")
    app.register_blueprint(mini_app_backend_api, url_prefix="/v1/backend/mini-app")
    app.register_blueprint(seller_api, url_prefix="/v1/seller")
    app.register_blueprint(function_api.router, url_prefix="/v1")
    app.register_blueprint(function_api.mini_app_router, url_prefix="/v1/mini-app")
    app.register_blueprint(export_api, url_prefix="/v1/export")
    app.register_blueprint(import_api, url_prefix="/v1/import")
    app.register_blueprint(autofill_api.route, url_prefix="/v1")


@after_boot.connect
def init_exception_ruler_keeper(app):
    from .binlog.job_processor import watchdog

    # 启动检查规则更新的后台任务
    watchdog.start(app)
