# 表头的填充字段，如果表头长度不一致，那么 pandas 的 MultiIndex 会报错。
FILLED_CHAR = ""
# 空格，主要是为了让 openpyxl 去区分这一行是否有值。（目前是基于 None 去合并单元格，但是空字符串也会被 openpyxl 识别为 None）
SPACE = " "
# 列表组件下的第一层下属子组件，会以 _ITEM 结尾
ARRAY_ITEM_SUFFIX = "_ITEM"
# 至少也会有一行吧
MIN_ROW_LENGTH = 1
# EXCEL 默认的列宽
FIXED_COLUMN_WIDTH = 20
# 选择组件的子组件，其组件类型会是 ENUM_ITEM + 选择组件的类型。
ENUM_ITEM = "enum-item"
# 付款方式的组件类型
PAYMENT_METHOD_ITEM_METHOD = "payment-method-item-method"
# 评分组件下的选项类型
RATE_ITEM_TYPE = "rate-item-type"
# 评分组件下的分值
RATE_ITEM_VALUE = "rate-item-value"
# 选择组件的子组件的名称前缀
ENUM_LEVEL_PREFIX = "层级"
# 序列的 key（序列，复合组件、列表组件的一个导入导出辅助子组件，用于标识所属行信息）
SERIAL = "SERIAL"
# 需要忽视数据读取的 worksheet 的名称
IGNORED_WORKSHEET_TITLE = "导入说明"
# 起始步骤的名称
BEGIN_STEP_NAME = "起始步骤"
# 全量数据导出的 worksheet 的名称
FULL_BUSINESS_ORDER_WORKSHEET_TITLE = "全量数据"
