from datetime import datetime
from typing import Any

import sqlalchemy as sa
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column

from robot_processor.db import BasicMixin
from robot_processor.db import DbBaseModel


class ImportTask(DbBaseModel, BasicMixin):
    """
    导入任务
    """

    org_id: Mapped[str] = mapped_column(sa.Integer, comment="所属租户 ID")
    creator_id: Mapped[int] = mapped_column(sa.Integer, comment="任务创建人 ID")
    creator_nickname: Mapped[str] = mapped_column(sa.String(32), comment="任务创建人的昵称")
    cursor: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="游标，用于标记读取到 excel")
    status: Mapped[str] = mapped_column(sa.String(32), comment="导入任务的状态")
    extra_data: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="额外信息")

    message: Mapped[str | None] = mapped_column(sa.Text, comment="相关描述")
    finished_time: Mapped[datetime | None] = mapped_column(sa.DateTime, comment="完成时间")
    exceptional_file_urls: Mapped[list] = mapped_column(sa.JSON, default=list, comment="需要导出的异常文件的全部链接")

    def to_data(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "org_id": self.org_id,
            "creator_id": self.creator_id,
            "creator_nickname": self.creator_nickname,
            "created_at": datetime.fromtimestamp(self.created_at).strftime("%Y-%m-%d %H:%M:%S"),
            "status": self.status,
            "message": self.message,
            "finished_time": (self.finished_time.strftime("%Y-%m-%d %H:%M:%S") if self.finished_time else None),
            "file_urls": self.exceptional_file_urls,
        }


class ExportTask(DbBaseModel, BasicMixin):
    """
    导出任务
    """

    org_id: Mapped[str] = mapped_column(sa.Integer, comment="所属租户 ID")
    creator_id: Mapped[int] = mapped_column(sa.Integer, comment="任务创建人 ID")
    creator_nickname: Mapped[str] = mapped_column(sa.String(32), comment="任务创建人的昵称")
    updator_id: Mapped[int | None] = mapped_column(sa.Integer, comment="任务修改人 ID，修改操作主要是撤销导出")
    updator_nickname: Mapped[str] = mapped_column(sa.String(32), comment="任务修改人的昵称")
    params: Mapped[dict] = mapped_column(sa.JSON, comment="导出任务的入参")
    file_urls: Mapped[list[str]] = mapped_column(sa.JSON, default=list, comment="导出文件的全部链接")
    status: Mapped[str] = mapped_column(sa.String(32), comment="导出任务的状态")
    message: Mapped[str | None] = mapped_column(sa.Text, comment="相关描述")
    finished_time: Mapped[datetime | None] = mapped_column(sa.DateTime, comment="完成时间")

    def to_data(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "created_at": self.created_at,
            "org_id": self.org_id,
            "creator_id": self.creator_id,
            "creator_nickname": self.creator_nickname,
            "updator_id": self.updator_id,
            "updator_nickname": self.updator_nickname,
            "status": self.status,
            "file_urls": self.file_urls,
            "params": self.params,
            "message": self.message,
            "finished_time": (self.finished_time.strftime("%Y-%m-%d %H:%M:%S") if self.finished_time else None),
        }
