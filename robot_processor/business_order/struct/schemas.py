from pydantic import BaseModel
from pydantic import Field

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.business_order.schema import FilterSchema
from robot_processor.business_order.struct.entities import ExceptionalDataContainer
from robot_processor.business_order.struct.entities import FileContainer
from robot_processor.business_order.struct.enums import ColumnSplitMode
from robot_processor.business_order.struct.enums import ExportScene
from robot_processor.business_order.struct.enums import FileType
from robot_processor.business_order.struct.enums import MultiRowsFormatter


class GetWidgetTitlesArgument(BaseModel):
    form_ids: list[int] | None = Field(default=None)
    column_split_mode: ColumnSplitMode = Field(default=ColumnSplitMode.ALL_SPLIT)


class ExportTaskParams(FilterSchema):
    titles: list[list[str]] = Field(description="需要导出的表头字段")
    filename: str = Field(description="文件名")
    org_id: str | None = Field(description="租户 ID")
    user_id: int | None = Field(description="用户乐言账号 ID")
    fixed_column_width: bool = Field(default=False, description="是否固定列宽")
    multi_rows_formatter: MultiRowsFormatter = Field(
        default=MultiRowsFormatter.MERGE_TO_SINGLE_ROW,
        description="多行数据导出时格式设置",
    )
    is_split_to_multi_files: bool = Field(default=False, description="是否需要拆分多个文件")
    export_scene: ExportScene = Field(default=ExportScene.VIEW, description="导出场景")
    column_split_mode: ColumnSplitMode = Field(default=ColumnSplitMode.ALL_SPLIT, description="级联组件和地址组件的列拆分模式")

    def generate_filename(self, split_file_number: int, in_iteration: bool) -> str:
        if in_iteration:
            if self.is_split_to_multi_files:
                filename = f"{self.filename}_{split_file_number}"
            else:
                filename = self.filename
        else:
            if split_file_number == 1:
                filename = self.filename
            else:
                filename = f"{self.filename}_{split_file_number}"
        return filename


class CreateImportTaskBody(BaseModel):
    """
    创建导入任务的基础数据。
    """

    file_url: str = Field(description="文件的 URL")
    file_type: FileType = Field(description="文件的类型")
    file_name: str = Field(description="文件名称")
    is_admin: bool = Field(description="是否在任务中心操作")


class ImportTaskExtraData(BaseModel):
    file_container: FileContainer = Field(description="用户上传的文件，含 XLSX 和附件")
    operator: AccountDetailV2 = Field(description="操作人")
    is_admin: bool = Field(default=False, description="是否为任务中心操作")
    exceptional_data_container: ExceptionalDataContainer | None = Field(default=None, description="处理异常的数据信息")
    success_business_order_ids: list[int] | None = Field(default=None, description="处理成功的工单 ID")


class ImportTaskCursor(BaseModel):
    xlsx_filename: str | None = Field(default=None)
    sheet_name: str | None = Field(default=None)


class ExportTaskQuery(BaseModel):
    page_no: int = Field(default=1)
    page_size: int = Field(default=20)
    export_scene: ExportScene | None = Field(default=None)


class ImportTaskQuery(BaseModel):
    page_no: int = Field(default=1)
    page_size: int = Field(default=20)


class UpdateBusinessOrderBody(BaseModel):
    is_admin: bool = Field(default=False)
