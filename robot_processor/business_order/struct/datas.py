from robot_processor.business_order.struct.contants import ARRAY_ITEM_SUFFIX
from robot_processor.business_order.struct.contants import ENUM_LEVEL_PREFIX
from robot_processor.business_order.struct.contants import RATE_ITEM_TYPE
from robot_processor.business_order.struct.contants import RATE_ITEM_VALUE
from robot_processor.business_order.struct.entities import AccurateWidgetDetail
from robot_processor.business_order.struct.entities import AccurateWidgetMeta
from robot_processor.business_order.struct.entities import AccurateWidgetTitle
from robot_processor.business_order.struct.entities import WidgetDetail
from robot_processor.business_order.struct.entities import WidgetName
from robot_processor.business_order.struct.enums import ColumnSplitMode
from robot_processor.business_order.struct.enums import CommonWidgetType
from robot_processor.business_order.struct.enums import OrderMode
from robot_processor.business_order.struct.enums import PreDefinedTitle
from robot_processor.form.models import WidgetInfo
from robot_processor.form.types import WidgetDataType
from robot_processor.form.types import WidgetId
from robot_processor.form.types import WidgetType


class PreDefinedWidgetItems:
    @staticmethod
    def get_sub_widget_items_for_order(order_widget_infos: list[WidgetInfo]) -> dict:
        sub_widget_items = {
            **PreDefinedWidgetItems.get_serial_widget_item(),
            **{
                WidgetName(
                    widget_label="订单号",
                    widget_type=CommonWidgetType.text.value,
                    widget_data_type=WidgetDataType.STRING,
                ): WidgetDetail(
                    widget_infos=[
                        WidgetInfo(
                            key="tid",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        )
                    ],
                    sub_widgets_mapping=None,
                    form_versions=[],
                ),
            },
        }

        if any([(widget_info.option_value or {}).get("mode") == OrderMode.child for widget_info in order_widget_infos]):
            sub_widget_items.update(
                {
                    WidgetName(
                        widget_label="子订单号",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): WidgetDetail(
                        widget_infos=[
                            WidgetInfo(
                                key="oid",
                                widget_id=WidgetId(0),
                                option_value={},
                                data_schema={},
                            )
                        ],
                        sub_widgets_mapping=None,
                        form_versions=[],
                    ),
                }
            )
        return sub_widget_items

    @staticmethod
    def get_sub_widget_items_for_payment_method() -> dict:
        return {
            WidgetName(
                widget_label="收款方式",
                widget_type=CommonWidgetType.payment_method_item_method.value,
                widget_data_type=WidgetDataType.STRING,
            ): WidgetDetail(
                widget_infos=[
                    WidgetInfo(
                        key="payment_method",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    )
                ],
                sub_widgets_mapping=None,
                form_versions=[],
            ),
            WidgetName(
                widget_label="订单号",
                widget_type=CommonWidgetType.text.value,
                widget_data_type=WidgetDataType.STRING,
            ): WidgetDetail(
                widget_infos=[
                    WidgetInfo(
                        key="tid",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    )
                ],
                sub_widgets_mapping=None,
                form_versions=[],
            ),
            WidgetName(
                widget_label="支付宝交易号",
                widget_type=CommonWidgetType.text.value,
                widget_data_type=WidgetDataType.STRING,
            ): WidgetDetail(
                widget_infos=[
                    WidgetInfo(
                        key="alipay_no",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    )
                ],
                sub_widgets_mapping=None,
                form_versions=[],
            ),
            WidgetName(
                widget_label="真实姓名",
                widget_type=CommonWidgetType.text.value,
                widget_data_type=WidgetDataType.STRING,
            ): WidgetDetail(
                widget_infos=[
                    WidgetInfo(
                        key="receive_name",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    )
                ],
                sub_widgets_mapping=None,
                form_versions=[],
            ),
            WidgetName(
                widget_label="支付宝账号",
                widget_type=CommonWidgetType.text.value,
                widget_data_type=WidgetDataType.STRING,
            ): WidgetDetail(
                widget_infos=[
                    WidgetInfo(
                        key="receive_account",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    )
                ],
                sub_widgets_mapping=None,
                form_versions=[],
            ),
        }

    @staticmethod
    def get_sub_widget_items_for_address(column_split_mode: ColumnSplitMode) -> dict | None:
        match column_split_mode:
            case ColumnSplitMode.ALL_SPLIT:
                return {
                    WidgetName(
                        widget_label="省份",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): WidgetDetail(
                        widget_infos=[
                            WidgetInfo(
                                key="state",
                                widget_id=WidgetId(0),
                                option_value={},
                                data_schema={},
                            )
                        ],
                        sub_widgets_mapping=None,
                        form_versions=[],
                    ),
                    WidgetName(
                        widget_label="城市",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): WidgetDetail(
                        widget_infos=[
                            WidgetInfo(
                                key="city",
                                widget_id=WidgetId(0),
                                option_value={},
                                data_schema={},
                            )
                        ],
                        sub_widgets_mapping=None,
                        form_versions=[],
                    ),
                    WidgetName(
                        widget_label="区县",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): WidgetDetail(
                        widget_infos=[
                            WidgetInfo(
                                key="zone",
                                widget_id=WidgetId(0),
                                option_value={},
                                data_schema={},
                            )
                        ],
                        sub_widgets_mapping=None,
                        form_versions=[],
                    ),
                    WidgetName(
                        widget_label="乡镇",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): WidgetDetail(
                        widget_infos=[
                            WidgetInfo(
                                key="town",
                                widget_id=WidgetId(0),
                                option_value={},
                                data_schema={},
                            )
                        ],
                        sub_widgets_mapping=None,
                        form_versions=[],
                    ),
                    WidgetName(
                        widget_label="姓名",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): WidgetDetail(
                        widget_infos=[
                            WidgetInfo(
                                key="name",
                                widget_id=WidgetId(0),
                                option_value={},
                                data_schema={},
                            )
                        ],
                        sub_widgets_mapping=None,
                        form_versions=[],
                    ),
                    WidgetName(
                        widget_label="手机号",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): WidgetDetail(
                        widget_infos=[
                            WidgetInfo(
                                key="mobile",
                                widget_id=WidgetId(0),
                                option_value={},
                                data_schema={},
                            )
                        ],
                        sub_widgets_mapping=None,
                        form_versions=[],
                    ),
                    WidgetName(
                        widget_label="详细地址",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): WidgetDetail(
                        widget_infos=[
                            WidgetInfo(
                                key="address",
                                widget_id=WidgetId(0),
                                option_value={},
                                data_schema={},
                            )
                        ],
                        sub_widgets_mapping=None,
                        form_versions=[],
                    ),
                }
            case ColumnSplitMode.PART_SPLIT:
                return {
                    WidgetName(
                        widget_label="姓名",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): WidgetDetail(
                        widget_infos=[
                            WidgetInfo(
                                key="name",
                                widget_id=WidgetId(0),
                                option_value={},
                                data_schema={},
                            )
                        ],
                        sub_widgets_mapping=None,
                        form_versions=[],
                    ),
                    WidgetName(
                        widget_label="手机号",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): WidgetDetail(
                        widget_infos=[
                            WidgetInfo(
                                key="mobile",
                                widget_id=WidgetId(0),
                                option_value={},
                                data_schema={},
                            )
                        ],
                        sub_widgets_mapping=None,
                        form_versions=[],
                    ),
                    WidgetName(
                        widget_label="详细地址",
                        widget_type=CommonWidgetType.full_address.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): WidgetDetail(
                        widget_infos=[
                            WidgetInfo(
                                key="full_address",
                                widget_id=WidgetId(0),
                                option_value={},
                                data_schema={},
                            )
                        ],
                        sub_widgets_mapping=None,
                        form_versions=[],
                    ),
                }
            case ColumnSplitMode.NOT_SPLIT:
                return None

    @staticmethod
    def get_sub_widget_items_for_upload() -> dict:
        return {
            **PreDefinedWidgetItems.get_serial_widget_item(),
            **{
                WidgetName(
                    widget_label="链接",
                    widget_type=CommonWidgetType.url.value,
                    widget_data_type=WidgetDataType.STRING,
                ): WidgetDetail(
                    widget_infos=[
                        WidgetInfo(
                            key="url",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        )
                    ],
                    sub_widgets_mapping=None,
                    form_versions=[],
                ),
            },
        }

    @staticmethod
    def get_serial_widget_item() -> dict:
        return {
            WidgetName(
                widget_label="序列",
                widget_type=CommonWidgetType.serial.value,
                widget_data_type=WidgetDataType.STRING,
            ): WidgetDetail(
                widget_infos=[
                    WidgetInfo(
                        key="serial",
                        widget_id=WidgetId(0),
                        option_value={"label": "序列"},
                        data_schema={},
                    )
                ],
                sub_widgets_mapping=None,
                form_versions=[],
            ),
        }

    @staticmethod
    def get_sub_widget_items_for_enum(
        enum_widget_type: WidgetType,
        enum_widget_infos: list[WidgetInfo],
    ) -> dict:
        max_level = max([((widget_info.option_value or {}).get("level") or 1) for widget_info in enum_widget_infos])

        match enum_widget_type:
            case CommonWidgetType.order_question.value:
                sub_widget_type = CommonWidgetType.order_question_level.value
                serial_widget_item = PreDefinedWidgetItems.get_serial_widget_item()
            case CommonWidgetType.select_tile.value:
                sub_widget_type = CommonWidgetType.select_tile_level.value
                serial_widget_item = PreDefinedWidgetItems.get_serial_widget_item()
            case CommonWidgetType.select_dropdown.value:
                sub_widget_type = CommonWidgetType.select_dropdown_level.value
                serial_widget_item = PreDefinedWidgetItems.get_serial_widget_item()
            case CommonWidgetType.radio_tile.value:
                sub_widget_type = CommonWidgetType.radio_tile_level.value
                serial_widget_item = {}
            case CommonWidgetType.radio_dropdown.value:
                sub_widget_type = CommonWidgetType.radio_dropdown_level.value
                serial_widget_item = {}
            case _:
                return {}
        return {
            **serial_widget_item,
            **{
                WidgetName(
                    widget_label=f"{ENUM_LEVEL_PREFIX}{level+1}",
                    widget_type=sub_widget_type,
                    widget_data_type=WidgetDataType.ENUM,
                ): WidgetDetail(
                    widget_infos=[
                        WidgetInfo(
                            key="value",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        )
                    ],
                    sub_widgets_mapping=None,
                    form_versions=[],
                )
                for level in range(max_level)
            },
        }

    @staticmethod
    def get_sub_widget_items_for_rate() -> dict:
        return {
            **PreDefinedWidgetItems.get_serial_widget_item(),
            **{
                WidgetName(
                    widget_label="选项类型",
                    widget_type=WidgetType(RATE_ITEM_TYPE),
                    widget_data_type=WidgetDataType.STRING,
                ): WidgetDetail(
                    widget_infos=[
                        WidgetInfo(
                            key="type",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        )
                    ],
                    sub_widgets_mapping=None,
                    form_versions=[],
                ),
                WidgetName(
                    widget_label="分值",
                    widget_type=WidgetType(RATE_ITEM_VALUE),
                    widget_data_type=WidgetDataType.STRING,
                ): WidgetDetail(
                    widget_infos=[
                        WidgetInfo(
                            key="value",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        )
                    ],
                    sub_widgets_mapping=None,
                    form_versions=[],
                ),
            },
        }


class PreDefinedWidget:
    @staticmethod
    def get_widget_title_for_business_order_id() -> AccurateWidgetTitle:
        """
        获取工单 ID 的 AccurateWidgetTitle。
        """
        return AccurateWidgetTitle(
            metas=[
                AccurateWidgetMeta(
                    widget_name=WidgetName(
                        widget_label="工单 ID",
                        widget_type=WidgetType("id"),
                        widget_data_type=WidgetDataType.STRING,
                    ),
                    widget_detail=AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="id",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                )
            ],
            show_names=[str(PreDefinedTitle.BUSINESS_ORDER_ID)],
        )

    @staticmethod
    def get_widget_info_for_table_serial() -> WidgetInfo:
        return WidgetInfo(
            key="serial",
            widget_id=WidgetId(0),
            option_value={"label": "序列"},
            data_schema={},
        )

    @staticmethod
    def get_widget_info_for_array_serial() -> WidgetInfo:
        return WidgetInfo(
            key=f"serial{ARRAY_ITEM_SUFFIX}",
            widget_id=WidgetId(0),
            option_value={"label": "序列"},
            data_schema={},
        )

    @staticmethod
    def get_sub_widget_items_for_order(order_widget_info: WidgetInfo) -> dict[WidgetName, AccurateWidgetDetail]:
        sub_widget_items = {
            **PreDefinedWidget.get_serial_widget_item(),
            **{
                WidgetName(
                    widget_label="订单号",
                    widget_type=CommonWidgetType.text.value,
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="tid",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
            },
        }
        if (order_widget_info.option_value or {}).get("mode") == OrderMode.child:
            sub_widget_items.update(
                {
                    WidgetName(
                        widget_label="子订单号",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="oid",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                },
            )
        return sub_widget_items

    @staticmethod
    def get_sub_widget_items_for_payment_method() -> dict[WidgetName, AccurateWidgetDetail]:
        return {
            WidgetName(
                widget_label="收款方式",
                widget_type=CommonWidgetType.payment_method_item_method.value,
                widget_data_type=WidgetDataType.STRING,
            ): AccurateWidgetDetail(
                widget_info=WidgetInfo(
                    key="payment_method",
                    widget_id=WidgetId(0),
                    option_value={},
                    data_schema={},
                ),
                sub_widgets_mapping=None,
            ),
            WidgetName(
                widget_label="订单号",
                widget_type=CommonWidgetType.text.value,
                widget_data_type=WidgetDataType.STRING,
            ): AccurateWidgetDetail(
                widget_info=WidgetInfo(
                    key="tid",
                    widget_id=WidgetId(0),
                    option_value={},
                    data_schema={},
                ),
                sub_widgets_mapping=None,
            ),
            WidgetName(
                widget_label="支付宝交易号",
                widget_type=CommonWidgetType.text.value,
                widget_data_type=WidgetDataType.STRING,
            ): AccurateWidgetDetail(
                widget_info=WidgetInfo(
                    key="alipay_no",
                    widget_id=WidgetId(0),
                    option_value={},
                    data_schema={},
                ),
                sub_widgets_mapping=None,
            ),
            WidgetName(
                widget_label="真实姓名",
                widget_type=CommonWidgetType.text.value,
                widget_data_type=WidgetDataType.STRING,
            ): AccurateWidgetDetail(
                widget_info=WidgetInfo(
                    key="receive_name",
                    widget_id=WidgetId(0),
                    option_value={},
                    data_schema={},
                ),
                sub_widgets_mapping=None,
            ),
            WidgetName(
                widget_label="支付宝账号",
                widget_type=CommonWidgetType.text.value,
                widget_data_type=WidgetDataType.STRING,
            ): AccurateWidgetDetail(
                widget_info=WidgetInfo(
                    key="receive_account",
                    widget_id=WidgetId(0),
                    option_value={},
                    data_schema={},
                ),
                sub_widgets_mapping=None,
            ),
        }

    @staticmethod
    def get_sub_widget_items_for_address(
        column_split_mode: ColumnSplitMode,
    ) -> dict[WidgetName, AccurateWidgetDetail] | None:
        match column_split_mode:
            case ColumnSplitMode.ALL_SPLIT:
                return {
                    WidgetName(
                        widget_label="省份",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="state",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                    WidgetName(
                        widget_label="城市",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="city",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                    WidgetName(
                        widget_label="区县",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="zone",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                    WidgetName(
                        widget_label="乡镇",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="town",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                    WidgetName(
                        widget_label="姓名",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="name",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                    WidgetName(
                        widget_label="手机号",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="mobile",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                    WidgetName(
                        widget_label="详细地址",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="address",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                }
            case ColumnSplitMode.PART_SPLIT:
                return {
                    WidgetName(
                        widget_label="姓名",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="name",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                    WidgetName(
                        widget_label="手机号",
                        widget_type=CommonWidgetType.text.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="mobile",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                    WidgetName(
                        widget_label="详细地址",
                        widget_type=CommonWidgetType.full_address.value,
                        widget_data_type=WidgetDataType.STRING,
                    ): AccurateWidgetDetail(
                        widget_info=WidgetInfo(
                            key="full_address",
                            widget_id=WidgetId(0),
                            option_value={},
                            data_schema={},
                        ),
                        sub_widgets_mapping=None,
                    ),
                }
            case ColumnSplitMode.NOT_SPLIT:
                return None

    @staticmethod
    def get_sub_widget_items_for_upload() -> dict[WidgetName, AccurateWidgetDetail]:
        return {
            **PreDefinedWidget.get_serial_widget_item(),
            **{
                WidgetName(
                    widget_label="链接",
                    widget_type=CommonWidgetType.url.value,
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="url",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
            },
        }

    @staticmethod
    def get_serial_widget_item() -> dict:
        return {
            WidgetName(
                widget_label="序列",
                widget_type=CommonWidgetType.serial.value,
                widget_data_type=WidgetDataType.STRING,
            ): AccurateWidgetDetail(
                widget_info=WidgetInfo(
                    key="serial",
                    widget_id=WidgetId(0),
                    option_value={"label": "序列"},
                    data_schema={},
                ),
                sub_widgets_mapping=None,
            ),
        }

    @staticmethod
    def get_sub_widget_items_for_enum(
        enum_widget_type: WidgetType,
        enum_widget_info: WidgetInfo,
    ) -> dict[WidgetName, AccurateWidgetDetail] | None:
        max_level = (enum_widget_info.option_value or {}).get("level") or 1

        if not isinstance(max_level, int) or max_level <= 0:
            return None

        match enum_widget_type:
            case CommonWidgetType.order_question.value:
                sub_widget_type = CommonWidgetType.order_question_level.value
                serial_widget_item = PreDefinedWidget.get_serial_widget_item()
            case CommonWidgetType.select_tile.value:
                sub_widget_type = CommonWidgetType.select_tile_level.value
                serial_widget_item = PreDefinedWidget.get_serial_widget_item()
            case CommonWidgetType.select_dropdown.value:
                sub_widget_type = CommonWidgetType.select_dropdown_level.value
                serial_widget_item = PreDefinedWidget.get_serial_widget_item()
            case CommonWidgetType.radio_tile.value:
                sub_widget_type = CommonWidgetType.radio_tile_level.value
                serial_widget_item = {}
            case CommonWidgetType.radio_dropdown.value:
                sub_widget_type = CommonWidgetType.radio_dropdown_level.value
                serial_widget_item = {}
            case _:
                return {}

        return {
            **serial_widget_item,
            **{
                WidgetName(
                    widget_label=f"{ENUM_LEVEL_PREFIX}{level+1}",
                    widget_type=sub_widget_type,
                    widget_data_type=WidgetDataType.ENUM,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="value",
                        widget_id=WidgetId(0),
                        option_value=(enum_widget_info.option_value or {}),
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                )
                for level in range(max_level)
            },
        }

    @staticmethod
    def get_sub_widget_items_for_rate() -> dict[WidgetName, AccurateWidgetDetail]:
        return {
            **PreDefinedWidget.get_serial_widget_item(),
            **{
                WidgetName(
                    widget_label="选项类型",
                    widget_type=WidgetType(RATE_ITEM_TYPE),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="type",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="分值",
                    widget_type=WidgetType(RATE_ITEM_VALUE),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="value",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
            },
        }


class DataConvertRules:
    @staticmethod
    def get_payment_method_value_to_readable_string_mapping() -> dict[int, str]:
        return {
            1: "通过淘宝会员转账",
            2: "通过填写支付宝账号转账",
            3: "通过淘宝订单号转账",
        }

    @staticmethod
    def get_payment_method_readable_string_to_value_mapping() -> dict[str, int]:
        return {
            "通过淘宝会员转账": 1,
            "通过填写支付宝账号转账": 2,
            "通过淘宝订单号转账": 3,
        }

    @staticmethod
    def get_rate_type_value_to_readable_string_mapping() -> dict[str, str]:
        return {"flower": "花花", "star": "星星"}

    @staticmethod
    def get_rate_type_readable_string_to_value_mapping() -> dict[str, str]:
        return {
            "花花": "flower",
            "星星": "star",
        }

    @staticmethod
    def get_rate_star_value_to_readable_string_mapping() -> dict[int, str]:
        return {
            1: "非常差",
            2: "差",
            3: "一般",
            4: "好",
            5: "非常好",
        }

    @staticmethod
    def get_rate_start_readable_string_to_value_mapping() -> dict[str, int]:
        return {
            "非常差": 1,
            "差": 2,
            "一般": 3,
            "好": 4,
            "非常好": 5,
        }

    @staticmethod
    def get_rate_flower_value_to_readable_string_mapping() -> dict[int, str]:
        return {1: "好评", 2: "中评", 3: "差评"}

    @staticmethod
    def get_rate_flower_readable_string_to_mapping() -> dict[str, int]:
        return {"好评": 1, "中评": 2, "差评": 3}

    @staticmethod
    def get_boolean_value_to_readable_string_mapping_by_widget_meta(
        widget_meta: AccurateWidgetMeta,
    ) -> dict[bool, str]:
        if (
            "trueLabel" not in widget_meta.widget_detail.widget_info.option_value
            or "falseLabel" not in widget_meta.widget_detail.widget_info.option_value
            or not isinstance(widget_meta.widget_detail.widget_info.option_value.get("trueLabel"), str)
            or not isinstance(
                widget_meta.widget_detail.widget_info.option_value.get("falseLabel"),
                str,
            )
        ):
            return {True: "是", False: "否"}
        else:
            return {
                True: widget_meta.widget_detail.widget_info.option_value["trueLabel"],
                False: widget_meta.widget_detail.widget_info.option_value["falseLabel"],
            }

    @staticmethod
    def get_boolean_readable_string_to_value_mapping_by_widget_meta(
        widget_meta: AccurateWidgetMeta,
    ) -> dict[str, bool]:
        if (
            "trueLabel" not in widget_meta.widget_detail.widget_info.option_value
            or "falseLabel" not in widget_meta.widget_detail.widget_info.option_value
            or not isinstance(widget_meta.widget_detail.widget_info.option_value.get("trueLabel"), str)
            or not isinstance(
                widget_meta.widget_detail.widget_info.option_value.get("falseLabel"),
                str,
            )
        ):
            return {
                "是": True,
                "否": False,
            }
        else:
            true_label = widget_meta.widget_detail.widget_info.option_value["trueLabel"]
            false_label = widget_meta.widget_detail.widget_info.option_value["falseLabel"]
            return {
                true_label: True,
                false_label: False,
            }

    @staticmethod
    def get_enum_value_to_readable_string_mapping_by_widget_meta(
        widget_meta: AccurateWidgetMeta,
    ) -> dict[str, str]:
        if (
            "level" not in widget_meta.widget_detail.widget_info.option_value
            or "options" not in widget_meta.widget_detail.widget_info.option_value
        ):
            return {}
        level: int = widget_meta.widget_detail.widget_info.option_value["level"]
        options: list[dict[str, str]] = widget_meta.widget_detail.widget_info.option_value["options"]
        if level > 1 or len(options) == 0:
            return {}

        convert_rules: dict[str, str] = {}
        for option in options:
            convert_rules[option["label"]] = option["value"]
        return convert_rules

    @staticmethod
    def get_enum_readable_string_to_value_mapping_by_widget_meta(
        widget_meta: AccurateWidgetMeta,
    ) -> dict[str, str]:
        if (
            "level" not in widget_meta.widget_detail.widget_info.option_value
            or "options" not in widget_meta.widget_detail.widget_info.option_value
        ):
            return {}
        level: int = widget_meta.widget_detail.widget_info.option_value["level"]
        options: list[dict[str, str]] = widget_meta.widget_detail.widget_info.option_value["options"]
        if level > 1 or len(options) == 0:
            return {}

        convert_rules: dict[str, str] = {}
        for option in options:
            convert_rules[option["value"]] = option["label"]
        return convert_rules

    @staticmethod
    def get_product_type_value_to_readable_string_mapping() -> dict[str, str]:
        return {"SINGLE": "单品", "PARENT": "组合商品", "CHILD": "组合商品子商品"}

    @staticmethod
    def get_product_type_readable_string_to_value_mapping() -> dict[str, str]:
        return {
            "单品": "SINGLE",
            "组合商品": "PARENT",
            "组合商品子商品": "CHILD",
        }


NO_FORM_WIDGET_TITLES: list[dict[str, list[str]]] = [
    {"widget_title": ["工单 ID"], "form_names": []},
    {"widget_title": ["起始步骤", "创建时间"], "form_names": []},
    {"widget_title": ["起始步骤", "创建人归属组（平台）"], "form_names": []},
    {"widget_title": ["起始步骤", "创建人(平台)"], "form_names": []},
    {"widget_title": ["起始步骤", "当前步骤处理人(飞梭)"], "form_names": []},
    {"widget_title": ["起始步骤", "当前步骤执行人归属组（飞梭）"], "form_names": []},
    {"widget_title": ["起始步骤", "当前步骤执行人归属组（平台）"], "form_names": []},
    {"widget_title": ["起始步骤", "当前步骤处理人(平台)"], "form_names": []},
    {"widget_title": ["起始步骤", "异常原因"], "form_names": []},
    {"widget_title": ["起始步骤", "当前步骤"], "form_names": []},
    {"widget_title": ["起始步骤", "预计完结时间"], "form_names": []},
    {"widget_title": ["起始步骤", "工单结束时间"], "form_names": []},
    {"widget_title": ["起始步骤", "创建人(飞梭)"], "form_names": []},
    {"widget_title": ["起始步骤", "创建人归属组（飞梭）"], "form_names": []},
    {"widget_title": ["起始步骤", "最近更新人(飞梭)"], "form_names": []},
    {"widget_title": ["起始步骤", "最近更新人归属组（飞梭）"], "form_names": []},
    {"widget_title": ["起始步骤", "工单模板名称"], "form_names": []},
    {"widget_title": ["起始步骤", "打款凭证", "序列"], "form_names": []},
    {"widget_title": ["起始步骤", "打款凭证", "链接"], "form_names": []},
    {"widget_title": ["起始步骤", "所属店铺"], "form_names": []},
    {"widget_title": ["起始步骤", "所属平台"], "form_names": []},
    {"widget_title": ["起始步骤", "工单状态"], "form_names": []},
    {"widget_title": ["起始步骤", "最近更新时间"], "form_names": []},
    {"widget_title": ["起始步骤", "最近更新人(平台)"], "form_names": []},
    {"widget_title": ["起始步骤", "最近更新人归属组（平台）"], "form_names": []},
]
