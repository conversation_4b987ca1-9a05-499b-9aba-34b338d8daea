from copy import deepcopy
from datetime import datetime

from loguru import logger
from pydantic.error_wrappers import ValidationError

from robot_processor.business_order.struct.entities import ImportFileResult
from robot_processor.business_order.struct.enums import ImportFileStatus
from robot_processor.business_order.struct.enums import ImportStatus
from robot_processor.business_order.struct.helpers.batch_update_bos_helper import Batch<PERSON><PERSON><PERSON>B<PERSON>H<PERSON>per
from robot_processor.business_order.struct.helpers.excel_output_fialed_data_helper import ExcelOutputFailedDataHelper
from robot_processor.business_order.struct.helpers.oss_helper import OssHelper
from robot_processor.business_order.struct.models import ImportTask
from robot_processor.business_order.struct.schemas import ImportTaskExtraData
from robot_processor.business_order.struct.utils import update_import_task
from robot_processor.constants import TASK_QUEUE_BACKGROUND_TASK
from robot_processor.constants import TaskPriority
from robot_processor.ext import db
from robot_processor.ext import task_queue


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.HIGH, time_limit=1000 * 60 * 60 * 3)
def import_excel_data_to_business_orders(import_task_id: int) -> None:
    try:
        # 查询导入任务。
        import_task: ImportTask | None = ImportTask.query.filter(ImportTask.id == import_task_id).first()
        if import_task is None:
            return None
        if import_task.status != ImportStatus.INIT:
            return None
        import_task.status = ImportStatus.RUNNING
        db.session.commit()

        logger.info(f"开始处理导入任务: {import_task_id}")

        # 额外数据校验。
        try:
            import_task_extra_data = ImportTaskExtraData.parse_obj(import_task.extra_data)
        except ValidationError as e:
            logger.exception(f"导入任务额外数据解析失败, import_task_id: {import_task_id}, error: {e}")
            update_import_task(
                import_task_id=import_task_id,
                status=ImportStatus.FAILED,
                message="导入任务额外数据解析失败",
            )
            return None

        # 初始化 Helper
        file_container = import_task_extra_data.file_container
        operator = import_task_extra_data.operator
        is_admin = import_task_extra_data.is_admin

        batch_update_bos_helper = BatchUpdateBosHelper(
            file_container=file_container,
            operator=operator,
            is_admin=is_admin,
        )

        try:
            # 对文件进行处理。
            exceptional_data_container, success_business_order_ids = batch_update_bos_helper.handle()
        except Exception as e:
            logger.exception(f"批量更新时发生未知异常: {e}")
            update_import_task(
                import_task_id=import_task_id,
                status=ImportStatus.FAILED,
                message="批量更新时发生未知异常",
                success_business_order_ids=batch_update_bos_helper.success_business_order_ids,
                exceptional_data_container=batch_update_bos_helper.exceptional_data_container,
            )
            return None

        if exceptional_data_container.is_empty:
            logger.info("任务已完成")
            update_import_task(
                import_task_id=import_task_id,
                status=ImportStatus.FINISHED,
                message="所有文件更新完成，无异常",
                finished_time=datetime.now(),
                success_business_order_ids=success_business_order_ids,
            )
            return None
        else:
            update_import_task(
                import_task_id=import_task_id,
                status=ImportStatus.EXPORTING_FAILED_DATA,
                message="批量更新结束，正在生成异常文件",
                success_business_order_ids=success_business_order_ids,
                exceptional_data_container=exceptional_data_container,
            )

        logger.info("准备生成异常文件")

        exceptional_file_urls: list[ImportFileResult] = deepcopy(exceptional_data_container.invalid_files)

        message: str = "异常文件已生成"

        excel_output_failed_data_helper = ExcelOutputFailedDataHelper(
            invalid_worksheets=exceptional_data_container.invalid_worksheets,
            failed_business_order_infos=exceptional_data_container.failed_business_order_infos,
        )
        for original_file_name, original_file_url, file in excel_output_failed_data_helper.handle():
            if file is None:
                logger.error("基于原始文件切割异常数据失败, url: {}".format(original_file_url))
                continue
            file_url, err = OssHelper.upload_single_file_to_oss(
                file,
                file_name=original_file_name,
            )
            if err is not None:
                logger.error(err)
                message = "部分异常文件上传失败"
            else:
                assert file_url is not None
                exceptional_file_urls.append(
                    ImportFileResult(
                        file_url=file_url,
                        file_name=original_file_name,
                        status=ImportFileStatus.PARTIAL_SUCCESS,
                    )
                )

        logger.info("异常文件已生成")

        update_import_task(
            import_task_id=import_task_id,
            status=ImportStatus.FINISHED,
            message=message,
            exceptional_file_urls=exceptional_file_urls,
            finished_time=datetime.now(),
        )
        return None
    except Exception as e:
        logger.exception(f"处理导入任务 {import_task_id} 时发生未知异常: {e}")
        update_import_task(
            import_task_id=import_task_id,
            status=ImportStatus.FAILED,
            message="处理导入任务时发生未知异常",
        )
        return None
