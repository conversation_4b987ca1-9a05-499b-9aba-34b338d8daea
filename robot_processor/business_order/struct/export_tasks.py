from copy import deepcopy

from loguru import logger
from pydantic.error_wrappers import ValidationError

from robot_processor.business_order.struct.enums import ExportScene
from robot_processor.business_order.struct.enums import ExportStatus
from robot_processor.business_order.struct.models import ExportTask
from robot_processor.business_order.struct.schemas import ExportTaskParams
from robot_processor.business_order.struct.tasks.export_business_orders_to_worksheet import \
    export_business_orders_to_worksheet
from robot_processor.business_order.struct.tasks.export_full_business_orders_task import export_full_business_orders
from robot_processor.business_order.struct.utils import update_export_task
from robot_processor.constants import TASK_QUEUE_BACKGROUND_TASK
from robot_processor.constants import TaskPriority
from robot_processor.ext import db
from robot_processor.ext import task_queue
from robot_processor.task_center.utils import generate_business_order_query


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.HIGH, time_limit=1000 * 60 * 60 * 3)
def export_excel_file(export_task_id: int) -> None:
    # 根据导出任务 ID 查询导出任务
    export_task: ExportTask | None = ExportTask.query.filter(ExportTask.id == export_task_id).first()
    if export_task is None:
        return None
    if export_task.status != ExportStatus.INIT:
        return None
    export_task.status = ExportStatus.RUNNING
    file_urls = deepcopy(export_task.file_urls)
    db.session.commit()

    logger.info(f"开始处理导出任务: {export_task_id}")

    # 对导出任务存储的导出参数进行解析。
    try:
        export_params = ExportTaskParams.parse_obj(export_task.params)
    except ValidationError as e:
        logger.exception(f"导出参数解析失败, export_task_id: {export_task_id}, error: {e}")
        update_export_task(
            export_task_id=export_task_id,
            status=ExportStatus.FAILED,
            message="导出参数解析失败",
        )
        return None
    # 参数校验
    if export_params.org_id is None or export_params.user_id is None:
        logger.error(f"缺少租户或用户信息, export_task_id: {export_task_id}")
        update_export_task(
            export_task_id=export_task_id,
            status=ExportStatus.FAILED,
            message="缺少租户或用户信息",
        )
        return None
    if export_params.form_id is None and export_params.export_scene == ExportScene.EDIT:
        update_export_task(
            export_task_id=export_task_id,
            status=ExportStatus.FAILED,
            message="未传入工单模板信息",
        )
        return None

    # 工单查询
    main_query, plain_query, err = generate_business_order_query(
        export_params.org_id,
        export_params.user_id,
        export_params,
        ignore_too_large_error=True,
    )
    if err is not None:
        logger.error(f"工单查询失败: {err}")
        update_export_task(
            export_task_id=export_task_id,
            status=ExportStatus.FAILED,
            message=err,
        )
        return None

    logger.info("工单总数: {}".format(main_query.count()))

    if export_params.export_scene == ExportScene.VIEW:
        logger.info("开始执行全量数据的导出")
        return export_full_business_orders(
            business_orders_query=main_query,
            export_task_id=export_task_id,
            export_params=export_params,
            file_urls=file_urls,
        )
    else:
        logger.info("开始执行步骤数据的导出")
        return export_business_orders_to_worksheet(
            business_orders_query=main_query,
            export_task_id=export_task_id,
            export_params=export_params,
            file_urls=file_urls,
        )
