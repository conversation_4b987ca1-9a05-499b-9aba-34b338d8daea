from flask import Blueprint
from flask import jsonify
from flask_sqlalchemy.pagination import Pagination
from sqlalchemy import func as sa_func

from robot_processor.business_order.struct.contants import BEGIN_STEP_NAME
from robot_processor.business_order.struct.datas import NO_FORM_WIDGET_TITLES
from robot_processor.business_order.struct.enums import ExportStatus
from robot_processor.business_order.struct.export_tasks import export_excel_file
from robot_processor.business_order.struct.helpers.export_helper import ExportHelper
from robot_processor.business_order.struct.helpers.export_helper import WidgetTitle
from robot_processor.business_order.struct.models import ExportTask
from robot_processor.business_order.struct.schemas import ExportTaskParams
from robot_processor.business_order.struct.schemas import ExportTaskQuery
from robot_processor.business_order.struct.schemas import GetWidgetTitlesArgument
from robot_processor.currents import g
from robot_processor.decorators import shop_required
from robot_processor.ext import db
from robot_processor.validator import validate

api = Blueprint("export-api", __name__)


@api.get("/widget_titles")
@shop_required
@validate
def get_widget_titles(query: GetWidgetTitlesArgument):
    assert g.login_user_detail

    result: list[dict[str, list[str]]] = []

    # 不传工单模板就返回一个默认的表头。
    if not query.form_ids:
        result = NO_FORM_WIDGET_TITLES
    else:
        export_helper = ExportHelper(form_ids=query.form_ids, column_split_mode=query.column_split_mode)
        step_name_to_widget_titles_mapping: dict[
            str, list[WidgetTitle]
        ] = export_helper.compute_widget_titles_for_steps()
        widget_title_names: list[dict[str, list[str]]] = [
            {"widget_title": ["工单 ID"], "form_names": []},
        ]

        for step_name, widget_titles in step_name_to_widget_titles_mapping.items():
            for widget_title in widget_titles:
                if widget_title.get_first_show_name() == BEGIN_STEP_NAME:
                    form_names = []
                else:
                    form_names_set = {
                        (form_version.meta or {}).get("name") for form_version in widget_title.form_versions.values()
                    }
                    form_names = [form_name for form_name in form_names_set if isinstance(form_name, str)]
                widget_title_names.append(
                    {
                        "widget_title": widget_title.actually_show_names,
                        "form_names": form_names,
                    }
                )
        result = widget_title_names

    return (
        jsonify(
            success=True,
            widget_title_names=result,
        ),
        200,
    )


@api.post("/export_tasks")
@shop_required
@validate
def create_export_task(body: ExportTaskParams):
    """
    新建导出任务。
    """
    assert g.shop.org_id

    body.org_id = g.shop.org_id
    body.user_id = g.auth.user_id

    export_task_params = body.dict()

    export_task = ExportTask(
        org_id=body.org_id,
        creator_id=body.user_id,
        creator_nickname=g.auth.login_user_nick,
        params=export_task_params,
        status=ExportStatus.INIT,
    )
    db.session.add(export_task)
    db.session.commit()

    export_excel_file.send_with_options(args=(export_task.id,))

    return jsonify(success=True, export_task_id=export_task.id)


@api.get("/export_tasks")
@shop_required
@validate
def get_export_tasks(query: ExportTaskQuery):
    """
    查询租户下的所有导出任务。
    """
    assert g.login_user_detail

    org_id = g.shop.org_id

    if query.export_scene:
        export_tasks_pagination: Pagination = (
            ExportTask.query.filter(ExportTask.org_id == org_id)
            .filter(sa_func.json_extract(ExportTask.params, "$.export_scene") == query.export_scene)
            .order_by(ExportTask.id.desc())
            .paginate(
                page=query.page_no,
                per_page=query.page_size,
            )
        )
    else:
        export_tasks_pagination = (
            ExportTask.query.filter(ExportTask.org_id == org_id)
            .order_by(ExportTask.id.desc())
            .paginate(
                page=query.page_no,
                per_page=query.page_size,
            )
        )

    return jsonify(
        success=True,
        total=export_tasks_pagination.total,
        data=[export_task.to_data() for export_task in export_tasks_pagination.items],
    )


@api.get("/export_tasks/<int:export_task_id>")
@shop_required
@validate
def get_export_task(export_task_id: int):
    """
    查看单个导出任务详情。
    """
    export_task: ExportTask | None = ExportTask.query.filter(ExportTask.id == export_task_id).first()
    if export_task is None:
        return jsonify(success=False, message="未找到导出任务"), 200

    return jsonify(
        success=True,
        data=export_task.to_data(),
    )
