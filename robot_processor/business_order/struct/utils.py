import datetime

from sqlalchemy.orm.attributes import flag_modified

from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.business_order.struct.entities import ExceptionalDataContainer
from robot_processor.business_order.struct.entities import ImportFileResult
from robot_processor.business_order.struct.enums import ExportStatus
from robot_processor.business_order.struct.enums import ImportStatus
from robot_processor.business_order.struct.models import ExportTask
from robot_processor.business_order.struct.models import ImportTask
from robot_processor.business_order.struct.schemas import ImportTaskCursor
from robot_processor.business_order.struct.schemas import ImportTaskExtraData
from robot_processor.client import aliyun_addrp_client
from robot_processor.client.aliyun_addrp import ExtractExpressResult
from robot_processor.db import in_transaction
from robot_processor.ext import db
from robot_processor.form.models import FormVersion
from robot_processor.form.models import Step


def update_cursor_for_import_task(
    import_task: ImportTask,
    cursor: ImportTaskCursor,
) -> None:
    # 更新导入任务的游标信息。
    import_task.cursor = cursor.dict()
    flag_modified(import_task, "cursor")
    db.session.commit()


def update_extra_data_for_import_task(
    import_task: ImportTask,
    extra_data: ImportTaskExtraData,
) -> None:
    # 更新导入任务的额外信息
    import_task.extra_data = extra_data.dict()
    flag_modified(import_task, "extra_data")
    db.session.commit()


def get_not_current_step_names_for_business_order(
    business_order: BusinessOrder,
) -> list[str]:
    if business_order.form_id is None:
        return []
    form_versions: list[FormVersion] = (
        db.ro_session.query(FormVersion)
        .filter(FormVersion.form_id == business_order.form_id)
        .order_by(FormVersion.id)
        .all()
    )
    if len(form_versions) == 0:
        return []
    form_version_id_to_form_version_mapping: dict[int, FormVersion] = {
        form_version.id: form_version for form_version in form_versions
    }
    sorted_form_versions: list[FormVersion] = sorted(
        form_versions,
        key=lambda fv: fv.id,
        reverse=True,
    )
    latest_form_version: FormVersion = sorted_form_versions[0]

    current_step: Step | None = (
        db.ro_session.query(Step)
        .join(Job, Job.step_id == Step.id)
        .filter(
            Job.id == business_order.current_job_id,
        )
        .first()
    )
    if current_step is None:
        return []

    if business_order.form_version_id is not None:
        form_version = form_version_id_to_form_version_mapping.get(business_order.form_version_id)
        if form_version is not None:
            step_names = (
                db.ro_session.query(Step.name)
                .filter(
                    Step.id.in_(form_version.step_id),
                    Step.name != current_step.name,
                )
                .all()
            )
            return [step_name for (step_name,) in step_names]

    step_names = (
        db.ro_session.query(Step.name)
        .filter(
            Step.id.in_(latest_form_version.step_id),
            Step.name != current_step.name,
        )
        .all()
    )
    return [step_name for (step_name,) in step_names]


def update_export_task(
    export_task_id: int,
    status: ExportStatus,
    message: str,
    file_urls: list[str] | None = None,
    finished_time: datetime.datetime | None = None,
):
    with in_transaction():
        newest_export_task: ExportTask | None = (
            db.session.query(ExportTask).filter(ExportTask.id == export_task_id).first()
        )
        if newest_export_task is None:
            return None

        newest_export_task.status = status
        newest_export_task.message = message
        if file_urls:
            newest_export_task.file_urls = file_urls
            flag_modified(newest_export_task, "file_urls")
        if finished_time:
            newest_export_task.finished_time = finished_time


def update_import_task(
    import_task_id: int,
    status: ImportStatus,
    message: str,
    exceptional_file_urls: list[ImportFileResult] | None = None,
    exceptional_data_container: ExceptionalDataContainer | None = None,
    finished_time: datetime.datetime | None = None,
    success_business_order_ids: list[int] | None = None,
) -> None:
    with in_transaction():
        import_task: ImportTask | None = db.session.query(ImportTask).filter(ImportTask.id == import_task_id).first()
        if import_task is None:
            return None

        import_task.status = status
        import_task.message = message

        if exceptional_file_urls:
            import_task.exceptional_file_urls = [
                import_file_result.dict() for import_file_result in exceptional_file_urls
            ]
            flag_modified(import_task, "exceptional_file_urls")
        if exceptional_data_container:
            import_task_extra_data = ImportTaskExtraData.validate(import_task.extra_data)
            import_task_extra_data.exceptional_data_container = exceptional_data_container
            import_task.extra_data = import_task_extra_data.dict()
            flag_modified(import_task, "extra_data")
        if finished_time:
            import_task.finished_time = finished_time
        if success_business_order_ids:
            import_task_extra_data = ImportTaskExtraData.validate(import_task.extra_data)
            import_task_extra_data.success_business_order_ids = success_business_order_ids
            import_task.extra_data = import_task_extra_data.dict()
            flag_modified(import_task, "extra_data")
    return None


def extract_address(address_string: str) -> ExtractExpressResult:
    address_string = address_string.replace("\r", " ")
    address_string = address_string.replace("\n", " ")
    address_string = address_string.replace("，", ", ")
    resp = aliyun_addrp_client.extract_express_to_result(address_string)
    return resp
