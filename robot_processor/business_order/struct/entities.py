from dataclasses import dataclass
from dataclasses import field as dataclass_field
from typing import Any
from typing import NamedTuple

import pandas as pd
from openpyxl.worksheet.worksheet import Worksheet
from pydantic.fields import Field
from pydantic.main import BaseModel

from robot_processor.business_order.struct.contants import ARRAY_ITEM_SUFFIX
from robot_processor.business_order.struct.contants import FILLED_CHAR
from robot_processor.business_order.struct.contants import SPACE
from robot_processor.business_order.struct.enums import ColumnSplitMode
from robot_processor.business_order.struct.enums import CommonWidgetType
from robot_processor.business_order.struct.enums import ExportScene
from robot_processor.business_order.struct.enums import ImportFileStatus
from robot_processor.business_order.struct.enums import MultiRowsFormatter
from robot_processor.form.models import FormVersion
from robot_processor.form.models import WidgetInfo
from robot_processor.form.types import WidgetDataType
from robot_processor.form.types import WidgetType


class WidgetName(NamedTuple):
    """
    组件名
    """

    widget_label: str
    widget_type: WidgetType
    widget_data_type: WidgetDataType | None

    def has_sub_widget_items(self) -> bool:
        if self.widget_type in [
            WidgetType("order"),
            WidgetType("payment-method"),
            WidgetType("address"),
            WidgetType("upload"),
            WidgetType("rate"),
            WidgetType("order-question"),
        ]:
            return True
        if self.widget_data_type in [
            WidgetDataType.ENUM,
            WidgetDataType.TABLE,
            WidgetDataType.COLLECTION,
            WidgetDataType.ARRAY,
        ]:
            return True
        return False


@dataclass
class WidgetDetail:
    """
    存储组件的元数据信息，包括组件本身、下属子组件、组件所属工单模板版本。
    """

    widget_infos: list[WidgetInfo]
    sub_widgets_mapping: dict[WidgetName, "WidgetDetail"] | None
    form_versions: list[FormVersion]


@dataclass
class WidgetMeta:
    """
    该数据结构用于记录组件当作表头的子项信息，基于当前组件名，以及其对应的组件。
    """

    widget_name: WidgetName
    widget_detail: WidgetDetail

    def get_possible_widget_info_keys(self) -> set[str]:
        return {widget_info.key for widget_info in self.widget_detail.widget_infos}

    def is_item(self) -> bool:
        possible_widget_keys = self.get_possible_widget_info_keys()
        for possible_widget_key in possible_widget_keys:
            if possible_widget_key.endswith(ARRAY_ITEM_SUFFIX):
                return True
        return False

    def is_serial(self) -> bool:
        possible_widget_keys = self.get_possible_widget_info_keys()
        for possible_widget_key in possible_widget_keys:
            if possible_widget_key in ["serial", "serial_ITEM"]:
                return True
        return False

    def get_widget_value_from_dict(self, data: dict) -> list[Any]:
        for widget_key in self.get_possible_widget_info_keys():
            if widget_value := data.get(widget_key):
                return widget_value if isinstance(widget_value, list) else [widget_value]
        return [None]


@dataclass
class WidgetTitle:
    metas: list[WidgetMeta]
    show_names: list[str] = dataclass_field(default_factory=list)
    form_versions: dict[int, FormVersion] = dataclass_field(default_factory=dict)

    def generate_show_names(self, prefix_show_name: str | None = None) -> list[str]:
        show_names = [widget_meta.widget_name.widget_label for widget_meta in self.metas]
        if prefix_show_name is not None:
            return [prefix_show_name] + show_names
        else:
            return show_names

    def get_first_show_name(self) -> str | None:
        if len(self.show_names) == 0:
            return None
        else:
            return self.show_names[0]

    @property
    def actually_show_names_length(self) -> int:
        return len(self.actually_show_names)

    @property
    def actually_show_names(self) -> list[str]:
        return [show_name for show_name in self.show_names if show_name != FILLED_CHAR]

    @property
    def filled_show_names_length(self) -> int:
        return len(self.show_names)

    def fill_show_names(self, length: int) -> None:
        if self.actually_show_names_length < length:
            self.show_names = [
                *self.actually_show_names,
                *[FILLED_CHAR for _ in range(length - self.actually_show_names_length)],
            ]

    @property
    def parent_widget_key(self) -> tuple:
        return tuple([meta.widget_name for meta in self.metas[:-1]])

    @property
    def widget_key(self) -> tuple:
        return tuple([meta.widget_name for meta in self.metas])


@dataclass
class WidgetValueLength:
    """
    提取到的组件值的长度信息。
    """

    original_widget_value_length: list[int] = dataclass_field(default_factory=list)
    resized_widget_value_length: list[int] = dataclass_field(default_factory=list)


@dataclass
class AccurateWidgetDetail:
    """
    精确的组件详情。
    """

    widget_info: WidgetInfo
    sub_widgets_mapping: dict[WidgetName, "AccurateWidgetDetail"] | None


@dataclass
class AccurateWidgetMeta:
    """
    精确的组件元数据。
    """

    widget_name: WidgetName
    widget_detail: AccurateWidgetDetail

    def extract_important_messages(
        self,
    ) -> dict:
        option_value = self.widget_detail.widget_info.option_value or {}
        important_messages: dict[str, Any] = {
            "widget_name": [str(i) for i in self.widget_name],
            # 选择类组件的信息
            "level": option_value.get("level"),
            "options": option_value.get("options"),
            # 布尔类组件的信息
            "trueLabel": option_value.get("trueLabel"),
            "falseLabel": option_value.get("falseLabel"),
        }
        return important_messages

    def get_widget_info_key(self) -> str:
        return self.widget_detail.widget_info.key

    def is_required(self) -> bool:
        """
        该层级是否为“必填”。
        """
        return bool((self.widget_detail.widget_info.option_value or {}).get("required"))

    def is_item(self) -> bool:
        """
        该层级是否为列表组件的 item 项。
        """
        if self.get_widget_info_key().endswith(ARRAY_ITEM_SUFFIX):
            return True
        return False

    def is_serial(self) -> bool:
        """
        该组件是否为复合组件、列表组件的序列。
        """
        if self.get_widget_info_key() in ["serial", "serial_ITEM"]:
            return True
        return False

    def create_empty_value(self) -> Any:
        if self.widget_name.widget_type == WidgetType("order"):
            return []
        elif self.widget_name.widget_type == WidgetType("payment-method"):
            return {}
        elif self.widget_name.widget_type == WidgetType("address"):
            return {}
        elif self.widget_name.widget_type == WidgetType("upload"):
            return []
        # 如果是列表或复合组件，则直接创建空列表。
        elif self.widget_name.widget_data_type in [
            WidgetDataType.TABLE,
            WidgetDataType.ARRAY,
        ]:
            return []
        elif self.widget_name.widget_data_type in [
            WidgetDataType.STRING,
            WidgetDataType.NUMBER,
            WidgetDataType.DATE,
            WidgetDataType.DATETIME,
            WidgetDataType.TIME,
            WidgetDataType.BOOLEAN,
        ]:
            return None
        elif self.widget_name.widget_data_type in [
            WidgetDataType.COLLECTION,
        ]:
            return {}
        elif self.widget_name.widget_data_type in [
            WidgetDataType.ENUM,
        ]:
            return []
        else:
            return None

    def get_origin_value_or_create_empty_value_from_dict(self, data: Any) -> tuple[bool, Any]:
        if isinstance(data, dict):
            # 如果该组件在 data 内存在，则返回其当前数据
            if self.get_widget_info_key() in data:
                return True, data[self.get_widget_info_key()]
            # 尚不存在的话，则需要建立空数据。
            return False, self.create_empty_value()
        else:
            return False, None

    def get_widget_value_from_dict(self, data: dict) -> list[Any]:
        if self.get_widget_info_key() in data:
            if original_widget_value := data[self.get_widget_info_key()]:
                if isinstance(original_widget_value, list):
                    return original_widget_value
                else:
                    return [original_widget_value]
        return [None]

    def get_full_address_widget_value_from_dict(self, data: dict) -> list[Any]:
        if self.get_widget_info_key() != "full_address":
            return [None]
        return [
            "{state}{city}{zone}{town}{address}".format(
                state=data.get("state") or "",
                city=data.get("city") or "",
                zone=data.get("zone") or "",
                town=data.get("town") or "",
                address=data.get("address") or "",
            )
        ]

    @staticmethod
    def convert_not_split_select_widget_value(extracted_value: list) -> list[Any]:
        final_widget_values = []
        # 提取每个多选
        for extracted_value_item in extracted_value:
            if isinstance(extracted_value_item, list):
                item_widget_value = []
                # 提取每个选项
                for select_widget_value in extracted_value_item:
                    if isinstance(select_widget_value, dict):
                        real_value = select_widget_value.get("value") or ""
                        item_widget_value.append(str(real_value))
                    else:
                        item_widget_value.append("")
                final_widget_values.append("/".join(item_widget_value))
            else:
                final_widget_values.append("")
        joined_value = "\n".join(final_widget_values) or SPACE
        return [joined_value]

    def get_not_split_select_widget_value_from_dict(self, data: dict) -> list[Any]:
        if self.widget_name.widget_type not in [
            CommonWidgetType.select_tile,
            CommonWidgetType.select_dropdown,
            CommonWidgetType.order_question,
        ]:
            return [None]
        # 提取出的原始数据
        extracted_value = data.get(self.get_widget_info_key()) or []
        return self.convert_not_split_select_widget_value(extracted_value)

    @staticmethod
    def convert_not_split_radio_widget_value(extracted_value: list) -> list[Any]:
        final_widget_values = []
        for radio_widget_value in extracted_value:
            if isinstance(radio_widget_value, dict):
                real_value = radio_widget_value.get("value") or ""
                final_widget_values.append(str(real_value))
            else:
                final_widget_values.append("")
        joined_value = "/".join(final_widget_values) or SPACE
        return [joined_value]

    def get_not_split_radio_widget_value_from_dict(self, data: dict) -> list[Any]:
        if self.widget_name.widget_type not in [
            CommonWidgetType.radio_tile,
            CommonWidgetType.radio_dropdown,
        ]:
            return [None]
        # 提取出的原始数据
        extracted_value = data.get(self.get_widget_info_key()) or []
        return self.convert_not_split_radio_widget_value(extracted_value)


@dataclass
class AccurateWidgetTitle:
    """
    精确的表头信息。
    """

    metas: list[AccurateWidgetMeta]
    show_names: list[str] = dataclass_field(default_factory=list)

    def generate_show_names(self, prefix_show_name: str | None = None) -> list[str]:
        """
        生成展示名。
        prefix_show_name 一般情况下，传递过来的都是“步骤名”。
        """
        show_names = [widget_meta.widget_name.widget_label for widget_meta in self.metas]
        if prefix_show_name is not None:
            return [prefix_show_name] + show_names
        else:
            return show_names

    def get_first_show_name(self) -> str | None:
        """
        获取首位 show_name。
        """
        if len(self.show_names) == 0:
            return None
        else:
            return self.show_names[0]

    @property
    def actually_show_names_length(self) -> int:
        return len(self.actually_show_names)

    @property
    def actually_show_names(self) -> list[str]:
        return [show_name for show_name in self.show_names if show_name != FILLED_CHAR]

    @property
    def filled_show_names_length(self) -> int:
        return len(self.show_names)

    def fill_show_names(self, length: int) -> None:
        if self.actually_show_names_length < length:
            self.show_names = [
                *self.actually_show_names,
                *[FILLED_CHAR for _ in range(length - self.actually_show_names_length)],
            ]

    @property
    def parent_widget_key(self) -> tuple[WidgetName, ...]:
        return tuple([meta.widget_name for meta in self.metas[:-1]])

    @property
    def widget_key(self) -> tuple[WidgetName, ...]:
        """
        以组件名组合成的具有层级信息的 key。
        """
        return tuple([meta.widget_name for meta in self.metas])

    @property
    def outermost_widget_info_key(self) -> str:
        """
        最外层的组件的 widget_info_key。
        """
        return self.metas[0].get_widget_info_key()

    @property
    def dropdown_values_meta(self) -> AccurateWidgetMeta | None:
        """
        拥有下拉选项的 meta.
        """
        for meta in self.metas:
            if meta.widget_name.widget_data_type in [
                WidgetDataType.BOOLEAN,
            ]:
                return meta
            elif meta.widget_name.widget_type in [
                CommonWidgetType.select_tile,
                CommonWidgetType.select_dropdown,
                CommonWidgetType.radio_tile,
                CommonWidgetType.radio_dropdown,
                CommonWidgetType.order_question,
                CommonWidgetType.rate_type,
                CommonWidgetType.rate_value,
                CommonWidgetType.payment_method_item_method,
            ]:
                return meta
        return None

    @property
    def is_read_only(self) -> bool:
        """
        该组件是否设置了只读。
        """
        for meta in self.metas:
            if meta.widget_name.widget_type in [
                CommonWidgetType.auto_number,
            ]:
                return True
            if (meta.widget_detail.widget_info.option_value or {}).get("readonly"):
                return True
        return False

    @property
    def is_editable(self) -> bool:
        if self.is_read_only:
            return False

        if all([not meta.widget_detail.widget_info.before for meta in self.metas]):
            return True

        for meta in self.metas:
            if meta.widget_detail.widget_info.before:
                if not (meta.widget_detail.widget_info.option_value or {}).get("beforeEditable"):
                    return False
        return True

    @property
    def has_required(self) -> bool:
        """
        是否存在必填。
        """
        for meta in self.metas:
            if meta.is_required():
                return True
        return False

    def to_dict(self) -> dict[str, Any]:
        meta_infos = []
        for meta in self.metas:
            meta_info = meta.extract_important_messages()
            meta_infos.append(meta_info)
        return {
            "show_names": self.actually_show_names,
            "meta_infos": meta_infos,
        }


class BusinessOrderRows(BaseModel):
    """
    工单在 excel 中的行信息。
    """

    sheet_name: str = Field(description="工单所属的 worksheet 的名称")
    start_row: int = Field(description="工单在 worksheet 中的起始行号")
    end_row: int = Field(description="工单在 worksheet 中的最终行号")


@dataclass
class WorksheetMetadata:
    multi_rows_formatter: MultiRowsFormatter
    export_scene: ExportScene
    column_split_mode: ColumnSplitMode


@dataclass
class WorksheetInfos:
    valid_worksheet_title_to_worksheet_mapping: dict[str, Worksheet] = dataclass_field(default_factory=dict)
    invalid_worksheet_title_to_reason_mapping: dict[str, str] = dataclass_field(default_factory=dict)
    worksheet_title_to_metadata_mapping: dict[str, WorksheetMetadata] = dataclass_field(default_factory=dict)


class AnalyzedWidgetValue(BaseModel):
    widget_value: list[Any]
    resized_widget_value_length: list[int]


class WidgetScopeRows(BaseModel):
    start_row: int
    end_row: int


@dataclass
class WorksheetContainer:
    worksheet_tag_to_dataframes_mapping: dict[str, list[pd.DataFrame]] = dataclass_field(default_factory=dict)
    worksheet_tag_to_business_order_rows_mapping: dict[str, list[int]] = dataclass_field(default_factory=dict)
    worksheet_tag_to_widget_titles_mapping: dict[str, list[AccurateWidgetTitle]] = dataclass_field(default_factory=dict)
    worksheet_tag_to_form_versions_mapping: dict[str, set[FormVersion]] = dataclass_field(default_factory=dict)
    rows_count_for_excel: int = dataclass_field(default=0)


@dataclass
class FullDataContainer:
    dataframes: list[pd.DataFrame] = dataclass_field(default_factory=list)
    business_order_rows: list[int] = dataclass_field(default_factory=list)
    rows_count_for_excel: int = dataclass_field(default=0)


class FailedBusinessOrderInfo(BaseModel):
    """
    处理失败的工单的内容。
    """

    id: int = Field(description="工单 ID")
    reason: str = Field(description="失败原因")
    worksheet_title: str = Field(description="工作表名称")
    file_name: str = Field(description="文件名")
    file_url: str = Field(description="文件 URL")
    start_row: int = Field(description="在 worksheet 中的开始行")
    end_row: int = Field(description="在 worksheet 中的结束行")
    worksheet_header_depth: int = Field(description="worksheet 中的表头的总体占用行数")


class InvalidWorksheet(BaseModel):
    """
    不合法的 worksheet。
    """

    worksheet_title: str = Field(description="worksheet 的名称")
    file_name: str = Field(description="工作表所属的文件名")
    file_url: str = Field(description="工作表所属的文件 URL")
    reason: str = Field(description="不合法的原因")


@dataclass
class MergedCellLocation:
    start_row: int
    end_row: int
    start_column: int
    end_column: int


class FileContainer(BaseModel):
    xlsx_filename_to_url_mapping: dict[str, str] = Field(
        default_factory=dict, description="需要解析的 excel 的文件名与其在 oss 中的 url 形成的映射关系"
    )
    other_filename_to_url_mapping: dict[str, str] = Field(
        default_factory=dict,
        description="非需要解析的 excel 文件的文件名与其在 oss 中的 url 形成的映射关系",
    )


class ImportFileResult(BaseModel):
    file_name: str = Field(description="文件名")
    file_url: str = Field(description="文件 URL")
    status: ImportFileStatus = Field(description="状态")


class ExceptionalDataContainer(BaseModel):
    invalid_files: list[ImportFileResult] = Field(default_factory=list)
    invalid_worksheets: list[InvalidWorksheet] = Field(default_factory=list)
    failed_business_order_infos: list[FailedBusinessOrderInfo] = Field(default_factory=list)

    @property
    def is_empty(self) -> bool:
        return all(
            [
                len([i for i in self.invalid_files if i.status != ImportFileStatus.SUCCESS]) == 0,
                len(self.invalid_worksheets) == 0,
                len(self.failed_business_order_infos) == 0,
            ]
        )
