import io
import string
from io import <PERSON><PERSON><PERSON>
from typing import Any
from typing import <PERSON>ary<PERSON>
from typing import Generator
from typing import Iterable
from urllib.parse import quote_plus
from urllib.request import urlopen

from loguru import logger
from openpyxl import Workbook
from openpyxl.cell.cell import Cell
from openpyxl.reader.excel import load_workbook
from openpyxl.styles import Alignment
from openpyxl.styles import Border
from openpyxl.styles import Side
from openpyxl.worksheet.worksheet import Worksheet

from robot_processor.business_order.struct.contants import IGNORED_WORKSHEET_TITLE
from robot_processor.business_order.struct.entities import FailedBusinessOrderInfo
from robot_processor.business_order.struct.entities import InvalidWorksheet
from robot_processor.business_order.struct.entities import MergedCellLocation


class ExcelOutputFailedDataHelper:
    def __init__(
        self,
        invalid_worksheets: list[InvalidWorksheet],
        failed_business_order_infos: list[FailedBusinessOrderInfo],
    ):
        self.invalid_worksheets = invalid_worksheets
        self.failed_business_order_infos = failed_business_order_infos

    def handle(self) -> Generator[tuple[str, str, BinaryIO | None], None, None]:
        (
            url_to_need_keep_worksheet_titles_mapping,
            url_to_need_handled_worksheet_title_to_bos_map,
        ) = self.get_need_handled_infos()

        urls_set: set[str] = set(url_to_need_keep_worksheet_titles_mapping.keys()) | set(
            url_to_need_handled_worksheet_title_to_bos_map.keys()
        )

        url_to_file_name_mapping: dict[str, str] = {
            invalid_worksheet.file_url: invalid_worksheet.file_name for invalid_worksheet in self.invalid_worksheets
        } | {
            failed_business_order_info.file_url: failed_business_order_info.file_name
            for failed_business_order_info in self.failed_business_order_infos
        }

        for url in urls_set:
            file_bytes_io = self.handle_single_file(
                file_url=url,
                url_to_need_keep_worksheet_titles_mapping=url_to_need_keep_worksheet_titles_mapping,
                url_to_need_handled_worksheet_title_to_bos_map=url_to_need_handled_worksheet_title_to_bos_map,
            )
            file_name = url_to_file_name_mapping[url]
            yield file_name, url, file_bytes_io

    def get_need_handled_infos(self) -> tuple[dict[str, set[str]], dict[str, dict[str, list[FailedBusinessOrderInfo]]]]:
        """
        计算得出每个 URL 对应的文件中，需要保留的工作表，以及需要进行工单删除的工作表。
        """
        url_to_need_keep_worksheet_titles_mapping: dict[str, set[str]] = {}
        url_to_need_handled_worksheet_title_to_bos_map: dict[str, dict[str, list[FailedBusinessOrderInfo]]] = {}

        # 需要原样保留的异常 worksheet。
        for invalid_worksheet_title in self.invalid_worksheets:
            need_keep_worksheet_titles: set[str] | None = url_to_need_keep_worksheet_titles_mapping.get(
                invalid_worksheet_title.file_url
            )
            if need_keep_worksheet_titles is None:
                need_keep_worksheet_titles = {IGNORED_WORKSHEET_TITLE}
            need_keep_worksheet_titles.add(invalid_worksheet_title.worksheet_title)
            url_to_need_keep_worksheet_titles_mapping[invalid_worksheet_title.file_url] = need_keep_worksheet_titles

        # 需要进行工单剔除的异常 worksheet。
        for failed_business_order_info in self.failed_business_order_infos:
            need_handled_worksheet_title_to_bo_mapping = url_to_need_handled_worksheet_title_to_bos_map.get(
                failed_business_order_info.file_url
            )
            if need_handled_worksheet_title_to_bo_mapping is None:
                need_handled_worksheet_title_to_bo_mapping = {}

            bos = need_handled_worksheet_title_to_bo_mapping.get(failed_business_order_info.worksheet_title)
            if bos is None:
                bos = []
            bo_ids = [bo.id for bo in bos]
            if failed_business_order_info.id not in bo_ids:
                bos.append(failed_business_order_info)
            need_handled_worksheet_title_to_bo_mapping[failed_business_order_info.worksheet_title] = bos
            url_to_need_handled_worksheet_title_to_bos_map[
                failed_business_order_info.file_url
            ] = need_handled_worksheet_title_to_bo_mapping

        return url_to_need_keep_worksheet_titles_mapping, url_to_need_handled_worksheet_title_to_bos_map

    def handle_single_file(
        self,
        file_url: str,
        url_to_need_keep_worksheet_titles_mapping: dict[str, set[str]],
        url_to_need_handled_worksheet_title_to_bos_map: dict[str, dict[str, list[FailedBusinessOrderInfo]]],
    ) -> BinaryIO | None:
        """
        处理文件。
        """
        quoted_url = quote_plus(file_url, safe=string.printable)
        try:
            with urlopen(quoted_url) as res:
                workbook: Workbook = load_workbook(io.BytesIO(res.read()))
        except Exception as e:
            logger.error(e)
            return None

        dont_removed_worksheet_titles: set[str] = {IGNORED_WORKSHEET_TITLE}
        dont_removed_worksheet_titles.update(url_to_need_keep_worksheet_titles_mapping.get(file_url) or set())
        need_handled_worksheet_title_to_bos_map = url_to_need_handled_worksheet_title_to_bos_map.get(file_url) or {}
        dont_removed_worksheet_titles.update(set(need_handled_worksheet_title_to_bos_map.keys()))

        need_removed_worksheet_titles: set[str] = set()

        for worksheet in workbook.worksheets:
            if worksheet.title not in dont_removed_worksheet_titles:
                need_removed_worksheet_titles.add(worksheet.title)

        for need_removed_worksheet_title in need_removed_worksheet_titles:
            del workbook[need_removed_worksheet_title]

        for worksheet_title, failed_business_order_infos in need_handled_worksheet_title_to_bos_map.items():
            if len(failed_business_order_infos) == 0:
                continue
            if worksheet_title not in workbook.sheetnames:
                continue
            worksheet = workbook[worksheet_title]
            self.handle_single_worksheet(worksheet, failed_business_order_infos)

        bytes_io = BytesIO()
        workbook.save(bytes_io)
        bytes_io.seek(0)
        return bytes_io

    @staticmethod
    def handle_single_worksheet(
        worksheet: Worksheet,
        failed_business_order_infos: list[FailedBusinessOrderInfo],
    ) -> None:
        """
        处理工作表。
        """
        business_order_id_to_failed_reason_mapping: dict[str, str] = {
            str(failed_business_order_info.id): failed_business_order_info.reason
            for failed_business_order_info in failed_business_order_infos
        }

        sorted_failed_business_order_infos: list[FailedBusinessOrderInfo] = sorted(
            failed_business_order_infos,
            key=lambda fbo: fbo.start_row,
            reverse=True,
        )
        worksheet_header_depth = sorted_failed_business_order_infos[0].worksheet_header_depth
        latest_row = worksheet.max_row
        # 被删除的行的行号组成的列表，其行号是从大到小排列的。
        deleted_rows: list[int] = []

        # 从最后一行开始，如果行号不是处理失败的工单的所属行，则进行删除
        for failed_business_order_info in sorted_failed_business_order_infos:
            # 如果最后一行在处理失败的工单的所属行的下面，则进行删除
            while latest_row > failed_business_order_info.end_row:
                deleted_rows.append(latest_row)
                # 最后一行变为其上方的一行
                latest_row -= 1
            # 如果最后一行在当前处理失败的工单的所属行中，则将最后一行设为当前处理失败的工单的所属行上的一行
            latest_row = failed_business_order_info.start_row - 1
            if latest_row <= worksheet_header_depth:
                break

        # 将最后一条处理失败的工单之上，到表头之下的所有行，也标记为删除。
        if latest_row > worksheet_header_depth:
            for row_number in range(worksheet_header_depth + 1, latest_row + 1):
                deleted_rows.append(row_number)

        # 倒序排列
        deleted_rows = sorted(deleted_rows, reverse=True)

        # 根据需要删除的行号，判断需要拆分单元的合并单元格的定位。
        need_unmerged_cell_locations: list[MergedCellLocation] = []

        for merged_cell in worksheet.merged_cells:
            if (merged_cell.min_row in deleted_rows) or (merged_cell.max_row in deleted_rows):
                if all(
                    [
                        merged_cell.min_row is not None,
                        merged_cell.max_row is not None,
                        merged_cell.min_col is not None,
                        merged_cell.max_col is not None,
                    ]
                ):
                    need_unmerged_cell_locations.append(
                        MergedCellLocation(
                            start_row=merged_cell.min_row,  # type: ignore[arg-type]
                            end_row=merged_cell.max_row,  # type: ignore[arg-type]
                            start_column=merged_cell.min_col,  # type: ignore[arg-type]
                            end_column=merged_cell.max_col,  # type: ignore[arg-type]
                        )
                    )

        # 将需要删除的行的合并单元格进行拆分
        for need_unmerged_cell_location in need_unmerged_cell_locations:
            worksheet.unmerge_cells(
                start_row=need_unmerged_cell_location.start_row,
                end_row=need_unmerged_cell_location.end_row,
                start_column=need_unmerged_cell_location.start_column,
                end_column=need_unmerged_cell_location.end_column,
            )

        # 将部分合并单元格的信息进行删除，并且将保留的合并单元格进行位移。
        for row in deleted_rows:
            # 删除该行。
            worksheet.delete_rows(row)

            for mcr in worksheet.merged_cells:
                # 如果该行号在合并单元格的上面，则合并单元格整体向上移动一行
                if row < (mcr.min_row or 1):
                    mcr.shift(row_shift=-1)

        # 补充工单 ID 对应的异常原因
        # 创建异常原因列
        new_last_column = worksheet.max_column + 1
        worksheet.insert_cols(new_last_column)
        worksheet.cell(row=1, column=new_last_column).value = "工单处理失败的原因(再次导入时请删除该列)"
        # 遍历工单 ID 列的每一行的数据，并补充原因到最后一列。
        business_order_id_column_cells: Iterable[Any] = next(worksheet.iter_cols(1, 1))
        for cell in business_order_id_column_cells:
            if cell.row == 1:
                continue
            if isinstance(cell, Cell) and cell.value is not None:
                original_cell_value = str(cell.value)
                if reason := business_order_id_to_failed_reason_mapping.get(original_cell_value):
                    worksheet.cell(row=cell.row, column=new_last_column).value = reason

        # 对异常原因列进行样式调整
        worksheet.merge_cells(
            start_row=1,
            start_column=new_last_column,
            end_row=worksheet_header_depth,
            end_column=new_last_column,
        )
        # 数据居中
        align = Alignment(horizontal="center", vertical="center", wrap_text=True)
        # 边框
        border = Border(
            left=Side(border_style="thin"),
            right=Side(border_style="thin"),
            top=Side(border_style="thin"),
            bottom=Side(border_style="thin"),
        )
        exceptional_reason_column_cells = next(worksheet.iter_cols(worksheet.max_column, worksheet.max_column))
        for exceptional_reason_cell in exceptional_reason_column_cells:
            exceptional_reason_cell.alignment = align
            exceptional_reason_cell.border = border
