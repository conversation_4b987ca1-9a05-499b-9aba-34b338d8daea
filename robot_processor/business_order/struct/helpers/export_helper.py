from functools import cached_property
from typing import cast

from sqlalchemy.orm import Load

from robot_processor.business_order.struct.contants import ARRAY_ITEM_SUFFIX
from robot_processor.business_order.struct.datas import PreDefinedWidgetItems
from robot_processor.business_order.struct.entities import WidgetDetail
from robot_processor.business_order.struct.entities import WidgetMeta
from robot_processor.business_order.struct.entities import WidgetName
from robot_processor.business_order.struct.entities import WidgetTitle
from robot_processor.business_order.struct.enums import ColumnSplitMode
from robot_processor.business_order.struct.enums import CommonWidgetType
from robot_processor.ext import db
from robot_processor.form.models import FormVersion
from robot_processor.form.models import Step
from robot_processor.form.models import Widget
from robot_processor.form.models import WidgetInfo
from robot_processor.form.types import WidgetDataType
from robot_processor.form.types import WidgetId
from robot_processor.form.types import WidgetInfoLabel
from robot_processor.form.types import WidgetType


class ExportHelper:
    def __init__(self, form_ids: list[int], column_split_mode: ColumnSplitMode = ColumnSplitMode.ALL_SPLIT) -> None:
        self.form_ids: list[int] = form_ids
        self.column_split_mode: ColumnSplitMode = column_split_mode

        self._form_versions: list[FormVersion] | None = None
        self._steps: list[Step] | None = None

    @cached_property
    def widget_id_to_widget_type_mapping(self) -> dict[WidgetId, WidgetType]:
        return cast(
            dict[WidgetId, WidgetType],
            dict(db.ro_session.query(Widget.id, Widget.schema["type"]).all()),
        )

    @cached_property
    def widget_id_to_widget_meta_mapping(self) -> dict[WidgetId, list]:
        return cast(
            dict[WidgetId, list],
            {
                widget_id: widget_meta or []
                for widget_id, widget_meta in db.ro_session.query(Widget.id, Widget.widget_meta).all()
            },
        )

    @cached_property
    def widget_id_to_widget_data_type_mapping(self) -> dict[WidgetId, WidgetDataType]:
        widgets: list[Widget] = db.ro_session.query(Widget).all()
        return {cast(WidgetId, widget.id): cast(WidgetDataType, widget.schema.get("widget_type")) for widget in widgets}

    @cached_property
    def widget_type_to_widget_id_mapping(self) -> dict[WidgetType, WidgetId]:
        return {
            widget_type: widget_id
            for widget_id, widget_type in cast(
                dict[WidgetId, WidgetType],
                dict(db.ro_session.query(Widget.id, Widget.schema["type"]).all()),
            ).items()
        }

    @property
    def form_versions(self) -> list[FormVersion]:
        if self._form_versions is not None:
            return self._form_versions
        form_versions: list[FormVersion] = (
            db.ro_session.query(FormVersion)
            .filter(FormVersion.form_id.in_(self.form_ids))
            .order_by(FormVersion.id)
            .all()
        )
        self._form_versions = form_versions
        return self._form_versions

    @property
    def steps(self) -> list[Step]:
        """
        根据 form_ids 找到所有非草稿状态的步骤。
        需要注意的是，已经删除的步骤，实际上也是会有不同的字段，并且也应该是可以进行数据编辑的。
        """
        if self._steps is not None:
            return self._steps
        steps: list[Step] = (
            db.ro_session.query(Step)
            .options(Load(Step).joinedload(Step.widget_collection))
            .options(Load(Step).load_only(Step.id, Step.name, Step.widget_collection_id))
            .filter(Step.form_id.in_(self.form_ids), Step.is_dirty.is_(False))
            .filter(Step.widget_collection_id.isnot(None), Step.widget_collection_id != "0")
            .all()
        )
        self._steps = steps
        return self._steps

    @property
    def form_id_to_form_versions_mapping(self) -> dict[int, list[FormVersion]]:
        """
        根据 form_ids 来获取所有的版本，并以 form_id 来聚合。
        """
        form_id_to_form_versions_mapping: dict[int, list[FormVersion]] = {}
        for form_version in self.form_versions:
            if form_version.form_id is None:
                continue
            form_versions_for_current_form_id = form_id_to_form_versions_mapping.get(form_version.form_id) or []
            form_versions_for_current_form_id.append(form_version)
            form_id_to_form_versions_mapping[form_version.form_id] = form_versions_for_current_form_id
        return form_id_to_form_versions_mapping

    @property
    def form_version_id_to_form_version_mapping(self) -> dict[int, FormVersion]:
        return {form_version.id: form_version for form_version in self.form_versions}

    @property
    def form_id_to_latest_form_version_mapping(self) -> dict[int, FormVersion]:
        sorted_form_versions: list[FormVersion] = sorted(
            self.form_versions,
            key=lambda fv: fv.id,
            reverse=True,
        )
        form_id_to_latest_form_version_mapping: dict[int, FormVersion] = {}
        for form_version in sorted_form_versions:
            if form_version.form_id is None:
                continue
            elif form_id_to_latest_form_version_mapping.get(form_version.form_id) is None:
                form_id_to_latest_form_version_mapping[form_version.form_id] = form_version
            else:
                continue
        return form_id_to_latest_form_version_mapping

    @staticmethod
    def get_widget_infos_by_steps(steps: list[Step]) -> list[WidgetInfo]:
        """
        获取指定步骤名下的所有的 widget_info（根据 widget_info 的 key 来去重）。
        :param steps:
        :return:
        """
        # 获取这一批步骤的所有 widget_collection_id
        widget_collection_ids: list[int] = [
            int(step.widget_collection_id) for step in steps if step.widget_collection_id is not None
        ]

        # 获取这一批步骤的所有 widget_info。
        widget_infos: list[WidgetInfo] = (
            db.ro_session.query(WidgetInfo).filter(WidgetInfo.widget_collection_id.in_(widget_collection_ids)).all()
        )
        return widget_infos

    @staticmethod
    def generate_widget_collection_id_to_widget_infos_mapping(
        widget_infos: list[WidgetInfo],
    ) -> dict[str, list[WidgetInfo]]:
        widget_collection_id_to_widget_infos_mapping: dict[str, list[WidgetInfo]] = {}
        for widget_info in widget_infos:
            widget_infos_for_current_widget_collection = (
                widget_collection_id_to_widget_infos_mapping.get(str(widget_info.widget_collection_id)) or []
            )
            widget_infos_for_current_widget_collection = [
                *widget_infos_for_current_widget_collection,
                widget_info,
            ]
            widget_collection_id_to_widget_infos_mapping[
                str(widget_info.widget_collection_id)
            ] = widget_infos_for_current_widget_collection
        return widget_collection_id_to_widget_infos_mapping

    @staticmethod
    def generate_step_id_to_widget_infos_mapping(
        steps: list[Step],
        widget_collection_id_to_widget_infos: dict[int, list[WidgetInfo]],
    ) -> dict[int, list[WidgetInfo]]:
        step_id_to_widget_infos: dict[int, list[WidgetInfo]] = {}
        for step in steps:
            step_id_to_widget_infos[step.id] = (
                widget_collection_id_to_widget_infos.get(step.widget_collection_id) or []  # type: ignore[call-overload]
            )
        return step_id_to_widget_infos

    @staticmethod
    def generate_form_version_id_to_available_widget_titles_mapping(
        widget_titles: list[WidgetTitle],
        form_versions: list[FormVersion],
    ) -> dict[int, list[WidgetTitle]]:
        form_version_id_to_available_widget_titles_mapping: dict[int, list[WidgetTitle]] = {}
        for form_version in form_versions:
            available_widget_titles = []
            for widget_title in widget_titles:
                if form_version.id in widget_title.form_versions:
                    available_widget_titles.append(widget_title)
                    continue
            form_version_id_to_available_widget_titles_mapping.update({form_version.id: available_widget_titles})
        return form_version_id_to_available_widget_titles_mapping

    @staticmethod
    def group_steps_by_step_name(steps: list[Step]) -> dict[str, list[Step]]:
        """
        不同平台的工单模板可能会是不一致的，目前规则是按照 **同名** 视为同一步骤，将其聚合。

        返回的结果是 dict[步骤名：同名的步骤组合成的列表]
        :return:
        """
        step_name_to_steps_mapping: dict[str, list[Step]] = {}
        for step in steps:
            if step.name is None:
                continue
            step_list = step_name_to_steps_mapping.get(step.name) or []
            step_name_to_steps_mapping.update({step.name: [*step_list, step]})

        return step_name_to_steps_mapping

    @staticmethod
    def generate_widget_info_key_to_steps_mapping(
        steps: list[Step],
        widget_infos: list[WidgetInfo],
    ) -> dict[str, list[Step]]:
        """
        获取所有步骤绑定的 widget_info，并按照 widget_info 的 key，聚合其所处的 step 列表。
        :return:
        """
        widget_collection_id_to_steps_mapping: dict[int, list[Step]] = {}

        for step in steps:
            if step.widget_collection_id is None:
                continue
            widget_collection_id = int(step.widget_collection_id)
            steps_for_current_widget_collection: list[Step] = (
                widget_collection_id_to_steps_mapping.get(widget_collection_id) or []
            )
            steps_for_current_widget_collection = [
                *steps_for_current_widget_collection,
                step,
            ]
            widget_collection_id_to_steps_mapping.update({widget_collection_id: steps_for_current_widget_collection})

        widget_info_key_to_steps_mapping: dict[str, list[Step]] = {}

        for widget_info in widget_infos:
            if widget_info.widget_collection_id is None:
                continue
            steps_for_current_widget_info_key = widget_info_key_to_steps_mapping.get(widget_info.key) or []
            steps_for_current_widget_collection_id: list[Step] = (
                widget_collection_id_to_steps_mapping.get(widget_info.widget_collection_id) or []
            )
            steps_for_current_widget_info = [
                *steps_for_current_widget_info_key,
                *steps_for_current_widget_collection_id,
            ]
            widget_info_key_to_steps_mapping.update({widget_info.key: steps_for_current_widget_info})

        return widget_info_key_to_steps_mapping

    def generate_widget_name_to_widget_infos_mapping(
        self,
        widget_infos: list[WidgetInfo],
    ) -> dict[WidgetName, list[WidgetInfo]]:
        """
        按照 **同名** + **同类型** 的规则来将 widget 进行合并。

        :param widget_infos:
        :return:
        """
        widget_name_to_widget_infos_mapping: dict[WidgetName, list[WidgetInfo]] = {}

        for widget_info in widget_infos:
            widget_info_type = self.widget_id_to_widget_type_mapping.get(
                widget_info.widget_id  # type: ignore[arg-type]
            )
            if widget_info_type is None:
                continue
            widget_data_type = self.widget_id_to_widget_data_type_mapping.get(
                widget_info.widget_id  # type: ignore[arg-type]
            )
            # 根据 标签 和 类型 来生成这一组件的 名称。
            current_widget_name: WidgetName = WidgetName(
                widget_label=WidgetInfoLabel(widget_info.label),
                widget_type=widget_info_type,
                widget_data_type=widget_data_type,
            )
            current_widget_infos = widget_name_to_widget_infos_mapping.get(current_widget_name) or []
            current_widget_infos.append(widget_info)
            widget_name_to_widget_infos_mapping[current_widget_name] = current_widget_infos

        return widget_name_to_widget_infos_mapping

    @staticmethod
    def group_widget_infos_by_step_name(
        step_name_to_steps_mapping: dict[str, list[Step]],
        widget_collection_id_to_widget_infos_mapping: dict[str, list[WidgetInfo]],
    ) -> dict[str, list[WidgetInfo]]:
        """
        将组件归类到同名步骤下，形成步骤名与其下属组件的映射。
        """
        step_name_to_widget_infos_mapping: dict[str, list[WidgetInfo]] = {}

        for step_name, steps in step_name_to_steps_mapping.items():
            widget_infos: list[WidgetInfo] = []
            for step in steps:
                if step.widget_collection_id is None:
                    continue
                widget_infos = [
                    *widget_infos,
                    *(widget_collection_id_to_widget_infos_mapping.get(step.widget_collection_id) or []),
                ]
            step_name_to_widget_infos_mapping[step_name] = widget_infos

        return step_name_to_widget_infos_mapping

    def generate_step_name_to_widget_item_mapping(
        self,
        step_name_to_widget_infos_mapping: dict[str, list[WidgetInfo]],
    ) -> dict[str, dict[WidgetName, list[WidgetInfo]]]:
        """
        获取同步骤名下，同名同类型的组件构成的名称与组件详情列表的映射。
        并将其与步骤名进行关联，从而返回。
        :return:
        """
        step_name_to_widget_item_mapping: dict[str, dict[WidgetName, list[WidgetInfo]]] = {}

        for step_name, widget_infos in step_name_to_widget_infos_mapping.items():
            # 按照规则同名、同类型的规则（以及配置的迁移规则），来将组件进行合并。
            widget_name_to_widget_infos_mapping = self.generate_widget_name_to_widget_infos_mapping(
                widget_infos=widget_infos,
            )
            step_name_to_widget_item_mapping[step_name] = widget_name_to_widget_infos_mapping

        return step_name_to_widget_item_mapping

    def generate_widget_name_to_available_form_versions_mapping(
        self,
        widget_name_to_widget_infos_mapping: dict[WidgetName, list[WidgetInfo]],
        widget_info_key_to_steps_mapping: dict[str, list[Step]],
    ) -> dict[WidgetName, list[FormVersion]]:
        """
        获取聚合后的组件，其可以使用的工单模板的版本。
        """
        widget_name_to_available_form_versions_mapping: dict[WidgetName, list[FormVersion]] = {}

        for widget_name, widget_infos in widget_name_to_widget_infos_mapping.items():
            # 获取该聚合后的组件下的所有组件的 key。
            widget_info_keys: list[str] = [widget_info.key for widget_info in widget_infos]

            # 根据这些组件的 key，找到他们可用于的步骤。
            step_set_for_current_widget_name: set[Step] = set()
            step_id_set_for_current_widget_name: set[int] = set()
            for widget_info_key in widget_info_keys:
                steps_for_current_widget_info_key: list[Step] = (
                    widget_info_key_to_steps_mapping.get(widget_info_key) or []
                )
                step_set_for_current_widget_name.update(steps_for_current_widget_info_key)
                step_id_set_for_current_widget_name.update([step.id for step in steps_for_current_widget_info_key])

            # 找到这些步骤所对应的工单模板的 id，以及这些工单模板的版本。
            form_id_set: set[int] = {
                step.form_id for step in step_set_for_current_widget_name if step.form_id is not None
            }
            form_version_set: set[FormVersion] = set()
            for form_id in form_id_set:
                form_versions: list[FormVersion] = self.form_id_to_form_versions_mapping.get(form_id) or []
                form_version_set.update(form_versions)

            available_form_version_set = set()

            # 如果工单模板的某一版本的 step_id 与当前 steps 的 step.id 存在重合，则说明该版本可以使用这个聚合后的组件
            for form_version in form_version_set:
                if step_id_set_for_current_widget_name & set(form_version.step_id):
                    available_form_version_set.add(form_version)

            # 将去重后的数据加入到结果中。
            widget_name_to_available_form_versions_mapping.update({widget_name: list(available_form_version_set)})

        return widget_name_to_available_form_versions_mapping

    def extract_sub_widget_infos(self, widget_info: WidgetInfo) -> list[WidgetInfo] | None:
        """
        提取该组件的下一层级上的所有子组件。
        如果没有任何子组件，则返回 None。
        """
        if widget_info.option_value.get("widget_type") in [
            WidgetDataType.TABLE,
            WidgetDataType.COLLECTION,
            WidgetDataType.ARRAY,
        ] and isinstance(fields := widget_info.option_value.get("fields"), list):

            sub_widget_infos: list[WidgetInfo] = []

            if widget_info.option_value.get("widget_type") in [
                WidgetDataType.TABLE,
            ]:
                sub_widget_infos.append(
                    WidgetInfo(
                        key="serial",
                        widget_id=self.widget_type_to_widget_id_mapping.get(WidgetType("text")),
                        option_value={"label": "序列"},
                        data_schema={},
                    )
                )
            elif widget_info.option_value.get("widget_type") in [
                WidgetDataType.ARRAY,
            ]:
                sub_widget_infos.append(
                    WidgetInfo(
                        key=f"serial{ARRAY_ITEM_SUFFIX}",
                        widget_id=self.widget_type_to_widget_id_mapping.get(WidgetType("text")),
                        option_value={"label": "序列"},
                        data_schema={},
                    )
                )

            for field in fields:
                # 不处理引用组件
                if field.get("before"):
                    continue
                sub_widget_info = WidgetInfo(
                    key=field.get("key"),
                    widget_id=field.get("id"),
                    option_value=field.get("option_value") or {},
                    data_schema=field.get("data_schema") or {},
                )
                sub_widget_infos.append(sub_widget_info)

            return sub_widget_infos
        else:
            return None

    def generate_sub_widget_mapping_by_widget_name(
        self,
        widget_name: WidgetName,
        widget_infos_for_current_widget_name: list[WidgetInfo],
    ) -> dict[WidgetName, WidgetDetail] | None:
        """
        获取聚合后的同名组件的子组件。
        该方法会涉及到递归处理。
        """
        # 描述说明组件。
        if widget_name.widget_type in [CommonWidgetType.text]:
            return None
        # 订单组件
        elif widget_name.widget_type in [CommonWidgetType.order]:
            return PreDefinedWidgetItems.get_sub_widget_items_for_order(
                order_widget_infos=widget_infos_for_current_widget_name
            )
        # 收款信息组件
        elif widget_name.widget_type in [CommonWidgetType.payment_method]:
            return PreDefinedWidgetItems.get_sub_widget_items_for_payment_method()
        # 地址组件
        elif widget_name.widget_type in [CommonWidgetType.address]:
            return PreDefinedWidgetItems.get_sub_widget_items_for_address(self.column_split_mode)
        # 文件组件
        elif widget_name.widget_type in [CommonWidgetType.upload]:
            return PreDefinedWidgetItems.get_sub_widget_items_for_upload()
        # 级联组件
        elif widget_name.widget_type in [
            CommonWidgetType.order_question,
            CommonWidgetType.select_tile,
            CommonWidgetType.select_dropdown,
            CommonWidgetType.radio_tile,
            CommonWidgetType.radio_dropdown,
        ]:
            if self.column_split_mode != ColumnSplitMode.NOT_SPLIT:
                return PreDefinedWidgetItems.get_sub_widget_items_for_enum(
                    enum_widget_type=widget_name.widget_type,
                    enum_widget_infos=widget_infos_for_current_widget_name,
                )
            else:
                return None
        # 评分组件
        elif widget_name.widget_type in [CommonWidgetType.rate]:
            return PreDefinedWidgetItems.get_sub_widget_items_for_rate()
        # 复合组件和容器组件
        elif widget_name.widget_data_type in [
            WidgetDataType.TABLE,
            WidgetDataType.COLLECTION,
            WidgetDataType.ARRAY,
        ]:
            # 获取当前组件名下的所有子组件。
            sub_widget_infos_for_current_widget_name: list[WidgetInfo] = []
            for widget_info in widget_infos_for_current_widget_name:
                if sub_widget_infos := self.extract_sub_widget_infos(widget_info):
                    sub_widget_infos_for_current_widget_name = (
                        sub_widget_infos_for_current_widget_name + sub_widget_infos
                    )
            # 将子组件进行合并。
            sub_widget_name_to_widget_infos_mapping = self.generate_widget_name_to_widget_infos_mapping(
                widget_infos=sub_widget_infos_for_current_widget_name,
            )

            # 获取子组件名。
            widget_name_to_widget_detail_mapping: dict[WidgetName, WidgetDetail] = {}
            for (
                sub_widget_name,
                sub_widget_infos,
            ) in sub_widget_name_to_widget_infos_mapping.items():
                # 该子组件还需要拆分，则进入递归。
                if sub_widget_name.has_sub_widget_items():
                    widget_name_to_widget_detail_mapping.update(
                        {
                            sub_widget_name: WidgetDetail(
                                sub_widget_infos,
                                self.generate_sub_widget_mapping_by_widget_name(sub_widget_name, sub_widget_infos),
                                [],
                            )
                        }
                    )
                # 否则，直接返回。
                else:
                    widget_name_to_widget_detail_mapping.update(
                        {sub_widget_name: WidgetDetail(sub_widget_infos, None, [])}
                    )
            return widget_name_to_widget_detail_mapping
        # 其他基础组件
        else:
            return None

    def generate_widget_name_to_widget_detail_mapping(
        self,
        widget_name_to_widget_infos_mapping: dict[WidgetName, list[WidgetInfo]],
        widget_info_key_to_steps_mapping: dict[str, list[Step]],
    ) -> dict[WidgetName, WidgetDetail]:
        """
        获取组件名与表头元数据的映射关系。

        :return:
        """
        widget_name_to_widget_detail_mapping: dict[WidgetName, WidgetDetail] = {}

        # 获取到组件名和可用版本的映射关系。
        widget_name_to_available_form_versions_mapping = self.generate_widget_name_to_available_form_versions_mapping(
            widget_name_to_widget_infos_mapping,
            widget_info_key_to_steps_mapping,
        )

        for widget_name, widget_infos in widget_name_to_widget_infos_mapping.items():
            widget_name_to_widget_detail_mapping.update(
                {
                    widget_name: WidgetDetail(
                        widget_infos=widget_infos,
                        sub_widgets_mapping=self.generate_sub_widget_mapping_by_widget_name(widget_name, widget_infos),
                        form_versions=widget_name_to_available_form_versions_mapping.get(widget_name) or [],
                    )
                }
            )

        return widget_name_to_widget_detail_mapping

    def generate_step_name_to_widget_meta_mapping(
        self,
        step_name_to_widget_item_mapping: dict[str, dict[WidgetName, list[WidgetInfo]]],
        widget_info_key_to_steps_mapping: dict[str, list[Step]],
    ):
        step_name_to_widget_meta_mapping: dict[str, dict[WidgetName, WidgetDetail]] = {}

        # 拆解步骤，获取组件、子组件以及组件可用的工单模板的版本。
        for (
            step_name,
            widget_name_to_widget_infos_mapping,
        ) in step_name_to_widget_item_mapping.items():
            widget_name_to_widget_detail_mapping = self.generate_widget_name_to_widget_detail_mapping(
                widget_name_to_widget_infos_mapping,
                widget_info_key_to_steps_mapping,
            )
            step_name_to_widget_meta_mapping.update({step_name: widget_name_to_widget_detail_mapping})

        return step_name_to_widget_meta_mapping

    def generate_widget_titles(
        self,
        widget_name_to_widget_detail_mapping: dict[WidgetName, WidgetDetail],
        prefix_widget_titles: WidgetTitle | None = None,
    ) -> list[WidgetTitle]:
        prefix_widget_titles = prefix_widget_titles if prefix_widget_titles is not None else WidgetTitle(metas=[])

        widget_titles: list[WidgetTitle] = []
        for widget_name, widget_detail in widget_name_to_widget_detail_mapping.items():
            if not widget_detail.sub_widgets_mapping:
                widget_titles.append(
                    WidgetTitle(
                        metas=[
                            *prefix_widget_titles.metas,
                            WidgetMeta(widget_name=widget_name, widget_detail=widget_detail),
                        ],
                        form_versions={
                            **prefix_widget_titles.form_versions,
                            **{form_version.id: form_version for form_version in widget_detail.form_versions},
                        },
                    )
                )
            else:
                sub_widget_titles = self.generate_widget_titles(
                    widget_detail.sub_widgets_mapping,
                    WidgetTitle(
                        metas=[
                            *prefix_widget_titles.metas,
                            WidgetMeta(widget_name=widget_name, widget_detail=widget_detail),
                        ],
                        form_versions={
                            **prefix_widget_titles.form_versions,
                            **{form_version.id: form_version for form_version in widget_detail.form_versions},
                        },
                    ),
                )
                widget_titles = widget_titles + sub_widget_titles

        return widget_titles

    def generate_step_name_to_widget_titles_mapping(
        self,
        step_name_to_widget_meta_mapping: dict[str, dict[WidgetName, WidgetDetail]],
    ) -> dict[str, list[WidgetTitle]]:
        step_name_to_widget_titles_mapping: dict[str, list[WidgetTitle]] = {}

        for step_name, widget_meta in step_name_to_widget_meta_mapping.items():
            # 生成该步骤的组件的 title。
            widget_titles: list[WidgetTitle] = self.generate_widget_titles(widget_meta)
            # 补充 show_names 信息。
            for widget_title in widget_titles:
                show_names = widget_title.generate_show_names(step_name)
                widget_title.show_names = show_names
            step_name_to_widget_titles_mapping[step_name] = widget_titles

        return step_name_to_widget_titles_mapping

    @staticmethod
    def group_widget_titles_by_step_name(
        widget_titles: list[WidgetTitle],
    ) -> dict[str, list[WidgetTitle]]:
        group_titles: dict[str, list[WidgetTitle]] = {}
        for widget_title in widget_titles:
            if widget_title.actually_show_names_length == 0:
                continue
            key = widget_title.get_first_show_name()
            if key is None:
                continue
            current_group_widget_titles: list[WidgetTitle] = group_titles.get(key) or []
            current_group_widget_titles.append(widget_title)
            group_titles[key] = current_group_widget_titles
        return group_titles

    def compute_widget_titles_for_steps(self) -> dict[str, list[WidgetTitle]]:
        steps: list[Step] = self.steps
        widget_infos: list[WidgetInfo] = self.get_widget_infos_by_steps(steps=steps)

        # 按照步骤是否同名，来将步骤进行合并。
        step_name_to_steps_mapping = self.group_steps_by_step_name(steps=steps)
        widget_collection_id_to_widget_infos_mapping = self.generate_widget_collection_id_to_widget_infos_mapping(
            widget_infos=widget_infos,
        )
        # 获取同名步骤与其对应的组件的映射关系。
        step_name_to_widget_infos_mapping = self.group_widget_infos_by_step_name(
            step_name_to_steps_mapping=step_name_to_steps_mapping,
            widget_collection_id_to_widget_infos_mapping=widget_collection_id_to_widget_infos_mapping,
        )
        # 将同名步骤下的组件进行相关合并。
        step_name_to_widget_item_mapping = self.generate_step_name_to_widget_item_mapping(
            step_name_to_widget_infos_mapping=step_name_to_widget_infos_mapping,
        )

        # 为同名步骤下组件补充层级关系和所属工单模板的版本信息。
        widget_info_key_to_steps_mapping = self.generate_widget_info_key_to_steps_mapping(
            steps=steps,
            widget_infos=widget_infos,
        )
        step_name_to_widget_meta_mapping = self.generate_step_name_to_widget_meta_mapping(
            step_name_to_widget_item_mapping=step_name_to_widget_item_mapping,
            widget_info_key_to_steps_mapping=widget_info_key_to_steps_mapping,
        )
        step_name_to_widget_titles_mapping = self.generate_step_name_to_widget_titles_mapping(
            step_name_to_widget_meta_mapping=step_name_to_widget_meta_mapping,
        )
        return step_name_to_widget_titles_mapping
