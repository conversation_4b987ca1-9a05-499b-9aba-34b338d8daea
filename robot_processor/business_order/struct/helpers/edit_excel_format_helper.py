import io
from typing import Any
from typing import BinaryIO

import numpy as np
import pandas as pd
from loguru import logger
from openpyxl import load_workbook
from openpyxl.cell import Cell
from openpyxl.styles import Alignment
from openpyxl.styles import Border
from openpyxl.styles import Color
from openpyxl.styles import Font
from openpyxl.styles import Side
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.worksheet.hyperlink import Hyperlink
from openpyxl.worksheet.worksheet import Worksheet

from robot_processor.business_order.struct.contants import FIXED_COLUMN_WIDTH
from robot_processor.business_order.struct.datas import DataConvertRules
from robot_processor.business_order.struct.entities import AccurateWidgetMeta
from robot_processor.business_order.struct.entities import AccurateWidgetTitle
from robot_processor.business_order.struct.enums import ColumnSplitMode
from robot_processor.business_order.struct.enums import CommonWidgetType
from robot_processor.business_order.struct.enums import ExportScene
from robot_processor.business_order.struct.enums import MultiRows<PERSON><PERSON>att<PERSON>
from robot_processor.form.types import WidgetDataType


class EditExcelFormatHelper:
    def __init__(
        self,
        excel_file_like_object: BinaryIO,
        worksheet_title_to_dataframe_mapping: dict[str, pd.DataFrame],
        worksheet_title_to_business_order_rows_mapping: dict[str, list[int]],
        worksheet_title_to_widget_titles_mapping: dict[str, list[AccurateWidgetTitle]],
        fix_column_width: bool = False,
        multi_rows_formatter: MultiRowsFormatter = MultiRowsFormatter.MERGE_CELLS,
        export_scene: ExportScene = ExportScene.VIEW,
        column_split_mode: ColumnSplitMode = ColumnSplitMode.ALL_SPLIT,
    ) -> None:
        self.excel_file_like_object = excel_file_like_object
        self.workbook = load_workbook(excel_file_like_object)
        self.worksheet_title_to_dataframe_mapping = worksheet_title_to_dataframe_mapping
        self.worksheet_title_to_business_order_rows_mapping = worksheet_title_to_business_order_rows_mapping
        self.worksheet_title_to_widget_titles_mapping = worksheet_title_to_widget_titles_mapping

        self.fix_column_width = fix_column_width
        self.multi_rows_formatter = multi_rows_formatter
        self.export_scene = export_scene
        self.column_split_mode = column_split_mode

    def format_excel(
        self,
    ):
        for worksheet in self.workbook.worksheets:
            self.format_worksheet(worksheet)
        formatted_excel_file_like_object = io.BytesIO()
        self.workbook.save(formatted_excel_file_like_object)
        return formatted_excel_file_like_object

    def format_worksheet(
        self,
        worksheet: Worksheet,
    ) -> None:
        worksheet_title: str = worksheet.title
        dataframe: pd.DataFrame | None = self.worksheet_title_to_dataframe_mapping.get(worksheet_title)
        if dataframe is None:
            return None
        business_order_rows: list[int] | None = self.worksheet_title_to_business_order_rows_mapping.get(worksheet_title)
        if business_order_rows is None:
            return None
        widget_titles: list[AccurateWidgetTitle] | None = self.worksheet_title_to_widget_titles_mapping.get(
            worksheet_title
        )
        if widget_titles is None:
            return None

        # 以下操作需要按照顺序进行。
        # 1、删除 pandas 产生的空列。
        self.remove_pandas_row_number_column(worksheet)
        # 2、设置列宽。
        self.set_column_widths(
            worksheet=worksheet,
            dataframe=dataframe,
            fix_column_width=self.fix_column_width,
        )
        # 3、合并表头单元格。
        self.merge_headers(worksheet=worksheet, dataframe=dataframe)
        # 4、合并数据行单元格。
        if self.multi_rows_formatter == MultiRowsFormatter.MERGE_CELLS:
            self.merge_data_rows(
                worksheet=worksheet,
                dataframe=dataframe,
                business_order_rows=business_order_rows,
            )
        # 5、设置单元格样式。
        self.set_style_for_cells(
            worksheet=worksheet,
            widget_titles=widget_titles,
            multi_rows_formatter=self.multi_rows_formatter,
            export_scene=self.export_scene,
            column_split_mode=self.column_split_mode,
        )

    @staticmethod
    def remove_pandas_row_number_column(
        worksheet: Worksheet,
    ) -> None:
        column_number = 1

        if worksheet.max_column > column_number:
            # 删除行号列
            worksheet.delete_cols(column_number)
            # worksheet 中的所有合并单元格向左移动一位。
            for mcr in worksheet.merged_cells:
                if column_number < (mcr.min_col or 1):
                    mcr.shift(col_shift=-1 * column_number)

    @staticmethod
    def set_column_widths(
        worksheet: Worksheet,
        dataframe: pd.DataFrame,
        fix_column_width: bool = False,
    ) -> None:
        # 采用固定列宽。
        if fix_column_width:
            for i in range(worksheet.max_column):
                worksheet.column_dimensions[get_column_letter(i + 1)].width = FIXED_COLUMN_WIDTH
            return None

        column_widths = (
            dataframe.columns.to_series()
            .apply(lambda series: max([len(str(value).encode("utf-8")) for value in series]))
            .values
        )
        #  计算每列的最大字符宽度
        max_widths = (
            dataframe.astype(str)
            .applymap(lambda x: len(str(x).encode("utf-8")))
            .agg(max)
            .values  # type: ignore[union-attr]
        )
        # 取前两者中每列的最大宽度
        widths = np.max([column_widths, max_widths], axis=0)

        for i, width in enumerate(widths, 1):
            # openpyxl引擎设置字符宽度时会缩水0.5左右个字符，所以干脆+2使左右都空出一个字宽。
            worksheet.column_dimensions[get_column_letter(i)].width = width + 2

        return None

    @staticmethod
    def merge_headers(
        worksheet: Worksheet,
        dataframe: pd.DataFrame,
    ) -> None:
        # 纵向合并
        if len(dataframe.columns) == 0:
            return None

        columns_count = len(dataframe.columns)
        # pandas 直接导出的 worksheet 中，表头和数据之间，会有一个空行。因此深度加一。
        column_depth = len(dataframe.columns[0]) + 1

        # 分列进行处理
        for column_index in range(1, columns_count + 1):
            start_row, end_row = (1, 1)
            while column_depth >= end_row:
                # 处于第一行时，不做处理
                if end_row == 1:
                    end_row += 1
                    continue
                # 处于最后一行时，将该区间内到所有行合并到一起
                elif column_depth == end_row and worksheet.cell(row=end_row, column=column_index).value is None:
                    worksheet.merge_cells(
                        start_column=column_index,
                        end_column=column_index,
                        start_row=start_row,
                        end_row=end_row,
                    )
                    start_row, end_row = end_row, end_row + 1
                    continue
                # 如果该行数据不为空，则将上一区间内的所有行的单元格进行合并
                elif worksheet.cell(row=end_row, column=column_index).value is not None:
                    worksheet.merge_cells(
                        start_column=column_index,
                        end_column=column_index,
                        start_row=start_row,
                        end_row=end_row - 1,
                    )
                    start_row, end_row = end_row, end_row + 1
                    continue
                # 如果该行数据为空，则说明该行可以被合并
                else:
                    end_row += 1

        return None

    @staticmethod
    def merge_data_rows(
        worksheet: Worksheet,
        dataframe: pd.DataFrame,
        business_order_rows: list[int],
    ):
        # 纵向合并
        if len(dataframe.columns) == 0:
            return
        if len(dataframe) <= 1:
            return

        columns_count = len(dataframe.columns)
        # 总行数为：列的最大深度 + pandas 产生的一条空行 + 数据的行数
        # row_count = len(dataframe.columns[0]) + 1 + len(dataframe)

        first_data_row = len(dataframe.columns[0]) + 2

        # 分列进行处理
        for column_index in range(1, columns_count + 1):
            current_business_order_start_row = first_data_row
            for business_order_row_count in business_order_rows:
                start_row, end_row = (
                    current_business_order_start_row,
                    current_business_order_start_row,
                )
                current_business_order_end_row = current_business_order_start_row + business_order_row_count - 1
                while current_business_order_end_row >= end_row:
                    # 处于第一行时，不做处理
                    if end_row == current_business_order_start_row:
                        end_row += 1
                        continue
                    # 处于最后一行时，将该区间内到所有行合并到一起
                    elif (
                        end_row == current_business_order_end_row
                        and worksheet.cell(row=end_row, column=column_index).value is None
                    ):
                        worksheet.merge_cells(
                            start_column=column_index,
                            end_column=column_index,
                            start_row=start_row,
                            end_row=end_row,
                        )
                        start_row, end_row = end_row, end_row + 1
                        continue
                    # 如果该行数据不为空，则将上一区间内的所有行的单元格进行合并
                    elif worksheet.cell(row=end_row, column=column_index).value is not None:
                        # 如果上一区间只有一行数据，也就没必要做合并操作了。
                        if start_row == end_row - 1:
                            start_row, end_row = end_row, end_row + 1
                            continue
                        worksheet.merge_cells(
                            start_column=column_index,
                            end_column=column_index,
                            start_row=start_row,
                            end_row=end_row - 1,
                        )
                        start_row, end_row = end_row, end_row + 1
                        continue
                    # 如果该行数据为空，则说明该行可以被合并
                    else:
                        end_row += 1

                current_business_order_start_row = current_business_order_end_row + 1

        return None

    @staticmethod
    def get_data_validation(
        widget_title: AccurateWidgetTitle,
    ) -> DataValidation | None:
        """
        从表头中提取数据验证模块。
        """
        if widget_title.dropdown_values_meta is None:
            return None
        data_convert_rule: dict[str, Any] = {}
        if widget_title.dropdown_values_meta.widget_name.widget_data_type == WidgetDataType.BOOLEAN:
            data_convert_rule = DataConvertRules.get_boolean_readable_string_to_value_mapping_by_widget_meta(
                widget_title.dropdown_values_meta
            )
        elif widget_title.dropdown_values_meta.widget_name.widget_type in [
            CommonWidgetType.payment_method_item_method,
        ]:
            data_convert_rule = DataConvertRules.get_payment_method_readable_string_to_value_mapping()
        else:
            data_convert_rule = DataConvertRules.get_enum_readable_string_to_value_mapping_by_widget_meta(
                widget_title.dropdown_values_meta
            )
        if len(data_convert_rule) == 0:
            return None
        dropdown_values = list(data_convert_rule.keys())
        data_validation = DataValidation(
            type="list",
            formula1=f'"{",".join(dropdown_values)}"',
            allowBlank=True,
        )
        return data_validation

    @staticmethod
    def set_style_for_cells(
        worksheet: Worksheet,
        widget_titles: list[AccurateWidgetTitle],
        multi_rows_formatter: MultiRowsFormatter = MultiRowsFormatter.MERGE_CELLS,
        export_scene: ExportScene = ExportScene.EDIT,
        column_split_mode: ColumnSplitMode = ColumnSplitMode.ALL_SPLIT,
    ) -> None:
        # 数据居中
        align = Alignment(horizontal="center", vertical="center", wrap_text=True)
        # 边框
        border = Border(
            left=Side(border_style="thin"),
            right=Side(border_style="thin"),
            top=Side(border_style="thin"),
            bottom=Side(border_style="thin"),
        )
        # 必填项的单元格颜色
        required_widget_font = Font(
            color=Color(rgb="00FF0000"),
        )
        # 只读项的单元格颜色
        read_only_widget_font = Font(
            color=Color(rgb="000000FF"),
        )

        # 工单数据的起始行号。
        if multi_rows_formatter == MultiRowsFormatter.MERGE_CELLS:
            first_data_row = widget_titles[0].filled_show_names_length + 2
        else:
            first_data_row = 3

        # 添加数据验证模块
        data_validations: list[DataValidation | None] = [
            EditExcelFormatHelper.get_data_validation(widget_title) for widget_title in widget_titles
        ]
        for dv in data_validations:
            if dv is not None:
                worksheet.add_data_validation(dv)

        for row in worksheet.iter_rows():
            for index, cell in enumerate(row):
                cell.alignment = align
                cell.border = border

                # 首个单元格里塞入 hyperlink，用于明确该 worksheet 是什么格式。
                if cell.row == 1 and cell.column == 1:
                    cell.hyperlink = Hyperlink(
                        ref="",
                        display=str(cell.value),
                        target="{}.{}.{}".format(str(multi_rows_formatter), str(export_scene), str(column_split_mode)),
                    )
                    cell.font = required_widget_font
                    continue

                # 检测是否为必填、只读的表头
                if index < len(widget_titles):
                    widget_title: AccurateWidgetTitle = widget_titles[index]
                    if multi_rows_formatter == MultiRowsFormatter.MERGE_CELLS:
                        if (
                            cell.row is not None
                            and cell.row < first_data_row
                            and isinstance(cell, Cell)
                            and cell.row > 1
                        ):
                            if widget_title.is_read_only:
                                cell.font = read_only_widget_font
                            else:
                                widget_meta: AccurateWidgetMeta = widget_title.metas[cell.row - 2]
                                if widget_meta.is_required():
                                    cell.value = "*{}".format(cell.value)
                                    cell.font = required_widget_font
                    else:
                        if cell.row is not None and cell.row < first_data_row and isinstance(cell, Cell):
                            if widget_title.is_read_only:
                                cell.font = read_only_widget_font
                            elif widget_title.has_required:
                                cell.value = "*{}".format(cell.value)
                                cell.font = required_widget_font

                # 添加下拉选项
                if (
                    cell.row is not None
                    and cell.row >= first_data_row
                    and isinstance(cell, Cell)
                    and index < len(data_validations)
                ):
                    data_validation: DataValidation | None = data_validations[index]
                    if data_validation is not None:
                        data_validation.add(cell)

                # 因为 pandas 直接导出的 excel 数据中，会有一个行号的列。
                # 而我们已经把这个列给删除，所以超链接的信息会错位。
                try:
                    if cell.hyperlink and cell.row is not None and cell.column is not None:
                        cell.hyperlink.ref = cell.coordinate
                except Exception as e:
                    logger.exception(e)

        return None
