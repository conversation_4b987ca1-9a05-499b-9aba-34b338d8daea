import json
from copy import deepcopy
from functools import cached_property
from hashlib import md5
from typing import Any
from typing import cast

import pandas as pd
from loguru import logger

from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.struct.contants import ENUM_LEVEL_PREFIX
from robot_processor.business_order.struct.contants import FILLED_CHAR
from robot_processor.business_order.struct.contants import MIN_ROW_LENGTH
from robot_processor.business_order.struct.contants import SPACE
from robot_processor.business_order.struct.datas import DataConvertRules
from robot_processor.business_order.struct.datas import PreDefinedWidget
from robot_processor.business_order.struct.entities import AccurateWidgetDetail
from robot_processor.business_order.struct.entities import AccurateWidgetMeta
from robot_processor.business_order.struct.entities import AccurateWidgetTitle
from robot_processor.business_order.struct.entities import FullDataContainer
from robot_processor.business_order.struct.entities import WidgetName
from robot_processor.business_order.struct.entities import WidgetV<PERSON><PERSON><PERSON>ength
from robot_processor.business_order.struct.entities import Worksheet<PERSON>ontainer
from robot_processor.business_order.struct.enums import ColumnSplitMode
from robot_processor.business_order.struct.enums import CommonWidgetType
from robot_processor.business_order.struct.enums import ExportScene
from robot_processor.business_order.struct.enums import MultiRowsFormatter
from robot_processor.business_order.struct.enums import PreDefinedTitle
from robot_processor.business_order.struct.enums import RateType
from robot_processor.ext import db
from robot_processor.form.models import FormVersion
from robot_processor.form.models import Step
from robot_processor.form.models import Widget
from robot_processor.form.models import WidgetInfo
from robot_processor.form.types import WidgetDataType
from robot_processor.form.types import WidgetId
from robot_processor.form.types import WidgetInfoLabel
from robot_processor.form.types import WidgetType

PRE_DEFINED_TITLES: list[PreDefinedTitle] = [
    PreDefinedTitle.BUSINESS_ORDER_ID,
]


class ExtractHelper:
    def __init__(
        self,
        business_order: BusinessOrder,
        export_scene: ExportScene = ExportScene.VIEW,
        column_split_mode: ColumnSplitMode = ColumnSplitMode.ALL_SPLIT,
    ):
        if business_order.form_id is None:
            raise Exception("BusinessOrder form_id is required")
        self.business_order = business_order
        self.export_scene = export_scene
        self.column_split_mode: ColumnSplitMode = column_split_mode

    @cached_property
    def widget_id_to_widget_type_mapping(self) -> dict[WidgetId, WidgetType]:
        return cast(
            dict[WidgetId, WidgetType],
            dict(db.ro_session.query(Widget.id, Widget.schema["type"]).all()),
        )

    @cached_property
    def widget_id_to_widget_meta_mapping(self) -> dict[WidgetId, list]:
        return cast(
            dict[WidgetId, list],
            {
                widget_id: widget_meta or []
                for widget_id, widget_meta in db.ro_session.query(Widget.id, Widget.widget_meta).all()
            },
        )

    @cached_property
    def widget_id_to_widget_data_type_mapping(self) -> dict[WidgetId, WidgetDataType]:
        widgets: list[Widget] = db.ro_session.query(Widget).all()
        return {cast(WidgetId, widget.id): cast(WidgetDataType, widget.schema.get("widget_type")) for widget in widgets}

    @cached_property
    def form_versions(self) -> list[FormVersion]:
        form_versions: list[FormVersion] = (
            db.ro_session.query(FormVersion)
            .filter(FormVersion.form_id == self.business_order.form_id)
            .order_by(FormVersion.id)
            .all()
        )
        return form_versions

    @property
    def form_version_id_to_form_version_mapping(self) -> dict[int, FormVersion]:
        return {form_version.id: form_version for form_version in self.form_versions}

    @property
    def latest_form_version(self) -> FormVersion | None:
        """
        获取最新的 form_version。
        """
        sorted_form_versions: list[FormVersion] = sorted(
            self.form_versions,
            key=lambda fv: fv.id,
            reverse=True,
        )
        if len(sorted_form_versions) == 0:
            return None
        return sorted_form_versions[0]

    def get_business_order_form_version(
        self,
    ) -> FormVersion | None:
        """
        获取工单的 form_version。
        如果工单没有 form_version_id，则尝试找到工单模板的最新版本的 form_version，并返回。
        """
        if self.business_order.form_version_id is not None:
            form_version = self.form_version_id_to_form_version_mapping.get(self.business_order.form_version_id)
            return form_version

        if self.business_order.form_id is None:
            return None
        return self.latest_form_version

    def get_steps(self) -> list[Step]:
        """
        根据工单模板的版本来获取步骤。
        """
        form_version = self.get_business_order_form_version()
        if form_version is None:
            return []
        steps = db.ro_session.query(Step).filter(Step.id.in_(form_version.step_id)).all()
        return steps

    def get_widget_infos_by_steps(
        self, steps: list[Step], filter_not_display_widget_info: bool = True
    ) -> list[WidgetInfo]:
        """
        获取指定步骤名下的所有的 widget_info（根据 widget_info 的 key 来去重）。
        """
        # 获取这一批步骤的所有 widget_collection_id
        widget_collection_ids: list[int] = [
            int(step.widget_collection_id) for step in steps if step.widget_collection_id is not None
        ]

        # 获取这一批步骤的所有 widget_info。
        widget_infos: list[WidgetInfo] = (
            db.ro_session.query(WidgetInfo).filter(WidgetInfo.widget_collection_id.in_(widget_collection_ids)).all()
        )
        widget_info_key_to_widget_info_mapping: dict[str, WidgetInfo] = {
            widget_info.key: widget_info for widget_info in widget_infos if not widget_info.before
        }
        filtered_widget_infos: list[WidgetInfo] = []
        # 过滤掉不需要展示的组件。
        if filter_not_display_widget_info:
            for widget_info in widget_infos:
                # 如果是引用了其他步骤的组件，则需要找到原始组件的 option_value，然后去补齐数据。
                if widget_info.before:
                    original_widget_info = widget_info_key_to_widget_info_mapping.get(widget_info.key) or widget_info
                    before_editable = (widget_info.option_value or {}).get("beforeEditable")
                    widget_info.option_value = deepcopy(original_widget_info.option_value)
                    widget_info.option_value["beforeEditable"] = before_editable
                    filtered_widget_infos.append(widget_info)
                    continue
                filtered_widget_infos.append(widget_info)
        else:
            filtered_widget_infos = widget_infos
        return filtered_widget_infos

    @staticmethod
    def group_widget_infos_by_step_name(
        steps: list[Step],
        widget_infos: list[WidgetInfo],
    ) -> dict[str, list[WidgetInfo]]:
        """
        生成每个步骤名称下的组件，并进行关联关系绑定。
        """
        # 将组件归纳到对应的组件集合中。
        widget_collection_id_to_widget_infos_mapping: dict[str, list[WidgetInfo]] = {}
        for widget_info in widget_infos:
            widget_infos_for_current_widget_collection = (
                widget_collection_id_to_widget_infos_mapping.get(str(widget_info.widget_collection_id)) or []
            )
            widget_infos_for_current_widget_collection = [
                *widget_infos_for_current_widget_collection,
                widget_info,
            ]
            widget_collection_id_to_widget_infos_mapping[
                str(widget_info.widget_collection_id)
            ] = widget_infos_for_current_widget_collection

        # 按照步骤的 widget_collection_id 进行关联。
        step_name_to_widget_infos_mapping: dict[str, list[WidgetInfo]] = {}
        for step in steps:
            if step.widget_collection_id is None:
                continue
            if step.name is None:
                continue
            widget_infos_for_current_step: list[WidgetInfo] | None = widget_collection_id_to_widget_infos_mapping.get(
                step.widget_collection_id,
            )
            if widget_infos_for_current_step is None:
                continue
            step_name_to_widget_infos_mapping[step.name] = widget_infos_for_current_step
        return step_name_to_widget_infos_mapping

    def generate_widget_name_for_widget_info(self, widget_info: WidgetInfo) -> WidgetName:
        """
        基于组件信息去生成其组件名称。
        """
        widget_info_type = self.widget_id_to_widget_type_mapping.get(widget_info.widget_id)  # type: ignore[arg-type]
        # 找不到就把它当作文本。
        if widget_info_type is None:
            widget_info_type = WidgetType("text")
        widget_data_type = self.widget_id_to_widget_data_type_mapping.get(
            widget_info.widget_id  # type: ignore[arg-type]
        )
        # 找不到就把它当作文本。
        if widget_data_type is None:
            widget_data_type = WidgetDataType.STRING
        widget_name = WidgetName(
            widget_label=WidgetInfoLabel(widget_info.label),
            widget_type=widget_info_type,
            widget_data_type=widget_data_type,
        )
        return widget_name

    def generate_step_name_to_widget_item_mapping(
        self,
        step_name_to_widget_infos_mapping: dict[str, list[WidgetInfo]],
    ) -> dict[str, dict[WidgetName, WidgetInfo]]:
        """
        生成步骤名和组件的关联关系。
        """
        step_name_to_widget_item_mapping: dict[str, dict[WidgetName, WidgetInfo]] = {}

        for step_name, widget_infos in step_name_to_widget_infos_mapping.items():
            widget_name_to_widget_info_mapping: dict[WidgetName, WidgetInfo] = {}
            for widget_info in widget_infos:
                widget_name = self.generate_widget_name_for_widget_info(widget_info=widget_info)
                widget_name_to_widget_info_mapping[widget_name] = widget_info
            step_name_to_widget_item_mapping[step_name] = widget_name_to_widget_info_mapping

        return step_name_to_widget_item_mapping

    def extract_sub_widget_infos(self, current_widget_info: WidgetInfo) -> list[WidgetInfo] | None:
        """
        从组件的 option_value 中提取出子组件。
        """
        if current_widget_info.option_value.get("widget_type") in [
            WidgetDataType.TABLE,
            WidgetDataType.COLLECTION,
            WidgetDataType.ARRAY,
        ] and isinstance(fields := current_widget_info.option_value.get("fields"), list):

            sub_widget_infos: list[WidgetInfo] = []

            if current_widget_info.option_value.get("widget_type") in [
                WidgetDataType.TABLE,
            ]:
                sub_widget_infos.append(PreDefinedWidget.get_widget_info_for_table_serial())
            elif current_widget_info.option_value.get("widget_type") in [
                WidgetDataType.ARRAY,
            ]:
                sub_widget_infos.append(PreDefinedWidget.get_widget_info_for_array_serial())

            for field in fields:
                field_option_value = field.get("option_value") or {}
                sub_widget_info = WidgetInfo(
                    key=field.get("key"),
                    widget_id=field.get("id"),
                    option_value=field.get("option_value") or {},
                    data_schema=field.get("data_schema") or {},
                    before=field_option_value.get("before"),
                )
                sub_widget_infos.append(sub_widget_info)

            return sub_widget_infos
        else:
            return None

    def generate_sub_widget_mapping_by_widget_name(
        self,
        widget_name: WidgetName,
        widget_info_for_current_widget_name: WidgetInfo,
    ) -> dict[WidgetName, AccurateWidgetDetail] | None:
        """
        计算组件的子组件信息。
        """
        if widget_name.widget_type in [CommonWidgetType.text]:
            return None
        # 订单组件
        elif widget_name.widget_type in [CommonWidgetType.order]:
            return PreDefinedWidget.get_sub_widget_items_for_order(
                order_widget_info=widget_info_for_current_widget_name
            )
        # 收款信息组件
        elif widget_name.widget_type in [CommonWidgetType.payment_method]:
            return PreDefinedWidget.get_sub_widget_items_for_payment_method()
        # 地址组件
        elif widget_name.widget_type in [CommonWidgetType.address]:
            return PreDefinedWidget.get_sub_widget_items_for_address(self.column_split_mode)
        # 文件组件
        elif widget_name.widget_type in [CommonWidgetType.upload]:
            return PreDefinedWidget.get_sub_widget_items_for_upload()
        # 级联组件
        elif widget_name.widget_type in [
            CommonWidgetType.order_question,
            CommonWidgetType.select_tile,
            CommonWidgetType.select_dropdown,
            CommonWidgetType.radio_tile,
            CommonWidgetType.radio_dropdown,
        ]:
            if self.column_split_mode != ColumnSplitMode.NOT_SPLIT:
                return PreDefinedWidget.get_sub_widget_items_for_enum(
                    enum_widget_type=widget_name.widget_type,
                    enum_widget_info=widget_info_for_current_widget_name,
                )
            else:
                return None
        # 评分组件
        elif widget_name.widget_type in [CommonWidgetType.rate]:
            return PreDefinedWidget.get_sub_widget_items_for_rate()
        # 复合组件、列表组件和容器组件
        elif widget_name.widget_data_type in [
            WidgetDataType.TABLE,
            WidgetDataType.COLLECTION,
            WidgetDataType.ARRAY,
        ]:
            # 获取当前组件名下的所有子组件。
            sub_widget_infos_for_current_widget_name: list[WidgetInfo] = []
            if sub_widget_infos := self.extract_sub_widget_infos(widget_info_for_current_widget_name):
                sub_widget_infos_for_current_widget_name = sub_widget_infos_for_current_widget_name + sub_widget_infos
            # 将子组件进行合并。
            sub_widget_name_to_widget_info_mapping = {
                self.generate_widget_name_for_widget_info(sub_widget_info): sub_widget_info
                for sub_widget_info in sub_widget_infos_for_current_widget_name
            }

            widget_name_to_widget_detail_mapping: dict[WidgetName, AccurateWidgetDetail] = {}

            for (
                sub_widget_name,
                sub_widget_info,
            ) in sub_widget_name_to_widget_info_mapping.items():
                # 该子组件还需要拆分，则进入递归。
                if sub_widget_name.has_sub_widget_items():
                    widget_name_to_widget_detail_mapping.update(
                        {
                            sub_widget_name: AccurateWidgetDetail(
                                widget_info=sub_widget_info,
                                sub_widgets_mapping=self.generate_sub_widget_mapping_by_widget_name(
                                    sub_widget_name, sub_widget_info
                                ),
                            )
                        }
                    )
                # 否则，直接返回。
                else:
                    widget_name_to_widget_detail_mapping.update(
                        {sub_widget_name: AccurateWidgetDetail(widget_info=sub_widget_info, sub_widgets_mapping=None)}
                    )
            return widget_name_to_widget_detail_mapping
        else:
            return None

    def generate_widget_name_to_widget_detail_mapping(
        self,
        widget_name_to_widget_info_mapping: dict[WidgetName, WidgetInfo],
    ) -> dict[WidgetName, AccurateWidgetDetail]:
        """
        生成组件名称和组件详情的关联关系。
        """
        widget_name_to_widget_detail_mapping: dict[WidgetName, AccurateWidgetDetail] = {}
        for widget_name, widget_info in widget_name_to_widget_info_mapping.items():
            widget_name_to_widget_detail_mapping.update(
                {
                    widget_name: AccurateWidgetDetail(
                        widget_info=widget_info,
                        sub_widgets_mapping=self.generate_sub_widget_mapping_by_widget_name(widget_name, widget_info),
                    )
                }
            )
        return widget_name_to_widget_detail_mapping

    def generate_step_name_to_widget_meta_mapping(
        self,
        step_name_to_widget_item_mapping: dict[str, dict[WidgetName, WidgetInfo]],
    ) -> dict[str, dict[WidgetName, AccurateWidgetDetail]]:
        """
        计算每个步骤下的组件的 meta 信息。
        """
        step_name_to_widget_meta_mapping: dict[str, dict[WidgetName, AccurateWidgetDetail]] = {}

        # 拆解步骤，获取组件、子组件以及组件可用的工单模板的版本。
        for (
            step_name,
            widget_name_to_widget_info_mapping,
        ) in step_name_to_widget_item_mapping.items():
            widget_name_to_widget_detail_mapping = self.generate_widget_name_to_widget_detail_mapping(
                widget_name_to_widget_info_mapping,
            )
            step_name_to_widget_meta_mapping.update({step_name: widget_name_to_widget_detail_mapping})

        return step_name_to_widget_meta_mapping

    def generate_widget_titles(
        self,
        widget_name_to_widget_detail_mapping: dict[WidgetName, AccurateWidgetDetail],
        prefix_widget_titles: AccurateWidgetTitle | None = None,
    ) -> list[AccurateWidgetTitle]:
        """
        根据组件名、组件详情去生成表头信息。
        """
        prefix_widget_titles = (
            prefix_widget_titles if prefix_widget_titles is not None else AccurateWidgetTitle(metas=[])
        )

        widget_titles: list[AccurateWidgetTitle] = []
        for widget_name, widget_detail in widget_name_to_widget_detail_mapping.items():
            if not widget_detail.sub_widgets_mapping:
                widget_titles.append(
                    AccurateWidgetTitle(
                        metas=[
                            *prefix_widget_titles.metas,
                            AccurateWidgetMeta(widget_name=widget_name, widget_detail=widget_detail),
                        ],
                    )
                )
            else:
                sub_widget_titles = self.generate_widget_titles(
                    widget_detail.sub_widgets_mapping,
                    AccurateWidgetTitle(
                        metas=[
                            *prefix_widget_titles.metas,
                            AccurateWidgetMeta(widget_name=widget_name, widget_detail=widget_detail),
                        ],
                    ),
                )
                widget_titles = widget_titles + sub_widget_titles

        return widget_titles

    def generate_step_name_to_widget_titles_mapping(
        self,
        step_name_to_widget_meta_mapping: dict[str, dict[WidgetName, AccurateWidgetDetail]],
    ) -> dict[str, list[AccurateWidgetTitle]]:
        """
        计算步骤下的表头信息，并进行关联。
        """
        step_name_to_widget_titles_mapping: dict[str, list[AccurateWidgetTitle]] = {}

        for step_name, widget_meta in step_name_to_widget_meta_mapping.items():
            # 生成该步骤的组件的 title。
            widget_titles: list[AccurateWidgetTitle] = self.generate_widget_titles(widget_meta)
            # 补充 show_names 信息。
            for widget_title in widget_titles:
                show_names = widget_title.generate_show_names(step_name)
                widget_title.show_names = show_names
            step_name_to_widget_titles_mapping[step_name] = widget_titles

        return step_name_to_widget_titles_mapping

    def convert_extracted_data_by_widget_meta(
        self,
        widget_value: Any,
        widget_meta: AccurateWidgetMeta,
        rate_type: RateType | None = None,
    ) -> Any:
        # 获取转换规则。
        data_convert_rule: dict[Any, Any] = {}

        # 无需拆分的地址组件的转换逻辑
        if (
            widget_meta.widget_name.widget_type
            in [
                CommonWidgetType.address,
            ]
            and self.column_split_mode == ColumnSplitMode.NOT_SPLIT
        ):
            if isinstance(widget_value, dict):
                return "{name} {mobile} {state}{city}{zone}{town}{address}".format(
                    name=widget_value.get("name") or "",
                    mobile=widget_value.get("mobile") or "",
                    state=widget_value.get("state") or "",
                    city=widget_value.get("city") or "",
                    zone=widget_value.get("zone") or "",
                    town=widget_value.get("town") or "",
                    address=widget_value.get("address") or "",
                )
            else:
                return widget_value
        elif widget_meta.widget_name.widget_type in [
            CommonWidgetType.payment_method_item_method,
        ]:
            data_convert_rule = DataConvertRules.get_payment_method_value_to_readable_string_mapping()
        elif widget_meta.widget_name.widget_type in [
            CommonWidgetType.rate_type,
        ]:
            data_convert_rule = DataConvertRules.get_rate_type_value_to_readable_string_mapping()
        elif widget_meta.widget_name.widget_type in [
            CommonWidgetType.rate_value,
        ]:
            if rate_type == RateType.star:
                data_convert_rule = DataConvertRules.get_rate_star_value_to_readable_string_mapping()
            elif rate_type == RateType.flower:
                data_convert_rule = DataConvertRules.get_rate_flower_value_to_readable_string_mapping()
            else:
                data_convert_rule = {}
        elif widget_meta.widget_name.widget_data_type in [
            WidgetDataType.BOOLEAN,
        ]:
            data_convert_rule = DataConvertRules.get_boolean_value_to_readable_string_mapping_by_widget_meta(
                widget_meta=widget_meta,
            )
        elif widget_meta.widget_name.widget_type in [
            CommonWidgetType.order_question_level,
            CommonWidgetType.radio_tile_level,
            CommonWidgetType.radio_dropdown_level,
            CommonWidgetType.select_tile_level,
            CommonWidgetType.select_dropdown_level,
        ]:
            data_convert_rule = DataConvertRules.get_enum_value_to_readable_string_mapping_by_widget_meta(
                widget_meta=widget_meta,
            )
        elif widget_meta.widget_detail.widget_info.key in [
            "COMBINE",
        ]:
            data_convert_rule = DataConvertRules.get_product_type_value_to_readable_string_mapping()
        else:
            data_convert_rule = {}

        if not data_convert_rule:
            return widget_value

        # 转换数据。
        try:
            if converted_widget_value := data_convert_rule.get(widget_value):
                return converted_widget_value
            else:
                return widget_value
        except TypeError:
            return widget_value

    def extract_need_convert_widget_values(
        self,
        data: list[Any],
        widget_meta: AccurateWidgetMeta,
    ) -> tuple[list[Any], list[int]]:
        widget_value: list[Any] = []
        widget_value_length: list[int] = []

        for data_item in data:
            if isinstance(data_item, dict):
                widget_value_item = widget_meta.get_widget_value_from_dict(data=data_item)
                rate_type = data_item.get("type")
                converted_widget_value = [
                    (
                        self.convert_extracted_data_by_widget_meta(
                            widget_value=wvi,
                            widget_meta=widget_meta,
                            rate_type=rate_type,
                        )
                    )
                    for wvi in widget_value_item
                ]
                widget_value.extend(converted_widget_value)
                widget_value_length.extend([MIN_ROW_LENGTH for _ in converted_widget_value])
            else:
                converted_widget_value = self.convert_extracted_data_by_widget_meta(
                    widget_value=data_item,
                    widget_meta=widget_meta,
                )
                widget_value.append(converted_widget_value)
                widget_value_length.append(MIN_ROW_LENGTH)
        return widget_value, widget_value_length

    @staticmethod
    def extract_enum_values(
        data: list[Any],
        widget_meta: AccurateWidgetMeta,
        in_iteration: bool,
    ) -> tuple[list[Any], list[int]]:
        # 如果没有数据，则输出一行空值。
        if len(data) == 0:
            return [None], [MIN_ROW_LENGTH]

        # 判断当前项是属于单选还是多选。
        # 单选的数据格式: [{"label": "层级 1", "value": "层级 1"}]
        # 多选的数据格式: [[{"label": "层级 1", "value": "层级 1"}], [{"label": "层级 1", "value": "层级 1"}]]

        widget_value: list[Any] = []
        widget_value_length: list[int] = []

        # 非遍历中的单选
        if (
            widget_meta.widget_name.widget_type
            in [
                CommonWidgetType.radio_tile,
                CommonWidgetType.radio_dropdown,
            ]
            and in_iteration is False
        ):
            widget_value = widget_meta.convert_not_split_radio_widget_value(extracted_value=data)
            widget_value_length.append(MIN_ROW_LENGTH)
        # 多选和遍历中的单选
        else:
            # 如果在遍历中，单选组件的格式则会变为 [[{"label": "层级 1", "value": "层级 1"}]]，处理逻辑与多选趋于相同。
            widget_value = widget_meta.convert_not_split_select_widget_value(extracted_value=data)
            widget_value_length.append(MIN_ROW_LENGTH)
        return widget_value, widget_value_length

    @staticmethod
    def extract_enum_item_values(
        data: list[Any],
        widget_meta: AccurateWidgetMeta,
    ) -> tuple[list[Any], list[int]]:
        level = int(widget_meta.widget_name.widget_label.split(ENUM_LEVEL_PREFIX)[-1])

        # 如果没有数据，则输出一行空值。
        if len(data) == 0:
            return [None], [MIN_ROW_LENGTH]

        # 判断当前项是属于单选还是多选。
        # 单选的数据格式: [{"label": "层级 1", "value": "层级 1"}]
        # 多选的数据格式: [[{"label": "层级 1", "value": "层级 1"}], [{"label": "层级 1", "value": "层级 1"}]]

        widget_value: list[Any] = []
        widget_value_length: list[int] = []

        # 单选
        if widget_meta.widget_name.widget_type in [
            CommonWidgetType.radio_tile_level,
            CommonWidgetType.radio_dropdown_level,
        ]:
            if level > len(data):
                widget_value.append(None)
                widget_value_length.append(MIN_ROW_LENGTH)
            else:
                di = data[level - 1] or {}
                if isinstance(di, dict):
                    extracted_widget_value = widget_meta.get_widget_value_from_dict(di)
                    widget_value.extend(extracted_widget_value)
                    widget_value_length.extend([MIN_ROW_LENGTH for _ in extracted_widget_value])
                else:
                    widget_value.append(di)
                    widget_value_length.append(MIN_ROW_LENGTH)
        # 多选和遍历中的单选
        else:
            # 如果在遍历中，单选组件的格式则会变为 [[{"label": "层级 1", "value": "层级 1"}]]，处理逻辑与多选趋于相同。
            for data_item in data:
                if not isinstance(data_item, list):
                    data_item = []
                if level > len(data_item):
                    widget_value.append(None)
                    widget_value_length.append(MIN_ROW_LENGTH)
                else:
                    di = data_item[level - 1] or {}
                    if isinstance(di, dict):
                        extracted_widget_value = widget_meta.get_widget_value_from_dict(di)
                        widget_value.extend(extracted_widget_value)
                        widget_value_length.extend([MIN_ROW_LENGTH for _ in extracted_widget_value])
                    else:
                        widget_value.append(di)
                        widget_value_length.append(MIN_ROW_LENGTH)
        return widget_value, widget_value_length

    def extract_business_order_value_by_accurate_widget_title(
        self,
        widget_title: AccurateWidgetTitle,
        widget_key_to_value_length_mapping: dict[tuple[WidgetName, ...], WidgetValueLength],
    ) -> list[Any]:
        """
        按照表头的层级关系，逐级抽取数据，以得出最终的组件值。
        """
        if len(widget_title.metas) == 0:
            return []
        data: list | dict | Any = self.business_order.data
        extracted_widget_value: list[Any] = []

        # 组件信息。
        widget_keys: list[tuple[WidgetName, ...]] = []
        widget_key_to_widget_meta_mapping: dict[tuple[WidgetName, ...], AccurateWidgetMeta] = {}
        widget_key_to_value_length_mapping_for_current_widget_title: dict[
            tuple[WidgetName, ...], WidgetValueLength
        ] = {}
        in_iteration: bool = False

        logger.info("正在处理工单 ID: {}, 表头: {} 的数据".format(self.business_order.id, widget_title.actually_show_names))

        # 迭代抽取数据
        for index, widget_meta in enumerate(widget_title.metas):
            # 获取当前层级的 widget key
            widget_key = tuple([meta.widget_name for meta in widget_title.metas][: index + 1])
            widget_keys.append(widget_key)
            widget_key_to_widget_meta_mapping[widget_key] = widget_meta
            # 如果处在复合组件或列表组件下，则是一个迭代中的状态。
            if widget_meta.widget_name.widget_data_type in [
                WidgetDataType.TABLE,
                WidgetDataType.ARRAY,
            ]:
                in_iteration = True

            # 本次提取出的数据。因为至少需要有一行，所以就把数据都放置在列表中，便于处理。
            current_widget_value_length: list[int] = []
            current_widget_value: list[Any] = []

            # 如果数据的类型为 dict。基本只有最外层会是这个情况。
            if isinstance(data, dict):
                if index == len(widget_title.metas) - 1:
                    # 如果是多选组件
                    if (
                        widget_meta.widget_name.widget_type
                        in [
                            CommonWidgetType.order_question,
                            CommonWidgetType.select_tile,
                            CommonWidgetType.select_dropdown,
                        ]
                        and self.column_split_mode == ColumnSplitMode.NOT_SPLIT
                    ):
                        current_widget_value = widget_meta.get_not_split_select_widget_value_from_dict(data=data)
                    # 如果是单选组件
                    elif (
                        widget_meta.widget_name.widget_type
                        in [
                            CommonWidgetType.radio_tile,
                            CommonWidgetType.radio_dropdown,
                        ]
                        and self.column_split_mode == ColumnSplitMode.NOT_SPLIT
                    ):
                        current_widget_value = widget_meta.get_not_split_radio_widget_value_from_dict(data=data)
                    else:
                        current_widget_value = widget_meta.get_widget_value_from_dict(data=data)
                    converted_widget_value = [
                        self.convert_extracted_data_by_widget_meta(
                            widget_value=cwv,
                            widget_meta=widget_meta,
                            rate_type=data.get("rate_type"),
                        )
                        for cwv in current_widget_value
                    ]
                else:
                    converted_widget_value = widget_meta.get_widget_value_from_dict(data=data)

                # 将抽取到的数据存放下来，并当作下次迭代的数据源。
                data = converted_widget_value
                extracted_widget_value = converted_widget_value

                # 如果组件为复合组件、列表组件、多选类组件，则说明他们会是一个多行的数据。
                # 因此长度是需要单独计算的。
                if widget_meta.widget_name.widget_data_type in [WidgetDataType.TABLE, WidgetDataType.ARRAY,] or (
                    widget_meta.widget_name.widget_type
                    in [
                        CommonWidgetType.order_question,
                        CommonWidgetType.select_tile,
                        CommonWidgetType.select_dropdown,
                    ]
                    and self.column_split_mode != ColumnSplitMode.NOT_SPLIT
                ):
                    widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                        original_widget_value_length=[MIN_ROW_LENGTH for _ in converted_widget_value],
                        resized_widget_value_length=[MIN_ROW_LENGTH for _ in converted_widget_value],
                    )
                # 其他类型的组件就是一个单行的结构。
                else:
                    widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                        original_widget_value_length=[MIN_ROW_LENGTH],
                        resized_widget_value_length=[MIN_ROW_LENGTH],
                    )

            elif isinstance(data, list):
                if len(data) == 0:
                    current_widget_value_length = [MIN_ROW_LENGTH]
                    current_widget_value = [None]
                    data = current_widget_value
                    extracted_widget_value = current_widget_value
                    widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                        original_widget_value_length=current_widget_value_length,
                        resized_widget_value_length=current_widget_value_length,
                    )
                    continue

                if widget_meta.is_serial():
                    current_widget_value_length = [MIN_ROW_LENGTH for _ in data]
                    current_widget_value = [None for _ in data]
                    extracted_widget_value = current_widget_value
                    widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                        original_widget_value_length=current_widget_value_length,
                        resized_widget_value_length=current_widget_value_length,
                    )
                    break

                # 如果是列表组件的子项
                if widget_meta.is_item():
                    # 如果已经是最后一层，直接抽取数据。
                    if index == len(widget_title.metas) - 1:
                        # 没有值则视为其有一行空数据。主要是为了 excel 能够填充上。
                        if len(data) == 0:
                            current_widget_value_length = [MIN_ROW_LENGTH]
                            current_widget_value = [None]
                        else:
                            # 获取每一层的数据。
                            for data_item in data:
                                # 如果是序列号，直接视为 None。目前序列号只是作为一个层级关系的表达而已。
                                if widget_meta.is_serial():
                                    widget_value_item = [None]
                                elif (
                                    widget_meta.widget_name.widget_type
                                    in [
                                        CommonWidgetType.order_question,
                                        CommonWidgetType.select_tile,
                                        CommonWidgetType.select_dropdown,
                                        CommonWidgetType.radio_tile,
                                        CommonWidgetType.radio_dropdown,
                                    ]
                                    and self.column_split_mode == ColumnSplitMode.NOT_SPLIT
                                ):
                                    if not isinstance(data_item, list):
                                        data_item = []
                                    widget_value_item, _ = self.extract_enum_values(
                                        data_item, widget_meta, in_iteration=False
                                    )
                                else:
                                    converted_widget_value_item = self.convert_extracted_data_by_widget_meta(
                                        widget_value=data_item,
                                        widget_meta=widget_meta,
                                    )
                                    widget_value_item = [converted_widget_value_item]
                                current_widget_value_length.append(len(widget_value_item))
                                current_widget_value.extend(widget_value_item)
                        extracted_widget_value = current_widget_value

                        widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                            original_widget_value_length=current_widget_value_length,
                            resized_widget_value_length=current_widget_value_length,
                        )
                        break
                    # 如果不是，则需要特殊处理。
                    else:
                        # 如果子项是复合组件或列表组件，则需要把子项数据拆解出来，组合成一个新的列表。
                        if widget_meta.widget_name.widget_data_type in [
                            WidgetDataType.TABLE,
                            WidgetDataType.ARRAY,
                        ]:
                            for data_item in data:
                                # 如果没有值，也认为其至少存在一个空行。
                                formated_data_item = data_item or [None]
                                current_widget_value.extend(formated_data_item)
                                current_widget_value_length.append(len(formated_data_item))
                            widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                                original_widget_value_length=current_widget_value_length,
                                resized_widget_value_length=current_widget_value_length,
                            )
                            data = current_widget_value
                            extracted_widget_value = current_widget_value
                        # 如果不是，则保持不变，交由下一层级继续处理。
                        else:
                            widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                                original_widget_value_length=[MIN_ROW_LENGTH for _ in data],
                                resized_widget_value_length=[MIN_ROW_LENGTH for _ in data],
                            )
                        continue

                # 如果是选择组件，并且处于最终层级，则直接提取数据。
                elif (
                    index == len(widget_title.metas) - 1
                    and widget_meta.widget_name.widget_type
                    in [
                        CommonWidgetType.select_tile,
                        CommonWidgetType.select_dropdown,
                        CommonWidgetType.order_question,
                        CommonWidgetType.radio_tile,
                        CommonWidgetType.radio_dropdown,
                    ]
                    and self.column_split_mode == ColumnSplitMode.NOT_SPLIT
                ):
                    for data_item in data:
                        if isinstance(data_item, dict):
                            item_container = widget_meta.get_widget_value_from_dict(data=data_item)
                        else:
                            item_container = [data_item]
                        (current_widget_value_item, current_widget_value_item_length,) = self.extract_enum_values(
                            data=item_container, widget_meta=widget_meta, in_iteration=in_iteration
                        )
                        current_widget_value.extend(current_widget_value_item)
                        current_widget_value_length.extend(current_widget_value_item_length)

                    extracted_widget_value = current_widget_value

                    widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                        original_widget_value_length=current_widget_value_length,
                        resized_widget_value_length=current_widget_value_length,
                    )
                    break
                # 如果需要处于最终层级，并且需要抽取的组件值是属于 full_address，则特殊处理一下。
                elif (
                    index == len(widget_title.metas) - 1
                    and widget_meta.widget_name.widget_type
                    in [
                        CommonWidgetType.full_address,
                    ]
                    and self.column_split_mode == ColumnSplitMode.PART_SPLIT
                ):
                    for data_item in data:
                        if isinstance(data_item, dict):
                            current_widget_value_item = widget_meta.get_full_address_widget_value_from_dict(data_item)
                        else:
                            current_widget_value_item = [None]
                        current_widget_value.extend(current_widget_value_item)
                        current_widget_value_length.append(len(current_widget_value_item))

                    extracted_widget_value = current_widget_value

                    widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                        original_widget_value_length=current_widget_value_length,
                        resized_widget_value_length=current_widget_value_length,
                    )
                    break
                # 如果是选择组件的下属子组件。则需要检测层级信息，提取不同位置的数据。
                elif widget_meta.widget_name.widget_type in [
                    CommonWidgetType.select_tile_level,
                    CommonWidgetType.select_dropdown_level,
                    CommonWidgetType.order_question_level,
                    CommonWidgetType.radio_tile_level,
                    CommonWidgetType.radio_dropdown_level,
                ]:
                    if in_iteration:
                        for data_item in data:
                            if not isinstance(data_item, list):
                                data_item = []
                            (
                                current_widget_value_item,
                                current_widget_value_item_length,
                            ) = self.extract_enum_item_values(data=data_item, widget_meta=widget_meta)
                            current_widget_value.extend(current_widget_value_item)
                            current_widget_value_length.extend(current_widget_value_item_length)
                    else:
                        (
                            current_widget_value,
                            current_widget_value_length,
                        ) = self.extract_enum_item_values(data=data, widget_meta=widget_meta)

                    extracted_widget_value = current_widget_value

                    widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                        original_widget_value_length=current_widget_value_length,
                        resized_widget_value_length=current_widget_value_length,
                    )
                    break

                # 部分类型的组件，需要进行数据转化，为提供可读性的信息。
                elif (
                    widget_meta.widget_name.widget_type
                    in [
                        CommonWidgetType.rate_type,
                        CommonWidgetType.rate_value,
                        CommonWidgetType.payment_method_item_method,
                    ]
                    or widget_meta.widget_name.widget_data_type
                    in [
                        WidgetDataType.BOOLEAN,
                    ]
                    or widget_meta.widget_detail.widget_info.key
                    in [
                        "COMBINE",
                    ]
                ):
                    (
                        current_widget_value,
                        current_widget_value_length,
                    ) = self.extract_need_convert_widget_values(data=data, widget_meta=widget_meta)

                    extracted_widget_value = current_widget_value

                    widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                        original_widget_value_length=current_widget_value_length,
                        resized_widget_value_length=current_widget_value_length,
                    )
                    break

                # 其他组件的处理逻辑
                else:
                    for data_item in data:
                        if isinstance(data_item, dict):
                            widget_value_item = widget_meta.get_widget_value_from_dict(data=data_item)
                        else:
                            widget_value_item = [data_item]
                        if (
                            widget_meta.widget_name.widget_type
                            in [
                                CommonWidgetType.select_tile,
                                CommonWidgetType.select_dropdown,
                                CommonWidgetType.order_question,
                                CommonWidgetType.radio_tile,
                                CommonWidgetType.radio_dropdown,
                            ]
                            and in_iteration
                        ):
                            current_widget_value.append(widget_value_item)
                            if widget_meta.widget_name.widget_data_type in [
                                CommonWidgetType.radio_tile,
                                CommonWidgetType.radio_dropdown,
                            ]:
                                current_widget_value_length.append(MIN_ROW_LENGTH)
                            else:
                                current_widget_value_length.append(len(widget_value_item))
                        else:
                            current_widget_value.extend(widget_value_item)
                            current_widget_value_length.append(len(widget_value_item))
                data = current_widget_value
                extracted_widget_value = current_widget_value

                widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                    original_widget_value_length=current_widget_value_length,
                    resized_widget_value_length=current_widget_value_length,
                )

            # 理论上不该出现的场景。
            else:
                logger.warning(f"非正常场景: {widget_key}, bo_data: {self.business_order.data}")

                extracted_widget_value = [data]
                widget_key_to_value_length_mapping_for_current_widget_title[widget_key] = WidgetValueLength(
                    original_widget_value_length=[MIN_ROW_LENGTH],
                    resized_widget_value_length=[MIN_ROW_LENGTH],
                )

        # 基于抽取出的数据，计算缩放信息。
        ExtractHelper.merge_widget_value_length(
            widget_keys,
            widget_key_to_value_length_mapping_for_current_widget_title,
            widget_key_to_value_length_mapping,
        )

        return extracted_widget_value

    @staticmethod
    def merge_widget_value_length(
        widget_keys: list[tuple[WidgetName, ...]],
        widget_key_to_value_length_mapping_for_current_widget_title: dict[tuple[WidgetName, ...], WidgetValueLength],
        widget_key_to_value_length_mapping: dict[tuple[WidgetName, ...], WidgetValueLength],
    ) -> dict[tuple[WidgetName, ...], WidgetValueLength]:
        """
        计算并合并 widget_value_length 信息。
        """
        # 对组件头对应的长度信息进行调整
        # 先按照当前组件内的信息，自底向上，放大长度。
        current_widget_key: tuple[WidgetName, ...] = widget_keys[-1]
        current_widget_value_length = widget_key_to_value_length_mapping_for_current_widget_title[current_widget_key]
        for parent_widget_key in widget_keys[:-1][::-1]:
            parent_widget_value_length = widget_key_to_value_length_mapping_for_current_widget_title[parent_widget_key]
            resize_widget_value_length = []
            # 当前组件的缩放值和父组件长度一致，则说明他们表层行信息一致，选择其中最长的作为最终缩放值。
            if len(parent_widget_value_length.resized_widget_value_length) == len(
                current_widget_value_length.resized_widget_value_length
            ):
                for parent, current in zip(
                    parent_widget_value_length.resized_widget_value_length,
                    current_widget_value_length.resized_widget_value_length,
                ):
                    if parent < current:
                        resize_widget_value_length.append(current)
                    else:
                        resize_widget_value_length.append(parent)
            # 以父组件的缩放值长度，去将当前组件值进行分组，明确树状结构走向。并选择较大的缩放值。
            else:
                size_index = 0
                for parent_widget_size in parent_widget_value_length.resized_widget_value_length:
                    current_widget_size = sum(
                        current_widget_value_length.resized_widget_value_length[
                            size_index : size_index + parent_widget_size
                        ]
                    )
                    size_index += parent_widget_size
                    if current_widget_size >= parent_widget_size:
                        resize_widget_value_length.append(current_widget_size)
                    else:
                        resize_widget_value_length.append(parent_widget_size)

            widget_key_to_value_length_mapping_for_current_widget_title[
                parent_widget_key
            ].resized_widget_value_length = resize_widget_value_length
            current_widget_value_length = widget_key_to_value_length_mapping_for_current_widget_title[parent_widget_key]

        # 将当前计算出的组件长度信息和既存的组件长度信息进行比对，留下单位长度较大的那一个。
        for (
            widget_key,
            widget_value_length,
        ) in widget_key_to_value_length_mapping_for_current_widget_title.items():
            if exists_widget_value_length := widget_key_to_value_length_mapping.get(widget_key):
                resize_value_length = []
                for exists, this in zip(
                    exists_widget_value_length.resized_widget_value_length,
                    widget_value_length.resized_widget_value_length,
                ):
                    if this > exists:
                        resize_value_length.append(this)
                    else:
                        resize_value_length.append(exists)
                widget_key_to_value_length_mapping[widget_key] = WidgetValueLength(
                    original_widget_value_length=widget_value_length.original_widget_value_length,
                    resized_widget_value_length=resize_value_length,
                )
            else:
                widget_key_to_value_length_mapping[widget_key] = widget_value_length

        # 自顶向下，按照当前父组件的最大长度，对子组件进行长度缩放。
        ExtractHelper.resize_sub_widget_key_value_length(
            widget_keys[0],
            widget_key_to_value_length_mapping,
        )

        for (
            key,
            value,
        ) in widget_key_to_value_length_mapping_for_current_widget_title.items():
            original_value = widget_key_to_value_length_mapping.get(key)
            if original_value is None:
                widget_key_to_value_length_mapping[key] = value

        return widget_key_to_value_length_mapping

    @staticmethod
    def get_max_sibling_value_length(
        current_key: tuple[WidgetName, ...],
        self_or_sibling_keys: list[tuple[WidgetName, ...]],
        widget_key_to_value_length_mapping: dict[tuple[WidgetName, ...], WidgetValueLength],
    ) -> list[int]:
        """
        获取当前组件所处在的层级中，最大长度的兄弟节点，可以以该节点的长度去将自身进行缩放。
        """
        max_sibling_value_length = widget_key_to_value_length_mapping[current_key].resized_widget_value_length
        for sibling_key in self_or_sibling_keys:
            sibling_value_length = widget_key_to_value_length_mapping.get(sibling_key)
            if sibling_value_length is None:
                continue
            if len(sibling_value_length.resized_widget_value_length) == len(max_sibling_value_length) and sum(
                sibling_value_length.resized_widget_value_length
            ) > sum(max_sibling_value_length):
                max_sibling_value_length = sibling_value_length.resized_widget_value_length
        return max_sibling_value_length

    @staticmethod
    def resize_sub_widget_key_value_length(
        parent_key: tuple[WidgetName, ...],
        widget_key_to_value_length_mapping: dict[tuple[WidgetName, ...], WidgetValueLength],
    ) -> None:
        """
        计算需要缩放的子组件的值的长度信息。
        """
        parent_value_length = widget_key_to_value_length_mapping[parent_key]
        self_or_sibling_key_to_value_length_mapping: dict[tuple[WidgetName, ...], WidgetValueLength] = {
            key: widget_key_to_value_length_mapping[key]
            for key in widget_key_to_value_length_mapping.keys()
            if len(key) == len(parent_key) + 1 and key[: len(parent_key)] == parent_key
        }
        self_or_sibling_keys = list(self_or_sibling_key_to_value_length_mapping.keys())
        if len(self_or_sibling_keys) == 0:
            return
        for (
            self_or_sibling_key,
            self_or_sibling_value_length,
        ) in self_or_sibling_key_to_value_length_mapping.items():
            resized_current_value_length = []
            start_index = 0
            max_sibling_value_length = ExtractHelper.get_max_sibling_value_length(
                self_or_sibling_key,
                self_or_sibling_keys,
                widget_key_to_value_length_mapping,
            )
            for index, length in enumerate(parent_value_length.original_widget_value_length):
                group_current_value_length = max_sibling_value_length[start_index : start_index + length]
                if (
                    len(group_current_value_length) > 0
                    and sum(group_current_value_length) < parent_value_length.resized_widget_value_length[index]
                ):
                    group_current_value_length[-1] += parent_value_length.resized_widget_value_length[index] - sum(
                        group_current_value_length
                    )
                resized_current_value_length.extend(group_current_value_length)
                start_index += length
            widget_key_to_value_length_mapping[
                self_or_sibling_key
            ].resized_widget_value_length = resized_current_value_length
            ExtractHelper.resize_sub_widget_key_value_length(
                self_or_sibling_key,
                widget_key_to_value_length_mapping,
            )

    @staticmethod
    def resize_widget_values(
        widget_titles: list[AccurateWidgetTitle],
        widget_values: list[list[Any]],
        widget_key_to_value_length_mapping: dict[tuple[WidgetName, ...], WidgetValueLength],
        multi_rows_formatter: MultiRowsFormatter = MultiRowsFormatter.MERGE_CELLS,
    ) -> list[list[Any]]:
        """
        将提取的组件值进行缩放，以达成他们最终输出的行级信息不错乱。
        """
        resized_widget_values: list[list[Any]] = []

        for widget_title, widget_value in zip(widget_titles, widget_values):
            parent_value_length = widget_key_to_value_length_mapping.get(widget_title.parent_widget_key)
            # 如果该组件就是最外层的组件，则直接加入数据
            if parent_value_length is None:
                if len(widget_value) > 0 and widget_value[0] is None:
                    widget_value[0] = SPACE
                resized_widget_values.append(widget_value)
                continue
            assert parent_value_length is not None

            # 获取当前的组件的长度信息。
            current_value_length = widget_key_to_value_length_mapping[widget_title.widget_key]

            resized_widget_value: list[Any] = []

            # 为序列补充数据
            if widget_title.metas[-1].is_serial():
                if len(widget_title.parent_widget_key) == 1:
                    widget_value = [i for i in range(1, len(widget_value) + 1)]
                else:
                    formatted_widget_value = []
                    start_index = 0
                    for length in parent_value_length.original_widget_value_length:
                        for index, _ in enumerate(widget_value[start_index : start_index + length], 1):
                            formatted_widget_value.append(index)
                        start_index += length
                    widget_value = formatted_widget_value

            for current_value, resized_length in zip(widget_value, current_value_length.resized_widget_value_length):
                if multi_rows_formatter == MultiRowsFormatter.MERGE_CELLS:
                    current_resize_widget_value = [
                        (current_value if current_value not in [FILLED_CHAR, None] else SPACE),
                        *[None for _ in range(resized_length - 1)],
                    ]
                    resized_widget_value.extend(current_resize_widget_value)
                elif multi_rows_formatter == MultiRowsFormatter.SPLIT:
                    latest_value = current_value if current_value not in [FILLED_CHAR, None] else SPACE
                    current_resize_widget_value = [latest_value for _ in range(resized_length)]
                    resized_widget_value.extend(current_resize_widget_value)
                else:
                    if current_value not in [FILLED_CHAR, None, SPACE]:
                        resized_widget_value = ["\n".join([*resized_widget_value, str(current_value)])]

            if len(resized_widget_value) == 0:
                resized_widget_value = [SPACE]

            resized_widget_values.append(resized_widget_value)
        return resized_widget_values

    def extract_business_order_values_by_steps(
        self,
        step_name_to_widget_titles_mapping: dict[str, list[AccurateWidgetTitle]],
        multi_rows_formatter: MultiRowsFormatter = MultiRowsFormatter.MERGE_CELLS,
    ) -> tuple[list[AccurateWidgetTitle], list[list[Any]]]:
        """
        提取每个步骤下的组件值，并将他们按序排列。
        """
        full_widget_titles: list[AccurateWidgetTitle] = []
        full_widget_values: list[list[Any]] = []

        for step_name, widget_titles in step_name_to_widget_titles_mapping.items():
            widget_values: list[list[Any]] = []
            widget_key_to_value_length_mapping: dict[tuple[WidgetName, ...], WidgetValueLength] = {}
            for widget_title in widget_titles:
                widget_value = self.extract_business_order_value_by_accurate_widget_title(
                    widget_title,
                    widget_key_to_value_length_mapping,
                )
                widget_values.append(widget_value)

            resized_widget_values = self.resize_widget_values(
                widget_titles,
                widget_values,
                widget_key_to_value_length_mapping,
                multi_rows_formatter,
            )
            full_widget_titles = [*full_widget_titles, *widget_titles]
            full_widget_values = [*full_widget_values, *resized_widget_values]

        return full_widget_titles, full_widget_values

    def extract_business_order_values_by_pre_defined_titles(
        self,
    ) -> tuple[list[AccurateWidgetTitle], list[list[Any]]]:
        """
        提取预定义的表头对应的组件值。（主要还是因为之前没有设置过“工单 ID”这个系统字段，所以才需要单独处理。）
        """
        widget_titles: list[AccurateWidgetTitle] = []
        widget_values: list[list[Any]] = []
        for title in PRE_DEFINED_TITLES:
            match title:
                case PreDefinedTitle.BUSINESS_ORDER_ID:
                    widget_titles.append(PreDefinedWidget.get_widget_title_for_business_order_id())
                    widget_values.append([self.business_order.id])

        return widget_titles, widget_values

    @staticmethod
    def fill_widget_titles(
        widget_titles: list[AccurateWidgetTitle],
    ) -> list[AccurateWidgetTitle]:
        """
        填充提取出来的表头名称，使其能够维持在同一个行数。避免引发错位。
        """
        max_show_names_length = max([widget_title.actually_show_names_length for widget_title in widget_titles])

        # 将表头展示名填充到相同的长度
        for widget_title in widget_titles:
            if widget_title.filled_show_names_length < max_show_names_length:
                widget_title.show_names = [
                    *widget_title.show_names,
                    *[FILLED_CHAR for _ in range(max_show_names_length - widget_title.filled_show_names_length)],
                ]

        return widget_titles

    @staticmethod
    def fill_widget_values(
        widget_values: list[list[Any]],
        multi_rows_formatter: MultiRowsFormatter = MultiRowsFormatter.MERGE_CELLS,
    ) -> list[list[Any]]:
        """
        填充提取出来的组件值，使其能够维持在同一个行数。避免引发错位。
        """
        max_widget_value_depth = max([len(widget_value) for widget_value in widget_values])

        filled_widget_values: list[list[Any]] = []
        for widget_value in widget_values:
            current_widget_value_depth = len(widget_value)
            if current_widget_value_depth < max_widget_value_depth:
                if multi_rows_formatter == MultiRowsFormatter.SPLIT:
                    # if len(widget_value) == 0:
                    #     filled_value = None
                    # else:
                    #     filled_value = widget_value[-1]
                    # filled_widget_values.append(
                    #     [
                    #         *widget_value,
                    #         *[
                    #             filled_value
                    #             for _ in range(
                    #                 max_widget_value_depth - current_widget_value_depth
                    #             )
                    #         ],
                    #     ]
                    # )
                    filled_value = None
                    filled_widget_values.append(
                        [
                            *widget_value,
                            *[filled_value for _ in range(max_widget_value_depth - current_widget_value_depth)],
                        ]
                    )
                else:
                    filled_widget_values.append(
                        [
                            *widget_value,
                            *[None for _ in range(max_widget_value_depth - current_widget_value_depth)],
                        ]
                    )
            else:
                filled_widget_values.append(widget_value)
        return filled_widget_values

    @staticmethod
    def filter_widget_titles_by_show_names(
        widget_titles: list[AccurateWidgetTitle],
        needed_widget_title_show_names: list[list[str]],
    ) -> dict[tuple[str, ...], AccurateWidgetTitle]:
        """
        根据需要的表头名称，去过滤出实际的表头信息，并将其与名称进行关联。
        """
        if len(widget_titles) == 0:
            return {}
        if widget_titles[0].actually_show_names_length == 0:
            return {}

        formatted_widget_title_show_names = [
            [show_name for show_name in widget_title_show_name if show_name != FILLED_CHAR]
            for widget_title_show_name in needed_widget_title_show_names
        ]

        filtered_widget_titles: dict[tuple[str, ...], AccurateWidgetTitle] = {}
        for widget_title in widget_titles:
            if widget_title.actually_show_names in formatted_widget_title_show_names:
                key = tuple(widget_title.show_names)
                filtered_widget_titles[key] = widget_title

        return filtered_widget_titles

    def generate_step_name_to_filtered_widget_titles_mapping(
        self,
        step_name_to_widget_titles_mapping: dict[str, list[AccurateWidgetTitle]],
        needed_widget_title_show_names: list[list[str]],
    ) -> dict[str, list[AccurateWidgetTitle]]:
        """
        根据需要的表头名称，过滤出每一个同名步骤下的表头。
        """
        step_name_to_filtered_widget_titles_mapping = {}

        for step_name, widget_titles in step_name_to_widget_titles_mapping.items():
            filtered_widget_titles = self.filter_widget_titles_by_show_names(
                widget_titles=widget_titles,
                needed_widget_title_show_names=needed_widget_title_show_names,
            )
            step_name_to_filtered_widget_titles_mapping[step_name] = [i for i in filtered_widget_titles.values()]
        return step_name_to_filtered_widget_titles_mapping

    def compute_widget_titles_for_steps(self) -> dict[str, list[AccurateWidgetTitle]]:
        """
        计算每个步骤上的表头信息。
        """
        steps: list[Step] = self.get_steps()
        widget_infos: list[WidgetInfo] = self.get_widget_infos_by_steps(steps=steps)

        # 将组件分类存至对应的步骤上。
        step_name_to_widget_infos_mapping = self.group_widget_infos_by_step_name(
            steps=steps,
            widget_infos=widget_infos,
        )

        # 基于组件去计算表头。
        step_name_to_widget_item_mapping = self.generate_step_name_to_widget_item_mapping(
            step_name_to_widget_infos_mapping=step_name_to_widget_infos_mapping,
        )
        step_name_to_widget_meta_mapping = self.generate_step_name_to_widget_meta_mapping(
            step_name_to_widget_item_mapping=step_name_to_widget_item_mapping,
        )
        step_name_to_widget_titles_mapping = self.generate_step_name_to_widget_titles_mapping(
            step_name_to_widget_meta_mapping=step_name_to_widget_meta_mapping,
        )
        return step_name_to_widget_titles_mapping

    @staticmethod
    def filter_pre_defined_widget_title_and_values(
        pre_defined_widget_titles: list[AccurateWidgetTitle],
        pre_defined_widget_values: list[list[Any]],
        needed_widget_title_show_names: list[list[str]],
    ) -> tuple[list[AccurateWidgetTitle], list[list[Any]]]:
        """
        根据传入的表头名称，过滤出需要保留的预定义表头和表头对应的数据。
        """
        filtered_pre_defined_widget_titles: list[AccurateWidgetTitle] = []
        filtered_pre_defined_widget_values: list[list[Any]] = []

        for pre_defined_widget_title, pre_defined_widget_value in zip(
            pre_defined_widget_titles, pre_defined_widget_values
        ):
            if pre_defined_widget_title.actually_show_names in needed_widget_title_show_names:
                filtered_pre_defined_widget_titles.append(pre_defined_widget_title)
                filtered_pre_defined_widget_values.append(pre_defined_widget_value)
        return filtered_pre_defined_widget_titles, filtered_pre_defined_widget_values

    def compute_full_dataframe_for_business_order(
        self,
        step_name_to_widget_titles_mapping: dict[str, list[AccurateWidgetTitle]],
        needed_widget_title_show_names: list[list[str]],
        multi_rows_formatter: MultiRowsFormatter = MultiRowsFormatter.MERGE_CELLS,
    ) -> pd.DataFrame:
        # 根据预定义的表头来从工单中提取出数据。
        (
            pre_defined_widget_titles,
            pre_defined_widget_values,
        ) = self.extract_business_order_values_by_pre_defined_titles()

        # 将预定义表头的数据进行过滤。
        (
            filtered_pre_defined_widget_titles,
            filtered_pre_defined_widget_values,
        ) = self.filter_pre_defined_widget_title_and_values(
            pre_defined_widget_titles=pre_defined_widget_titles,
            pre_defined_widget_values=pre_defined_widget_values,
            needed_widget_title_show_names=needed_widget_title_show_names,
        )

        # 过滤步骤上的组件对应的表头。
        step_name_to_filtered_widget_titles_mapping = self.generate_step_name_to_filtered_widget_titles_mapping(
            step_name_to_widget_titles_mapping=step_name_to_widget_titles_mapping,
            needed_widget_title_show_names=needed_widget_title_show_names,
        )
        # 提取每一个步骤的表头对应的数据。
        (filtered_step_widget_titles, filtered_step_widget_values,) = self.extract_business_order_values_by_steps(
            step_name_to_widget_titles_mapping=step_name_to_filtered_widget_titles_mapping,
            multi_rows_formatter=multi_rows_formatter,
        )

        final_step_widget_titles: list[AccurateWidgetTitle] = []
        final_step_widget_values = []

        widget_titles = []
        for step_widget_titles in step_name_to_widget_titles_mapping.values():
            widget_titles.extend(step_widget_titles)

        filtered_widget_title_show_names_to_widget_title_mapping: dict[
            tuple[str, ...], AccurateWidgetTitle
        ] = self.filter_widget_titles_by_show_names(
            widget_titles=widget_titles,
            needed_widget_title_show_names=needed_widget_title_show_names,
        )
        if filtered_pre_defined_widget_titles:
            for filtered_pre_defined_widget_title, filtered_pre_defined_widget_value in zip(
                filtered_pre_defined_widget_titles, filtered_pre_defined_widget_values
            ):
                filtered_widget_title_show_names_to_widget_title_mapping.update(
                    {tuple(filtered_pre_defined_widget_title.actually_show_names): filtered_pre_defined_widget_title}
                )
                filtered_step_widget_titles.append(filtered_pre_defined_widget_title)
                filtered_step_widget_values.append(filtered_pre_defined_widget_value)

        for needed_widget_title_show_name in needed_widget_title_show_names:
            key = tuple([show_name for show_name in needed_widget_title_show_name if show_name != FILLED_CHAR])
            if widget_title := filtered_widget_title_show_names_to_widget_title_mapping.get(key):
                index = filtered_step_widget_titles.index(widget_title)
                final_step_widget_values.append(filtered_step_widget_values[index])
                final_step_widget_titles.append(filtered_step_widget_titles[index])
            else:
                final_step_widget_values.append([SPACE])
                final_step_widget_titles.append(AccurateWidgetTitle(metas=[], show_names=list(key)))

        # 将表头及其对应的数据进行内容填充。
        filled_widget_titles = self.fill_widget_titles(final_step_widget_titles)
        filled_widget_values = self.fill_widget_values(
            final_step_widget_values,
            multi_rows_formatter=multi_rows_formatter,
        )

        # 将非 None 的数据，全部转化为 string 类型。
        converted_widget_values: list[list[str | None]] = [
            [str(i) if i is not None else None for i in filled_widget_value]
            for filled_widget_value in filled_widget_values
        ]

        # 设定表头。
        # 合并单元格会使用基于合并单元格的多层级的表头。
        if multi_rows_formatter == MultiRowsFormatter.MERGE_CELLS:
            columns: pd.MultiIndex = pd.MultiIndex.from_tuples(
                [
                    filled_widget_title.show_names
                    for filled_widget_title in filled_widget_titles
                    if filled_widget_title.show_names is not None
                ],
            )
        # 其他场景则使用平铺到一个层级的表头。
        else:
            columns = pd.MultiIndex.from_tuples(
                [("_".join(filled_widget_title.actually_show_names),) for filled_widget_title in filled_widget_titles]
            )

        # 将表头和数据进行关联。
        pandas_data = {}
        for k, v in zip(columns, converted_widget_values):
            pandas_data.update({k: v})

        current_business_order_dataframe = pd.DataFrame(pandas_data, columns=columns)
        return current_business_order_dataframe

    @staticmethod
    def update_full_data_container(
        dataframe: pd.DataFrame,
        full_data_container: FullDataContainer,
    ) -> FullDataContainer:
        current_business_order_row_count = len(dataframe)

        full_data_container.dataframes.append(dataframe)
        full_data_container.business_order_rows.append(current_business_order_row_count)
        full_data_container.rows_count_for_excel += current_business_order_row_count
        return full_data_container

    def compute_dataframe_for_business_order(
        self,
        step_name_to_widget_titles_mapping: dict[str, list[AccurateWidgetTitle]],
        needed_widget_title_show_names: list[list[str]],
        multi_rows_formatter: MultiRowsFormatter = MultiRowsFormatter.MERGE_CELLS,
    ) -> tuple[pd.DataFrame, list[AccurateWidgetTitle]]:
        """
        从工单中提取出一个 pandas 的 dataframe 数据。
        """
        # 根据预定义的表头来从工单中提取出数据。
        (
            pre_defined_widget_titles,
            pre_defined_widget_values,
        ) = self.extract_business_order_values_by_pre_defined_titles()

        # 将预定义表头的数据进行过滤。
        (
            filtered_pre_defined_widget_titles,
            filtered_pre_defined_widget_values,
        ) = self.filter_pre_defined_widget_title_and_values(
            pre_defined_widget_titles=pre_defined_widget_titles,
            pre_defined_widget_values=pre_defined_widget_values,
            needed_widget_title_show_names=needed_widget_title_show_names,
        )

        # 过滤步骤上的组件对应的表头。
        step_name_to_filtered_widget_titles_mapping = self.generate_step_name_to_filtered_widget_titles_mapping(
            step_name_to_widget_titles_mapping=step_name_to_widget_titles_mapping,
            needed_widget_title_show_names=needed_widget_title_show_names,
        )
        # 提取每一个步骤的表头对应的数据。
        (filtered_step_widget_titles, filtered_step_widget_values,) = self.extract_business_order_values_by_steps(
            step_name_to_widget_titles_mapping=step_name_to_filtered_widget_titles_mapping,
            multi_rows_formatter=multi_rows_formatter,
        )

        # 将表头及其对应的数据进行内容填充。
        filled_widget_titles = self.fill_widget_titles(filtered_pre_defined_widget_titles + filtered_step_widget_titles)
        filled_widget_values = self.fill_widget_values(
            filtered_pre_defined_widget_values + filtered_step_widget_values,
            multi_rows_formatter=multi_rows_formatter,
        )

        # 将非 None 的数据，全部转化为 string 类型。
        converted_widget_values: list[list[str | None]] = [
            [str(i) if i is not None else None for i in filled_widget_value]
            for filled_widget_value in filled_widget_values
        ]

        # 设定表头。
        # 合并单元格会使用基于合并单元格的多层级的表头。
        if multi_rows_formatter == MultiRowsFormatter.MERGE_CELLS:
            columns: pd.MultiIndex = pd.MultiIndex.from_tuples(
                [
                    filled_widget_title.show_names
                    for filled_widget_title in filled_widget_titles
                    if filled_widget_title.show_names is not None
                ],
            )
        # 其他场景则使用平铺到一个层级的表头。
        else:
            columns = pd.MultiIndex.from_tuples(
                [("_".join(filled_widget_title.actually_show_names),) for filled_widget_title in filled_widget_titles]
            )

        # 将表头和数据进行关联。
        pandas_data = {}
        for k, v in zip(columns, converted_widget_values):
            pandas_data.update({k: v})

        current_business_order_dataframe = pd.DataFrame(pandas_data, columns=columns)
        return current_business_order_dataframe, filled_widget_titles

    def append_dataframe_to_worksheet(
        self,
        dataframe: pd.DataFrame,
        widget_titles: list[AccurateWidgetTitle],
        worksheet_container: WorksheetContainer,
    ) -> WorksheetContainer:
        """
        将 dataframe 加入到同 worksheet 名下。
        """
        business_order_form_version = self.get_business_order_form_version()
        if business_order_form_version is None:
            return worksheet_container

        worksheet_tag = self.generate_worksheet_tag(widget_titles)

        # 将当前工单的 dataframe 加到所属的 worksheet 中。
        current_business_order_row_count = len(dataframe)
        worksheet_container.rows_count_for_excel += current_business_order_row_count

        dataframes = worksheet_container.worksheet_tag_to_dataframes_mapping.get(worksheet_tag) or []
        dataframes.append(dataframe)
        worksheet_container.worksheet_tag_to_dataframes_mapping.update({worksheet_tag: dataframes})

        business_order_rows = worksheet_container.worksheet_tag_to_business_order_rows_mapping.get(worksheet_tag) or []
        business_order_rows.append(current_business_order_row_count)
        worksheet_container.worksheet_tag_to_business_order_rows_mapping.update({worksheet_tag: business_order_rows})

        worksheet_container.worksheet_tag_to_widget_titles_mapping.update({worksheet_tag: widget_titles})

        form_versions = worksheet_container.worksheet_tag_to_form_versions_mapping.get(worksheet_tag) or set()
        form_versions.add(business_order_form_version)
        worksheet_container.worksheet_tag_to_form_versions_mapping.update({worksheet_tag: form_versions})

        return worksheet_container

    @staticmethod
    def generate_worksheet_tag(filtered_widget_titles: list[AccurateWidgetTitle]) -> str:
        """
        基于最终需要使用的表头信息，计算出一个 hash 值。
        """
        messages = []
        for filtered_widget_title in filtered_widget_titles:
            messages.append(filtered_widget_title.to_dict())
        messages_json = json.dumps(messages, sort_keys=True)
        md5_hexdigest = md5(messages_json.encode()).hexdigest()
        worksheet_tag = "版本 {}".format(md5_hexdigest)
        return worksheet_tag
