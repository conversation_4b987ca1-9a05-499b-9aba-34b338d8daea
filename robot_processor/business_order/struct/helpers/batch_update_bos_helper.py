import io
import json
import string
from urllib.parse import quote_plus
from urllib.request import urlopen

from loguru import logger
from openpyxl.worksheet.worksheet import Worksheet

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.business_order.fsm import BusinessOrderStatusController
from robot_processor.business_order.job_action import JobAction
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.struct import errors
from robot_processor.business_order.struct.contants import IGNORED_WORKSHEET_TITLE
from robot_processor.business_order.struct.entities import AnalyzedWidgetValue
from robot_processor.business_order.struct.entities import ExceptionalDataContainer
from robot_processor.business_order.struct.entities import FailedBusinessOrderInfo
from robot_processor.business_order.struct.entities import FileContainer
from robot_processor.business_order.struct.entities import ImportFileResult
from robot_processor.business_order.struct.entities import InvalidWorksheet
from robot_processor.business_order.struct.entities import WorksheetInfos
from robot_processor.business_order.struct.entities import WorksheetMetadata
from robot_processor.business_order.struct.enums import ImportFileStatus
from robot_processor.business_order.struct.helpers.excel_analyze_helper import ExcelAnalyzeHelper
from robot_processor.business_order.struct.helpers.update_bo_data_helper import UpdateBoDataHelper
from robot_processor.business_order.struct.utils import get_not_current_step_names_for_business_order
from robot_processor.db import in_transaction
from robot_processor.error.errors import ScenarioError


class BatchUpdateBosHelper:
    def __init__(
        self,
        file_container: FileContainer,
        operator: AccountDetailV2,
        is_admin: bool,
    ) -> None:
        """
        理论上可以逐个工单处理，再补充一个游标，更利于失败恢复等操作。
        """
        # 必要内容
        self.file_container = file_container
        self.operator = operator
        self.is_admin = is_admin
        self.reason = "批量更新"

        # 输出内容
        self.exceptional_data_container = ExceptionalDataContainer()
        self.success_business_order_ids: list[int] = []

        # 计算内容
        self.xlsx_file_to_worksheet_infos_mapping: dict[str, WorksheetInfos] = {}

    def handle(self) -> tuple[ExceptionalDataContainer, list[int]]:
        for file_name in self.file_container.xlsx_filename_to_url_mapping.keys():
            self.handle_xlsx_file(file_name)
        return self.exceptional_data_container, self.success_business_order_ids

    def handle_xlsx_file(self, file_name: str) -> None:
        """
        整个 Excel 文件的处理。
        """
        url = self.file_container.xlsx_filename_to_url_mapping[file_name]
        logger.info("正在处理 {}, url: {}".format(file_name, url))
        quoted_url = quote_plus(url, safe=string.printable)

        # 读取 Excel 文件。
        try:
            with urlopen(quoted_url) as res:
                excel_analyze_helper = ExcelAnalyzeHelper(excel_file_like_object=io.BytesIO(res.read()))
        except Exception as e:
            logger.error(f"error: {errors.INVALID_XLSX_FILE}, url: {url}")
            logger.exception(e)
            self.exceptional_data_container.invalid_files.append(
                ImportFileResult(file_name=file_name, file_url=url, status=ImportFileStatus.INVALID_FILE)
            )
            return None

        logger.info("文件已下载完成")

        worksheet_infos: WorksheetInfos = excel_analyze_helper.check_worksheets()
        self.xlsx_file_to_worksheet_infos_mapping[file_name] = worksheet_infos
        if len(worksheet_infos.valid_worksheet_title_to_worksheet_mapping) == 0:
            logger.error(f"error: {errors.NOT_FOUND_EXCEL_SHEETS}, url: {url}")
            self.exceptional_data_container.invalid_files.append(
                ImportFileResult(file_name=file_name, file_url=url, status=ImportFileStatus.INVALID_FILE)
            )
            return None

        logger.info("工作表检测完成")

        # 是否存在处理失败的工作表
        has_invalid_worksheet: bool = False

        # 先将部分已知的异常工作表保存下来。
        for invalid_worksheet_title, reason in worksheet_infos.invalid_worksheet_title_to_reason_mapping.items():
            # 如果是导入说明就不用管了
            if invalid_worksheet_title == IGNORED_WORKSHEET_TITLE:
                continue
            has_invalid_worksheet = True
            self.exceptional_data_container.invalid_worksheets.append(
                InvalidWorksheet(
                    worksheet_title=invalid_worksheet_title,
                    file_name=file_name,
                    file_url=url,
                    reason=reason,
                )
            )

        # 对该 Excel 文件中，每个合法的工作表进行处理。
        for worksheet in worksheet_infos.valid_worksheet_title_to_worksheet_mapping.values():
            has_failed_case, err = self.handle_worksheet(excel_analyze_helper, worksheet, file_name)
            if err is not None:
                self.exceptional_data_container.invalid_worksheets.append(
                    InvalidWorksheet(
                        worksheet_title=worksheet.title,
                        file_name=file_name,
                        file_url=url,
                        reason=err,
                    )
                )
                has_invalid_worksheet = True
            else:
                if has_failed_case:
                    has_invalid_worksheet = True

        if not has_invalid_worksheet:
            self.exceptional_data_container.invalid_files.append(
                ImportFileResult(file_name=file_name, file_url=url, status=ImportFileStatus.SUCCESS)
            )

        return None

    def handle_worksheet(
        self,
        excel_analyze_helper: ExcelAnalyzeHelper,
        worksheet: Worksheet,
        file_name: str,
    ) -> tuple[bool, str] | tuple[bool, None]:
        """
        处理单个工作表。
        """
        logger.info(f"开始处理工作表: {worksheet.title}")

        has_failed_case: bool = False

        worksheet_infos: WorksheetInfos = self.xlsx_file_to_worksheet_infos_mapping[file_name]
        file_url: str = self.file_container.xlsx_filename_to_url_mapping[file_name]
        metadata: WorksheetMetadata = worksheet_infos.worksheet_title_to_metadata_mapping[worksheet.title]

        # 获取每个工单对应的行号信息
        (bo_id_to_bo_rows_mapping, err,) = excel_analyze_helper.get_each_business_order_rows_count_by_worksheet(
            worksheet=worksheet, multi_rows_formatter=metadata.multi_rows_formatter
        )
        if err is not None:
            logger.error(f"提取工单行号失败: {err}, 工作表名称: {worksheet.title}")
            return True, err

        logger.info("工单行号处理完成")

        # 提取表头
        needed_widget_title_show_names: list[list[str]] = excel_analyze_helper.get_widget_title_show_names(
            worksheet=worksheet,
        )
        if len(needed_widget_title_show_names) == 0:
            return True, "未能提取到有效表头名称"

        logger.info("表头获取完成")

        # 提取工单在 Excel 的工作表内提供的数据信息。
        (
            bo_id_to_analyzed_widget_values_mapping,
            err,
        ) = excel_analyze_helper.get_bo_id_to_analyzed_widget_values_mapping(
            worksheet=worksheet,
            bo_id_to_bo_rows_mapping=bo_id_to_bo_rows_mapping,
            multi_rows_formatter=metadata.multi_rows_formatter,
        )
        if err is not None:
            logger.error(f"提取 Excel 内工单数据失败: {err}, 工作表名称: {worksheet.title}")
            return True, err

        logger.info("工作表内工单数据提取完成")

        # 获取当前工作表的表头高度。
        header_depth: int = excel_analyze_helper.worksheet_title_to_header_depth_mapping[worksheet.title]

        # 逐个工单 ID 去处理，尽可能保证每个工单的实时性。
        for business_order_id in bo_id_to_bo_rows_mapping.keys():
            logger.info(f"正在处理工单: {business_order_id}")
            with in_transaction():
                err = self.handle_business_order(
                    business_order_id=business_order_id,
                    metadata=metadata,
                    needed_widget_title_show_names=needed_widget_title_show_names,
                    analyzed_widget_values=bo_id_to_analyzed_widget_values_mapping[business_order_id],
                )
                if err is not None:
                    logger.warning("工单: {} 处理失败: {}".format(business_order_id, err))
                    bo_rows = bo_id_to_bo_rows_mapping[business_order_id]
                    self.exceptional_data_container.failed_business_order_infos.append(
                        FailedBusinessOrderInfo(
                            id=business_order_id,
                            reason=err,
                            worksheet_title=worksheet.title,
                            file_name=file_name,
                            file_url=file_url,
                            start_row=bo_rows.start_row,
                            end_row=bo_rows.end_row,
                            worksheet_header_depth=header_depth,
                        )
                    )
                    has_failed_case = True
                else:
                    logger.info("工单: {} 处理成功".format(business_order_id))
                    self.success_business_order_ids.append(business_order_id)

        return has_failed_case, None

    def handle_business_order(
        self,
        business_order_id: int,
        metadata: WorksheetMetadata,
        needed_widget_title_show_names: list[list[str]],
        analyzed_widget_values: list[AnalyzedWidgetValue],
    ) -> str | None:
        """
        处理单个工单。
        """
        # 查询工单
        business_order: BusinessOrder | None = BusinessOrder.query.filter(
            BusinessOrder.id == business_order_id,
        ).first()
        if business_order is None:
            return "未找到工单"

        if business_order.form_id is None:
            return "工单缺失模板信息"
        if business_order.current_job_id is None:
            return "工单缺失当前任务信息"

        # 检测该工单的非当前步骤的步骤名称
        not_current_step_names = get_not_current_step_names_for_business_order(
            business_order=business_order,
        )

        logger.info("工单 ID: {} 所需要无视的步骤名有: {}".format(business_order.id, not_current_step_names))

        update_bo_data_helper = UpdateBoDataHelper(
            business_order=business_order,
            ignored_step_names=not_current_step_names,
            metadata=metadata,
        )

        logger.info(f"工单 ID: {business_order.id} 从 Excel 中获取到的数据为: {analyzed_widget_values}")

        # 基于数据库内信息和 Excel 中的内容，转换出工单的内容
        try:
            bo_data, err = update_bo_data_helper.update_bo_data(
                needed_widget_title_show_names=needed_widget_title_show_names,
                analyzed_widget_values=analyzed_widget_values,
                other_filename_to_url_mapping=self.file_container.other_filename_to_url_mapping,
            )
            if err is not None:
                return err
        except Exception as e:
            logger.exception(e)
            return "转换工单数据时发生异常"

        # 使用 JSON 序列化一下数据
        try:
            bo_data = json.loads(json.dumps(bo_data, ensure_ascii=False, default=str))
        except Exception as e:
            logger.exception(e)
            return "数据格式化失败"

        try:
            bosc = BusinessOrderStatusController(
                business_order=business_order,
                operator=self.operator,
                is_admin=self.is_admin,
            )
            JobAction(
                business_order.current_job_id,
                business_order_status_controller=bosc,
            ).save(bo_data, self.operator, self.reason)
        except ScenarioError as e:
            return e.biz_display
        except Exception as e:
            logger.exception("工单执行更新操作时出现未知异常: ", e)
            return "工单执行更新操作时出现未知异常"

        return None
