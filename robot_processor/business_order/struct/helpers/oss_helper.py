import io
import os
import string
import tempfile
from datetime import datetime
from typing import <PERSON>ary<PERSON>
from urllib.parse import quote_plus
from urllib.request import urlopen
from uuid import uuid4
from zipfile import ZipFile

import oss2
from flask import current_app
from loguru import logger
from werkzeug.datastructures.file_storage import FileStorage

from robot_processor.business_order.struct.entities import FileContainer
from robot_processor.business_order.struct.enums import FileType
from robot_processor.client import oss_client


class OssHelper:
    @staticmethod
    def upload_single_file_to_oss(
        file_like_object: BinaryIO,
        file_name: str,
        check_file_suffix_is_xlsx: bool = True,
    ) -> tuple[None, str] | tuple[str, None]:
        now = datetime.now()
        today = now.today().date().strftime("%Y-%m-%d")

        base_url = current_app.config.get("OSS_BIZ_BASE_URL", "")
        headers = {
            "x-oss-storage-class": oss2.BUCKET_STORAGE_CLASS_STANDARD,
            "x-oss-object-acl": oss2.OBJECT_ACL_PUBLIC_READ,
        }

        if check_file_suffix_is_xlsx:
            file_name = file_name if file_name.endswith(".xlsx") else file_name + ".xlsx"

        with file_like_object as f:
            f.seek(0)
            object_key = "business_order/count/{date}/{uuid_hex}/{filename}".format(
                date=today,
                uuid_hex=uuid4().hex,
                filename=file_name,
            )
            try:
                result = oss_client.bucket.put_object(object_key, f, headers=headers)
            except Exception as e:
                logger.error("OSS 请求失败：{}".format(e))
                return None, "OSS 请求失败"
            if result.status >= 400:
                logger.error("于 OSS 创建文件失败：{}".format(result.resp))
                return None, "于 OSS 创建文件失败"

            url = base_url + object_key
            return url, None

    @staticmethod
    def unzip_file(file: FileStorage) -> tempfile.TemporaryDirectory:
        zip_file = ZipFile(io.BytesIO(file.read()))
        temp_dir = tempfile.TemporaryDirectory()
        zip_file.extractall(temp_dir.name)
        zip_file.close()
        return temp_dir

    @staticmethod
    def get_files_from_temp_dir(
        temp_dir: tempfile.TemporaryDirectory,
    ) -> tuple[dict[str, str], dict[str, str]]:
        """
        从 unzip 解压到的临时文件夹中，找寻需要处理的 xlsx 文件和需要转换为链接的附件。
        """
        xlsx_filename_to_url_mapping: dict[str, str] = {}
        other_filename_to_url_mapping: dict[str, str] = {}
        # 由于不同的打包工具逻辑不同，所以压缩包里的文件可能会被解压到一个文件夹中，或直接在最外层这两种可能。
        # 现在最外层找下是否有 Excel 文件。
        for file_name in os.listdir(temp_dir.name):
            filepath = os.path.join(temp_dir.name, file_name)
            if os.path.isfile(filepath):
                # 以后缀名来判断实际上并不合理。
                if os.path.splitext(file_name)[1] == ".xlsx":
                    guessed_filename = OssHelper.guess_filename(file_name)
                    with open(filepath, "rb") as f:
                        url, err = OssHelper.upload_single_file_to_oss(
                            io.BytesIO(f.read()),
                            guessed_filename,
                            check_file_suffix_is_xlsx=False,
                        )
                        if err is not None:
                            logger.error("filename: {} 上传失败".format(guessed_filename))
                        else:
                            assert url is not None
                            xlsx_filename_to_url_mapping[guessed_filename] = url

        outermost_not_found_xlsx = len(xlsx_filename_to_url_mapping.keys()) == 0

        for file_name in os.listdir(temp_dir.name):
            filepath = os.path.join(temp_dir.name, file_name)
            if os.path.isdir(filepath):
                # 如果最外层没有找到 Excel 文件，那么就认为是解压到的第一层文件夹中了
                if outermost_not_found_xlsx:
                    # 遍历该文件夹
                    for filename in os.listdir(filepath):
                        current_filepath = os.path.join(filepath, filename)
                        # 如果是文件夹，则说明是附件
                        if os.path.isdir(current_filepath):
                            for sub_filename in os.listdir(current_filepath):
                                sub_filepath = os.path.join(current_filepath, sub_filename)
                                if os.path.isfile(sub_filepath):
                                    guessed_sub_filename = OssHelper.guess_filename(sub_filename)
                                    with open(sub_filepath, "rb") as f:
                                        url, err = OssHelper.upload_single_file_to_oss(
                                            io.BytesIO(f.read()),
                                            guessed_sub_filename,
                                            check_file_suffix_is_xlsx=False,
                                        )
                                        if err is not None:
                                            logger.error("filename: {} 上传失败".format(guessed_sub_filename))
                                        else:
                                            assert url is not None
                                            other_filename_to_url_mapping[guessed_sub_filename] = url

                        # 如果是单文件，则检测是否为 excel，不是的话，就不管了。
                        elif os.path.isfile(current_filepath):
                            if os.path.splitext(filename)[1] == ".xlsx":
                                guessed_filename = OssHelper.guess_filename(filename)
                                with open(current_filepath, "rb") as f:
                                    url, err = OssHelper.upload_single_file_to_oss(
                                        io.BytesIO(f.read()),
                                        guessed_filename,
                                        check_file_suffix_is_xlsx=False,
                                    )
                                    if err is not None:
                                        logger.error("filename: {} 上传失败".format(guessed_filename))
                                    else:
                                        assert url is not None
                                        other_filename_to_url_mapping[guessed_filename] = url

                # 如果已经在最外层找到了 Excel 文件，则认为其同级的文件夹内全是附件。
                else:
                    for filename in os.listdir(filepath):
                        current_filepath = os.path.join(filepath, filename)
                        if os.path.isfile(current_filepath):
                            guessed_filename = OssHelper.guess_filename(filename)
                            with open(current_filepath, "rb") as f:
                                url, err = OssHelper.upload_single_file_to_oss(
                                    io.BytesIO(f.read()),
                                    guessed_filename,
                                    check_file_suffix_is_xlsx=False,
                                )
                                if err is not None:
                                    logger.error("filename: {} 上传失败".format(guessed_filename))
                                else:
                                    assert url is not None
                                    other_filename_to_url_mapping[guessed_filename] = url

        return xlsx_filename_to_url_mapping, other_filename_to_url_mapping

    @staticmethod
    def guess_filename(
        filename: str,
    ):
        try:
            return filename.encode("cp437").decode("utf-8")
        except Exception:
            return filename

    @staticmethod
    def parse_file(file_name: str, file_type: FileType, url: str) -> tuple[FileContainer, None] | tuple[None, str]:
        """
        提取文件，归纳需要处理的 Excel 文件和一些附件。
        """
        quoted_url = quote_plus(url, safe=string.printable)

        if file_type == FileType.zip:
            try:
                # 下载
                with urlopen(quoted_url) as res:
                    # 解压
                    temp_dir = OssHelper.unzip_file(res)
                    # 解析
                    (
                        xlsx_filename_to_url_mapping,
                        other_filename_to_url_mapping,
                    ) = OssHelper.get_files_from_temp_dir(temp_dir)

                    if len(xlsx_filename_to_url_mapping) == 0:
                        temp_dir.cleanup()
                        return None, "未能获取到 Excel 文件"
                    temp_dir.cleanup()
                    return (
                        FileContainer(
                            xlsx_filename_to_url_mapping=xlsx_filename_to_url_mapping,
                            other_filename_to_url_mapping=other_filename_to_url_mapping,
                        ),
                        None,
                    )
            except Exception as e:
                logger.error(e)
                return None, "处理 ZIP 文件失败"
        elif file_type == FileType.xlsx:
            xlsx_filename_to_url_mapping = {file_name: url}
            other_filename_to_url_mapping = {}
            return (
                FileContainer(
                    xlsx_filename_to_url_mapping=xlsx_filename_to_url_mapping,
                    other_filename_to_url_mapping=other_filename_to_url_mapping,
                ),
                None,
            )
