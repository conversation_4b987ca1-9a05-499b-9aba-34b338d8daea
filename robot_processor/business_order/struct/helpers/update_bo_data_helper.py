import datetime
from copy import deepcopy
from typing import Any

from loguru import logger

from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.struct import errors
from robot_processor.business_order.struct.contants import BEGIN_STEP_NAME
from robot_processor.business_order.struct.contants import ENUM_LEVEL_PREFIX
from robot_processor.business_order.struct.contants import FILLED_CHAR
from robot_processor.business_order.struct.contants import MIN_ROW_LENGTH
from robot_processor.business_order.struct.datas import DataConvertRules
from robot_processor.business_order.struct.entities import AccurateWidgetMeta
from robot_processor.business_order.struct.entities import AccurateWidgetTitle
from robot_processor.business_order.struct.entities import AnalyzedWidgetValue
from robot_processor.business_order.struct.entities import WidgetName
from robot_processor.business_order.struct.entities import WidgetScopeRows
from robot_processor.business_order.struct.entities import WorksheetMetadata
from robot_processor.business_order.struct.enums import ColumnSplitMode
from robot_processor.business_order.struct.enums import CommonWidgetType
from robot_processor.business_order.struct.enums import ExportScene
from robot_processor.business_order.struct.enums import MultiRowsFormatter
from robot_processor.business_order.struct.helpers.extract_helper import ExtractHelper
from robot_processor.business_order.struct.utils import extract_address
from robot_processor.form.types import WidgetDataType


class UpdateBoDataHelper:
    def __init__(
        self, business_order: BusinessOrder, ignored_step_names: list[str], metadata: WorksheetMetadata
    ) -> None:
        self.business_order = business_order
        self.ignored_step_names: list[str] = [BEGIN_STEP_NAME, *ignored_step_names]
        self.metadata = metadata

    def update_bo_data(
        self,
        needed_widget_title_show_names: list[list[str]],
        analyzed_widget_values: list[AnalyzedWidgetValue],
        other_filename_to_url_mapping: dict[str, str] | None = None,
    ) -> tuple[dict[str, Any], None] | tuple[dict[str, Any], str]:
        extract_helper = ExtractHelper(
            business_order=self.business_order,
            export_scene=ExportScene.EDIT,
            column_split_mode=self.metadata.column_split_mode,
        )
        # 获取工单的步骤的表头信息。
        step_name_to_widget_titles_mapping = extract_helper.compute_widget_titles_for_steps()
        widget_titles: list[AccurateWidgetTitle] = []
        for (
            step_name,
            widget_titles_for_step,
        ) in step_name_to_widget_titles_mapping.items():
            widget_titles.extend(widget_titles_for_step)

        formatted_widget_title_show_names = [
            [
                show_name.replace("*", "", 1) if show_name.startswith("*") else show_name
                for show_name in widget_title_show_name
                if show_name != FILLED_CHAR
            ]
            for widget_title_show_name in needed_widget_title_show_names
        ]

        filtered_widget_titles: list[AccurateWidgetTitle] = []
        filtered_analyzed_widget_values: list[AnalyzedWidgetValue] = []

        for index, formatted_widget_title_show_name in enumerate(formatted_widget_title_show_names):
            has_widget_title = False
            for widget_title in widget_titles:
                if self.metadata.multi_rows_formatter == MultiRowsFormatter.MERGE_CELLS:
                    if widget_title.actually_show_names == formatted_widget_title_show_name:
                        filtered_widget_titles.append(widget_title)
                        has_widget_title = True
                else:
                    if "_".join(widget_title.actually_show_names) == "_".join(formatted_widget_title_show_name):
                        filtered_widget_titles.append(widget_title)
                        has_widget_title = True
            if has_widget_title:
                filtered_analyzed_widget_values.append(analyzed_widget_values[index])

        if self.metadata.multi_rows_formatter == MultiRowsFormatter.MERGE_CELLS:
            return self.update_bo_data_by_merge_cells(
                filtered_widget_titles,
                filtered_analyzed_widget_values,
                other_filename_to_url_mapping,
            )
        elif self.metadata.multi_rows_formatter == MultiRowsFormatter.SPLIT:
            return self.update_bo_data_by_split(
                filtered_widget_titles,
                filtered_analyzed_widget_values,
                other_filename_to_url_mapping,
            )
        else:
            return {}, errors.NOT_SUPPORT_MULTI_ROWS_FORMATTER

    def convert_widget_value(
        self,
        widget_value: Any,
        widget_meta: AccurateWidgetMeta,
        other_filename_to_url_mapping: dict[str, str] | None = None,
    ) -> Any:
        data_convert_rule: dict[str, Any] = {}
        converted_value: Any = None
        if widget_value is None:
            return None
        elif widget_meta.widget_name.widget_type in [
            CommonWidgetType.address,
        ]:
            widget_value_string = str(widget_value)
            extracted_address_info = extract_address(widget_value_string)
            converted_value = extracted_address_info.to_bill()
            return converted_value
        elif widget_meta.widget_name.widget_type in [
            CommonWidgetType.full_address,
        ]:
            widget_value_string = str(widget_value)
            extracted_address_info = extract_address(widget_value_string)
            converted_value = extracted_address_info.to_address()
            return converted_value
        elif (
            widget_meta.widget_name.widget_type
            in [
                CommonWidgetType.order_question,
                CommonWidgetType.select_tile,
                CommonWidgetType.select_dropdown,
            ]
            and self.metadata.column_split_mode == ColumnSplitMode.NOT_SPLIT
        ):
            widget_value_string = str(widget_value)
            widget_value_list = widget_value_string.split("\n")
            converted_value = []
            for widget_value_item in widget_value_list:
                sub_converted_value = []
                widget_value_item_list = widget_value_item.split("/")
                for item in widget_value_item_list:
                    sub_converted_value.append(
                        {
                            "label": item,
                            "value": item,
                        }
                    )
                converted_value.append(sub_converted_value)
            return converted_value
        elif (
            widget_meta.widget_name.widget_type
            in [
                CommonWidgetType.radio_tile,
                CommonWidgetType.radio_dropdown,
            ]
            and self.metadata.column_split_mode == ColumnSplitMode.NOT_SPLIT
        ):
            widget_value_string = str(widget_value)
            widget_value_list = widget_value_string.split("/")
            converted_value = []
            for widget_value_item in widget_value_list:
                converted_value.append(
                    {
                        "label": widget_value_item,
                        "value": widget_value_item,
                    }
                )
            return converted_value
        elif widget_meta.widget_name.widget_data_type in [
            WidgetDataType.BOOLEAN,
        ]:
            data_convert_rule = DataConvertRules.get_boolean_readable_string_to_value_mapping_by_widget_meta(
                widget_meta=widget_meta,
            )
            converted_value = data_convert_rule.get(str(widget_value))
            if converted_value is None:
                return str(widget_value)
            return converted_value
        elif widget_meta.widget_name.widget_type in [
            CommonWidgetType.payment_method_item_method,
        ]:
            data_convert_rule = DataConvertRules.get_payment_method_readable_string_to_value_mapping()
            converted_value = data_convert_rule.get(str(widget_value))
            if converted_value is None:
                return str(widget_value)
            return converted_value
        elif widget_meta.widget_name.widget_type in [
            CommonWidgetType.rate_type,
        ]:
            data_convert_rule = DataConvertRules.get_rate_type_readable_string_to_value_mapping()
            converted_value = data_convert_rule.get(str(widget_value))
            if converted_value is None:
                return str(widget_value)
            return converted_value
        elif widget_meta.widget_name.widget_type in [
            CommonWidgetType.rate_value,
        ]:
            data_convert_rule = {
                **DataConvertRules.get_rate_start_readable_string_to_value_mapping(),
                **DataConvertRules.get_rate_flower_readable_string_to_mapping(),
            }
            converted_value = data_convert_rule.get(str(widget_value))
            if converted_value is None:
                return str(widget_value)
            return converted_value
        elif widget_meta.widget_detail.widget_info.key in [
            "COMBINE",
        ]:
            data_convert_rule = DataConvertRules.get_product_type_readable_string_to_value_mapping()
            converted_value = data_convert_rule.get(str(widget_value))
            if converted_value is None:
                return str(widget_value)
            return converted_value
        elif widget_meta.widget_name.widget_type in [
            CommonWidgetType.url,
        ]:
            file_url = (other_filename_to_url_mapping or {}).get(str(widget_value))
            if file_url:
                return file_url
            return str(widget_value)
        elif widget_meta.widget_name.widget_type in [
            CommonWidgetType.order_question_level,
            CommonWidgetType.select_tile_level,
            CommonWidgetType.select_dropdown_level,
            CommonWidgetType.radio_tile_level,
            CommonWidgetType.radio_dropdown_level,
        ]:
            data_convert_rule = DataConvertRules.get_enum_readable_string_to_value_mapping_by_widget_meta(
                widget_meta=widget_meta,
            )
            converted_value = data_convert_rule.get(str(widget_value))
            if converted_value is None:
                return str(widget_value)
            return converted_value
        elif widget_meta.widget_name.widget_data_type in [
            WidgetDataType.DATE,
        ]:
            if isinstance(widget_value, datetime.date):
                return widget_value.strftime("%Y-%m-%d")
            elif isinstance(widget_value, datetime.datetime):
                return widget_value.strftime("%Y-%m-%d")
            else:
                return str(widget_value)
        elif widget_meta.widget_name.widget_data_type in [
            WidgetDataType.TIME,
        ]:
            if isinstance(widget_value, datetime.time):
                return widget_value.strftime("%H:%M:%S")
            elif isinstance(widget_value, datetime.datetime):
                return widget_value.strftime("%H:%M:%S")
            else:
                return str(widget_value)
        elif widget_meta.widget_name.widget_data_type in [WidgetDataType.DATETIME]:
            if isinstance(widget_value, datetime.datetime):
                return widget_value.strftime("%Y-%m-%d %H:%M:%S")
            else:
                return str(widget_value)
        elif widget_meta.widget_name.widget_data_type in [WidgetDataType.NUMBER]:
            try:
                return float(widget_value)
            except ValueError:
                return str(widget_value)
        else:
            return str(widget_value)

    def update_bo_data_by_merge_cells(
        self,
        widget_titles: list[AccurateWidgetTitle],
        analyzed_widget_values: list[AnalyzedWidgetValue],
        other_filename_to_url_mapping: dict[str, str] | None = None,
    ) -> tuple[dict[str, Any], None] | tuple[dict[str, Any], str]:
        # todo: 支持从部分拆分和不拆分的地址组件、级联组件复原数据。

        # 每个步骤下的组件当前需要遍历的行号信息。
        step_name_to_widget_key_to_rows_mapping: dict[str, dict[tuple[WidgetName, ...], int]] = {}
        step_name_to_widget_key_to_widget_scope_rows_mapping: dict[
            str, dict[tuple[WidgetName, ...], list[WidgetScopeRows]]
        ] = {}

        # 每一行的数据组合成的列表。
        each_row_analyzed_widget_values: list[list[Any]] = []

        # 将每一列提取出来的数据转化为每一行的数据。
        length: int = sum(analyzed_widget_values[0].resized_widget_value_length)
        for row_number in range(length):
            one_row_analyzed_widget_values = []
            for analyzed_widget_value in analyzed_widget_values:
                if row_number >= len(analyzed_widget_values):
                    return {}, errors.BUSINESS_ORDER_DATA_HAS_INVALID_STYLE
                one_row_analyzed_widget_values.append(analyzed_widget_value.widget_value[row_number])
            each_row_analyzed_widget_values.append(one_row_analyzed_widget_values)

        bo_data = deepcopy(self.business_order.data)

        # 逐行处理。
        for row, one_row_analyzed_widget_value in enumerate(each_row_analyzed_widget_values, 1):
            # 行数据处理时，分列执行逻辑。
            for index, (widget_title, widget_value) in enumerate(zip(widget_titles, one_row_analyzed_widget_value)):
                step_name = widget_title.get_first_show_name()
                if step_name is None or step_name in self.ignored_step_names:
                    continue
                # 只读项和前序不可编辑的组件，不做处理。
                if not widget_title.is_editable:
                    continue
                # 如果是 Ellipsis，说明其为合并单元格内的数据，并无意义。(序列号会用来删除父组件的行，需要单独处理)
                if widget_value is Ellipsis and not widget_title.metas[-1].is_serial():
                    continue

                parent_data: dict | list | Any = bo_data
                max_widget_meta_depth: int = len(widget_title.metas)

                # 按照组件层级，依次抽取并更新数据。
                for widget_meta_depth, widget_meta in enumerate(widget_title.metas, 1):
                    logger.info(parent_data)
                    logger.info(widget_meta)
                    widget_info_key = widget_meta.get_widget_info_key()

                    # 父组件 key，主要用于复合组件和列表组件的子组件进行行号确认。
                    parent_widget_key = tuple(
                        [meta.widget_name for meta in widget_title.metas][: widget_meta_depth - 1]
                    )

                    if isinstance(parent_data, dict):
                        # 如果走到了最终层级，直接更新数据即可。
                        if widget_meta_depth == max_widget_meta_depth:
                            got_value = self.convert_widget_value(
                                widget_value, widget_meta, other_filename_to_url_mapping
                            )
                            if widget_meta.widget_name.widget_type in [
                                CommonWidgetType.full_address,
                            ]:
                                parent_data.update(got_value)
                            else:
                                parent_data[widget_info_key] = got_value
                            continue
                        # 获取或者创建空数据，并更新原始 data。
                        (
                            _,
                            data,
                        ) = widget_meta.get_origin_value_or_create_empty_value_from_dict(parent_data)
                        parent_data[widget_info_key] = data
                        parent_data = data
                    elif isinstance(parent_data, list):
                        # 如果是序列的话，需要更新父组件的迭代行号。
                        if widget_meta.is_serial():
                            if widget_value is not Ellipsis:
                                widget_key_to_rows_mapping = step_name_to_widget_key_to_rows_mapping.get(step_name)
                                if widget_key_to_rows_mapping is None:
                                    widget_key_to_rows_mapping = {}
                                    step_name_to_widget_key_to_rows_mapping[step_name] = widget_key_to_rows_mapping
                                widget_key_to_widget_scope_rows_mapping = (
                                    step_name_to_widget_key_to_widget_scope_rows_mapping.get(step_name)
                                )
                                if widget_key_to_widget_scope_rows_mapping is None:
                                    widget_key_to_widget_scope_rows_mapping = {}
                                    step_name_to_widget_key_to_widget_scope_rows_mapping[
                                        step_name
                                    ] = widget_key_to_widget_scope_rows_mapping

                                # 每一行序列号对应的行号信息。
                                parent_widget_scope_rows = widget_key_to_widget_scope_rows_mapping.get(
                                    parent_widget_key
                                )
                                if not parent_widget_scope_rows:
                                    resized_widget_value_length = analyzed_widget_values[
                                        index
                                    ].resized_widget_value_length
                                    widget_scope_rows: list[WidgetScopeRows] = []
                                    start_row = 1
                                    for i in resized_widget_value_length:
                                        end_row = start_row + i - 1
                                        widget_scope_rows.append(
                                            WidgetScopeRows(
                                                start_row=start_row,
                                                end_row=end_row,
                                            )
                                        )
                                        start_row = end_row + 1

                                    widget_key_to_widget_scope_rows_mapping[parent_widget_key] = widget_scope_rows

                                parent_widget_rows = widget_key_to_rows_mapping.get(parent_widget_key)
                                if not parent_widget_rows:
                                    widget_key_to_rows_mapping[parent_widget_key] = MIN_ROW_LENGTH
                                    parent_widget_rows = widget_key_to_rows_mapping.get(parent_widget_key)
                                else:
                                    # 找到序列的父组件的所属组件
                                    grand_parent_widget_keys = [
                                        widget_key
                                        for widget_key in widget_key_to_rows_mapping.keys()
                                        if widget_key
                                        in [
                                            tuple([m.widget_name for m in widget_title.metas[: i - 1]])
                                            for i in range(len(widget_title.metas))
                                            if i - 1 >= 0
                                        ]
                                    ]
                                    # 没有找到的话，则说明其本就是最外层。
                                    if len(grand_parent_widget_keys) == 0:
                                        parent_widget_rows += MIN_ROW_LENGTH
                                    else:
                                        # 找到最近一层的父组件 key
                                        sorted_grand_parent_widget_keys = sorted(grand_parent_widget_keys, key=len)
                                        nearly_grand_parent_widget_key = sorted_grand_parent_widget_keys[-1]
                                        # 获取最近一层的父组件的当前遍历行号。
                                        nearly_grand_parent_widget_key_rows = widget_key_to_rows_mapping[
                                            nearly_grand_parent_widget_key
                                        ]
                                        # 获取最近一层的父组件的各行作用域的信息。
                                        nearly_grand_parent_widget_scope_rows = widget_key_to_widget_scope_rows_mapping[
                                            nearly_grand_parent_widget_key
                                        ]
                                        # 获取当前作用域的信息。
                                        scope_rows = nearly_grand_parent_widget_scope_rows[
                                            nearly_grand_parent_widget_key_rows - 1
                                        ]
                                        # 如果已经超过了当前作用域的行号，则说明进入了新的作用域。
                                        if row > scope_rows.end_row:
                                            # 把旧作用域的数据行进行删减。
                                            while parent_widget_rows < len(parent_data):
                                                parent_data.pop(-1)
                                            parent_widget_rows = MIN_ROW_LENGTH
                                        elif row == scope_rows.start_row:
                                            parent_widget_rows = MIN_ROW_LENGTH
                                        else:
                                            parent_widget_rows += MIN_ROW_LENGTH
                                    widget_key_to_rows_mapping[parent_widget_key] = parent_widget_rows

                            # 如果是最后一行，则判断下父组件当前迭代到的行号，将超出的行数据进行删除
                            if row == len(each_row_analyzed_widget_values):
                                widget_key_to_rows_mapping = step_name_to_widget_key_to_rows_mapping.get(step_name)
                                if widget_key_to_rows_mapping is None:
                                    widget_key_to_rows_mapping = {}
                                    step_name_to_widget_key_to_rows_mapping[step_name] = widget_key_to_rows_mapping
                                rows = widget_key_to_rows_mapping.get(widget_title.parent_widget_key) or 0
                                while rows < len(parent_data):
                                    parent_data.pop(-1)

                            continue
                        # 走到最终层级的数据处理。
                        if widget_meta_depth == max_widget_meta_depth:
                            widget_key_to_rows_mapping = step_name_to_widget_key_to_rows_mapping.get(step_name)
                            if widget_key_to_rows_mapping is None:
                                widget_key_to_rows_mapping = {}
                                step_name_to_widget_key_to_rows_mapping[step_name] = widget_key_to_rows_mapping
                            parent_widget_rows = widget_key_to_rows_mapping.get(parent_widget_key) or 1
                            # 列表组件的子组件，比如列表组件_数值: [0, 100, 1]。只需要更新所在行的数据即可。
                            if widget_meta.is_item():
                                while parent_widget_rows > len(parent_data):
                                    parent_data.append(None)
                                parent_data[parent_widget_rows - 1] = self.convert_widget_value(
                                    widget_value,
                                    widget_meta,
                                    other_filename_to_url_mapping,
                                )
                            # 多选组件的子组件，也是一个多行结构需要明确当前所在行。
                            elif widget_meta.widget_name.widget_type in [
                                CommonWidgetType.select_tile_level,
                                CommonWidgetType.select_dropdown_level,
                                CommonWidgetType.order_question_level,
                            ]:
                                while parent_widget_rows > len(parent_data):
                                    parent_data.append([])
                                current_row_data = parent_data[parent_widget_rows - 1]
                                level = int(widget_meta.widget_name.widget_label.split(ENUM_LEVEL_PREFIX)[-1])
                                while level > len(current_row_data):
                                    current_row_data.append({})
                                converted_value = self.convert_widget_value(
                                    widget_value,
                                    widget_meta,
                                    other_filename_to_url_mapping,
                                )
                                if converted_value not in [None, ""]:
                                    current_row_data[level - 1] = {
                                        "label": converted_value,
                                        "value": converted_value,
                                    }
                            # 单选组件，只需要更新所在行的数据即可。
                            elif widget_meta.widget_name.widget_type in [
                                CommonWidgetType.radio_tile_level,
                                CommonWidgetType.radio_dropdown_level,
                            ]:
                                level = int(widget_meta.widget_name.widget_label.split(ENUM_LEVEL_PREFIX)[-1])
                                while level > len(parent_data):
                                    parent_data.append({})
                                converted_value = self.convert_widget_value(
                                    widget_value,
                                    widget_meta,
                                    other_filename_to_url_mapping,
                                )
                                if converted_value not in [None, ""]:
                                    parent_data[level - 1] = {
                                        "label": converted_value,
                                        "value": converted_value,
                                    }
                            else:
                                while parent_widget_rows > len(parent_data):
                                    parent_data.append({})
                                current_row_data = parent_data[parent_widget_rows - 1]
                                current_row_data[widget_info_key] = self.convert_widget_value(
                                    widget_value,
                                    widget_meta,
                                    other_filename_to_url_mapping,
                                )
                            continue
                        # 没有走到最终层级的话，则需要抽取数据，以供子组件处理。
                        else:
                            widget_key_to_rows_mapping = step_name_to_widget_key_to_rows_mapping.get(step_name)
                            if widget_key_to_rows_mapping is None:
                                widget_key_to_rows_mapping = {}
                                step_name_to_widget_key_to_rows_mapping[step_name] = widget_key_to_rows_mapping
                            parent_widget_rows = widget_key_to_rows_mapping.get(parent_widget_key) or MIN_ROW_LENGTH
                            while parent_widget_rows > len(parent_data):
                                if widget_meta.is_item():
                                    parent_data.append(widget_meta.create_empty_value())
                                else:
                                    parent_data.append({widget_info_key: widget_meta.create_empty_value()})
                            if widget_meta.is_item():
                                parent_data = parent_data[parent_widget_rows - 1]
                            else:
                                current_row_data = parent_data[parent_widget_rows - 1]
                                (
                                    _,
                                    extracted_data,
                                ) = widget_meta.get_origin_value_or_create_empty_value_from_dict(current_row_data)
                                current_row_data[widget_info_key] = extracted_data
                                parent_data = extracted_data

        return bo_data, None

    def update_bo_data_by_split(
        self,
        widget_titles: list[AccurateWidgetTitle],
        analyzed_widget_values: list[AnalyzedWidgetValue],
        other_filename_to_url_mapping: dict[str, str] | None = None,
    ) -> tuple[dict[str, Any], None] | tuple[dict[str, Any], str]:
        # todo: 支持从部分拆分和不拆分的地址组件、级联组件复原数据。

        step_name_to_widget_key_to_rows_mapping: dict[str, dict[tuple[WidgetName, ...], int]] = {}

        step_name_to_widget_key_to_row_is_set_mapping: dict[
            str, dict[tuple[WidgetName, ...], dict[tuple[int, ...], bool]]
        ] = {}

        each_row_analyzed_widget_values: list[list[Any]] = []

        length: int = sum(analyzed_widget_values[0].resized_widget_value_length)

        for row_number in range(length):
            one_row_analyzed_widget_values = []
            for analyzed_widget_value in analyzed_widget_values:
                if row_number >= len(analyzed_widget_values):
                    return {}, errors.BUSINESS_ORDER_DATA_HAS_INVALID_STYLE
                one_row_analyzed_widget_values.append(analyzed_widget_value.widget_value[row_number])
            each_row_analyzed_widget_values.append(one_row_analyzed_widget_values)

        bo_data = deepcopy(self.business_order.data)

        # 逐行处理。
        for row, one_row_analyzed_widget_value in enumerate(each_row_analyzed_widget_values, 1):
            # 当前行需要无视数据写入的最外层的组件键。
            step_name_to_ignored_outermost_widget_keys: dict[str, list[tuple[WidgetName, ...]]] = {}

            # 数据分列。
            for index, (widget_title, widget_value) in enumerate(zip(widget_titles, one_row_analyzed_widget_value)):
                step_name = widget_title.get_first_show_name()
                # 起始步骤的值不做处理。
                if step_name is None or step_name in self.ignored_step_names:
                    continue
                # 只读项和前序不可编辑的组件，不做处理。
                if not widget_title.is_editable:
                    continue
                # 如果是 Ellipsis，说明其为合并单元格内的数据，并无意义。
                if widget_value is Ellipsis:
                    continue

                # 判断当前组件是否不再需要处理。（主要定义为，序列号为空，下属节点都无效。）
                ignored_outermost_widget_keys = step_name_to_ignored_outermost_widget_keys.get(step_name)
                if ignored_outermost_widget_keys is None:
                    ignored_outermost_widget_keys = []
                    step_name_to_ignored_outermost_widget_keys[step_name] = ignored_outermost_widget_keys
                for ignored_outermost_widget_key in ignored_outermost_widget_keys:
                    current_widget_key = tuple([meta.widget_name for meta in widget_title.metas])
                    # 如果当前组件的 key 位于需要忽视的组件下，则不再处理。
                    if (
                        len(current_widget_key) > len(ignored_outermost_widget_key)
                        and current_widget_key[: len(ignored_outermost_widget_key)] == ignored_outermost_widget_key
                    ):
                        continue

                parent_data: dict | list | Any = bo_data
                max_widget_meta_depth: int = len(widget_title.metas)

                for widget_meta_depth, widget_meta in enumerate(widget_title.metas, 1):
                    logger.info(parent_data)
                    logger.info(widget_meta)
                    widget_info_key = widget_meta.get_widget_info_key()

                    parent_widget_key = tuple(
                        [meta.widget_name for meta in widget_title.metas][: widget_meta_depth - 1]
                    )

                    if isinstance(parent_data, dict):
                        if widget_meta_depth == max_widget_meta_depth:
                            # 获取当前行信息，处于最外层则默认为 1。
                            widget_key_to_rows_mapping = step_name_to_widget_key_to_rows_mapping.get(step_name)
                            if widget_key_to_rows_mapping is None:
                                widget_key_to_rows_mapping = {}
                                step_name_to_widget_key_to_rows_mapping[step_name] = widget_key_to_rows_mapping
                            parent_widget_rows = widget_key_to_rows_mapping.get(parent_widget_key) or 1
                            # 找寻父辈
                            grand_parent_widget_keys = [
                                widget_key
                                for widget_key in widget_key_to_rows_mapping.keys()
                                if widget_key
                                in [
                                    tuple([m.widget_name for m in widget_title.metas[: i - 1]])
                                    for i in range(len(widget_title.metas))
                                    if i - 1 >= 0
                                ]
                            ]
                            sorted_grand_parent_widget_keys = sorted(grand_parent_widget_keys, key=len)

                            key = tuple(
                                [
                                    *[widget_key_to_rows_mapping.get(k) or 1 for k in sorted_grand_parent_widget_keys],
                                    parent_widget_rows,
                                ]
                            )

                            # 检测该组件是否有更新过值。如果不在遍历中，且已经更新过了值，则不需要再依据当前行来更新数据。
                            widget_key_to_set_value_times = step_name_to_widget_key_to_row_is_set_mapping.get(step_name)
                            if widget_key_to_set_value_times is None:
                                widget_key_to_set_value_times = {}
                                step_name_to_widget_key_to_row_is_set_mapping[step_name] = widget_key_to_set_value_times
                            widget_key = tuple(meta.widget_name for meta in widget_title.metas)
                            rows_to_is_set_mapping = widget_key_to_set_value_times.get(widget_key)
                            if rows_to_is_set_mapping is None:
                                rows_to_is_set_mapping = {key: True}
                                widget_key_to_set_value_times[widget_key] = rows_to_is_set_mapping
                            else:
                                is_set = rows_to_is_set_mapping.get(key)
                                if not is_set:
                                    rows_to_is_set_mapping[key] = True
                                else:
                                    continue

                            got_value = self.convert_widget_value(
                                widget_value, widget_meta, other_filename_to_url_mapping
                            )
                            parent_data[widget_info_key] = got_value
                            continue
                        # 获取或者创建空数据，并更新原始 data。
                        (
                            _,
                            data,
                        ) = widget_meta.get_origin_value_or_create_empty_value_from_dict(parent_data)
                        parent_data[widget_info_key] = data
                        parent_data = data
                    elif isinstance(parent_data, list):
                        # 如果是序列的话，需要更新父组件的迭代行号。
                        if widget_meta.is_serial():
                            # 多行数据的话，序列这一列没有值的话，就直接忽视该行。
                            if widget_value is None:
                                ignored_outermost_widget_keys = step_name_to_ignored_outermost_widget_keys.get(
                                    step_name
                                )
                                if ignored_outermost_widget_keys is None:
                                    ignored_outermost_widget_keys = []
                                    step_name_to_ignored_outermost_widget_keys[
                                        step_name
                                    ] = ignored_outermost_widget_keys
                                ignored_outermost_widget_keys.append(parent_widget_key)
                                continue

                            # 多行数据的序列号，如果有值，并且和上一次不同，则说明进入了新行

                            widget_key_to_rows_mapping = step_name_to_widget_key_to_rows_mapping.get(step_name)
                            if widget_key_to_rows_mapping is None:
                                widget_key_to_rows_mapping = {}
                                step_name_to_widget_key_to_rows_mapping[step_name] = widget_key_to_rows_mapping
                            try:
                                serial_number = int(widget_value)
                            except ValueError:
                                # 如果序列号无法转换成整数类型，则直接忽视该复合组件的整行数据。
                                ignored_outermost_widget_keys = step_name_to_ignored_outermost_widget_keys.get(
                                    step_name
                                )
                                if ignored_outermost_widget_keys is None:
                                    ignored_outermost_widget_keys = []
                                    step_name_to_ignored_outermost_widget_keys[
                                        step_name
                                    ] = ignored_outermost_widget_keys
                                ignored_outermost_widget_keys.append(parent_widget_key)
                                continue
                            widget_key_to_rows_mapping[parent_widget_key] = serial_number
                            continue

                        if widget_meta_depth == max_widget_meta_depth:
                            widget_key_to_rows_mapping = step_name_to_widget_key_to_rows_mapping.get(step_name)
                            if widget_key_to_rows_mapping is None:
                                widget_key_to_rows_mapping = {}
                                step_name_to_widget_key_to_rows_mapping[step_name] = widget_key_to_rows_mapping
                            parent_widget_rows = widget_key_to_rows_mapping.get(parent_widget_key) or 1
                            # 找寻父辈
                            grand_parent_widget_keys = [
                                widget_key
                                for widget_key in widget_key_to_rows_mapping.keys()
                                if widget_key
                                in [
                                    tuple([m.widget_name for m in widget_title.metas[: i - 1]])
                                    for i in range(len(widget_title.metas))
                                    if i - 1 >= 0
                                ]
                            ]
                            sorted_grand_parent_widget_keys = sorted(grand_parent_widget_keys, key=len)

                            key = tuple(
                                [
                                    *[widget_key_to_rows_mapping.get(k) or 1 for k in sorted_grand_parent_widget_keys],
                                    parent_widget_rows,
                                ]
                            )

                            # 检测该组件是否有更新过值。如果不在遍历中，且已经更新过了值，则不需要再依据当前行来更新数据。
                            widget_key_to_set_value_times = step_name_to_widget_key_to_row_is_set_mapping.get(step_name)
                            if widget_key_to_set_value_times is None:
                                widget_key_to_set_value_times = {}
                                step_name_to_widget_key_to_row_is_set_mapping[step_name] = widget_key_to_set_value_times
                            widget_key = tuple(meta.widget_name for meta in widget_title.metas)
                            rows_to_is_set_mapping = widget_key_to_set_value_times.get(widget_key)
                            if rows_to_is_set_mapping is None:
                                rows_to_is_set_mapping = {key: True}
                                widget_key_to_set_value_times[widget_key] = rows_to_is_set_mapping
                            else:
                                is_set = rows_to_is_set_mapping.get(key)
                                if not is_set:
                                    rows_to_is_set_mapping[key] = True
                                else:
                                    continue

                            if widget_meta.is_item():
                                while parent_widget_rows > len(parent_data):
                                    parent_data.append(None)
                                parent_data[parent_widget_rows - 1] = self.convert_widget_value(
                                    widget_value,
                                    widget_meta,
                                    other_filename_to_url_mapping,
                                )
                            elif widget_meta.widget_name.widget_type in [
                                CommonWidgetType.select_tile_level,
                                CommonWidgetType.select_dropdown_level,
                                CommonWidgetType.order_question_level,
                            ]:
                                while parent_widget_rows > len(parent_data):
                                    parent_data.append([])
                                current_row_data = parent_data[parent_widget_rows - 1]
                                level = int(widget_meta.widget_name.widget_label.split(ENUM_LEVEL_PREFIX)[-1])
                                while level > len(current_row_data):
                                    current_row_data.append({})
                                converted_value = self.convert_widget_value(
                                    widget_value,
                                    widget_meta,
                                    other_filename_to_url_mapping,
                                )
                                if converted_value not in [None, ""]:
                                    current_row_data[level - 1] = {
                                        "label": converted_value,
                                        "value": converted_value,
                                    }
                            elif widget_meta.widget_name.widget_type in [
                                CommonWidgetType.radio_tile_level,
                                CommonWidgetType.radio_dropdown_level,
                            ]:
                                level = int(widget_meta.widget_name.widget_label.split(ENUM_LEVEL_PREFIX)[-1])
                                while level > len(parent_data):
                                    parent_data.append({})
                                converted_value = self.convert_widget_value(
                                    widget_value,
                                    widget_meta,
                                    other_filename_to_url_mapping,
                                )
                                if converted_value not in [None, ""]:
                                    parent_data[level - 1] = {
                                        "label": converted_value,
                                        "value": converted_value,
                                    }
                            else:
                                while parent_widget_rows > len(parent_data):
                                    parent_data.append({widget_info_key: widget_meta.create_empty_value()})
                                current_row_data = parent_data[parent_widget_rows - 1]
                                current_row_data[widget_info_key] = self.convert_widget_value(
                                    widget_value,
                                    widget_meta,
                                    other_filename_to_url_mapping,
                                )
                            continue
                        else:
                            widget_key_to_rows_mapping = step_name_to_widget_key_to_rows_mapping.get(step_name)
                            if widget_key_to_rows_mapping is None:
                                widget_key_to_rows_mapping = {}
                                step_name_to_widget_key_to_rows_mapping[step_name] = widget_key_to_rows_mapping
                            parent_widget_rows = widget_key_to_rows_mapping.get(parent_widget_key) or MIN_ROW_LENGTH
                            while parent_widget_rows > len(parent_data):
                                if widget_meta.is_item():
                                    parent_data.append(widget_meta.create_empty_value())
                                elif widget_meta.widget_name.widget_type in [
                                    CommonWidgetType.select_tile,
                                    CommonWidgetType.select_dropdown,
                                    CommonWidgetType.order_question,
                                ]:
                                    parent_data.append(widget_meta.create_empty_value())
                                else:
                                    parent_data.append({widget_info_key: widget_meta.create_empty_value()})
                            if widget_meta.is_item():
                                parent_data = parent_data[parent_widget_rows - 1]
                            else:
                                current_row_data = parent_data[parent_widget_rows - 1]
                                (
                                    _,
                                    extracted_data,
                                ) = widget_meta.get_origin_value_or_create_empty_value_from_dict(current_row_data)
                                current_row_data[widget_info_key] = extracted_data
                                parent_data = extracted_data

        return bo_data, None
