import typing
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import cast

from flask import Blueprint
from flask import jsonify
from flask import request
from loguru import logger
from more_itertools import first
from pydantic import BaseModel
from result import Err
from result import Ok
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.assistant.permission import config as permission_config
from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.base_schemas import Success
from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.business_order.errors import BusinessOrderValidateError
from robot_processor.business_order.fsm import BatchBusinessOrderStatusController
from robot_processor.business_order.fsm import BusinessOrderStatusController
from robot_processor.business_order.job_action import JobAction
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import BusinessOrderFlag
from robot_processor.business_order.models import BusinessOrderTradeMap
from robot_processor.business_order.models import Job
from robot_processor.business_order.schema import BusinessOrderQuerySchema
from robot_processor.business_order.schema import BusinessOrderSchema
from robot_processor.business_order.schema import CreateAction
from robot_processor.business_order.schema import GetBatchBusinessOrderActionsRequest
from robot_processor.business_order.schema import GetBusinessOrderActionsReqeust
from robot_processor.business_order.schema import GetBusinessOrderByOid
from robot_processor.business_order.schema import JobPatchBodySchema
from robot_processor.business_order.schema import JobPatchFail
from robot_processor.business_order.schema import StepAssistantsResponse
from robot_processor.business_order.utils.operate_debounce import CreationLock
from robot_processor.business_order.utils.operate_debounce import JobOperateLock
from robot_processor.constants import SHOP_EXPIRE_MSG
from robot_processor.currents import g
from robot_processor.decorators import load_shop
from robot_processor.decorators import org_required
from robot_processor.decorators import shop_required
from robot_processor.enums import Action
from robot_processor.enums import ColumnOperator
from robot_processor.enums import Creator
from robot_processor.enums import FromType
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.enums import StepType
from robot_processor.enums import UserType
from robot_processor.enums import WidgetValueUniqueCheckType
from robot_processor.error.base import BizError
from robot_processor.error.errors import BusinessOrderNeedEnterExceptionalPoolError
from robot_processor.error.errors import JobCantPickError
from robot_processor.error.job_process import CustomizeException
from robot_processor.error.validate import ValidateError
from robot_processor.ext import cache
from robot_processor.ext import db
from robot_processor.form.api.utils import decorator_check_form_step
from robot_processor.form.models import Form
from robot_processor.form.models import FormVersion
from robot_processor.form.models import StepWrapper
from robot_processor.form.models import WidgetInfo
from robot_processor.job.approve import ApproveJobController
from robot_processor.logging import vars as logging_vars
from robot_processor.shop.models import ContractInfo
from robot_processor.shop.models import Shop
from robot_processor.types.common import BuyerInfo
from robot_processor.utils import BaseResponse
from robot_processor.utils import GenericResponse
from robot_processor.utils import get_nearest_prev_human_job_id
from robot_processor.utils import normalize_buyer_uid
from robot_processor.utils import unwrap_optional
from robot_processor.validator import validate

api = Blueprint("business-order-api", __name__)


@api.errorhandler(BizError)
def business_order_error_handler(err: BizError):
    if isinstance(err, BusinessOrderValidateError):
        errors = err.to_response()
        for error in errors:
            if error["check_type"] == WidgetValueUniqueCheckType.CREATE_FORBIDDEN:
                check_type = WidgetValueUniqueCheckType.CREATE_FORBIDDEN
                break
        else:
            check_type = WidgetValueUniqueCheckType.REMIND
        return (
            jsonify(
                success=False,
                reason=str(err),
                check_type=check_type,
                errors=errors,
                debug_log=err.to_log(),
            ),
            200,
        )
    else:
        return jsonify(success=False, reason=err.biz_display), 200


@api.patch("/business_orders/<int:bo_id>/jobs/<int:job_id>")
@ValidateError.decorator(rewrite_response_status=True)
@org_required
@validate
@logging_vars.bind_logger_info
def trigger_job_action(
    bo_id: typing.Annotated[int, logging_vars.BusinessOrderId],
    job_id: typing.Annotated[int, logging_vars.JobId],
    body: JobPatchBodySchema,
):
    login_user: AccountDetailV2 = unwrap_optional(g.login_user_detail)
    action = body.action

    business_order: BusinessOrder | None = BusinessOrder.query.filter(BusinessOrder.id == bo_id).first()
    if business_order is None:
        return JobPatchFail(reason="未发现该工单").as_response()
    job: Job | None = Job.query.filter(Job.id == job_id).first()
    if job is None:
        return JobPatchFail(reason="未找到该任务").as_response()

    if job_id not in business_order.job_history:
        return JobPatchFail(reason="Job ID 不属于该工单").as_response()

    try:
        # 只校验权限，不关心是否是执行客服
        action_permissions = {"delete": "P_BUSINESS_ORDER_DELETE"}
        action_permission = permission_config.get_permission(action_permissions.get(action, ""))
        if action_permission and not login_user.has_permission(action_permission):
            # 权限校验失败时
            raise BizError("当前用户权限不足", status_code=403)
        operate_reason = body.operate_reason or ""

        is_admin = body.is_admin

        # 实例化工单状态控制器。
        match action:
            case Action.reject:
                specified_reject_id = body.data.get("specified_reject_id")
                # 不指定则默认退回到前一个人工步骤
                if specified_reject_id is None:
                    specified_reject_id = get_nearest_prev_human_job_id(job)
                bosc = BusinessOrderStatusController(
                    business_order=business_order,
                    operator=login_user,
                    is_admin=is_admin,
                    revert_to_job_id=specified_reject_id,
                )
            case Action.recall:
                bosc = BusinessOrderStatusController(
                    business_order=business_order,
                    operator=login_user,
                    is_admin=is_admin,
                    revert_to_job_id=job_id,
                )
            case _:
                bosc = BusinessOrderStatusController(
                    business_order=business_order,
                    operator=login_user,
                    is_admin=is_admin,
                )

        job_operate_lock = JobOperateLock(bo_id, job_id, body=body.dict(), action=action)
        job_action = JobAction(
            job_id=job_id,
            job=job,
            business_order_status_controller=bosc,
            operate_lock=job_operate_lock,
        )

        # 检测是否可操作。
        ok, reason = bosc.can_do_by_action(action)
        if not ok:
            raise BizError(reason, status_code=400)

        match action:
            case Action.close:
                job_action.close(operate_assistant=login_user, operate_reason=operate_reason)
            case Action.reopen:
                job_action.reopen(operate_assistant=login_user, operate_reason=operate_reason)
            case Action.save:
                job_action.save(
                    data_for_save=body.data or {},
                    operate_assistant=login_user,
                    operate_reason=operate_reason,
                    ignore_check_type_remind_error=body.force,
                )
            case Action.assign:
                assignee_assistant = body.job_assistant.to_account_detail() if body.job_assistant else None
                if not assignee_assistant:
                    return JobPatchFail(reason="选择的指派客服信息获取失败").as_response()
                job_action.assign(
                    assignee_assistant=assignee_assistant,
                    operate_assistant=login_user,
                    operate_reason=operate_reason,
                )
            case Action.deliver:
                assignee_assistant = body.job_assistant.to_account_detail() if body.job_assistant else None
                if not assignee_assistant:
                    return JobPatchFail(reason="选择的指派客服信息获取失败").as_response()
                job_action.deliver(
                    deliver_to_assistant=assignee_assistant,
                    operate_assistant=login_user,
                    operate_reason=operate_reason,
                )
            case Action.accept:
                next_assistant = body.next_job_assistant.to_account_detail() if body.next_job_assistant else None
                job_action.accept(
                    data_for_accept=body.data,
                    next_job_assignee_assistant=next_assistant,
                    operate_assistant=login_user,
                    operate_reason=operate_reason,
                    ignore_check_type_remind_error=body.force,
                )
            case Action.upgrade:
                job_action.upgrade(
                    data_for_upgrade=body.data,
                    operate_assistant=login_user,
                    operate_reason=operate_reason,
                    ignore_check_type_remind_error=body.force,
                )
            case Action.pick:
                job_action.pick(operate_assistant=login_user, operate_reason=operate_reason)
            case Action.recall:
                job_action.recall(operate_assistant=login_user, operate_reason=operate_reason)
            case Action.reject:
                assignee_assistant = body.job_assistant.to_account_detail() if body.job_assistant else None
                job_action.reject(
                    operate_assistant=login_user,
                    operate_reason=operate_reason,
                    specified_reject_id=body.data.get("specified_reject_id"),
                    pofa=body.assign_account_exception,
                    assignee_assistant=assignee_assistant,
                )
            case Action.pause:
                job_action.pause(operate_assistant=login_user, operate_reason=operate_reason)
            case Action.unpause:
                job_action.unpause(operate_assistant=login_user, operate_reason=operate_reason)
            case Action.delete:
                job_action.delete(operate_assistant=login_user, operate_reason=operate_reason)
            case Action.retry:
                job_action.retry(operate_assistant=login_user, operate_reason=operate_reason)
            case Action.skip:
                next_assistant = body.next_job_assistant.to_account_detail() if body.next_job_assistant else None
                job_action.skip(
                    operate_assistant=login_user,
                    operate_reason=operate_reason,
                    extra_data=body.data.get("skip_data", {}),
                    next_job_assignee_assistant=next_assistant,
                )
            case Action.complete:
                job_action.complete(operate_assistant=login_user, operate_reason=operate_reason)
            case Action.approve:
                job_action.approve(
                    operate_assistant=login_user,
                    operate_reason=operate_reason,
                )
            case Action.overrule:
                job_action.overrule(operate_assistant=login_user, operate_reason=operate_reason)
            case _:
                return JobPatchFail(reason="不支持的操作").as_response()
    except BusinessOrderNeedEnterExceptionalPoolError as e:
        Job.Utils.mark_failed(Job.query.get(job_id), str(e))  # type: ignore[arg-type]
        return JobPatchFail(reason=e.biz_display).as_response()
    except JobCantPickError as e:
        return JobPatchFail(reason=e.biz_display, code=409).as_response()
    else:
        return Success().as_response()


class CreateBusinessOrderApi:
    class ValidationError(BaseModel):
        name: str
        check_tip: str
        check_type: WidgetValueUniqueCheckType
        not_satisfied: list[dict]

    class ValidationErrorResponse(BaseModel):
        reason: str = "表单校验不通过"
        check_type: WidgetValueUniqueCheckType
        errors: list["CreateBusinessOrderApi.ValidationError"]
        debug_log: str | None

    class CreateErrorResponse(BaseModel):
        reason: str

    class SuccessResponse(BaseModel):
        bo_id: int


CreateBusinessOrderApi.ValidationError.update_forward_refs()
CreateBusinessOrderApi.ValidationErrorResponse.update_forward_refs()


@api.post("/seller_business_orders")
@ValidateError.decorator
@org_required
@validate
def create_business_order(query: BusinessOrderQuerySchema, body: BusinessOrderSchema):
    """
    创建并提交：校验数据，可能创建失败，成功则流转
    创建并保存：不校验数据，工单必定创建成功

    会做 API 和 Token 两层限流
    """
    contract = ContractInfo.query.filter_by(org_id=str(g.org_id)).first()
    if not contract or contract.is_expired:
        return CreateBusinessOrderApi.CreateErrorResponse(reason=SHOP_EXPIRE_MSG), 403

    # 这里选择赋值，而不是修改 schema model 的原因有两点：
    # - 希望去除 HTTP 参数和 jwt 之间的重复信息 https://git.leyantech.com/digismart/robot-processor/-/issues/311
    # - 避免对已有代码的过大改动
    client_ua = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36"
    )
    if request.headers.get("User-Agent") == client_ua and not body.sid:
        logger.warning(f"拦截无 sid 的客户端请求 body: {body}")
        return (
            CreateBusinessOrderApi.CreateErrorResponse(reason="当前客户端版本版本过低，请刷新后使用"),
            400,
        )
    body.sid = body.sid or g.auth.store_id
    body.creator_type = g.auth.login_user_type
    body.creator_user_id = g.auth.login_user_id
    body.mid = body.mid or g.auth.phone_number
    body.org_id = g.org_id
    if body.creator_type == Creator.ASSISTANT:
        body.from_type = FromType.ASSISTANT
        body.user_nick = body.user_nick or g.auth.login_user_nick
    elif body.creator_type == Creator.LEYAN:
        body.from_type = FromType.LEYAN
        # 网页工单等终端，使用手机短信验证登录，此时 kiosk 签发的token 中，login_user_nick 实际上是手机号而不是账号名
        body.user_nick = body.user_nick or g.auth.nick_name

    assert g.login_user_detail
    body.modify_data()
    is_ok, error = body.check()
    if not is_ok:
        return CreateBusinessOrderApi.CreateErrorResponse(reason=error), 400
    shop = unwrap_optional(Shop.Queries.optimal_shop_by_sid(body.sid, org_id=g.org_id))
    creation_lock = CreationLock(
        leyan_request_id=query.leyan_request_id,
        leyan_request_token=query.leyan_request_token,
        body=body.dict(),
        redis_client=getattr(cache.cache, "_write_client", None),
    )
    business_manager = BusinessManager(
        body.form,
        shop,
        g.login_user_detail,
        unwrap_optional(body.from_type),
        creation_lock,
        ignore_default_data_config=True,
    )
    if body.uid or body.buyer_open_uid:
        buyer_info = BuyerInfo(open_uid=body.buyer_open_uid, nick=normalize_buyer_uid(body.uid))
    else:
        buyer_info = None
    match business_manager.pipeline_create_order(
        body.data,
        buyer_info,
        action=(body.create_action if body.create_action is not None else CreateAction.SUBMIT),
        appoint_assignee=(body.next_job_assistant.to_account_detail() if body.next_job_assistant else None),
        ignore_check_type_remind_error=body.force,
    ):
        case Ok(bo):
            return CreateBusinessOrderApi.SuccessResponse(bo_id=bo.id), 200
        case Err(BusinessOrderValidateError() as err):
            errors = err.to_response()
            for error in errors:
                if error["check_type"] == WidgetValueUniqueCheckType.CREATE_FORBIDDEN:
                    check_type = WidgetValueUniqueCheckType.CREATE_FORBIDDEN
                    break
            else:
                check_type = WidgetValueUniqueCheckType.REMIND
            return CreateBusinessOrderApi.ValidationErrorResponse(
                check_type=check_type, errors=errors, debug_log=err.to_log()
            )
        case Err(err):
            return CreateBusinessOrderApi.CreateErrorResponse(reason=str(err)), 400


@api.get("/seller_business_orders")
@ValidateError.decorator
@org_required
@validate
def list_business_orders_by_oid(query: GetBusinessOrderByOid):
    oid = query.oid
    sid = query.sid
    # TODO 现在 sid 不会显示传递,会写入 auth 中,先这么 hack 下,之后改成更通用的方式
    if not sid:
        sid = g.auth.store_id
    if not (sid and oid):
        return jsonify(reason="sid or oid 参数缺失"), 400
    if sid not in g.org_sids:
        return jsonify(reason="非法店铺id"), 400
    base_query = BusinessOrder.query.filter(BusinessOrder.sid == sid, BusinessOrder.deleted.isnot(True))
    # 使用or查询会忽略索引导致全BusinessOrderTradeMap扫描
    orders = (
        base_query.outerjoin(
            BusinessOrderTradeMap,
            BusinessOrderTradeMap.business_order_id == BusinessOrder.id,
        )
        .filter(
            BusinessOrderTradeMap.tid_or_oid == oid,
        )
        .union_all(base_query.filter(BusinessOrder.v_oid == oid))
        .order_by(BusinessOrder.updated_at.desc())
    )

    return jsonify(business_orders=[order.to_dict() for order in orders])


@api.get("/business_orders/<bo_id>/jobs/<job_id>/assistants")
@ValidateError.decorator
@org_required
@validate
def get_job_assistants(bo_id, job_id) -> StepAssistantsResponse:
    from robot_processor.business_order.seller.schema import job_coordinator_list

    bo = BusinessOrder.query.get_or_404(bo_id)
    job = bo.jobs.filter(Job.id == job_id).first_or_404()  # type: ignore[attr-defined]

    shop = load_shop() or bo.shop
    form = bo.form.wraps(shop)  # type: ignore[union-attr,arg-type]

    coordinator_list = job_coordinator_list(form.co_edit, job)
    return StepAssistantsResponse(data=coordinator_list)


class GetOperatorsRequest(BaseModel):
    type: Optional[str]


class GetOperatorsResponse(BaseModel):
    class BasicOperator(BaseModel):
        name: str
        label: str
        types: List[str]
        operator: str

    data: Optional[List[BasicOperator]]


@api.get("/operators")
@shop_required
@validate
def get_operators(query: GetOperatorsRequest) -> GetOperatorsResponse:
    operators = ColumnOperator.operators(query.type)
    return GetOperatorsResponse(data=operators)


class GetStepAssistantsRequest(BaseModel):
    sid: Optional[str]
    keyword: Optional[str]


GetStepAssistantsResponse = GenericResponse[list[AccountDetailV2]]


@api.get("/forms/<form_id>/step_assistants_v2/<step_id>")
@api.get("/forms/<form_id>/steps/<step_id>/assistants")
@shop_required
@ValidateError.decorator(rewrite_response_status=True)
@decorator_check_form_step
@validate
def get_step_assistants(form_id, step_id, query: GetStepAssistantsRequest) -> GetStepAssistantsResponse:
    if query.sid:
        shop = unwrap_optional(Shop.Queries.optimal_shop_by_sid(query.sid, org_id=g.auth.org_id))
    else:
        shop = g.shop
    form = Form.query.get(form_id).wraps(shop)  # type: ignore[union-attr]
    step = form.steps.filter_by(id=step_id).first()
    if not step:
        return GetStepAssistantsResponse.Failed("步骤不可用")
    if not step.is_human():
        return GetStepAssistantsResponse.Failed("非人工步骤")
    step = step.get_latest_published_step(need_same_step_type=True) or step
    step = cast(StepWrapper, step.wraps(shop))
    assistant_info = step.get_assistants_v2()
    account_list = assistant_info.get_latest_assignee_account_details(shop)
    if query.keyword:

        def filter_fn(account: AccountDetailV2):
            keyword = query.keyword
            if not keyword:
                return False
            if account.user_type == UserType.ASSISTANT:
                return keyword in (account.user_nick or "")
            elif account.user_type == UserType.LEYAN:
                return keyword in (account.user_nick or "") or keyword in (account.phone or "")
            else:
                return False

        account_list = list(filter(filter_fn, account_list))

    return GetStepAssistantsResponse.Success(account_list)


@api.get("/jobs/<job_id>/assignee/status")
@shop_required
def get_job_assignee_status(job_id: int) -> Dict[str, Any]:
    job = Job.query.filter(Job.id == job_id).first()
    if job is None or job.shop is None:
        return {"success": False, "error_code": 404, "message": "查询不到该任务"}
    if job.step_type != StepType.human:
        # 非人工步骤直接返回 True。
        return {"success": True, "enabled": True}
    assignee = job.get_assignee_assistant()
    if assignee is None:
        return {
            "success": True,
            "enabled": False,
        }
    return {
        "success": True,
        "enabled": assignee.is_valid(),
    }


@api.get("/jobs/<job_id>/approvers")
@shop_required
def get_job_approver_infos(job_id: int) -> Dict[str, Any]:
    """
    获取指定任务的审批人列表，以及审批人是否执行了审批操作。
    审批人都是去获取最新版本的步骤上的 assistants_v2 的内容。
    如果当前步骤尚未完成，那么已经执行过审批操作的人的信息，是去取 JobApprover 中 is_approved 标识为 True 的人。
    如果当前步骤已经完成了，那么审批人是去取 job 上的 extra_data 中的归档信息。
    :param job_id:
    :return:
    """
    job: Job | None = Job.query.filter(Job.id == job_id).first()
    if job is None or job.shop is None:
        return {"success": False, "error_code": 404, "message": "查询不到该任务"}

    latest_version_step = job.current_step() or job.step
    if latest_version_step is None:
        return {"success": False, "error_code": 400, "message": "该任务缺失步骤信息"}

    if latest_version_step.step_type != StepType.approve:
        return {"success": False, "error_code": 400, "message": "该任务步骤非审批节点"}

    approve_job_controller = ApproveJobController(job)
    assistants = latest_version_step.get_assistants_v2()

    if job.status != JobStatus.SUCCEED:
        operated_approver_ids = approve_job_controller.get_operated_approver_ids()
    else:
        operated_approver_ids = approve_job_controller.get_archived_operated_approvers()
    # 按照 step 中的 assistants 的 details 内的顺序进行遍历。
    # 主要是为了防止依次审批的数据展示异常。
    approver_infos: list = []
    for approver in approve_job_controller.get_all_valid_approvers(True):
        approver_info = {
            "user_id": approver.user_id,
            "user_type": Creator.LEYAN,
            "user_nick": approver.user_nick,
            "is_approved": False,
        }
        if (approver.user_id in operated_approver_ids) or (
            approver.bound_feisuo_user is not None and approver.bound_feisuo_user.user_id in operated_approver_ids
        ):
            approver_info.update({"is_approved": True})
        approver_infos.append(approver_info)
    return {
        "success": True,
        "all_approvers": approver_infos,
        "approve_type": assistants.approve_type,
    }


BusinessOrderVersionResponse = GenericResponse[Optional[FormVersion.View.BusinessOrderDetail]]


@api.get("/business_orders/<business_order_id>/version")
@shop_required
@validate
def get_business_order_version(business_order_id) -> BusinessOrderVersionResponse:
    bo: BusinessOrder | None = BusinessOrder.query.get(business_order_id)
    if not bo:
        return BusinessOrderVersionResponse.Failed("未找到工单实例")

    form_version = bo.get_form_version()
    version_view = FormVersion.View.BusinessOrderDetail.from_orm_(form_version)
    # 替换 ui_schema 中 before=True 的 option_value
    for step in version_view.steps:
        for widget_info_obj in step.ui_schema:
            if not widget_info_obj.get("before"):
                continue
            if "key" in widget_info_obj and str(widget_info_obj["key"]).startswith("system_"):
                continue
            widget_info = first(WidgetInfo.Queries.by_keys_published([widget_info_obj["key"]]), None)
            if not widget_info:
                continue
            widget_info_obj["option_value"] = {**widget_info.option_value}
    return BusinessOrderVersionResponse.Success(version_view)


class ModifyBusinessOrderFlagReq(BaseModel):
    flag: BusinessOrderFlag


@api.patch("/business_orders/<business_order_id>/flag")
@shop_required
@validate
def modify_business_order_flag(business_order_id, body: ModifyBusinessOrderFlagReq) -> BaseResponse:
    bo: BusinessOrder | None = BusinessOrder.query.get(business_order_id)
    if not bo:
        return BaseResponse.Failed("未找到工单实例")

    bo.data["flag"] = body.flag.value
    flag_modified(bo, "data")
    db.session.commit()
    return BaseResponse.Success(None)


class ExceptionPoolSchema(BaseModel):
    bo_id: int
    reason: str
    suggestion: Optional[str]


@api.post("/exceptional_pool")
@validate
def add_business_order_to_exceptional_pool(body: ExceptionPoolSchema):
    bo = BusinessOrder.query.get(body.bo_id)
    Job.Utils.mark_failed(bo.current_job, CustomizeException(body.reason, body.suggestion or ""))  # type: ignore[union-attr,arg-type]  # noqa: E501
    return jsonify(success=True)


@api.post("/business_order_actions")
@shop_required
@validate
def get_business_order_actions(body: GetBusinessOrderActionsReqeust):
    """
    查询操作人可执行工单的操作。
    :param body:
    :return:
    """
    operator = g.login_user_detail
    if operator is None:
        return jsonify(msg="未检测到登录用户"), 401

    business_order = BusinessOrder.query.filter(BusinessOrder.id == body.business_order_id).first()
    if business_order is None:
        return jsonify(data={}, msg="未找到工单"), 200

    bsc = BusinessOrderStatusController(
        business_order=business_order,
        operator=operator,
        operator_logged_shop_sid=g.shop.sid,
    )

    bo_actions = bsc.get_actions()

    return jsonify(data=bo_actions)


@api.post("/batch_business_order_actions")
@shop_required
@validate
def get_batch_business_order_actions(body: GetBatchBusinessOrderActionsRequest):
    """
    查询操作人对于多笔工单可执行的操作。
    :param body:
    :return:
    """
    operator = g.login_user_detail
    if operator is None:
        return jsonify(msg="未检测到登录用户"), 401

    business_orders = BusinessOrder.query.filter(BusinessOrder.id.in_(body.business_order_ids)).all()

    if len(business_orders) == 0:
        return jsonify(data={}, msg="未找到工单")

    bbsc = BatchBusinessOrderStatusController(
        business_orders=business_orders,
        operator=operator,
        operator_logged_shop_sid=g.shop.sid,
    )

    bo_actions = bbsc.get_all_business_order_actions()

    return jsonify(data=bo_actions)


@api.get("/tags")
def get_tags_dummy():
    """避免前端报错而保留的接口，以后可以删除"""
    return {}


class ListAutoJobExceptionRules(BaseModel):
    job_type: JobType


@api.get("/exception-rules")
@validate
def list_auto_job_exception_rules(query: ListAutoJobExceptionRules):
    from robot_processor.business_order.binlog.job_processor import exception_ruler_keeper

    ruler = exception_ruler_keeper.exception_ruler
    rules = [
        {
            "id": rule.id,
            "reason": rule.reason,
            "suggestion": rule.suggestion,
        }
        for rule in ruler.rules.get(query.job_type, [])
        if rule.enabled
    ]
    return jsonify(data=rules)
