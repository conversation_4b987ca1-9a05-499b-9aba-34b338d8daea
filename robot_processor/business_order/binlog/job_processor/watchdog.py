from apscheduler.schedulers.background import BackgroundScheduler
from loguru import logger
from requests import Session

scheduler = BackgroundScheduler()
http = Session()


@scheduler.scheduled_job("interval", minutes=1)
def keep_exception_ruler_updated():
    """检查 exception ruler"""
    from robot_processor.app import app
    from robot_processor.business_order.binlog.job_processor import exception_ruler_keeper

    with app.app_context():
        if exception_ruler_keeper.need_update:
            logger.debug(
                "update exception ruler, local version: {}, server version: {}".format(
                    exception_ruler_keeper.local_version,
                    exception_ruler_keeper.server_version,
                )
            )
            exception_ruler_keeper.update_exception_ruler()


def update_exception_rule_hit_count():
    """更新异常任务匹配规则的命中次数"""
    from robot_processor.app import app
    from robot_processor.business_order.exception_rule import ExceptionRule
    from robot_processor.db import in_transaction

    query = {
        "target": "summarize(aliasByNode(statsd.robot-processor.exceptional-rule.matched.*.count, 4), '1d', 'sum')",
        "from": "-90d",
        "format": "json",
    }
    host = app.config.get(
        "GRAPHITE_APP_ENDPOINT",
        "http://victoriametrics-carbonapi-service.victoriametrics",
    )
    response = http.get(f"{host}/render", params=query, timeout=10)
    result = dict()
    for each_rule in response.json():
        rule_id = int(each_rule["tags"]["name"])
        hit_count = 0
        for each_day in each_rule["datapoints"]:
            day_hit_count, timestamp = each_day
            if day_hit_count is not None:
                hit_count += day_hit_count
        result[rule_id] = hit_count

    with app.app_context():
        with in_transaction():
            exception_rule_map = {rule.id: rule for rule in ExceptionRule.query}
            for rule_id in result:
                if rule_id in exception_rule_map:
                    exception_rule_map[rule_id].hit_count = result[rule_id]


def start(app):
    import os

    from robot_processor.business_order.binlog.job_processor import exception_ruler_keeper

    lain_procname = os.environ.get("LAIN_PROCNAME")
    if lain_procname is not None:
        if lain_procname == "binlog-job-consumer":
            scheduler.scheduled_job("interval", minutes=10)(update_exception_rule_hit_count)
        # 在 lain 集群内才启动后台同步任务
        scheduler.start()
    else:
        try:
            with app.app_context():
                exception_ruler_keeper.init()
        except AssertionError:
            pass
