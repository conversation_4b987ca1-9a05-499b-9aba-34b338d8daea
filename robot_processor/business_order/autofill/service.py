import threading
from datetime import datetime
from functools import cached_property

from leyan_grpc.client.venice import GaiaStub
from leyan_proto.digismart.trade.dgt_trade_pb2 import GetRecentTradesRequest
from leyan_proto.digismart.trade.dgt_trade_pb2_grpc import DgtTradeServiceStub
from loguru import logger
from pydantic import BaseModel
from requests import Session
from requests import post
from result import Err
from result import Ok
from robot_types.core import Symbol

from robot_processor.app import app
from robot_processor.business_order.autofill.assembler import \
    chat_history_to_pred
from robot_processor.business_order.autofill.assembler import slots_to_pred
from robot_processor.business_order.autofill.assembler import \
    trade_order_to_pred
from robot_processor.business_order.autofill.config import auto_fill_config
from robot_processor.business_order.autofill.models import AutoFillFormInfo
from robot_processor.business_order.autofill.models import AutoFillRecord
from robot_processor.ext import db


def pred_task(
    record_id: int,
    chat_history: str,
    form: str,
    slots: str,
    trade_info: str | None,
    token: str,
):
    inputs = {
        "form": form,
        "slots": slots,
        "chat_history": chat_history,
    }
    if trade_info:
        inputs["trade_info"] = trade_info
    response = post(
        "https://dify.leyantech.com/v1/workflows/run",
        headers={"Authorization": f"Bearer {token}"},
        json={"inputs": inputs, "user": "robot-processor"},
        timeout=60 * 5,
    )
    with app.app_context():
        logger.bind(method="AutoFill").info(
            f"inputs: {inputs}, response: {response.json()}"
        )
        record = AutoFillRecord.query.filter_by(id=record_id).one()
        record.finished_at = datetime.now()
        record.status = AutoFillRecord.Status.FINISHED
        record.mark_finished(response.json())


class AutoFillBroker:
    session = Session()
    trade_stub: DgtTradeServiceStub = GaiaStub.auto_stub(DgtTradeServiceStub)

    def __init__(
        self,
        form_id: int,
        seller_nick: str,
        buyer_nick: str,
        buyer_open_uid: str,
        access_token: str | None,
    ):
        self.form_id = form_id
        self.seller_nick = seller_nick
        self.buyer_nick = buyer_nick
        self.buyer_open_uid = buyer_open_uid
        self.access_token = access_token

    def pred(self):
        if not self.chat_history:
            return Err(Exception("没有对话信息"))
        if self.form_info is None:
            return Err(Exception("当前工单模板未开放使用自动填充"))
        chat_history = chat_history_to_pred(self.chat_history)
        form = self.form_info.subject
        slots = slots_to_pred(self.form_info.slots)
        record = AutoFillRecord.create(
            self.form_id,
            self.seller_nick,
            self.buyer_nick,
            self.buyer_open_uid,
            chat_history,
        )

        task = threading.Thread(
            target=pred_task,
            kwargs={
                "record_id": record.id,
                "chat_history": chat_history,
                "form": form,
                "slots": slots,
                "trade_info": (
                    trade_order_to_pred(self.trade_info) if self.trade_info else None
                ),
                "token": auto_fill_config.WORKFLOW_TOKEN,
            },
        )
        task.daemon = True
        task.start()
        return Ok(record)

    @cached_property
    def chat_history(self):
        return self.session.get(
            auto_fill_config.CHAT_HISTORY_API,
            headers={"really": auto_fill_config.CHAT_HISTORY_AUTH},
            params={"seller_nick": self.seller_nick, "buyer_nick": self.buyer_nick},
        ).json()["chat_history"]


    @cached_property
    def form_info(self) -> AutoFillFormInfo | None:
        return AutoFillFormInfo.query.filter_by(form_id=self.form_id).first()

    @cached_property
    def trade_info(self):
        if not self.access_token:
            return None
        request = GetRecentTradesRequest(
            seller_nick=self.seller_nick,
            buyer_id=self.buyer_nick,
            buyer_open_uid=self.buyer_open_uid,
            access_token=self.access_token,
        )
        response = self.trade_stub.GetRecentTrades(request)
        if not response.trades:
            return None
        trade_info = response.trades[0]
        for each in response.trades:
            if int(each.trade_id) > int(trade_info.trade_id):
                trade_info = each
        return trade_info


class AutoFillFormInfoInitializer:
    def __init__(self, form_id: int):
        from robot_processor.business_order.models import get_form_composer
        from robot_processor.form.models import Form
        from robot_processor.utils import unwrap_optional

        self.form = db.session.get_one(Form, form_id)
        self.form_composer = get_form_composer(
            unwrap_optional(self.form.versions.first()).id
        )

    def init_form_info(self):
        subject = self.form.name
        form_info = AutoFillFormInfo(
            form_id=self.form.id,
            optimized=False,
            slots=self.prepare_slots(),
            subject=subject,
        )
        return form_info

    def prepare_slots(self):
        flow_graph = self.form_composer.flow_graph_wrapper
        step_node = flow_graph.get_edge_target_node(flow_graph.begin.connections[0])
        step = self.form_composer.step_id_mapper[step_node.step_id]
        slot_def_list: list[SlotDef] = []
        for symbol in step.symbols:
            slot_def = self.prepare_symbol(symbol)
            if slot_def:
                slot_def_list.append(slot_def)

        return [slot_def.dict(exclude_none=True) for slot_def in slot_def_list]

    def prepare_symbol(self, symbol: Symbol):
        from robot_processor.widget.selector_utils import serialize_all_options

        enum = None
        slot_type: str
        match symbol.component_id:
            case "boolean" | "date" | "datetime" | "time" | "number" | "string":
                slot_type = symbol.component_id
            case "text":
                slot_type = "string"
            case "upload-file" | "upload-image" | "upload-video":
                slot_type = "array[string]"
            case "reissue-product":
                slot_type = "array[{SKU: string, QUANTITY: integer}]"
            case "select-dropdown-single" | "select-tile-single":
                slot_type = "string"
                if enum_options := symbol.render_config.get("options"):
                    enum = serialize_all_options(enum_options)
            case "select-dropdown-multi" | "select-tile-multi" | "order-question":
                slot_type = "array[string]"
                if enum_options := symbol.render_config.get("options"):
                    enum = serialize_all_options(enum_options)
            case "address":
                slot_type = "string"
            case _:
                return None
        if not symbol.label:
            return None
        slot_def = SlotDef(
            key=symbol.name, desc=symbol.label, type=slot_type, enum=enum
        )
        return slot_def


class SlotDef(BaseModel):
    key: str
    desc: str
    type: str
    enum: list[str] | None = None
    # conv: str | None = None
