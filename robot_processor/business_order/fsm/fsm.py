from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from concurrent.futures import as_completed
from typing import Callable
from typing import Dict
from typing import Iterable
from typing import List
from typing import Optional
from typing import Tuple

from flask import current_app
from loguru import logger
from pydantic import BaseModel
from pydantic import Field
from sqlalchemy import func

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.assistant.schema import AssistantV2
from robot_processor.business_order.exception_rule.models import ExceptionBusinessOrder
from robot_processor.business_order.fsm import constants
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import JobApprover
from robot_processor.business_order.models import JobExecuteCron
from robot_processor.business_order.models import JobTask
from robot_processor.business_order.models import Step
from robot_processor.client import kiosk_client
from robot_processor.client import robot_transfer
from robot_processor.enums import Action
from robot_processor.enums import ApproveType
from robot_processor.enums import Assignee<PERSON>ule
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import Creator
from robot_processor.enums import JobStatus
from robot_processor.enums import JobTaskRunStatus
from robot_processor.enums import JobType
from robot_processor.enums import PaymentStatus
from robot_processor.enums import PermissionFunctionCode
from robot_processor.enums import SelectType
from robot_processor.enums import ShopStatus
from robot_processor.enums import StepType
from robot_processor.ext import db
from robot_processor.form.models import Form
from robot_processor.form.models import StepRetry
from robot_processor.job.skip_helper import SkipHelper
from robot_processor.rpa_service.models import Rpa
from robot_processor.shop.models import Shop
from robot_processor.utils import cached_instance_method
from robot_processor.utils import copy_current_app_context

# 创建线程池。
thread_pool: ThreadPoolExecutor = ThreadPoolExecutor()


class StatusFlowException(Exception):
    pass


Conditions = List[str]


"""
对于一个工单可执行操作的判断条件主要有两个方面：
1、工单的当前状态。
2、操作人是否在当前步骤的执行客服中。
除此之外，部分操作需要特定的一些条件进行检测。

这一块我成为 can_do，主要是判断操作人是否能做该行为。

transitions: {
    # 当前状态
    "status_a": {
        # 可变更为的状态
        "status_b": [
            # 可操作的行为。
            "action_a",
            "action_b"
        ],
        "status_c": [
            "action_c",
            "action_a",
        ]
    }
}
"""
transitions: Dict[BusinessOrderStatus, list[Action]] = {
    # “初始”状态的变更。
    BusinessOrderStatus.INIT: [Action.create_and_save, Action.create_and_submit],
    # “进行中”状态的变更。
    BusinessOrderStatus.RUNNING: [
        Action.publish,
        Action.skip,
        Action.complete,
        Action.recall,
        Action.panic,
        Action.process,
        Action.close,
        Action.finish,
        Action.delete,
    ],
    # “成功”状态的变更。
    BusinessOrderStatus.SUCCEED: [
        Action.delete,
        Action.upgrade,
        Action.reject,
        Action.recall,
        Action.update_order,
    ],
    # “关闭”状态的变更。
    BusinessOrderStatus.CLOSE: [
        Action.reopen,
        Action.delete,
        Action.upgrade,
        Action.update_order,
    ],
    # “暂停中”状态的变更。
    BusinessOrderStatus.PAUSED: [Action.unpause, Action.complete, Action.close, Action.delete, Action.update_order],
    # “待受理”状态的变更。
    BusinessOrderStatus.PENDING: [
        Action.accept,
        Action.reject,
        Action.recall,
        Action.skip,
        Action.save,
        Action.pause,
        Action.remind,
        Action.deliver,
        Action.assign,
        Action.delete,
        Action.upgrade,
        Action.approve,
        Action.overrule,
        Action.complete,
        Action.close,
        Action.update_order,
    ],
    # “异常中”状态的变更。
    BusinessOrderStatus.IN_EXCEPTION: [
        Action.skip,
        Action.retry,
        Action.assign,
        Action.recall,
        Action.complete,
        Action.close,
        Action.delete,
        Action.upgrade,
        Action.update_order,
    ],
    # “待提交”状态的变更。
    BusinessOrderStatus.TO_BO_SUBMITTED: [
        Action.accept,
        Action.reject,
        Action.skip,
        Action.save,
        Action.remind,
        Action.delete,
        Action.upgrade,
        Action.pause,
        Action.deliver,
        Action.assign,
        Action.complete,
        Action.close,
        Action.update_order,
    ],
    # “待领取”状态的变更。
    BusinessOrderStatus.TO_BE_COLLECTED: [
        Action.recall,
        Action.remind,
        Action.upgrade,
        Action.assign,
        Action.pick,
        Action.update_order,
    ],
}


class StepInfo(BaseModel):
    step_id: int
    step_uuid: str
    step_name: str
    step_type: StepType
    index: int  # 第几步
    job_id: int  # 对应的job_id


class ActionInfo(BaseModel):
    """
    前端目前所需要的 Action 内的信息内容。
    """

    label: str = Field(description="该操作的名称")
    disabled: bool = Field(description="该操作是否不可用")
    disabled_info: str | None = Field(description="该操作不可用的原因")
    job_id: int | None = Field(description="Job ID")
    job_step_id: int | None = Field(description="任务对应步骤的 ID")
    job_step_uuid: str | None = Field(description="任务对应步骤的 UUID")
    # current_job_assignee 对应当前任务已分配的执行客服的 assignee, assignee_user_id, assignee_type 集合体。
    # “待领取”状态则不应当存在实际有效的信息。
    job_assignee: AccountDetailV2 | None = Field(description="任务已经分配的执行客服的信息。")
    # 执行客服的候选人信息
    coordinators: List[AccountDetailV2] = Field(default=[], description="候选人的详细信息的列表")
    can_operate_steps: Optional[List[StepInfo]] = Field(description="可操作的步骤，退回、撤回、订正所需。")
    next_human_job_need_assistant: Optional[bool] = Field(description="下一步任务是否为人工任务，且是否需要指派人")
    next_human_job_step_uuid: Optional[str] = Field(description="下一步任务是否为人工任务，如果是则输出其 step_uuid")


class BusinessOrderStatusController:
    """
    单笔工单的状态处理。
    该类实例主要用于判断工单状态的流向、操作工单状态变更。

    当前类中，以 `can_<action_name>` 的方法来判断是否可以进行此项操作。
    以 `<action_name>` 的方法来修改工单状态。（也是仅仅修改工单状态，基本不含其他操作。）
    """

    def __init__(
        self,
        business_order: BusinessOrder,
        operator: AccountDetailV2,
        is_admin: bool = False,
        # 期望回退到的 job 的 id，如果传入，则会在“撤回”、“退回”操作进行检查，不传则不检查。
        revert_to_job_id: Optional[int] = None,
        operator_logged_shop_sid: str | None = None,
        # 以下为优化性能的一些数据，如果提前传入，则可以减少数据库的查询。
        shop: Shop | None = None,
        form: Form | None = None,
        all_jobs: dict[int, Job] | None = None,
        all_steps: dict[int, Step] | None = None,
        valid_candidate_assistants: list[AccountDetailV2] | None = None,
    ) -> None:
        """
        shop、form、all_jobs、all_steps、valid_candidate_assistants 默认情况下不填，会自动查询。
        但如果是批量处理，可以预先查询出来，然后塞入，以减少查询。

        :param business_order:
        :param operator:                        操作人，通过 Token 来获取，主要用于检测“角色管理”中的权限。
        :param operator_logged_shop_sid:        操作人所登录进的店铺的 sid。
        :param is_admin:                        是否以管理员身份进行处理。
        :param shop:                            该工单对应的 Shop 信息。（如果不传递，则会在用到时自动获取。）
        :param form:                            该工单对应的 Form 信息。（如果不传递，则会在用到时自动获取。）
        :param all_jobs:                        该工单的所有已经生成的 Job 信息。（如果不传递，则会在用到时自动获取。）
        :param all_steps:                       该工单的所有任务对应 Step 信息。（如果不传递，则会在用到时自动获取。）
        :param valid_candidate_assistants:      该工单的当前可用执行客服信息。（如果不传递，则会在用到时自动获取。）
        """
        # 基础信息。
        self.business_order: BusinessOrder = business_order
        self.is_admin: bool = is_admin

        # 操作人的相关信息。
        self.operator: AccountDetailV2 = operator
        self.operator_logged_shop_sid: str | None = operator_logged_shop_sid
        if self.operator.user_type in [Creator.RPA, Creator.LDAP, Creator.SYSTEM]:
            self.is_admin = True

        # 期望回退到的 job 的 id
        self.revert_to_job_id: int | None = revert_to_job_id

        # 减少同 sid 导致的找错店铺的概率
        # 工单已经存在，店铺哪怕被关闭了，也应该先正常返回工单相关的状态等逻辑。
        business_order_shop: Shop | None = Shop.query.filter(Shop.sid == business_order.sid).first()
        if shop is None and business_order_shop is None:
            raise StatusFlowException(f"该工单: {self.business_order.id} 没有对应的店铺信息")
        else:
            self.shop: Shop = shop or business_order_shop  # type: ignore[assignment]

        if form is None and self.business_order.form is None:
            raise StatusFlowException(f"该工单: {self.business_order.id} 没有对应的工单模板")
        else:
            self.form: Form = form or self.business_order.form  # type: ignore[assignment]

        self.roles: set[AccountDetailV2] = {operator}
        if operator.bound_feisuo_user:
            self.roles.add(operator.bound_feisuo_user)
        self._all_jobs: dict[int, Job] | None = all_jobs
        self._all_steps: dict[int, Step] | None = all_steps
        self._current_valid_candidate_assistants: set[AccountDetailV2] | None = (
            None if valid_candidate_assistants is None else set(valid_candidate_assistants)
        )
        self._history_valid_candidate_assistants: dict[int, set[AccountDetailV2]] | None = None
        self._permissions: set[str] = set(operator.permissions)
        if self.is_admin:
            self._permissions.update(
                [
                    PermissionFunctionCode.BUSINESS_ORDER_DELETE,
                    PermissionFunctionCode.BUSINESS_ORDER_CLOSE,
                    PermissionFunctionCode.BUSINESS_ORDER_RETRY,
                    PermissionFunctionCode.BUSINESS_ORDER_ASSIGN,
                ]
            )

        self._current_job: Job | None = None
        self._current_step: Step | None = None
        self._form_owners: AssistantV2 | None = None
        self._current_job_latest_step: Step | None = None

    def get_form_owners(self) -> AssistantV2:
        """
        获取工单管理员。
        :return:
        """
        if self._form_owners is not None:
            return self._form_owners
        self._form_owners = self.form.get_owners()
        return self._form_owners

    def can_do_by_action(self, action: Action) -> tuple[bool, str]:
        """
        根据传入的 action 判断是否可以执行。
        """
        if self.business_order.deleted:
            return False, constants.DELETED_BUSINESS_ORDER
        checker = action_checker_mapping.get(action)
        if checker is None:
            return False, constants.NOT_IMPLEMENT_ACTION
        try:
            return checker(self)
        except StatusFlowException as e:
            logger.error(e)
            return False, str(e)
        except Exception as e:
            logger.exception(f"检测操作 {action} 检测时发生异常: {e}")
            return False, f"检测操作 {action} 时发生异常。"

    def get_actions(self) -> dict[Action, ActionInfo]:
        """
        获取当前工单可执行的所有操作。

        如果存在无法执行的操作，则输出缘由。
        :return:
        """
        result: dict[Action, ActionInfo] = {}
        try:
            current_job = self.get_current_job()
            current_step = self.get_current_step()
            # 取当前角色与可执行客服的交集。
            if self.is_admin:
                intersection = self.roles
            else:
                if all(
                    [
                        current_step.step_type in [StepType.human, StepType.approve],
                        self.check_operator_in_expected_candidates(),
                    ]
                ):
                    intersection = self.roles & self.get_current_valid_candidate_assistants()
                elif self.check_operator_in_form_owners():
                    intersection = self.roles
                else:
                    intersection = {self.operator}

            for action, checker in action_checker_mapping.items():
                if action == Action.update_order:
                    # 目前仅通过开放平台接口支持，未提供给页面使用
                    continue
                try:
                    ok, reason = checker(self)
                except Exception as e:
                    logger.exception(f"检测操作 {action} 时发生异常: {e}")
                    ok = False
                    reason = f"检测操作 {action} 时发生异常"
                if not ok:
                    action_info = ActionInfo(
                        label=action.label,
                        disabled=True,
                        disabled_info=reason,
                    )
                    result[action] = action_info
                    continue
                # 回传是否可操作、错误信息、当前 job_id。
                action_info = ActionInfo(
                    label=action.label,
                    disabled=False,
                    job_id=current_job.id,
                    job_step_id=current_job.step_id,
                    job_step_uuid=current_job.step_uuid,
                    job_assignee=AccountDetailV2(
                        user_nick=current_job.assignee,
                        user_type=current_job.assignee_type,
                        user_id=current_job.assignee_user_id,
                    ),
                    coordinators=list(intersection),
                )
                # “退回” 和 “撤回” 需要特别回传一份可“退回”到的步骤列表。
                if action in [Action.reject]:
                    can_operate_steps = self.get_can_reject_steps()
                    action_info.can_operate_steps = can_operate_steps
                if action in [Action.recall]:
                    can_operate_steps = self.get_can_recall_steps()
                    action_info.can_operate_steps = can_operate_steps
                    action_info.coordinators = list(
                        self.get_coordinators_by_can_operate_history_steps(can_operate_steps)
                    )
                # “订正” 需要返回可以“订正”的步骤列表。
                if action in [Action.upgrade]:
                    can_operate_steps = self.get_can_upgrade_steps()
                    action_info.can_operate_steps = can_operate_steps
                    action_info.coordinators = list(
                        self.get_coordinators_by_can_operate_history_steps(can_operate_steps)
                    )
                # “提交” 需要检测一下下一步步骤是否为手动分配。
                if action in [Action.accept]:
                    next_job_latest_step = self.get_next_job_latest_step()
                    if (
                        next_job_latest_step is not None
                        and next_job_latest_step.is_human()
                        and next_job_latest_step.assignee_rule == AssigneeRule.MANUAL
                    ):
                        action_info.next_human_job_need_assistant = True
                        action_info.next_human_job_step_uuid = next_job_latest_step.step_uuid
                if action in [Action.pick]:
                    if not self.check_operator_in_form_owners():
                        action_info.coordinators = list(
                            self.get_current_valid_candidate_assistants(check_form_co_edit=False) & self.roles
                        )
                result[action] = action_info
        except StatusFlowException as e:
            logger.error(e)
        return result

    @property
    def all_jobs(self) -> Dict[int, Job]:
        """
        获取当前工单的所有任务。
        返回值为一个 job id 与 Job 实例的字典。
        :return:
        """
        if self._all_jobs is not None:
            return self._all_jobs
        jobs: List[Job] = Job.query.filter(Job.business_order_id == self.business_order.id).all()
        self._all_jobs = {job.id: job for job in jobs}
        return self._all_jobs

    @property
    def all_steps(self) -> Dict[int, Step]:
        """
        获取当前工单的所有步骤。
        返回值为一个 step id 与 Step 实例的字典。

        目前看起来，获取全部步骤会有内存压力，导致服务被 Kill。
        实际上，对于操作而言，只需要关注工单的当前工单模板的所有步骤信息（用于订正、退回等判断）；
        以及每个 step_uuid 对应的最新版本的步骤信息（用于执行客服的判断）。
        :return:
        """
        if self._all_steps is not None:
            return self._all_steps
        # 获取当前工单所有执行过的任务对应的步骤 ID。
        step_ids: list[int | None] = [job.step_id for job in self.all_jobs.values()]
        # 获取每个 step_uuid 对应的最新版本的步骤的 ID。
        all_latest_step_ids_query: Iterable[tuple[int]] = (
            db.session.query(func.max(Step.id))
            .filter(Step.form_id == self.form.id, Step.is_dirty.is_(False))
            .group_by(
                Step.step_uuid,
            )
        )
        all_latest_step_ids: list[int] = [step_id for (step_id,) in all_latest_step_ids_query]
        steps: list[Step] = Step.query.filter(Step.id.in_(step_ids + all_latest_step_ids)).all()
        self._all_steps = {step.id: step for step in steps}
        return self._all_steps

    @cached_instance_method
    def get_operated_approvers(self) -> set[AccountDetailV2]:
        """
        获取执行过审批操作的审批人。
        :return:
        """
        current_step = self.get_current_step()
        if current_step.step_type != StepType.approve:
            return set()
        current_job = self.get_current_job()
        job_approvers: list[JobApprover] = JobApprover.query.filter(
            JobApprover.job_id == current_job.id, JobApprover.is_approved.is_(True)
        ).all()
        operated_approvers: set[AccountDetailV2] = {
            AccountDetailV2(
                user_id=job_approver.user_id,
                user_type=Creator.LEYAN,
            )
            for job_approver in job_approvers
        }
        return operated_approvers

    def get_all_step_can_retry_mapping(self) -> dict[int, bool]:
        """
        获取所有步骤是否可以重试的 mapping。
        :return:
        """
        step_uuid_mapping = {step.step_uuid: step for step in self.all_steps.values()}

        step_retry_instances: list[StepRetry] = StepRetry.query.filter(
            StepRetry.step_uuid.in_(list(step_uuid_mapping.keys()))
        ).all()

        step_uuid_can_retry_mapping = {instance.step_uuid: instance.can_retry for instance in step_retry_instances}

        step_can_retry_mapping: dict[int, bool] = {}

        for step_uuid, step in step_uuid_mapping.items():
            if step.step_type in [
                StepType.human,
                StepType.jump,
                StepType.exclusive_gateway,
                StepType.iterate_gw_end,
                StepType.iterate_gw_begin,
            ]:
                step_can_retry_mapping.update({step.id: True})
                continue

            step_can_retry_mapping.update({step.id: step_uuid_can_retry_mapping.get(step_uuid) or False})

        return step_can_retry_mapping

    def get_job_valid_candidate_assistants(self, job: Job) -> dict[int, set[AccountDetailV2]]:
        """
        根据 job 来查询其对应步骤的最新版本上的可执行客服列表。
        """
        step = self.get_job_latest_step(job)
        try:
            assistant_info = step.get_assistants_v2()
            candidates = assistant_info.get_latest_assignee_account_details(self.shop)
            return {job.id: {c for c in candidates if c.is_valid()}}
        except Exception as e:
            logger.exception(f"Job: {job.id} 客服获取失败: {e}")
            return {job.id: set()}

    def get_current_valid_candidate_assistants(self, check_form_co_edit: bool = True) -> set[AccountDetailV2]:
        """
        获取当前步骤的所有可执行操作的执行客服列表。
        :return:
        """
        current_job = self.get_current_job()
        # 如果当前表单不允许协同编辑，则返回当前任务已经分派的执行客服。
        if check_form_co_edit and not self.form.co_edit:
            current_assistant = AccountDetailV2(
                user_type=current_job.assignee_type,
                user_id=current_job.assignee_user_id,
                user_nick=current_job.assignee,
            )
            return {current_assistant}

        if self._current_valid_candidate_assistants is not None:
            return self._current_valid_candidate_assistants
        # 获取所有的执行客服（含候选人）。
        self._current_valid_candidate_assistants = (
            self.get_job_valid_candidate_assistants(job=current_job).get(current_job.id) or set()
        )
        return self._current_valid_candidate_assistants

    def get_first_human_job_candidate_assistants(self) -> set[AccountDetailV2]:
        """
        获取最新的工单发起人列表。
        :return:
        """
        first_human_step = self.get_first_human_job_latest_step()
        if first_human_step is None:
            return set()
        assistant_info = first_human_step.get_assistants_v2()
        candidates = assistant_info.get_latest_assignee_account_details(self.shop)
        return {c for c in candidates if c.is_valid()}

    def get_history_valid_candidate_assistants(self) -> dict[int, set[AccountDetailV2]]:
        """
        获取历史人工步骤的所有可执行操作的执行客服列表。
        :return:
        """
        if self._history_valid_candidate_assistants is not None:
            return self._history_valid_candidate_assistants

        history_human_jobs = self.get_history_jobs(is_human_job=True)
        history_candidates = {}
        # 基于线程池进行批量查询。
        tasks = [
            thread_pool.submit(
                copy_current_app_context(self.get_job_valid_candidate_assistants),
                job=job,
            )
            for job in history_human_jobs
        ]
        # 统计结果。
        TIMEOUT = current_app.config.get("HISTORY_CANDIDATE_TIMEOUT", 5)
        for task in as_completed(tasks, timeout=TIMEOUT):
            cas = task.result()
            history_candidates.update(cas)

        self._history_valid_candidate_assistants = history_candidates
        return self._history_valid_candidate_assistants

    def get_coordinators_by_can_operate_history_steps(self, steps: list[StepInfo]) -> set[AccountDetailV2]:
        """
        根据历史可操作的历史步骤信息，来获取最终的可执行的客服列表。
        :param steps:
        :return:
        """
        if self.is_admin:
            return self.roles
        history_valid_candidate_assistants = self.get_history_valid_candidate_assistants()
        coordinators = self.roles
        for step_info in steps:
            candidate_assistants = history_valid_candidate_assistants.get(step_info.job_id) or set()
            coordinators = candidate_assistants & coordinators

        if len(coordinators) == 0 and self.check_operator_in_form_owners():
            return {self.operator}

        return coordinators

    def get_current_job(self) -> Job:
        """
        获取当前任务。
        :return:
        """
        if self._current_job is not None:
            return self._current_job
        job = self.all_jobs.get(self.business_order.current_job_id)  # type: ignore[arg-type]
        if job is None:
            raise StatusFlowException(f"工单 {self.business_order.id} 缺失当前任务信息")
        self._current_job = job
        return self._current_job

    def get_current_step(self) -> Step:
        """
        获取当前步骤。
        :return:
        """
        if self._current_step is not None:
            return self._current_step
        current_job = self.get_current_job()
        self._current_step = self.all_steps.get(current_job.step_id)  # type: ignore[arg-type]
        if self._current_step is None:
            raise StatusFlowException(f"工单 {self.business_order.id} 未找到当前任务对应的步骤")
        return self._current_step

    def get_current_job_latest_step(self) -> Step:
        """
        获取当前任务的最新版本的步骤信息。
        主要是用于获取执行客服、审批人的信息。
        :return:
        """
        if self._current_job_latest_step is not None:
            return self._current_job_latest_step
        current_job_latest_step = self.get_current_step()
        for step in self.all_steps.values():
            if step.step_uuid == current_job_latest_step.step_uuid and step.id > current_job_latest_step.id:
                current_job_latest_step = step
        if current_job_latest_step is None:
            raise StatusFlowException(f"工单 {self.business_order.id} 未找到当前任务对应的步骤")
        self._current_job_latest_step = current_job_latest_step
        return self._current_job_latest_step

    def get_job_latest_step(self, job: Job) -> Step:
        """
        获取任务的最新版本的步骤信息。
        :return:
        """
        if job.id == self.get_current_job().id:
            return self.get_current_job_latest_step()
        all_version_steps = filter(lambda x: x.step_uuid == job.step_uuid, self.all_steps.values())
        latest_step = sorted(all_version_steps, key=lambda x: x.id)[-1]
        return latest_step

    def get_first_human_job_latest_step(self) -> Step | None:
        """
        获取第一步人工任务的步骤的最新版本。
        :return:
        """
        first_two_job_ids: list[int] = self.business_order.job_history[:2]
        if len(first_two_job_ids) == 0:
            return None
        # 获取实际的执行的第一步任务。
        first_job = self.all_jobs.get(first_two_job_ids[0])
        if first_job is None:
            return None
        first_step = self.all_steps.get(first_job.step_id)  # type: ignore[arg-type]
        if first_step is None:
            return None
        # 较旧的工单会没有起始步骤，因此第一步会是人工步骤。
        if first_step.step_type == StepType.human:
            first_human_job_latest_step = first_step
        else:
            # 如果第一步不是人工步骤，那么尝试去取第二个执行的任务作为第一步人工任务。
            if len(first_two_job_ids) < 2:
                return None
            first_human_job = self.all_jobs.get(first_two_job_ids[1])
            if first_human_job is None:
                return None
            first_human_step = self.all_steps.get(first_human_job.step_id)  # type: ignore[arg-type]
            if first_human_step is None:
                return None
            if first_human_step.step_type != StepType.human:
                return None
            first_human_job_latest_step = first_human_step

        # 查找最新版本的第一步人工任务的步骤。
        for step in self.all_steps.values():
            if step.step_uuid == first_human_job_latest_step.step_uuid and step.id > first_human_job_latest_step.id:
                first_human_job_latest_step = step

        return first_human_job_latest_step

    def get_prev_job(self) -> Optional[Job]:
        """
        获取前置任务。
        :return:
        """
        current_job = self.get_current_job()
        reversed_job_history: list[int] = self.business_order.job_history[::-1]
        if current_job.id not in reversed_job_history:
            return None
        cur_job_index = reversed_job_history.index(current_job.id)
        prev_job_index = cur_job_index + 1
        if prev_job_index < len(reversed_job_history):
            prev_job_id = reversed_job_history[prev_job_index]
            return self.all_jobs.get(prev_job_id)
        return None

    def get_prev_step(self) -> Step | None:
        """
        获取前序步骤。
        :return:
        """
        prev_job = self.get_prev_job()
        if prev_job is None:
            return None
        return self.all_steps.get(prev_job.step_id)  # type: ignore[arg-type]

    def get_history_job_ids(self) -> list[int]:
        """
        获取当前 Job 前的所有 Job ID。(以倒序排列)
        :return:
        """
        current_job = self.get_current_job()
        reversed_job_history: list[int] = self.business_order.job_history[::-1]
        if current_job.id not in reversed_job_history:
            return []
        cur_job_index = reversed_job_history.index(current_job.id)
        if cur_job_index + 1 >= len(reversed_job_history):
            return []
        if self.business_order.is_completed():
            return reversed_job_history
        else:
            return reversed_job_history[cur_job_index + 1 :]

    def get_history_jobs(self, is_human_job: bool = False) -> List[Job]:
        """
        获取当前 Job 前的所有 Job。(以倒序排列，并且去重)

        :param is_human_job:    是否仅需要人工任务。
        :return:
        """
        current_job = self.get_current_job()
        results = []
        history_job_ids = dict.fromkeys(self.get_history_job_ids())
        for job_id in history_job_ids:
            job = self.all_jobs.get(job_id)
            if job is None:
                continue
            step = self.all_steps.get(job.step_id)  # type: ignore[arg-type]
            if step is None:
                continue
            # 如果工单当前任务此前已经执行过，并且工单并没有完结，则跳过该步骤。
            if job.id == current_job.id and not self.business_order.is_completed():
                continue
            # 看是否只需要人工步骤的任务。
            if is_human_job:
                if step.is_human():
                    results.append(job)
            else:
                results.append(job)
        return results

    def get_next_job(self) -> Optional[Job]:
        """
        获取工单当前任务的下一步的任务。
        :return:
        """
        current_step = self.get_current_step()
        next_step_uuids = current_step.next_step_ids
        if len(next_step_uuids) == 0:
            return None

        next_step_uuid = next_step_uuids[0]

        next_jobs = [
            job
            for job in self.all_jobs.values()
            if job.business_order_id == self.business_order.id and job.step_uuid == next_step_uuid
        ]

        if len(next_jobs) == 0:
            return None
        return next_jobs[0]

    def get_next_job_latest_step(self) -> Step | None:
        """
        获取工单当前任务的下一步的任务的最新版本的步骤。
        """
        next_job = self.get_next_job()
        if next_job is None:
            return None
        return self.get_job_latest_step(next_job)

    def get_revert_to_job_and_step(self) -> Tuple[Optional[Job], Optional[Step]]:
        """
        根据传递的 revert_to_job_id 来获取任务和步骤信息。
        :return:
        """
        if self.revert_to_job_id is None:
            return None, None
        revert_to_job = self.all_jobs.get(self.revert_to_job_id)
        if revert_to_job is None:
            return None, None
        revert_to_step = self.all_steps.get(revert_to_job.step_id)  # type: ignore[arg-type]
        return revert_to_job, revert_to_step

    def check_current_status_support_this_action(self, action: Action) -> bool:
        """
        检测当前状态是否可以执行该 action。
        :param action:
        :return:
        """
        # 据产品等相关意见，商家后台方面，“待领取”需要被视为“待受理”。
        if self.is_admin and self.business_order.is_to_be_collected():
            transition = transitions.get(BusinessOrderStatus.PENDING)
        elif (
            self.business_order.status == BusinessOrderStatus.TO_BO_SUBMITTED
            and self.get_current_step().step_type == StepType.approve
        ):
            transition = transitions.get(BusinessOrderStatus.PENDING)
        else:
            transition = transitions.get(self.business_order.status)
        if transition is None:
            return False
        if action in transition:
            return True
        return False

    def check_operator_in_expected_candidates(self, check_form_co_edit: bool = True) -> bool:
        """
        检测操作人是否在当前的合规客服中。

        如果开启了管理员模式，则不再判断，直接返回通过。
        :return:
        """
        if self.is_admin:
            return True
        if self.get_current_step().step_type not in [
            StepType.human,
            StepType.approve,
        ]:
            return True
        valid_candidate_assistants = self.get_current_valid_candidate_assistants(check_form_co_edit=check_form_co_edit)
        intersection = self.roles & valid_candidate_assistants
        if len(intersection) > 0:
            return True
        return False

    def check_operator_in_business_order_creators(self) -> bool:
        """
        检测操作人是否为工单发起人。
        :return:
        """
        if self.is_admin:
            return True

        valid_candidate_assistants = self.get_first_human_job_candidate_assistants()
        intersection = self.roles & valid_candidate_assistants
        if len(intersection) > 0:
            return True
        return False

    @cached_instance_method
    def check_operator_in_form_owners(self) -> bool:
        """
        进行工单管理员的校验。（缓存结果）
        """
        form_owners = self.get_form_owners()
        if form_owners.select_type == SelectType.all:
            return True
        if form_owners == AssistantV2.parse(
            {
                "details": [],
                "online_only": False,
                "select_type": 2,
                "leyan_accounts": [],
                "assign_strategy": 1,
                "assignee_groups": [],
                "channel_accounts": [],
            }
        ):
            return False
        form_owner_group_uuids = {group.group_uuid for group in form_owners.assignee_groups if group.enable}
        for role in self.roles:
            group_uuids = {group["uuid"] for group in role.groups}
            if group_uuids.intersection(form_owner_group_uuids):
                return True
        form_owner_channel_accounts = set(form_owners.channel_accounts)
        if len(form_owner_channel_accounts & self.roles) > 0:
            return True
        form_owner_leyan_accounts = set(form_owners.leyan_accounts)
        if len(form_owner_leyan_accounts & self.roles) > 0:
            return True

        form_owners_bound_leyan_accounts = set(
            kiosk_client.get_bound_leyan_users_by_platform_user_ids(
                [
                    channel_account.user_id
                    for channel_account in form_owner_channel_accounts
                    if channel_account.user_id is not None
                ]
            )
        )

        if len(form_owners_bound_leyan_accounts & self.roles) > 0:
            return True
        return False

    def check_operator_is_next_approver(self) -> bool:
        """
        检测用户是否为下一位审批人。
        :return:
        """
        current_job_latest_step = self.get_current_job_latest_step()
        assistants = current_job_latest_step.get_assistants_v2()
        all_valid_approvers = self.get_current_valid_candidate_assistants(check_form_co_edit=False)
        operated_approvers = self.get_operated_approvers()

        match assistants.approve_type:
            case ApproveType.ANY_ONE_TO_SIGN | ApproveType.PARALLEL_MULTI_TO_SIGN:
                # 或签或者会签。
                if not (self.roles & all_valid_approvers):
                    return False
                if self.roles & operated_approvers:
                    return False
                return True
            case ApproveType.SEQUENTIAL_MULTI_TO_SIGN:
                if assistants.select_type == SelectType.all:
                    return True
                # 依次审批。
                # 按照 step 中的 assistants 的 details 内的顺序进行遍历。
                for approver in assistants.details or []:
                    if approver.bound_feisuo_user not in all_valid_approvers:
                        continue
                    if approver.bound_feisuo_user not in operated_approvers:
                        return approver.bound_feisuo_user in self.roles
                return False
        return False

    def check_revert_to_job_in_can_revert_steps(self, can_revert_steps: List[StepInfo]) -> bool:
        """
        检测传入的 revert_job_id 对应的信息在 can_revert_steps 中。
        :param can_revert_steps:
        :return:
        """
        revert_to_job, revert_to_step = self.get_revert_to_job_and_step()
        if revert_to_job is None or revert_to_step is None:
            return False
        for step_info in can_revert_steps:
            if step_info.step_id == revert_to_step.id and step_info.job_id == revert_to_job.id:
                return True
        return False

    def check_current_step_support_skip(self) -> bool:
        """
        检测当前步骤是否支持“跳过”。

        optimize：SkipHelper.is_rpa_can_skip() 方法会涉及一次数据库查询和一次 HTTP 请求。
                  在多笔工单处理时，需要看下如何优化。
        :return:
        """
        current_step = self.get_current_step()
        if current_step.step_type == StepType.jump:
            return True
        prev_step = self.get_prev_step()
        current_job = self.get_current_job()
        return all(
            [
                current_step.can_skip,
                (prev_step is not None and prev_step.step_type != StepType.begin),
                SkipHelper(current_job, current_step).is_rpa_can_skip(),
            ]
        )

    def check_operator_in_history_assignees(self) -> bool:
        """
        检测操作人是否是历史步骤处理人。

        如果开启了管理员模式，则不再判断，直接返回通过。

        前置的所有人工步骤的操作人，不需要再通过 Job.get_assignee_assistant() 方法来获取。
        因为 roles 中的数据应当是实时查询过得到的，应当是 Kiosk 的正确数据。
        :return:
        """
        if self.is_admin:
            return True

        history_job_assignees: set[AccountDetailV2] = set()
        for job_assignees in self.get_history_valid_candidate_assistants().values():
            history_job_assignees = history_job_assignees | job_assignees

        intersection = self.roles & history_job_assignees
        return len(intersection) > 0

    def check_current_job_is_not_processing_when_exception(self) -> bool:
        """
        检测异常中的工单是否正在执行。
        :return:
        """
        current_job = self.get_current_job()
        is_processing_business_order = ExceptionBusinessOrder.Queries.in_processing_by_job(current_job)
        return not is_processing_business_order

    def check_current_job_can_operate_when_running(self) -> bool:
        """
        检测进行中的工单是否可以操作。
        :return:
        """
        current_job = self.get_current_job()
        current_step = self.get_current_step()
        if current_step.step_type == StepType.jump:
            return True
        if not (rpa_id := current_step.rpa_id):
            return False
        else:
            rpa = Rpa.query.get(rpa_id)
        if rpa is None:
            return False
        if rpa.task == JobType.JOB_EXECUTE_CRON:
            if JobExecuteCron.query.filter(
                JobExecuteCron.business_order_id == self.business_order.id,
                JobExecuteCron.job_id == current_job.id,
            ).first():
                return True
        elif rpa.task == JobType.CHECK_LID:
            return True
        elif rpa.tag == "支付交易":
            transfer = robot_transfer.find_transfer(self.business_order.id).get("transfer", {})
            transfer_status = PaymentStatus(transfer.get("status") or PaymentStatus.INIT)
            if transfer_status.can_skip():
                return True
        elif job_task := JobTask.query.filter(JobTask.job_id == current_job.id).first():
            return job_task.run_status != JobTaskRunStatus.RUNNING.value
        return False

    def can_accept(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“提交”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.accept):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if self.get_current_step().step_type == StepType.approve:
            return False, constants.JOB_TYPE_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_expected_candidates() or self.check_operator_in_form_owners()):
            return False, constants.INVALID_ASSIGNEE
        return True, ""

    def can_save(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“保存”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.save):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_expected_candidates() or self.check_operator_in_form_owners()):
            return False, constants.INVALID_ASSIGNEE
        return True, ""

    def can_reject(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“退回”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.reject):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if self.get_current_step().step_type == StepType.approve:
            return False, constants.JOB_TYPE_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_expected_candidates() or self.check_operator_in_form_owners()):
            return False, constants.INVALID_ASSIGNEE
        # 获取前一步。
        prev_step = self.get_prev_step()
        if prev_step is None or prev_step.step_type == StepType.begin:
            return False, constants.FIRST_HUMAN_STEP_NOT_SUPPORT_REJECT
        can_reject_steps = self.get_can_reject_steps()
        if len(can_reject_steps) == 0:
            return False, constants.NO_REJECT_STEPS
        if self.revert_to_job_id is not None:
            if not self.check_revert_to_job_in_can_revert_steps(can_revert_steps=can_reject_steps):
                return False, constants.JOB_NOT_SUPPORT_REVERT
        return True, ""

    def handle_iterate_gateway(
        self,
        job_index: int,
        job_ids: list[int],
    ) -> int:
        """
        递归处理遍历网关嵌套的情况。
        :param job_index:       遍历网关结束节点的 job 在 倒序的 job_history 中的 index。
        :param job_ids:         大多情况下，传递倒序排列的 job_history 或历史任务的 id 组成的列表即可。
        :return:
        """
        # 获取遍历网关结束节点的信息。
        iterate_gw_end_job_id = job_ids[job_index]
        iterate_gw_end_job = self.all_jobs.get(iterate_gw_end_job_id)
        if iterate_gw_end_job is None:
            return -1
        iterate_gw_end_step = self.all_steps.get(iterate_gw_end_job.step_id)  # type: ignore[arg-type]
        if iterate_gw_end_step is None:
            return -1
        coupled_step_uuid = iterate_gw_end_job.raw_step_v2.get("data", {}).get("coupled_step_uuid")
        if coupled_step_uuid is None:
            return -1

        # 开始倒序推导。
        history_job_index = job_index + 1
        if history_job_index >= len(job_ids):
            return -1
        while 0 <= history_job_index < len(job_ids):
            next_job_id = job_ids[history_job_index]
            next_job = self.all_jobs.get(next_job_id)
            if next_job is None:
                return -1
            next_step = self.all_steps.get(next_job.step_id)  # type: ignore[arg-type]
            if next_step is None:
                return -1
            # 如果该步骤对应到遍历网关结束节点的 coupled_step，则说明找到了当前遍历网关的起点。
            if next_step.step_uuid == coupled_step_uuid:
                prev_job_index = history_job_index + 1
                prev_job_id = job_ids[prev_job_index]
                prev_job = self.all_jobs.get(prev_job_id)
                if prev_job is None:
                    return -1
                prev_step = self.all_steps.get(prev_job.step_id)  # type: ignore[arg-type]
                if prev_step is None:
                    return -1
                # 如果当前遍历网关的起点的前一步不是当前遍历网关的结束节点，则说明找到了最开始的遍历流程。
                # 返回前一步的 index。
                if prev_step != iterate_gw_end_step:
                    return prev_job_index
            # 如果该步骤为遍历网关的结束节点，则需要递归推导。
            if next_step.step_type == StepType.iterate_gw_end:
                sub_coupled_step_uuid = next_job.raw_step_v2.get("data", {}).get("coupled_step_uuid")
                if sub_coupled_step_uuid is None:
                    return -1
                # 获取到推导出的遍历网关起点的前一步的 index。
                history_job_index = self.handle_iterate_gateway(
                    history_job_index,
                    job_ids,
                )
                iterate_gw_begin_job = self.all_jobs.get(job_ids[history_job_index - 1])
                if iterate_gw_begin_job is None:
                    return -1
                iterate_gw_begin_step = self.all_steps.get(iterate_gw_begin_job.step_id)  # type: ignore[arg-type]
                if iterate_gw_begin_step is None:
                    return -1
                if iterate_gw_begin_step.step_uuid == sub_coupled_step_uuid:
                    return history_job_index
                continue
            if (
                next_step.step_type
                in [
                    StepType.auto,
                    StepType.approve,
                ]
                and not next_step.can_retry
            ):
                return -1
            history_job_index += 1
            continue
        return history_job_index

    @cached_instance_method
    def get_can_revert_jobs(self, is_check_recall: bool = False) -> list[Job]:
        """
        获取可以倒回的 job。
        :return:
        """
        # 检测 job_history 长度。
        history_job_ids = self.get_history_job_ids()
        executed_job_history = list(dict.fromkeys(history_job_ids[::-1]))
        if len(executed_job_history) == 0:
            return []
        begin_job = self.all_jobs.get(executed_job_history[0])
        if begin_job is None:
            return []
        begin_step = self.all_steps.get(begin_job.step_id)  # type: ignore[arg-type]
        if begin_step is None:
            return []

        # 基于历史任务生成一些 mapping 信息。
        history_jobs = self.get_history_jobs()
        step_uuid_mapping = {}
        step_job_mapping = {}
        for executed_job in history_jobs:
            step = self.all_steps.get(executed_job.step_id)  # type: ignore[arg-type]
            if step is not None:
                step_job_mapping.update({step.id: executed_job})
                step_uuid_mapping.update({step.step_uuid: step})

        # 开始尝试递归查询可以回到的步骤。
        can_revert_jobs = []
        history_job_index: int = 0
        while 0 <= history_job_index < len(history_job_ids):
            history_job_id = history_job_ids[history_job_index]
            history_job = self.all_jobs.get(history_job_id)
            if history_job is None:
                break
            history_job_step = self.all_steps.get(history_job.step_id)  # type: ignore[arg-type]
            if history_job_step is None:
                break
            # 如果该任务没有成功执行过，则不在记录。
            if history_job.status != JobStatus.SUCCEED and history_job_step.step_type not in [
                StepType.iterate_gw_begin,
                StepType.iterate_gw_end,
            ]:
                history_job_index += 1
                continue

            if (history_job_step.step_uuid == begin_step.step_uuid) or history_job_step.step_type == StepType.begin:
                # 说明已经走到了起始步骤上，直接退出循环。
                break
            if (
                history_job_step.step_type
                in [
                    StepType.auto,
                    StepType.approve,
                    StepType.human,
                ]
                and not history_job_step.can_retry
            ):
                # 如果步骤为自动化步骤、人工步骤、审批节点，且可不支持重复执行。
                # 则需要中断循环。
                break
            if history_job_step.step_type == StepType.exclusive_gateway:
                # 遇到排他网关，则需要跑到网关前继续查找。
                history_job_index += 1
                continue
            if history_job_step.step_type == StepType.iterate_gw_begin:
                # 说明找到了当前遍历网关的起点。
                # 如果是撤回检测，则直接中断。
                if is_check_recall:
                    break
                # 需要检测一下当前遍历网关已经执行过的任务是否支持重复执行。
                prev_job_index = history_job_index + 1
                if prev_job_index >= len(history_job_ids):
                    break
                prev_job = self.all_jobs.get(history_job_ids[prev_job_index])
                if prev_job is None:
                    break
                prev_step = self.all_steps.get(prev_job.step_id)  # type: ignore[arg-type]
                if prev_step is None:
                    break
                if prev_step.step_type == StepType.iterate_gw_end:
                    # 说明其在其中一次遍历中。需要获取遍历网关起点前的一步。
                    history_job_index = self.handle_iterate_gateway(prev_job_index, history_job_ids)
                    continue
                history_job_index = prev_job_index
                continue
            if history_job_step.step_type == StepType.iterate_gw_end:
                # 说明其在其中一次遍历中。
                history_job_index = self.handle_iterate_gateway(history_job_index, history_job_ids)
                continue

            if history_job not in can_revert_jobs:
                can_revert_jobs.append(history_job)
            history_job_index += 1

        return can_revert_jobs

    def get_can_reject_steps(self) -> List[StepInfo]:
        """
        获取可“退回”的步骤。

        参考 SkipHelper.get_reject_steps() 方法的实现，降低部分数据库查询。
        后续将删除 SkipHelper.get_reject_steps() 方法。

        当前步骤在退回时，用户可选的回退步骤列表
        只能退回到之前可重复执行的步骤上
        如果中间有支付步骤，不允许退回到支付步骤之前，不然会重复打款
        算法：从当前节点往前寻找所有可重复执行的步骤,删除中间的分支步骤
        :return:
        """
        can_reject_steps = []
        can_revert_jobs = self.get_can_revert_jobs()
        # 正序排列，以获取 index。
        for i, job in enumerate(can_revert_jobs[::-1]):
            step = self.all_steps.get(job.step_id)  # type: ignore[arg-type]
            if step is None:
                break
            else:
                can_reject_steps.append(
                    StepInfo(
                        step_id=step.id,
                        step_uuid=step.step_uuid,
                        step_name=step.name,
                        step_type=step.step_type,
                        index=i,
                        job_id=job.id,
                    )
                )
        return can_reject_steps

    def get_can_recall_steps(self) -> List[StepInfo]:
        """
        获取可“撤回”的步骤。

        可撤回的判定需要满足两个条件：
        1、前一步任务是人工步骤任务。
        2、当前操作人是前一步任务的操作人。

        前一步人工步骤的操作人，不需要再通过 Job.get_assignee_assistant() 方法来获取。
        因为 roles 中的数据应当是实时查询过得到的，应当是 Kiosk 的正确数据。
        :return:
        """
        can_revert_jobs = self.get_can_revert_jobs(True)
        for job in can_revert_jobs:
            step = self.all_steps.get(job.step_id)  # type: ignore[arg-type]
            if step is None:
                break
            if step.step_type == StepType.human:
                job_assignee = AccountDetailV2(
                    user_id=job.assignee_user_id,
                    user_type=job.assignee_type,
                    user_nick=job.assignee,
                )
                if job_assignee.bound_feisuo_user not in self.roles:
                    return []
                else:
                    return [
                        StepInfo(
                            step_id=step.id,
                            step_uuid=step.step_uuid,
                            step_name=step.name,
                            step_type=step.step_type,
                            index=0,
                            job_id=job.id,
                        )
                    ]
        return []

    def can_recall(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“撤回”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.recall):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if self.get_current_step().step_type == StepType.approve:
            return False, constants.JOB_TYPE_NOT_SUPPORT_THIS_ACTION
        can_recall_steps = self.get_can_recall_steps()
        if len(can_recall_steps) == 0:
            return False, constants.OPERATOR_IS_NOT_PREV_HUMAN_JOB_ASSIGNEE
        if self.revert_to_job_id is not None:
            if not self.check_revert_to_job_in_can_revert_steps(can_revert_steps=can_recall_steps):
                return False, constants.JOB_NOT_SUPPORT_REVERT
        if self.business_order.is_in_exception():
            if not self.check_current_job_is_not_processing_when_exception():
                return False, constants.CURRENT_JOB_IS_PROCESSING
        if self.business_order.is_running():
            if not self.check_current_job_can_operate_when_running():
                return False, constants.RUNNING_JOB_NOT_SUPPORT_OPERATE
        return True, ""

    def recall(self) -> None:
        """
        撤回。
        :return:
        """
        self.business_order.set_status(BusinessOrderStatus.RUNNING)

    def can_overrule(self) -> tuple[bool, str]:
        """
        判断是否可以执行“驳回”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.overrule):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        current_step = self.get_current_step()
        if current_step.step_type != StepType.approve:
            return False, constants.JOB_TYPE_NOT_SUPPORT_THIS_ACTION
        all_approvers = self.get_current_valid_candidate_assistants(check_form_co_edit=False)
        operated_approvers = self.get_operated_approvers()
        if not (self.roles & all_approvers):
            return False, constants.INVALID_ASSIGNEE
        if self.roles & operated_approvers:
            return False, constants.OPERATOR_WAS_APPROVED
        if not self.check_operator_is_next_approver():
            return False, constants.OPERATOR_IS_NOT_NEXT_APPROVER
        # 获取前一步。
        prev_step = self.get_prev_step()
        if prev_step is None or prev_step.step_type == StepType.begin:
            return False, constants.FIRST_HUMAN_STEP_NOT_SUPPORT_REJECT
        can_reject_steps = self.get_can_reject_steps()
        if len(can_reject_steps) == 0:
            return False, constants.NO_REJECT_STEPS
        return True, ""

    def overrule(self) -> None:
        """
        驳回。
        :return:
        """
        self.business_order.status = BusinessOrderStatus.PENDING

    def can_approve(self) -> tuple[bool, str]:
        """
        判断是否可以执行“审批”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.approve):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        current_step = self.get_current_step()
        if current_step.step_type != StepType.approve:
            return False, constants.JOB_TYPE_NOT_SUPPORT_THIS_ACTION
        all_approvers = self.get_current_valid_candidate_assistants(check_form_co_edit=False)
        operated_approvers = self.get_operated_approvers()
        if not (self.roles & all_approvers):
            return False, constants.INVALID_ASSIGNEE
        if self.roles & operated_approvers:
            return False, constants.OPERATOR_WAS_APPROVED
        if not self.check_operator_is_next_approver():
            return False, constants.OPERATOR_IS_NOT_NEXT_APPROVER
        return True, ""

    def approve(self) -> None:
        """
        审批。
        :return:
        """
        self.business_order.status = BusinessOrderStatus.RUNNING

    def can_skip(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“跳过”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.skip):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_expected_candidates() or self.check_operator_in_form_owners()):
            return False, constants.INVALID_ASSIGNEE
        if not self.check_current_step_support_skip():
            return False, constants.CURRENT_STEP_NOT_SUPPORT_SKIP
        if self.business_order.is_in_exception():
            if not self.check_current_job_is_not_processing_when_exception():
                return False, constants.CURRENT_JOB_IS_PROCESSING
        if self.business_order.is_running():
            if not self.check_current_job_can_operate_when_running():
                return False, constants.RUNNING_JOB_NOT_SUPPORT_OPERATE
        return True, ""

    def can_pause(self) -> Tuple[bool, str]:
        """
        判断是是否可以执行“暂停”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.pause):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_expected_candidates() or self.check_operator_in_form_owners()):
            return False, constants.INVALID_ASSIGNEE
        return True, ""

    def can_unpause(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“启用”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.unpause):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_expected_candidates() or self.check_operator_in_form_owners()):
            return False, constants.INVALID_ASSIGNEE
        return True, ""

    def can_remind(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“催促”操作。
        :return:
        """
        if not self.is_admin and PermissionFunctionCode.REMIND_WITHOUT_ADMIN not in self._permissions:
            return False, constants.OPERATOR_DO_NOT_HAVE_PERMISSION
        if not self.check_current_status_support_this_action(Action.remind):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        current_step = self.get_current_step()
        if not current_step.is_human():
            return False, constants.JOB_TYPE_NOT_SUPPORT_THIS_ACTION
        return True, ""

    def can_deliver(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“转交”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.deliver):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_expected_candidates() or self.check_operator_in_form_owners()):
            return False, constants.INVALID_ASSIGNEE
        current_step = self.get_current_step()
        if not current_step.is_human():
            return False, constants.JOB_TYPE_NOT_SUPPORT_THIS_ACTION
        return True, ""

    def deliver(self) -> None:
        """
        转交。
        工单转交给其他人处理人，工单状态都应该为待受理。by xiaoye.li
        :return:
        """
        self.business_order.set_status(BusinessOrderStatus.PENDING)

    def can_retry(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“重试”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.retry):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_history_assignees() or self.check_operator_in_form_owners()):
            return False, constants.OPERATOR_NOT_IN_HISTORY_ASSIGNEES
        current_step = self.get_current_step()
        if current_step.step_type not in [StepType.auto, StepType.jump]:
            return False, constants.JOB_TYPE_NOT_SUPPORT_THIS_ACTION
        if self.business_order.is_in_exception():
            if not self.check_current_job_is_not_processing_when_exception():
                return False, constants.CURRENT_JOB_IS_PROCESSING
            if PermissionFunctionCode.BUSINESS_ORDER_RETRY not in self._permissions:
                return False, constants.OPERATOR_DO_NOT_HAVE_PERMISSION
        return True, ""

    def can_assign(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“指派”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.assign):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        current_step = self.get_current_step()
        if not current_step.is_human():
            return False, constants.JOB_TYPE_NOT_SUPPORT_THIS_ACTION
        if self.business_order.is_in_exception():
            if not self.check_current_job_is_not_processing_when_exception():
                return False, constants.CURRENT_JOB_IS_PROCESSING
            if PermissionFunctionCode.BUSINESS_ORDER_ASSIGN not in self._permissions:
                return False, constants.OPERATOR_DO_NOT_HAVE_PERMISSION
        else:
            if not self.is_admin:
                return False, constants.IS_NOT_ADMIN
        return True, ""

    def can_complete(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“完成”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.complete):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_expected_candidates() or self.check_operator_in_form_owners()):
            return False, constants.INVALID_ASSIGNEE
        if self.business_order.is_in_exception():
            if not self.check_current_job_is_not_processing_when_exception():
                return False, constants.CURRENT_JOB_IS_PROCESSING
        if self.business_order.is_running():
            if not self.check_current_job_can_operate_when_running():
                return False, constants.RUNNING_JOB_NOT_SUPPORT_OPERATE
        return True, ""

    def can_close(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“关闭”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.close):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_expected_candidates() or self.check_operator_in_form_owners()):
            return False, constants.INVALID_ASSIGNEE
        if self.business_order.is_in_exception():
            if not self.check_current_job_is_not_processing_when_exception():
                return False, constants.CURRENT_JOB_IS_PROCESSING
            if PermissionFunctionCode.BUSINESS_ORDER_CLOSE not in self._permissions:
                return False, constants.OPERATOR_DO_NOT_HAVE_PERMISSION
        if self.business_order.is_running():
            if not self.check_current_job_can_operate_when_running():
                return False, constants.RUNNING_JOB_NOT_SUPPORT_OPERATE
        return True, ""

    def can_reopen(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“重启”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.reopen):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_expected_candidates() or self.check_operator_in_form_owners()):
            return False, constants.INVALID_ASSIGNEE
        return True, ""

    def can_delete(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“删除”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.delete):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (self.check_operator_in_expected_candidates() or self.check_operator_in_form_owners()):
            return False, constants.INVALID_ASSIGNEE
        if self.business_order.is_in_exception():
            if not self.check_current_job_is_not_processing_when_exception():
                return False, constants.CURRENT_JOB_IS_PROCESSING
        if PermissionFunctionCode.BUSINESS_ORDER_DELETE not in self._permissions:
            return False, constants.OPERATOR_DO_NOT_HAVE_PERMISSION
        if self.business_order.is_running() and not self.check_current_job_can_operate_when_running():
            return False, constants.RUNNING_JOB_NOT_SUPPORT_OPERATE
        return True, ""

    def can_pick(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“领取”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.pick):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (
            self.check_operator_in_expected_candidates(check_form_co_edit=False) or self.check_operator_in_form_owners()
        ):
            return False, constants.INVALID_ASSIGNEE
        return True, ""

    def can_upgrade(self) -> Tuple[bool, str]:
        """
        判断是否可以执行“订正”操作。
        :return:
        """
        if not self.check_current_status_support_this_action(Action.upgrade):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if self.get_current_step().step_type == StepType.approve:
            return False, constants.JOB_TYPE_NOT_SUPPORT_THIS_ACTION
        if not any(
            [
                self.check_operator_in_expected_candidates(),
                self.check_operator_in_history_assignees(),
                self.check_operator_in_form_owners(),
            ]
        ):
            return False, constants.INVALID_ASSIGNEE
        if self.business_order.is_in_exception():
            if not self.check_current_job_is_not_processing_when_exception():
                return False, constants.CURRENT_JOB_IS_PROCESSING
            if PermissionFunctionCode.BUSINESS_ORDER_RETRY not in self._permissions:
                return False, constants.OPERATOR_DO_NOT_HAVE_PERMISSION
        if len(self.get_can_upgrade_steps()) == 0:
            return False, constants.NO_UPGRADE_STEPS
        return True, ""

    def can_update_order(self) -> tuple[bool, str]:
        if not self.check_current_status_support_this_action(Action.update_order):
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not (self.is_admin or self.check_operator_in_form_owners()):
            return False, constants.OPERATOR_DO_NOT_HAVE_PERMISSION
        return True, ""

    def get_can_upgrade_steps(self) -> List[StepInfo]:
        """
        获取可“订正”的步骤。

        1、账号为当前步骤的执行客服，可以对前序人工步骤进行订正。
        2、历史步骤处理人可以对历史步骤进行订正。
        :return:
        """
        steps = []
        history: List[int] = self.business_order.job_history
        history_human_jobs = self.get_history_jobs(is_human_job=True)
        is_current_step_candidate = any(
            [self.is_admin, self.check_operator_in_expected_candidates(), self.check_operator_in_form_owners()]
        )
        current_job = self.get_current_job()

        for history_job in history_human_jobs:
            # 如果任务没有执行成功过，则不记录。
            if history_job.status != JobStatus.SUCCEED:
                continue

            step = self.all_steps.get(history_job.step_id)  # type: ignore[arg-type]
            if step is None:
                continue

            # 如果工单当前任务此前已经执行过，并且工单并没有完结，则跳过该步骤。
            if history_job.id == current_job.id and not self.business_order.is_completed():
                continue

            history_job_assignee = AccountDetailV2(
                user_id=history_job.assignee_user_id,
                user_type=history_job.assignee_type,
                user_nick=history_job.assignee,
            )
            if is_current_step_candidate or (history_job_assignee.bound_feisuo_user in self.roles):
                steps.append(
                    StepInfo(
                        step_id=step.id,
                        step_uuid=step.step_uuid,
                        step_name=step.name,
                        step_type=step.step_type,
                        index=history.index(history_job.id),
                        job_id=history_job.id,
                    )
                )
        return steps

    def can_recover(self) -> Tuple[bool, str]:
        if self.business_order.deleted is not True:
            return False, constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION
        if not self.is_admin:
            return False, constants.IS_NOT_ADMIN
        return True, ""

    def can_copy(self) -> tuple[bool, str]:
        """
        判断是否可以执行“复制”操作。
        :return:
        """
        if self.is_admin:
            # 如果用户没有单店复制和群店复制的权限，那么则返回不可操作。
            if (
                len(
                    {
                        PermissionFunctionCode.TASK_BUSINESS_ORDER_COPY,
                        PermissionFunctionCode.ORG_TASK_BUSINESS_ORDER_COPY,
                    }
                    & self._permissions
                )
                == 0
            ):
                return False, constants.OPERATOR_NOT_HAVE_COPY_PERMISSIONS
            # 如果用户仅有单店复制的权限，且当前登录的店铺的 sid 与工单的 sid 不一致，则返回不可操作。
            if all(
                [
                    PermissionFunctionCode.TASK_BUSINESS_ORDER_COPY in self._permissions,
                    PermissionFunctionCode.ORG_TASK_BUSINESS_ORDER_COPY not in self._permissions,
                    self.business_order.sid != self.operator_logged_shop_sid,
                ]
            ):
                return False, constants.OPERATOR_NOT_HAVE_COPY_PERMISSIONS
        else:
            if not any([self.check_operator_in_business_order_creators(), self.check_operator_in_form_owners()]):
                return False, constants.OPERATOR_NOT_IN_FORM_OWNERS_OR_CREATORS
        return True, ""


class BatchBusinessOrderStatusController:
    """
    面向多笔工单的状态处理。
    对于一些数据，需要先行一次获取到，避免单条反复请求，影响性能。

    todo：kiosk 创建可批量查询用户的接口，transfer 创建可批量查询打款单的接口。
    """

    def __init__(
        self,
        business_orders: List[BusinessOrder],
        operator: AccountDetailV2,
        is_admin: bool = False,
        bo_revert_to_job_id_mapping: dict[int, int] | None = None,
        operator_logged_shop_sid: str | None = None,
    ) -> None:
        self.business_orders: List[BusinessOrder] = business_orders
        self.operator: AccountDetailV2 = operator
        self.is_admin: bool = is_admin
        self.bo_revert_to_job_id_mapping: dict[int, int] = bo_revert_to_job_id_mapping or {}
        self.operator_logged_shop_sid: str | None = operator_logged_shop_sid

        # 缓存对象。
        self._all_jobs: dict[int, Job] | None = None
        self._all_business_order_status_controllers: list[BusinessOrderStatusController] | None = None

    def get_all_jobs(self) -> Dict[int, Job]:
        """
        获取所有工单的所有任务。
        返回值为一个 job id 与 Job 实例的字典。
        :return:
        """
        if self._all_jobs is not None:
            return self._all_jobs
        business_order_ids: List[int] = [business_order.id for business_order in self.business_orders]
        jobs: List[Job] = Job.query.filter(Job.business_order_id.in_(business_order_ids)).all()
        self._all_jobs = {job.id: job for job in jobs}
        return self._all_jobs

    def get_all_steps(self) -> Dict[int, Step]:
        """
        获取所有工单的所有步骤。
        返回值为一个 step id 与 Step 实例的字典。
        :return:
        """
        step_ids: list[int | None] = [job.step_id for job in self.get_all_jobs().values()]
        form_ids: list[int | None] = [business_order.form_id for business_order in self.business_orders]

        all_latest_step_ids_query: Iterable[tuple[int]] = (
            db.session.query(func.max(Step.id))
            .filter(Step.form_id.in_(form_ids), Step.is_dirty.is_(False))
            .group_by(
                Step.step_uuid,
            )
        )
        all_latest_step_ids: list[int] = [step_id for (step_id,) in all_latest_step_ids_query]
        steps: list[Step] = Step.query.filter(Step.id.in_(step_ids + all_latest_step_ids)).all()
        return {step.id: step for step in steps}

    def get_all_shops(self) -> Dict[str, Shop]:
        """
        获取所有工单对应的店铺，并以其 sid 作为键名。
        :return:
        """
        shop_sids: List[str | None] = [business_order.sid for business_order in self.business_orders]
        shops: List[Shop] = Shop.query.filter(Shop.sid.in_(shop_sids), Shop.status.in_([ShopStatus.ENABLE])).all()
        return {shop.sid: shop for shop in shops if shop.sid is not None}

    def get_all_forms(self) -> Dict[int, Form]:
        """
        获取所有工单对应的工单模板，并以 form_id 作为键名。
        :return:
        """
        form_ids: List[int | None] = [business_order.form_id for business_order in self.business_orders]
        forms = Form.query.filter(Form.id.in_(form_ids)).all()
        return {f.id: f for f in forms}

    @staticmethod
    def get_business_order_current_valid_candidate_assistants(
        business_order: BusinessOrder,
        all_jobs: Dict[int, Job],
        all_steps: Dict[int, Step],
        all_shops: Dict[str, Shop],
    ) -> Dict[int, List[AccountDetailV2]]:
        """
        获取工单当前的可执行客服列表。
        :return:
        """
        shop = all_shops.get(business_order.sid)  # type: ignore[arg-type]
        if shop is None:
            return {business_order.id: []}
        current_job_id = business_order.current_job_id
        current_job = all_jobs.get(current_job_id)  # type: ignore[arg-type]
        if current_job is None:
            return {business_order.id: []}
        current_job_latest_step = all_steps.get(current_job.step_id)  # type: ignore[arg-type]
        if current_job_latest_step is None:
            return {business_order.id: []}
        for step in all_steps.values():
            if step.step_uuid == current_job_latest_step.step_uuid and step.id > current_job_latest_step.id:
                current_job_latest_step = step
        try:
            assistant_info = current_job_latest_step.get_assistants_v2()
            candidates = assistant_info.get_latest_assignee_account_details(shop)
        except Exception as e:
            logger.error(f"工单：{business_order.id} 获取客服列表失败：{e}")
            return {business_order.id: []}
        return {business_order.id: [c for c in candidates if c.is_valid()]}

    def get_all_business_order_status_controllers(self) -> list[BusinessOrderStatusController]:
        """
        获取所有工单的 BusinessOrderStatusController 实例。
        :return:
        """
        if self._all_business_order_status_controllers is not None:
            return self._all_business_order_status_controllers

        # 先行获取一些结果。
        all_jobs = self.get_all_jobs()
        all_steps = self.get_all_steps()
        all_shops = self.get_all_shops()
        all_forms = self.get_all_forms()
        bo_vca_mapping: Dict[int, List[AccountDetailV2]] = {}

        # 如果不是管理员模式，则需要去获取当前任务的可执行客服信息。
        if not self.is_admin:
            tasks = [
                thread_pool.submit(
                    copy_current_app_context(self.get_business_order_current_valid_candidate_assistants),  # noqa: E501
                    business_order=business_order,
                    all_jobs=all_jobs,
                    all_steps=all_steps,
                    all_shops=all_shops,
                )
                for business_order in self.business_orders
            ]

            for task in as_completed(tasks, timeout=3):
                result: Dict[int, List[AccountDetailV2]] = task.result()
                bo_vca_mapping.update(result)
        elif StepType.approve in {step.step_type for step in all_steps.values()}:
            tasks = [
                thread_pool.submit(
                    copy_current_app_context(self.get_business_order_current_valid_candidate_assistants),  # noqa: E501
                    business_order=business_order,
                    all_jobs=all_jobs,
                    all_steps=all_steps,
                    all_shops=all_shops,
                )
                for business_order in self.business_orders
            ]

            for task in as_completed(tasks, timeout=3):
                result = task.result()
                bo_vca_mapping.update(result)

        # 实例化所有工单的 BusinessOrderStatusController。
        self._all_business_order_status_controllers = [
            BusinessOrderStatusController(
                business_order=business_order,
                operator=self.operator,
                is_admin=self.is_admin,
                operator_logged_shop_sid=self.operator_logged_shop_sid,
                revert_to_job_id=self.bo_revert_to_job_id_mapping.get(business_order.id),
                shop=all_shops.get(business_order.sid),  # type: ignore[arg-type]
                form=all_forms.get(business_order.form_id),  # type: ignore[arg-type]
                all_jobs=all_jobs,
                all_steps=all_steps,
                valid_candidate_assistants=bo_vca_mapping.get(business_order.id) or [],
            )
            for business_order in self.business_orders
        ]
        return self._all_business_order_status_controllers

    def get_all_business_order_actions(self) -> dict[int, dict[Action, ActionInfo]]:
        """
        返回所有工单可执行的操作。
        :return:
        """
        all_bo_status_controllers = self.get_all_business_order_status_controllers()

        results = {}

        # 获取所有工单的可执行操作。
        for bsc in all_bo_status_controllers:
            actions = bsc.get_actions()
            results[bsc.business_order.id] = actions
        return results

    def get_all_business_orders_can_do_by_action(self, action: Action) -> dict[int, Tuple[bool, str]]:
        """
        查看所有工单是否可以执行该项指定的操作。
        :param action:      需要执行的操作。
        :return:
        """
        all_bo_status_controllers = self.get_all_business_order_status_controllers()

        results = {}

        # 查看所有工单是否可以执行该项操作。
        for bsc in all_bo_status_controllers:
            results[bsc.business_order.id] = bsc.can_do_by_action(action)
        return results


action_checker_mapping: dict[Action, Callable[[BusinessOrderStatusController], tuple[bool, str]]] = {
    Action.accept: BusinessOrderStatusController.can_accept,
    Action.save: BusinessOrderStatusController.can_save,
    Action.reject: BusinessOrderStatusController.can_reject,
    Action.recall: BusinessOrderStatusController.can_recall,
    Action.skip: BusinessOrderStatusController.can_skip,
    Action.pause: BusinessOrderStatusController.can_pause,
    Action.unpause: BusinessOrderStatusController.can_unpause,
    Action.remind: BusinessOrderStatusController.can_remind,
    Action.deliver: BusinessOrderStatusController.can_deliver,
    Action.retry: BusinessOrderStatusController.can_retry,
    Action.assign: BusinessOrderStatusController.can_assign,
    Action.complete: BusinessOrderStatusController.can_complete,
    Action.close: BusinessOrderStatusController.can_close,
    Action.reopen: BusinessOrderStatusController.can_reopen,
    Action.delete: BusinessOrderStatusController.can_delete,
    Action.pick: BusinessOrderStatusController.can_pick,
    Action.upgrade: BusinessOrderStatusController.can_upgrade,
    Action.recover: BusinessOrderStatusController.can_recover,
    Action.approve: BusinessOrderStatusController.can_approve,
    Action.overrule: BusinessOrderStatusController.can_overrule,
    Action.copy: BusinessOrderStatusController.can_copy,
    Action.update_order: BusinessOrderStatusController.can_update_order,
}
