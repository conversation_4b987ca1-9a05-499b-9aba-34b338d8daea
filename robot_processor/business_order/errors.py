from dataclasses import asdict
from dataclasses import dataclass
from dataclasses import field
from dataclasses import replace
from typing import TYPE_CHECKING
from typing import Any

from robot_types.core import Condition
from robot_types.core import Filter
from robot_types.core import OperatorEnum
from robot_types.core import Predicate
from robot_types.core import Scope
from robot_types.core import Symbol
from robot_types.core import SymbolTable
from robot_types.helper import serialize
from robot_types.helper.predefined import BizType

from robot_processor.error.errors import BusinessOrderError
from robot_processor.logging import to_log
from robot_processor.logging.formmat_utils import operator_to_log

if TYPE_CHECKING:
    from robot_processor.business_order.business_order_manager import FormValidator


class BusinessOrderValidateError(BusinessOrderError):
    @dataclass
    class NotSatisfied:
        loc: list[int]
        extra: dict = field(default_factory=dict)

    @dataclass
    class ValidateError:
        name: str
        not_satisfied: list["BusinessOrderValidateError.NotSatisfied"]

    @dataclass
    class NotSatisfiedCondition:
        label: str
        operator: str
        value: Any
        value_type_spec: Any
        children: list["BusinessOrderValidateError.NotSatisfiedCondition"] | None

        def to_dict(self):
            from robot_processor.utils import filter_none

            return filter_none(asdict(self))

        @classmethod
        def from_condition(cls, condition: Condition, symbol_table: SymbolTable, current_scope: Scope):
            def get_symbol_label(symbol):
                label_list: list[str] = []
                cursor = symbol
                while True:
                    if cursor.label:
                        label_list.append(cursor.label)
                    if cursor.type_spec.type == "array" and cursor.type_spec.items:
                        if not cursor.children:
                            break
                        cursor = cursor.children[0]
                    else:
                        break
                return "/".join(label_list)

            a_symbol: Symbol | None = None
            if condition.a.qualifier == "var" and (
                a_symbol := condition.a.var.resolve_symbol(symbol_table, current_scope)
            ):
                label = get_symbol_label(a_symbol)
            else:
                label = to_log(condition)
            operators = BizType.BO_FORM_VALIDATOR.provide_operator_resolver().resolve(
                condition.a.type_spec,
                condition.a.var.path if condition.a.qualifier == "var" else None,
            )
            operator = operator_to_log(condition.o, operators=operators)
            if condition.o == [OperatorEnum.EACH] and condition.b.qualifier == "predicate" and a_symbol is not None:
                value = None
                value_type_spec = {"type": "boolean"}
                each_scope = Scope(id=condition.a.var.path, parent=current_scope.id)
                each_symbol_table = replace(symbol_table)
                each_symbol_table.add_scope(each_scope)
                each_symbol_table.add_symbols(each_scope, a_symbol.children[0].children)
                children = cls.from_predicate(condition.b.predicate, each_symbol_table, each_scope).children
            else:
                if condition.b is not None:
                    match condition.b.qualifier:
                        case "var":
                            b_symbol = symbol_table.lookup(symbol_name=condition.b.var.path, scope=current_scope)
                            if b_symbol:
                                value = get_symbol_label(b_symbol)
                            else:
                                value = to_log(condition.b)
                        case "const":
                            value = serialize(condition.b.const.value)
                        case _:
                            value = to_log(condition.b)
                    value_type_spec = condition.b.type_spec.to_deprecated()
                    children = None
                else:
                    value = None
                    value_type_spec = None
                    children = None
            return cls(
                label=label,
                operator=operator,
                value=value,
                value_type_spec=value_type_spec,
                children=children,
            )

        @classmethod
        def from_predicate(cls, predicate: Predicate, symbol_table: SymbolTable, current_scope: Scope):
            label = "条件组"
            operator = "满足全部" if predicate.relation == "and" else "满足任一"
            value = None
            children = [
                cls.from_condition(condition, symbol_table, current_scope) for condition in predicate.conditions
            ]
            return cls(
                label=label,
                operator=operator,
                value=value,
                value_type_spec={"type": "boolean"},
                children=children,
            )

    def __init__(self, errors: list[ValidateError], form_validator: "FormValidator"):
        self.errors = errors
        self._form_validator = form_validator
        super().__init__("表单校验不通过")

    def to_response(self):

        structured_errors: list[dict] = []

        for error in self.errors:
            rule = self._form_validator.get_validation_rule(error.name)
            not_satisfied_list: list[dict] = []
            structured_error = dict(
                name=error.name,
                check_tip=rule.check_tip,
                check_type=rule.check_type,
                not_satisfied=not_satisfied_list,
            )
            for not_satisfied in error.not_satisfied:
                condition = self._get_error_condition(rule.name, not_satisfied)
                if isinstance(condition, Condition):
                    not_satisfied_list.append(
                        self.NotSatisfiedCondition.from_condition(
                            condition,
                            symbol_table=self._form_validator.symbol_table,
                            current_scope=self._form_validator.current_scope,
                        ).to_dict()
                    )
                else:
                    not_satisfied_list.append(
                        self.NotSatisfiedCondition.from_predicate(
                            condition,
                            symbol_table=self._form_validator.symbol_table,
                            current_scope=self._form_validator.current_scope,
                        ).to_dict()
                    )
            structured_errors.append(structured_error)
        return structured_errors

    def to_log(self, without_extra=False):
        tmpl = "规则: {rule_name} 条件: {condition}{extra}"
        return "\n".join(
            [
                tmpl.format(
                    rule_name=error.name,
                    condition=to_log(self._get_error_condition(error.name, not_satisfied)),
                    extra=("" if (not (extra := not_satisfied.extra) or without_extra) else f" 额外信息: {extra}"),
                )
                for error in self.errors
                for not_satisfied in error.not_satisfied
            ]
        )

    def _get_error_condition(self, name: str, not_satisfied: "NotSatisfied"):
        def by_loc(not_satisfied_: BusinessOrderValidateError.NotSatisfied, predicate_: Predicate) -> Predicate:
            cur, cascade = not_satisfied_.loc[0], not_satisfied_.loc[1:]
            if isinstance(predicate_, Condition):
                return predicate_
            predicate_ = predicate_.conditions[cur]
            if cascade:
                return by_loc(
                    BusinessOrderValidateError.NotSatisfied(loc=cascade, extra=not_satisfied_.extra), predicate_
                )
            return predicate_

        predicate = self._form_validator.get_validation_rule(name).to_value().predicate
        if isinstance(predicate, Filter) and len(not_satisfied.loc) > 1:
            not_satisfied = replace(not_satisfied, loc=not_satisfied.loc[1:])
        return by_loc(not_satisfied, predicate)


class ComponentValueUniqueRemind(BusinessOrderError):
    """组件上的值唯一提示"""

    _biz_display = "值唯一校验不通过"

    def __init__(self, widgets: dict[str, list[str]]):
        super().__init__("值唯一校验不通过")
        # key 为组件的 key, value 为重复的工单ID
        self.widgets = widgets
