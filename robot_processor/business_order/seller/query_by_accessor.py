import time
from functools import cached_property
from typing import Any
from typing import Dict
from typing import List
from typing import Literal
from typing import Op<PERSON>
from typing import Tuple

from flask import current_app
from loguru import logger
from pydantic import BaseModel
from pydantic import Field
from sqlalchemy import and_
from sqlalchemy import func
from sqlalchemy import not_
from sqlalchemy import or_

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.assistant.schema import AssistantV2
from robot_processor.assistant.schema import SelectType
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import BusinessOrderTradeMap
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import JobApprover
from robot_processor.business_order.seller.enums import MyBusinessOrdersTab
from robot_processor.business_order.seller.schema import BusinessOrderStats
from robot_processor.business_order.seller.schema import to_order_list_by_user_id
from robot_processor.business_order.utils.global_search_bo import global_search_by_multi_keywords
from robot_processor.client import kiosk_client
from robot_processor.database_util import QueryExplainer
from robot_processor.database_util import cache_query_count_with_limit
from robot_processor.enums import AssigneeRule
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import Creator
from robot_processor.enums import FormMold
from robot_processor.enums import PageSort
from robot_processor.enums import StepType
from robot_processor.enums import TimeoutEnum
from robot_processor.ext import db
from robot_processor.form.models import Form
from robot_processor.form.models import Step
from robot_processor.logging import vars as log_vars
from robot_processor.shop.models import Shop
from robot_processor.users.services import AssistantService
from robot_processor.utils import unwrap_optional


class CanOperateFormInfo(BaseModel):
    """
    可以操作的工单模板的信息。
    """

    operator_form_ids: list[int] = Field(default_factory=list, description="作为执行人可以操作的工单模板的 id 列表")
    operator_step_ids: list[int] = Field(default_factory=list, description="作为执行人可以操作的步骤的 id 列表")
    operator_step_uuids: list[str] = Field(default_factory=list, description="作为执行人可以操作的步骤的 uuid 列表")
    free_pick_step_ids: list[int] = Field(
        default_factory=list, description="作为执行人可以操作的自由领取的步骤的 id 列表"
    )


class AccessorQuery:
    def __init__(self, sid_list: list[str], user_id: int, org_id: int):
        """
        :param sid_list: 店铺 sid 列表
        :param user_id:  乐言用户 id
        """
        self._sid_list = set(sid_list)
        self.user_id = user_id
        self.org_id = org_id

    @cached_property
    def platform_user_ids(self) -> list[int]:
        return kiosk_client.get_platform_user_ids_by_leyan_user_id(self.user_id)

    def job_assignee_condition(self):
        """判断一个 job 是否指派给了指定乐言账户的查询条件。

        ⚠️ 注意：这里之所以没有用更简洁的 Job.feisuo_assignee_user_id == self.user_id 来判断，是因为 feisuo_assignee_user_id
        字段上没有添加索引，查询特别慢。而 Job 表因为数据量大，添加索引时间太长，所以暂时不添加索引。未来如果添加了索引，可以考虑用更简洁的查询条件。
        """
        return or_(
            and_(Job.assignee_user_id == self.user_id, Job.assignee_type == Creator.LEYAN),
            and_(Job.assignee_user_id.in_(self.platform_user_ids), Job.assignee_type == Creator.ASSISTANT),
        )

    def bo_creator_condition(self):
        """判断一个 business_order 是否由指定乐言账户创建的查询条件。

        ⚠️ 注意：这里之所以没有用更简洁的 BusinessOrder.feisuo_creator_user_id == self.user_id 来判断，是因为 feisuo_creator_user_id
        字段上没有添加索引，查询特别慢。而 BusinessOrder 表因为数据量大，添加索引时间太长，所以暂时不添加索引。未来如果添加了索引，可以考虑用更简洁的查询条件。
        """
        return or_(
            and_(BusinessOrder.creator_user_id == self.user_id, BusinessOrder.creator_type == Creator.LEYAN),
            and_(
                BusinessOrder.creator_user_id.in_(self.platform_user_ids),
                BusinessOrder.creator_type == Creator.ASSISTANT,
            ),
        )

    def business_orders_stats(self, keyword) -> BusinessOrderStats:
        sids = self._sid_list
        query = (
            db.session.query(BusinessOrder.id.distinct())
            .filter(BusinessOrder.sid.in_(sids), BusinessOrder.deleted.isnot(True))
            .join(Job, Job.business_order_id == BusinessOrder.id)
            .join(Form, Form.id == BusinessOrder.form_id)
        )
        if keyword:
            keyword = keyword.strip()
            query = query.outerjoin(BusinessOrderTradeMap, BusinessOrderTradeMap.business_order_id == BusinessOrder.id)
            query = query.filter(
                or_(
                    BusinessOrder.v_oid == keyword,
                    BusinessOrder.v_tid == keyword,
                    BusinessOrderTradeMap.tid_or_oid == keyword,
                    BusinessOrder.uid.contains(keyword),
                    BusinessOrder.buyer_open_uid == keyword,
                )
            )

        unprocessed_count = self.tab_unprocessed_filter(query).limit(100).count()

        processed_count = cache_query_count_with_limit(self.tab_processed_filter(query), limit=100)

        all_count = self.tab_all_filter(query).limit(100).count()

        created_count = self.tab_created_filter(query).limit(100).count()

        to_be_approve_count = self.tab_to_be_approve_filter(query).limit(100).count()

        # 目前 get_stats 接口大部分都是 relate_count 导致的.
        # 大部分情况下, relate_count 都是往上增加的, 如果查询结果等 100 , 就直接缓存一天
        # ref: https://git.leyantech.com/digismart/robot-processor/-/issues/83
        relate_count = cache_query_count_with_limit(self.tab_relate_filter(query), limit=100)

        return BusinessOrderStats(
            UNPROCESSED=unprocessed_count,
            PROCESSED=processed_count,
            ALL=all_count,
            CREATED=created_count,
            RELATE=relate_count,
            TO_BE_APPROVE=to_be_approve_count,
        )

    def accessors_forms_filter(self, query_field: tuple, tab: MyBusinessOrdersTab, picked, status):
        query = (
            db.session.query(*query_field)
            .select_from(BusinessOrder)
            .join(Job, Job.business_order_id == BusinessOrder.id)
            .filter(BusinessOrder.sid.in_(self._sid_list), BusinessOrder.deleted.isnot(True))
        )
        if tab == MyBusinessOrdersTab.FREE_PICK:
            query = self.tab_free_pick_filter(query, picked=picked)
        elif tab == MyBusinessOrdersTab.ALL:
            query = self.tab_all_filter(query)
        elif tab == MyBusinessOrdersTab.UNPROCESSED:
            query = self.tab_unprocessed_filter(query)
        elif tab == MyBusinessOrdersTab.PROCESSED:
            query = self.tab_processed_filter(query)
        elif tab == MyBusinessOrdersTab.CREATED:
            query = self.tab_created_filter(query)
        elif tab == MyBusinessOrdersTab.RELATE:
            query = self.tab_relate_filter(query)
        elif tab == MyBusinessOrdersTab.TO_BE_APPROVE:
            query = self.tab_to_be_approve_filter(query)

        if status:
            query = query.filter(BusinessOrder.status == status)

        return query

    def accessors_forms(self, tab: str, picked=True, status=None):
        tab = MyBusinessOrdersTab[tab.upper()]
        if tab == MyBusinessOrdersTab.ALL:
            # 在网页工单的 "全部" tab 下，仅对店铺条件进行过滤，不对 job assign 情况进行过滤
            # 这么修改的原因是：避免联表查询带来的扫描上千万行的极端性能问题
            # ref: https://git.leyantech.com/digismart/robot-processor/-/issues/80#note_1207424
            log_vars.SqlSource.set()
            query = db.session.query(BusinessOrder.form_id, func.count()).filter(
                BusinessOrder.form_id.in_(self.visible_form_ids)
            )
        else:
            query = self.accessors_forms_filter(
                (BusinessOrder.form_id, func.count(BusinessOrder.id.distinct())), tab, picked, status
            )
        # 不统计已删除的工单的数量。
        query = query.filter(BusinessOrder.deleted.isnot(True)).group_by(BusinessOrder.form_id)
        form_ids = {q[0]: q[1] for q in query}
        forms: list[dict] = []
        if not form_ids:
            return forms
        for f in Form.query.filter(Form.id.in_(form_ids.keys())):
            info = f.brief()
            info.update({"total": form_ids[f.id]})
            forms.append(info)
        return forms

    def accessors_form_steps(self, form_ids, tab: str, picked=True, status=None):
        if not form_ids:
            return []
        tab = MyBusinessOrdersTab[tab.upper()]
        if tab == MyBusinessOrdersTab.PROCESSED:
            bo_query = self.accessors_forms_filter((BusinessOrder.id.distinct(),), tab, picked, status)
            query = (
                db.session.query(Job.step_id.distinct())
                .select_from(BusinessOrder)
                .join(Job, BusinessOrder.id == Job.business_order_id)
                .filter(Job.id == BusinessOrder.current_job_id)
                .filter(BusinessOrder.id.in_(bo_query))
                .filter(BusinessOrder.form_id.in_(form_ids))
            )
        else:
            query = self.accessors_forms_filter((Job.step_id.distinct(),), tab, picked, status).filter(
                BusinessOrder.form_id.in_(form_ids)
            )
        return [step_id for step_id, in query]

    def business_orders(
        self,
        page_tab,
        keyword,
        filter_close,
        sort_name,
        form_id,
        form_ids,
        step_name,
        page,
        per_page,
        picked=True,
        status=None,
        flag=None,
    ) -> Tuple[Optional[str], Optional[List[Dict[str, Any]]], Optional[Dict[str, int]]]:
        session = db.ro_session
        query = (
            session.query(BusinessOrder)
            .filter(BusinessOrder.sid.in_(self._sid_list), BusinessOrder.deleted.isnot(True))
            .join(Job, Job.business_order_id == BusinessOrder.id)
            .join(Form, Form.id == BusinessOrder.form_id)
        )

        if page_tab == MyBusinessOrdersTab.ALL:
            query = self.tab_all_filter(query)
        elif page_tab == MyBusinessOrdersTab.UNPROCESSED:
            query = self.tab_unprocessed_filter(query)

        elif page_tab == MyBusinessOrdersTab.PROCESSED:
            query = self.tab_processed_filter(query)
        elif page_tab == MyBusinessOrdersTab.CREATED:
            query = self.tab_created_filter(query)
        elif page_tab == MyBusinessOrdersTab.FREE_PICK:
            query = self.tab_free_pick_filter(query, picked=picked)
        elif page_tab == MyBusinessOrdersTab.RELATE:
            query = self.tab_relate_filter(query)
        elif page_tab == MyBusinessOrdersTab.TO_BE_APPROVE:
            query = self.tab_to_be_approve_filter(query)
        else:
            return "非法tab页", [], None

        timeout = {"untimeout_count": 0, "timeout_count": 0, "unset_count": 0}

        if keyword:
            # 先兼容前端传入 str 或 list[str] 这两种数据格式的可能。
            # 1、前端还没有修改完，网页工单还没处理。
            # 2、这样目前不会影响到当前正在使用的用户。
            keywords = []
            if isinstance(keyword, str):
                keywords = [keyword.strip()]
            elif isinstance(keyword, list):
                keywords = [k.strip() for k in keyword]
            if not keywords:
                return None, [], timeout

            if es_query_ids := global_search_by_multi_keywords(list(self._sid_list), keywords):
                query = query.filter(BusinessOrder.id.in_(es_query_ids))
            else:
                return None, [], timeout
        if filter_close:
            query = query.filter(BusinessOrder.status != BusinessOrderStatus.CLOSE)

        if status:
            query = query.filter(BusinessOrder.status == status)
        if flag is not None:
            query = query.filter(BusinessOrder.flag == flag)
        if form_id or form_ids:
            if form_id:
                query = query.filter(BusinessOrder.form_id == form_id)
            else:
                query = query.filter(BusinessOrder.form_id.in_(form_ids))
            if step_name:
                if current_app.config.get("FILTER_STEP_USE_OLD", True):
                    query = query.filter(
                        func.json_contains(
                            Job._raw_step_v2,
                            func.json_object("name", step_name),
                        ),
                    )
                else:
                    subquery = query.subquery()
                    step_query = db.session.query(Step.id).filter(Step.name == step_name, Step.is_dirty.isnot(True))
                    if form_id:
                        step_query = step_query.filter(Step.form_id == form_id)
                    else:
                        step_query = step_query.filter(Step.form_id.in_(form_ids))
                    query = (
                        BusinessOrder.query.join(Job, Job.business_order_id == BusinessOrder.id)
                        .join(subquery, subquery.c.id == BusinessOrder.id)
                        .filter(
                            Job.id == BusinessOrder.current_job_id,
                            Job.step_id.in_(step_query.subquery()),  # type: ignore[arg-type]
                        )
                    )

        if sort_name == PageSort.ASC.name or sort_name == PageSort.UPDATEASC.name:
            sort = BusinessOrder.updated_at.asc()
        elif sort_name == PageSort.DESC.name or sort_name == PageSort.UPDATEDESC.name:
            sort = BusinessOrder.updated_at.desc()
        elif sort_name == PageSort.CREATEDESC.name:
            sort = BusinessOrder.created_at.desc()
        elif sort_name == PageSort.CREATEACS.name:
            sort = BusinessOrder.created_at.asc()
        else:
            sort = (BusinessOrder.is_timeout.desc(), BusinessOrder.message_ts.asc())  # type: ignore[assignment]

            c = (
                db.session.query(func.count("*"), BusinessOrder.is_timeout)
                .group_by(BusinessOrder.is_timeout)
                .filter(BusinessOrder.id == query.with_entities(BusinessOrder.id).subquery().c.id)
                .all()
            )
            for count, name in c:
                if name == TimeoutEnum.UN_TIMEOUT.value:
                    timeout["untimeout_count"] += count
                elif name == TimeoutEnum.TIMEOUT.value:
                    timeout["timeout_count"] += count
                elif name == TimeoutEnum.UNSET.value:
                    timeout["unset_count"] += count
                else:
                    timeout["unset_count"] += count
        if isinstance(sort, tuple):  # type: ignore[unreachable]
            query = query.order_by(*sort)  # type: ignore[unreachable]
        else:
            query = query.order_by(sort)
        if (explainer := QueryExplainer(query, business="MY_BUSINESS_ORDER")) and explainer.is_slow_query():
            return f"查询数据量大于上限{explainer.row_limit}条，建议您增加筛选条件缩小范围后查询", None, None

        items = query.distinct().limit(per_page).offset((page - 1) * per_page).all()

        data = [
            to_order_list_by_user_id(
                bo,
                self.user_id,
                show_visited=page_tab in (MyBusinessOrdersTab.UNPROCESSED, MyBusinessOrdersTab.FREE_PICK),
            )
            for bo in items
        ]

        return None, data, timeout

    @cached_property
    def visible_form_ids(self):
        """当前用户可以查看的工单"""
        from robot_processor.form.getter import filter_form_ids_visible_by_user

        user_id, org_id = self.user_id, self.org_id
        form_ids = list(
            {
                form_id
                for form_id, in Form.Queries.by_sids(list(self._sid_list))
                .filter(Form.Filters.subscribed)
                .with_entities(Form.id)
            }
        )
        return filter_form_ids_visible_by_user(org_id, form_ids, user_id)

    @cached_property
    def accessible_form_ids(self):
        """当前用户可以创建的工单"""
        # 这个函数的计算结果会被 all_filter 和 related_filter 使用，所以使用 cached_property 进行复用，以减少重复查询
        from robot_processor.form.getter import filter_form_ids_creatable_by_user

        user_id, org_id = self.user_id, self.org_id
        if not user_id or not org_id:
            return []
        start = time.time()
        form_ids = filter_form_ids_creatable_by_user(org_id, self.all_accessible_form_ids, user_id)
        (
            logger.bind(k="db", org_id=org_id, **{"metrics.query_time_ms": int((time.time() - start) * 1000)}).info(
                f"forms accessible to user: {user_id} {form_ids}"
            )
        )
        return form_ids

    @cached_property
    def all_form_ids(self) -> list[int]:
        form_ids_query = (
            Form.Queries.by_sids(list(self._sid_list)).filter(Form.Filters.subscribed).with_entities(Form.id)
        )
        all_form_ids = [form_id for form_id, in form_ids_query]
        return all_form_ids

    @cached_property
    def all_accessible_form_ids(self) -> list[int]:
        form_ids_query = (
            Form.Queries.by_sids(list(self._sid_list))
            .filter(Form.form_mold == FormMold.CUSTOM)
            .filter(Form.Filters.subscribed)
            .with_entities(Form.id)
        )
        all_form_ids = [form_id for form_id, in form_ids_query]
        return all_form_ids

    def tab_all_filter(self, query):
        #  全部 包括用户作为分派人（已分派）或者候选人（未实际分派）
        log_vars.SqlSource.set()
        if self.org_id == 2966:
            query = query.filter(BusinessOrder.form_id.in_(self.all_form_ids))
        else:
            query = query.filter(
                or_(
                    BusinessOrder.form_id.in_(self.accessible_form_ids),
                    self.job_assignee_condition(),
                    self.bo_creator_condition(),
                )
            )
        return query

    def tab_unprocessed_filter(self, query):
        """筛选范围为当前步骤"""
        log_vars.SqlSource.set()

        query = query.filter(
            self.job_assignee_condition(),
            BusinessOrder.status.in_(
                (
                    BusinessOrderStatus.RUNNING,
                    BusinessOrderStatus.PENDING,
                    BusinessOrderStatus.PAUSED,
                    BusinessOrderStatus.IN_EXCEPTION,
                    BusinessOrderStatus.TO_BO_SUBMITTED,
                )
            ),
            Job.unprocessed_condition(),
            Job.id == BusinessOrder.current_job_id,
        )
        return query

    def tab_to_be_approve_filter(self, query):
        """
        过滤出待我审批的工单。
        """
        query = (
            query.join(Step, Step.id == Job.step_id)
            .join(JobApprover, JobApprover.job_id == Job.id, isouter=True)
            .filter(
                Step.step_type == StepType.approve,
                BusinessOrder.status.in_(
                    [
                        BusinessOrderStatus.PENDING,
                        BusinessOrderStatus.TO_BO_SUBMITTED,
                        BusinessOrderStatus.PAUSED,
                    ]
                ),
                Job.unprocessed_condition(),
                Job.id == BusinessOrder.current_job_id,
            )
            .filter(JobApprover.user_id == self.user_id)
        )
        return query

    def tab_processed_filter(self, query):
        """筛选范围为非当前步骤"""
        log_vars.SqlSource.set()
        query = query.filter(
            self.job_assignee_condition(),
            Job.processed_condition(),
            or_(
                BusinessOrder.status == BusinessOrderStatus.SUCCEED,
                and_(Job.id != BusinessOrder.current_job_id, BusinessOrder.status != BusinessOrderStatus.SUCCEED),
            ),
        )
        return query

    def tab_created_filter(self, query):
        log_vars.SqlSource.set()
        query = query.filter(self.bo_creator_condition(), Job.id == BusinessOrder.current_job_id)
        return query

    def tab_relate_filter(self, query):
        # 候选人工单 筛选出所有accessor作为候选人的工单
        # accessor -> form_ids -> bos
        log_vars.SqlSource.set()

        new_relate_rule_switch = current_app.config.get("NEW_RELATE_RULE_SWITCH", False)
        new_relate_rule_shop_sids = current_app.config.get("NEW_RELATE_RULE_SHOP_SIDS", [])

        if new_relate_rule_switch or self._sid_list.intersection(set(new_relate_rule_shop_sids)):
            query = query.join(Step, Step.id == Job.step_id).filter(
                BusinessOrder.form_id.in_(self.can_operate_form_info.operator_form_ids),
                BusinessOrder.status.in_(
                    [
                        BusinessOrderStatus.RUNNING,
                        BusinessOrderStatus.IN_EXCEPTION,
                        BusinessOrderStatus.PAUSED,
                        BusinessOrderStatus.PENDING,
                        BusinessOrderStatus.TO_BO_SUBMITTED,
                        BusinessOrderStatus.TO_BE_COLLECTED,
                    ]
                ),
                not_(self.job_assignee_condition()),
                Job.id == BusinessOrder.current_job_id,
                Step.step_type == StepType.human,
                Step.id.in_(self.can_operate_form_info.operator_step_ids),
            )
            return query
        else:
            query = query.filter(
                BusinessOrder.form_id.in_(self.accessible_form_ids),
                BusinessOrder.status.in_(
                    [
                        BusinessOrderStatus.RUNNING,
                        BusinessOrderStatus.IN_EXCEPTION,
                        BusinessOrderStatus.PAUSED,
                        BusinessOrderStatus.PENDING,
                        BusinessOrderStatus.TO_BO_SUBMITTED,
                        BusinessOrderStatus.TO_BE_COLLECTED,
                    ]
                ),
                not_(self.job_assignee_condition()),
                Job.id == BusinessOrder.current_job_id,
            )
            return query

    def tab_free_pick_filter(self, query, picked: bool = True):
        log_vars.SqlSource.set()
        query = query.join(
            Step,
            Step.id == Job.step_id,
        )
        if picked:
            query = query.filter(
                Step.id.in_(self.can_operate_form_info.free_pick_step_ids),
                Job.assignee_user_id == self.user_id,
                BusinessOrder.status.in_((BusinessOrderStatus.PENDING,)),
            )
        else:
            query = query.filter(
                Step.id.in_(self.can_operate_form_info.free_pick_step_ids),
                Job.assignee_user_id.is_(None),
                BusinessOrder.status.in_((BusinessOrderStatus.TO_BE_COLLECTED,)),
            )

        return query

    def get_shops(self) -> list[Shop]:
        """
        获取店铺信息。
        :return:
        """
        shops: list[Shop] = db.ro_session.query(Shop).filter(Shop.sid.in_(self._sid_list)).all()
        return shops

    def get_all_human_step_valid_candidate_assistants(self, form_ids: list[int]):
        """
        获取所有人工步骤的可执行客服信息。
        生成 step.id 与可执行客服的映射。

        :param form_ids:
        :return:
        """
        all_steps: list[Step] = (
            db.ro_session.query(Step)
            .filter(Step.id.in_(Step.Queries.step_ids(form_id=form_ids, is_dirty=False, step_type=StepType.human)))
            .all()
        )
        # 对 step 进行排序，方便获取最新一条。
        all_steps = sorted(all_steps, key=lambda s: s.id, reverse=True)
        assistant_service = AssistantService.from_org(int(self.org_id))
        step_uuid_to_valid_candidate_assistants_mapping: dict[str, Literal["ALL"] | list[AccountDetailV2]] = dict()

        for step in all_steps:
            # 如果已经存在了该 step_uuid 对应的客服列表信息，则跳过该条。
            if step.step_uuid in step_uuid_to_valid_candidate_assistants_mapping.keys():
                continue
            assistants_info: AssistantV2 | None = step.get_assistants_v2()
            if assistants_info is None:
                step_uuid_to_valid_candidate_assistants_mapping.update({step.step_uuid: []})
            elif assistants_info.select_type == SelectType.all:
                step_uuid_to_valid_candidate_assistants_mapping.update({step.step_uuid: "ALL"})
            else:
                valid_candidate_assistants = set(
                    [
                        leyan_user.to_account_detail_v2()
                        for leyan_user in assistant_service.filter_leyan_users_by_users_and_groups(
                            assistants_info.details or [], list({g.group_uuid for g in assistants_info.assignee_groups})
                        )
                    ]
                )
                step_uuid_to_valid_candidate_assistants_mapping[step.step_uuid] = list(valid_candidate_assistants)

        result: dict[int, Literal["ALL"] | list[AccountDetailV2]] = dict()
        for step in all_steps:
            result[step.id] = step_uuid_to_valid_candidate_assistants_mapping[step.step_uuid]
        return result

    @cached_property
    def can_operate_form_info(self) -> CanOperateFormInfo:
        """
        查询当前账号可以操作的工单模板 id 和 步骤 id。
        :return:
        """
        # 查询该账号可以操作的工单模板 id 和 step_uuid。
        step_id_to_assistants_mapping = self.get_all_human_step_valid_candidate_assistants(form_ids=self.all_form_ids)
        step_ids = []
        step_uuids = set()
        form_ids = set()
        free_pick_step_ids = []

        steps: list[Step] = (
            db.ro_session.query(Step).filter(Step.id.in_(list(step_id_to_assistants_mapping.keys()))).all()
        )

        step_id_to_step_mapping = {step.id: step for step in steps}

        for step_id, valid_candidate_assistants in step_id_to_assistants_mapping.items():
            if valid_candidate_assistants == "ALL":
                step_ids.append(step_id)
                step = unwrap_optional(step_id_to_step_mapping.get(step_id))
                step_uuids.add(step.step_uuid)
                if step and step.form_id:
                    form_ids.add(step.form_id)
                if step and step.assignee_rule == AssigneeRule.FREE_PICK:
                    free_pick_step_ids.append(step_id)
            elif isinstance(valid_candidate_assistants, list):
                if len(valid_candidate_assistants) == 0:
                    continue
                else:
                    if self.user_id in [i.user_id for i in valid_candidate_assistants]:
                        step_ids.append(step_id)
                        step = unwrap_optional(step_id_to_step_mapping.get(step_id))
                        step_uuids.add(step.step_uuid)
                        if step and step.form_id:
                            form_ids.add(step.form_id)
                        if step and step.assignee_rule == AssigneeRule.FREE_PICK:
                            free_pick_step_ids.append(step_id)

        return CanOperateFormInfo(
            operator_form_ids=list(form_ids),
            operator_step_ids=step_ids,
            operator_step_uuids=list(step_uuids),
            free_pick_step_ids=free_pick_step_ids,
        )
