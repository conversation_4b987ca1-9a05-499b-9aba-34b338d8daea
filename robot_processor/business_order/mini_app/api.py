"""淘宝小程序工单接口"""

from typing import Literal
from typing import cast

from flask import Blueprint
from flask import jsonify
from flask import request
from loguru import logger
from sqlalchemy import and_

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.base_schemas import Response
from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.business_order.errors import BusinessOrderValidateError
from robot_processor.business_order.mini_app.schema import BuyerBusinessOrderListSchema
from robot_processor.business_order.mini_app.schema import BuyerBusinessOrderSchema
from robot_processor.business_order.mini_app.schema import CurrentBuyerInfoRes
from robot_processor.business_order.mini_app.schema import buyer_bo_detail_info
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import FromType
from robot_processor.business_order.schema import CreateAction
from robot_processor.buyer_table.models import BuyerTable
from robot_processor.buyer_table.models import ServiceBuffet
from robot_processor.buyer_table.schemas import BuyerTableDetail
from robot_processor.client import taobao_client
from robot_processor.client import trade_client
from robot_processor.currents import g
from robot_processor.decorators import mini_app_required
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import Creator
from robot_processor.enums import WidgetValueUniqueCheckType
from robot_processor.ext import db
from robot_processor.form.models import Address
from robot_processor.form.models import AddressSchema
from robot_processor.form.models import Form
from robot_processor.form.models import FormShop
from robot_processor.logging import log_vars
from robot_processor.shop.models import GrantRecord
from robot_processor.shop.models import Shop
from robot_processor.types.common import BuyerInfo
from robot_processor.utils import ResultUtil
from robot_processor.utils import ts2date
from robot_processor.utils import wrap_status_code_in_response_body
from robot_processor.validator import validate

api = Blueprint("mini-app-business-order-api", __name__)
api.after_request(wrap_status_code_in_response_body)


def create_business_order(body: BuyerBusinessOrderSchema):
    """全部上线后可以代替 post"""
    shop = Shop.query.filter_by(org_id=g.shop["org_id"], sid=g.shop["sid"]).first()  # type: ignore[index]
    if not shop or not shop.is_valid():
        return jsonify(data={"code": 400, "errorMsg": "工单链接已失效，请联系客服处理"})
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    try:
        body.sid = g.shop["sid"]  # type: ignore[index]
        body.from_type = FromType.BUYER
        body.creator_type = Creator.USER
        body.creator_user_id = None
        body.buyer_open_uid = getattr(g, "buyer_open_uid", "")
        body.uid = getattr(g, "user_nick", "") or body.uid
        logger.info(f"creating business order with {body}")
        body.modify_data()
        is_ok, error = body.check()
        if not is_ok:
            return jsonify(data={"code": 400, "errorMsg": error})
        creator = AccountDetailV2(user_type=body.creator_type, user_id=body.creator_user_id, user_nick=body.uid)
        if body.buyer_open_uid:
            buyer_info = BuyerInfo(open_uid=body.buyer_open_uid, nick=body.uid)
        else:
            buyer_info = None
        business_manager = BusinessManager(body.form, shop, creator, body.from_type, ignore_default_data_config=True)
        create_result = business_manager.pipeline_create_order(
            data=body.data,
            buyer_info=buyer_info,
            action=(body.create_action if body.create_action is not None else CreateAction.SUBMIT),
            appoint_assignee=None,
            ignore_check_type_remind_error=body.force,
        )
        if create_result.is_ok():
            return jsonify(
                data={"code": 200, "errorMsg": create_result.unwrap().id, "bo_id": create_result.unwrap().id}
            )
        err = create_result.unwrap_err()
        if isinstance(err, BusinessOrderValidateError):
            errors = err.to_response()
            for error in errors:
                if error["check_type"] == WidgetValueUniqueCheckType.CREATE_FORBIDDEN:
                    check_type = WidgetValueUniqueCheckType.CREATE_FORBIDDEN
                    break
            else:
                check_type = WidgetValueUniqueCheckType.REMIND
            return jsonify(
                data={
                    "code": 400,
                    "errorMsg": "表单校验不通过",
                    "check_type": check_type,
                    "errors": errors,
                }
            )
        else:
            return jsonify(data={"code": 400, "errorMsg": str(err)})
    except Exception as e:
        logger.opt(exception=e).error("创建用户工单失败")
        db.session.rollback()
        return jsonify(data={"code": 500, "errorMsg": "系统异常"})


@api.post("/business_orders")
@mini_app_required
@validate
def post(body: BuyerBusinessOrderSchema):
    """创建/修改工单"""
    if request.args.get("validator_mode"):
        return create_business_order(body)
    _logger = logger.bind(k="mini app post business order")
    shop = Shop.query.filter_by(org_id=g.shop["org_id"], sid=g.shop["sid"]).first()  # type: ignore[index]
    if not shop or not shop.is_valid():
        return jsonify(data={"code": 400, "errorMsg": "工单链接已失效，请联系客服处理"})
    try:
        body.sid = g.shop["sid"]  # type: ignore[index]
        body.from_type = FromType.BUYER
        body.creator_type = Creator.USER
        body.creator_user_id = None
        body.buyer_open_uid = getattr(g, "buyer_open_uid", "")
        body.uid = getattr(g, "user_nick", "") or body.uid
        _logger.info("creating business order with {}", body)
        rep_dict = BusinessManager.create_buyer_business_order(org_id=g.org_id, body=body)
        _logger.info(str(rep_dict))
        if ResultUtil.not_success(rep_dict):
            db.session.rollback()
            return jsonify(data={"code": 400, "errorMsg": ResultUtil.get_reason(rep_dict)})
        return jsonify(data={"code": 200, "errorMsg": ResultUtil.get_data(rep_dict)})
    except BaseException as e:
        _logger.opt(exception=e).error("创建用户工单失败")
        db.session.rollback()
        return jsonify(data={"code": 500, "errorMsg": "系统异常"})


@api.get("/business_orders/<business_order_id>")
@mini_app_required
def mini_app_get_business_order_detail(business_order_id):
    """查看工单详情(消费者提交内容&工单状态)"""
    bo = BusinessOrder.query.filter(BusinessOrder.deleted.isnot(True)).filter_by(id=business_order_id).first()
    if not bo:
        return jsonify(reason="工单不存在"), 404
    data = buyer_bo_detail_info(bo)
    return jsonify(success=True, data=data)


@api.post("/business_orders/list")
@mini_app_required
@validate
def mini_app_get_business_order_list(body: BuyerBusinessOrderListSchema):
    """查看工单列表"""
    sid = g.shop["sid"]  # type: ignore[index]
    buyer_open_uid = getattr(g, "buyer_open_uid", "")
    uid = getattr(g, "user_nick", "")
    logger.info(f"mini_app_get_business_order_list. uid: {uid}, buyer_open_uid:{buyer_open_uid}")

    if not buyer_open_uid:
        logger.error("mini-app. buyer_open_uid is none")
        return jsonify(success=True, data={})

    paginate = (
        BusinessOrder.query.filter(
            and_(
                BusinessOrder.deleted.isnot(True),
                BusinessOrder.buyer_open_uid == buyer_open_uid,
                BusinessOrder.sid == sid,
                BusinessOrder.creator_type == Creator.USER,
            )
        )
        .join(Form, Form.id == BusinessOrder.form_id)
        .order_by(BusinessOrder.created_at.desc())
        .paginate(page=body.page, per_page=body.per_page, cached=True, timeout=10)
    )

    data = []
    form_ids = []
    for bo in paginate.items:
        step_num = []
        for job in bo.jobs:
            step_num.append(job)
        form_ids.append(str(bo.form_id))
        data.append(
            {
                "id": bo.id,
                "form_id": bo.form_id,
                "step_num": len(step_num),
                "name": bo.form.name,
                "status": (
                    "FINISH" if bo.status in [BusinessOrderStatus.SUCCEED, BusinessOrderStatus.CLOSE] else "RUNNING"
                ),
                "created_at": ts2date(bo.created_at),
            }
        )

    # 获取买家自助服务台的部分信息。
    buyer_tables: list[BuyerTable] = BuyerTable.query.filter(BuyerTable.form_id.in_(form_ids)).all()

    table_names = {bt.form_id: bt.name for bt in buyer_tables}
    logger.info(f"mini_app_get_business_order_list. table_names: {table_names}")
    for item in data:
        form_id = item.get("form_id")
        if form_id is None:
            continue
        if (name := table_names.get(str(form_id))) is not None:
            item.update({"name": name})
    logger.info(f"mini_app_get_business_order_list. data: {data}")

    return jsonify(
        success=True,
        data={
            "data": data,
            "pages": paginate.pages,
            "per_page": paginate.per_page,
            "page": paginate.page,
            "total": paginate.total,
        },
    )


@api.get("/trades")
@mini_app_required
def mini_app_get_trades():
    buyer_nick: str = request.args.get("user_nick", "")
    open_uid: str = request.args.get("open_id", "")
    if not buyer_nick:
        return jsonify(reason="缺失买家信息"), 400
    shop_info = cast(dict, g.shop)
    shop: Shop | None = (
        Shop.query.filter(Shop.sid == shop_info["sid"])
        .filter(Shop.platform.in_([Shop.Platform.TAOBAO, Shop.Platform.TMALL]))
        .first()
    )
    if not shop:
        return jsonify(reason=f"未找到店铺信息 {g.shop}"), 400

    record = shop.get_recent_record()
    if not record or not record.access_token:
        return jsonify(trades=[], reason="缺失授权记录"), 400
    token: str = record.access_token
    trades = trade_client.get_trades_by_buyer(str(shop.sid), shop.nick, buyer_nick, open_uid, token)
    return jsonify(success=True, data=trades)


@api.get("/current-buyer")
@mini_app_required
@validate
def get_current_buyer_info() -> Response[CurrentBuyerInfoRes]:
    from robot_processor.client import get_buyer_nick
    from robot_processor.utils import is_buyer_nick_encrypt

    buyer_nick = request.args.get("user_nick", None)
    buyer_open_uid = request.args.get("open_id", None)

    strategy: Literal["valid", "encrypt", "lack"]
    if not buyer_nick:
        strategy = "lack"
    elif is_buyer_nick_encrypt(buyer_nick):
        strategy = "encrypt"
        buyer_nick = None  # 要返回给前端，如果是加密的就置空
    else:
        strategy = "valid"

    if strategy != "valid" and buyer_open_uid is None:
        return Response(succeed=False, data=CurrentBuyerInfoRes())

    if strategy != "valid":  # 通过 buyer_open_uid / tid 拿
        if res := get_buyer_nick(g.shop["sid"], buyer_open_uid=buyer_open_uid):  # type: ignore[index]
            strategy = "valid"
            buyer_nick = res

    return Response(
        succeed=strategy == "valid", data=CurrentBuyerInfoRes(buyer_open_uid=buyer_open_uid, buyer_nick=buyer_nick)
    )


@api.get("/widget-data/address")
@mini_app_required
def get_address():
    address_query = AddressSchema.AddressQuery.build(
        province=request.args.get("province"),
        city=request.args.get("city"),
        zone=request.args.get("zone"),
        query_town=bool(request.args.get("query_town")),
    )
    address_list = Address.get_address_by_query(address_query)
    return jsonify(success=True, data=dict(result=[address.dict() for address in address_list]))


@api.get("/buyer-table/list/<buffet_id>")
@mini_app_required
def buyer_tables(buffet_id: int):
    """买家查看可创建的工单列表"""
    buyer_tables: list[BuyerTable.View] = BuyerTable.get_all_by_service_buffet_id(buffet_id)

    shop: Shop | None = Shop.query.filter(Shop.sid == g.shop["sid"]).first()  # type:ignore[index]
    if shop is None:
        return jsonify(data=[])

    form_ids = [bt.form_id for bt in buyer_tables]

    ro_session = db.ro_session

    forms = ro_session.query(Form).filter(Form.id.in_(form_ids)).all()

    form_mappings: dict[int, Form] = {f.id: f for f in forms}

    form_shops = (
        ro_session.query(FormShop).filter(FormShop.channel_id == shop.channel_id, FormShop.form_id.in_(form_ids)).all()
    )

    form_shop_mapping: dict[int, FormShop] = {fs.form_id: fs for fs in form_shops}

    tables = []

    for table_info in buyer_tables:
        form_id = table_info.form_id
        detail = BuyerTableDetail.generate(
            table_info, form_mappings.get(form_id), form_shop_mapping.get(form_id)
        ).dict()

        tables.append(detail)
    return jsonify(data=tables)


@api.get("/buyer-table/ui_schema/<buffet_id>")
@mini_app_required
def ui_schema(buffet_id: int):
    """个性化配置"""
    buffet: ServiceBuffet | None = ServiceBuffet.query.get(buffet_id)
    if buffet is None:
        return jsonify(data={})
    shop: Shop | None = Shop.query.filter(Shop.sid == buffet.sid).first()
    if shop is None:
        return jsonify(data={})

    shop_dict = shop.brief()

    grant_record: GrantRecord | None = shop.get_recent_record()
    if grant_record and grant_record.access_token:
        (
            taobao_client.shop_seller_get(grant_record.access_token, fields="pic_path")
            .map(lambda response: response["shop"]["pic_path"])
            .map(lambda relative_pic_path: f"http://logo.taobao.com/shop-logo{relative_pic_path}")
            .map(lambda pic_url: shop_dict.update(pic_url=pic_url))
        )

    buffet.ui_schema["shop_title"] = shop_dict.get("title")
    buffet.ui_schema["shop_pic_url"] = shop_dict.get("pic_url")
    buffet.ui_schema["form_history"] = buffet.ui_schema.get("form_history", {})
    return jsonify(data=buffet.ui_schema)
