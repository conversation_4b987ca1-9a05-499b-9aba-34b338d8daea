import datetime
import time
from typing import TYPE_CHECKING
from typing import List
from typing import Op<PERSON>
from typing import cast

import sqlalchemy as sa
from pydantic import BaseModel
from pydantic import Field
from robot_types.helper.operator_resolver import OperatorEnum
from robot_types.helper.sql import FieldResolver
from robot_types.helper.sql import FieldResolverRegistry
from robot_types.helper.sql import TableFieldRegistry
from sqlalchemy import func
from sqlalchemy import or_
from sqlalchemy.ext.hybrid import hybrid_method
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import synonym_for

from robot_processor.business_order.exception_rule.schemas import Account
from robot_processor.business_order.exception_rule.schemas import BusinessOrderBrief
from robot_processor.business_order.exception_rule.schemas import ExceptionInfo
from robot_processor.business_order.exception_rule.schemas import ExceptionPoolEventMsg
from robot_processor.business_order.exception_rule.schemas import ExceptionPoolType
from robot_processor.business_order.exception_rule.schemas import JobBrief
from robot_processor.db import BasicMixin
from robot_processor.db import DbBase<PERSON>odel
from robot_processor.db import in_transaction
from robot_processor.enums import UserType
from robot_processor.types import exception_pool as exception_pool_type
from robot_processor.types.exception_rule import ExceptionRuleExtraConfig

if TYPE_CHECKING:
    from robot_processor.business_order.models import BusinessOrder
    from robot_processor.business_order.models import Job


class ExceptionRule(DbBaseModel, BasicMixin):
    """异常展示规则，根据 job.exc_info 匹配对应的规则"""

    priority: Mapped[int] = mapped_column(sa.Integer, default=0, comment="优先级，数值越大优先级越高")
    enabled: Mapped[bool] = mapped_column(sa.Boolean, default=False, comment="规则是否生效")
    scopes: Mapped[list] = mapped_column(sa.JSON, default=list, comment="匹配规则生效范围，如人工步骤、RPA创建补发单等")
    rules: Mapped[list] = mapped_column(sa.JSON, default=list, comment="匹配规则")
    extra_config: Mapped[ExceptionRuleExtraConfig] = mapped_column(
        sa.JSON,
        default=lambda: ExceptionRuleExtraConfig(actions=[], auto_retry=False),
        comment="额外的配置信息",
    )

    reason: Mapped[str] = mapped_column(sa.Text, nullable=False, default="", comment="异常原因")
    suggestion: Mapped[str] = mapped_column(sa.Text, nullable=False, default="", comment="操作建议")

    updated_by: Mapped[str] = mapped_column(sa.String(32), nullable=False, comment="更新人")
    hit_count: Mapped[int] = mapped_column(sa.Integer, default=0, comment="规则命中次数")

    def __repr__(self):
        return (
            "<ExceptionRule "
            "id={id} "
            "priority={priority}, "
            "enabled={enabled} "
            "scopes={scopes} "
            "reason={reason} "
            "suggestion={suggestion} "
            "rules={rules}"
            "can_auto_retry={can_auto_retry}"
            ">"
        ).format(
            id=self.id,
            priority=self.priority,
            enabled=self.enabled,
            scopes=self.scopes,
            reason=self.reason,
            suggestion=self.suggestion,
            rules=self.rules,
            can_auto_retry=self.can_auto_retry,
        )

    @property
    def can_auto_retry(self):
        return bool(self.extra_config.get("auto_retry", False))

    class View:
        class Matcher(BaseModel):
            """用于规则匹配"""

            class Config:
                orm_mode = True

            id: int
            priority: int = Field(description="优先级，数值越大优先级越高")
            enabled: bool = Field(description="规则是否生效")
            scopes: list = Field(description="匹配规则生效范围，如人工步骤、RPA创建补发单等")
            rules: list = Field(description="匹配规则")
            extra_config: dict = Field(description="额外的配置信息")
            reason: str = Field(description="异常原因")
            suggestion: str = Field(description="操作建议")

            @property
            def can_auto_retry(self):
                return bool(self.extra_config.get("auto_retry", False))

            @property
            def is_rate_limit_error(self):
                """是否是限流类异常"""
                return self.extra_config.get("is_rate_limit_error", False)

            @property
            def is_client_offline_error(self):
                """是否是客户端不在线类异常，结合apollo配置自动重试"""
                return self.extra_config.get("is_client_offline_error", False)


class ExceptionRuleUnmatched(DbBaseModel, BasicMixin):
    """未匹配任何规则的异常信息"""

    __table_args__ = (sa.Index("ix_message_digest", "message_digest"),)

    scope: Mapped[str | None] = mapped_column(sa.String(32), nullable=True, comment="任务类型")
    raw_exc: Mapped[str] = mapped_column(sa.Text, nullable=False, comment="原始错误信息")
    message_digest: Mapped[str] = mapped_column(sa.String(32), nullable=False, comment="原始错误信息的摘要，用于索引")

    relate_job: Mapped[list] = mapped_column(sa.JSON, default=list, comment="关联的 Job")
    hit_count: Mapped[int] = mapped_column(sa.Integer, sa.Computed("json_length(relate_job)"), comment="发生次数")

    @classmethod
    def calculate_md5(cls, scope: str, raw_exc: str):
        """计算原始错误信息的摘要"""
        import hashlib
        import json

        obj = {"scope": scope, "raw_exc": raw_exc}

        message_digest = hashlib.md5(json.dumps(obj, sort_keys=True).encode()).hexdigest()
        return message_digest


class ExceptionBusinessOrder(DbBaseModel):
    """异常的任务"""

    sql_field_resolver_registry = FieldResolverRegistry()
    sql_table_field_registry = TableFieldRegistry()
    __tablename__ = "exceptional_business_order_pool"
    __table_args__ = (
        sa.Index(
            "exceptional_business_order_pool.bo_id_and_job_id.idx",
            "business_order_id",
            "current_job_id",
        ),
        sa.Index("exceptional_business_order_pool.name.idx", "name"),
        sa.Index("exceptional_business_order_pool.creator_name.idx", "creator_name"),
        sa.Index("exceptional_business_order_pool.assignee_name.idx", "assignee_name"),
        sa.Index("exceptional_business_order_pool.updated_at.idx", "updated_at"),
        sa.Index("exceptional_business_order_pool.reason.idx", "reason"),
        sa.Index("exceptional_business_order_pool.current_job_name.idx", "current_job_name"),
        sa.Index(
            "exceptional_business_order_pool.current_job_rpa_name.idx",
            "current_job_rpa_name",
        ),
        sa.Index(
            "exceptional_business_order_pool.org_and_sid_and_ts.idx",
            "org_id",
            "sid",
            "updated_at",
        ),
        sa.Index("exceptional_business_order_pool.current_job_id.idx", "current_job_id"),
    )

    exception_info: Mapped[dict] = mapped_column(sa.JSON, comment="异常信息. 异常原因/操作建议")
    reason: Mapped[str] = mapped_column(sa.String(128), comment="异常原因")

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    created_at: Mapped[datetime.datetime] = mapped_column(sa.TIMESTAMP, nullable=False, server_default=sa.func.now())
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.TIMESTAMP,
        nullable=False,
        server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
    )
    updater: Mapped[dict | None] = mapped_column(sa.JSON, comment="异常池更新人")
    is_processing: Mapped[bool] = mapped_column(sa.Boolean, default=False, comment="是否在处理中")

    org_id: Mapped[int] = mapped_column(sa.Integer, nullable=False, comment="租户id")
    sid: Mapped[str] = mapped_column(sa.String(32), nullable=False, comment="店铺id")
    shop_name: Mapped[str] = mapped_column(sa.String(128), nullable=False, comment="店铺名称")
    shop_platform: Mapped[str] = mapped_column(sa.String(64), nullable=False, comment="店铺平台")

    sys_type: Mapped[int] = mapped_column(
        sa.Integer,
        nullable=False,
        comment="系统类型，表示是否是老系统创建的工单，还是重构后创建的工单。 1: 老系统 2:新系统",
    )
    name: Mapped[str] = mapped_column(sa.String(32), nullable=False, comment="工单名称")
    bo_info: Mapped[exception_pool_type.BoInfo] = mapped_column(sa.JSON, nullable=False, comment="工单信息")

    @synonym_for("bo_info")  # type: ignore[misc]
    @property
    def platform_creator(self) -> exception_pool_type.User | None:
        return self.bo_info.get("platform_creator")

    @synonym_for("bo_info")  # type: ignore[misc]
    @property
    def platform_creator_user_group(self) -> list[exception_pool_type.UserGroup] | None:
        return self.bo_info.get("platform_creator_user_group")

    @synonym_for("bo_info")  # type: ignore[misc]
    @property
    def ly_creator(self) -> exception_pool_type.User | None:
        return self.bo_info.get("ly_creator")

    @synonym_for("bo_info")  # type: ignore[misc]
    @property
    def ly_creator_user_group(self) -> list[exception_pool_type.UserGroup] | None:
        return self.bo_info.get("ly_creator_user_group")

    business_order_id: Mapped[str] = mapped_column(sa.String(32), nullable=False, comment="工单id")
    bo_created_at: Mapped[int | None] = mapped_column(sa.Integer, comment="工单创建时间")
    creator_name: Mapped[str | None] = mapped_column(sa.String(32), comment="工单创建人")

    current_job_info: Mapped[exception_pool_type.JobInfo] = mapped_column(sa.JSON, comment="当前任务信息")

    @synonym_for("current_job_info")  # type: ignore[misc]
    @property
    def platform_assignee(self) -> exception_pool_type.User | None:
        return self.current_job_info.get("platform_assignee")

    @synonym_for("current_job_info")  # type: ignore[misc]
    @property
    def platform_assignee_user_group(
        self,
    ) -> list[exception_pool_type.UserGroup] | None:
        return self.current_job_info.get("platform_assignee_user_group")

    @synonym_for("current_job_info")  # type: ignore[misc]
    @property
    def ly_assignee(self) -> exception_pool_type.User | None:
        return self.current_job_info.get("ly_assignee")

    @synonym_for("current_job_info")  # type: ignore[misc]
    @property
    def ly_assignee_user_group(self) -> list[exception_pool_type.UserGroup] | None:
        return self.current_job_info.get("ly_assignee_user_group")

    current_job_id: Mapped[str] = mapped_column(sa.String(32), comment="当前任务id")
    current_job_name: Mapped[str] = mapped_column(sa.String(32), comment="当前任务名称")
    current_job_rpa_name: Mapped[str | None] = mapped_column(sa.String(32), comment="当前任务rpa名称")

    assignee: Mapped[dict | None] = mapped_column(sa.JSON, comment="当前任务处理人")
    assignee_name: Mapped[str | None] = mapped_column(sa.String(32), comment="当前任务处理人名称")
    task_type: Mapped[str | None] = mapped_column(sa.String(32), comment="任务类型")
    auto_retry_status: Mapped[str | None] = mapped_column(sa.String(32), comment="自动重试状态")

    # 创建人/用户组筛选[不区分平台/乐言账号]
    @hybrid_method
    def creator_by(self, creator_name: str):
        """根据工单创建人进行筛选"""
        if self.platform_creator and self.platform_creator["nick"] == creator_name:
            return True
        if self.ly_creator and self.ly_creator["nick"] == creator_name:
            return True
        return False

    @creator_by.expression
    def creator_by_expression(self, creator_name: str):
        """根据工单创建人进行筛选"""
        return or_(
            self.bo_info["platform_creator"]["nick"] == creator_name,  # type: ignore[index,arg-type]
            self.bo_info["ly_creator"]["nick"] == creator_name,  # type: ignore[index,arg-type]
            self.creator_name == creator_name,  # type: ignore[arg-type]  # 兼容历史数据
        )

    @hybrid_method
    def creator_user_group_by(self, user_group_uuid_list: List[str]):
        """根据工单创建人用户组进行筛选"""
        groups = (self.platform_creator_user_group or []) + (self.ly_creator_user_group or [])
        for group in groups:
            if group["uuid"] in user_group_uuid_list:
                return True
        return False

    @creator_user_group_by.expression
    def creator_user_group_by_expression(self, user_group_uuid_list: List[str]):
        """根据工单创建人用户组进行筛选"""
        return or_(  # type: ignore[arg-type]
            func.json_contains(
                self.bo_info[find_field],  # type: ignore[literal-required] #
                func.json_object("uuid", user_group_uuid),
            )
            for user_group_uuid in user_group_uuid_list
            for find_field in {"platform_creator_user_group", "ly_creator_user_group"}
        )

    # 创建人/用户组筛选[平台账号]
    @classmethod
    def build_platform_creator(cls, bo: "BusinessOrder"):
        """根据工单获取创建人[平台账号]信息"""
        if bo.creator_type != UserType.ASSISTANT:
            # 没有平台账号信息
            return None

        return exception_pool_type.User(
            type=bo.creator_type.name, id=bo.creator_user_id, nick=bo.aid  # type: ignore[typeddict-item]
        )

    @hybrid_method
    def platform_creator_by(self, creator_user_id: str) -> bool:
        """根据工单创建人[平台账号]进行筛选"""
        return self.platform_creator is not None and self.platform_creator["id"] == creator_user_id

    @platform_creator_by.expression  # type: ignore[arg-type]
    def platform_creator_by_expression(self, creator_user_id: int):
        """根据工单创建人[平台账号]进行筛选"""
        return self.bo_info["platform_creator"]["id"] == creator_user_id  # type: ignore[index] #

    @classmethod
    def build_platform_creator_user_group(cls, bo: "BusinessOrder"):
        """根据工单获取创建人用户组[平台账号]信息"""
        if bo.creator_type != UserType.ASSISTANT:
            # 没有平台账号信息
            return None
        if bo.creator_group is None:
            return None  # type: ignore[unreachable]
        return [
            exception_pool_type.UserGroup(
                uuid=user_group["uuid"],
                name=user_group["name"],
                type=user_group["type"],
            )
            for user_group in bo.creator_group
        ]

    @hybrid_method
    def platform_creator_user_group_by(self, user_group_uuid_list: List[str]):
        """根据工单创建人用户组[平台账号]进行筛选"""
        if groups := self.platform_creator_user_group:
            for group in groups:
                if group["uuid"] in user_group_uuid_list:
                    return True
        return False

    @platform_creator_user_group_by.expression
    def platform_creator_user_group_by_expression(self, user_group_uuid_list: List[str]):
        """根据工单创建人用户组[平台账号]进行筛选"""
        return or_(  # type: ignore[arg-type]
            func.json_contains(
                self.bo_info["platform_creator_user_group"],
                func.json_object("uuid", user_group_uuid),
            )
            for user_group_uuid in user_group_uuid_list
        )

    # 乐言账号创建人/用户组
    @classmethod
    def build_ly_creator(cls, bo: "BusinessOrder"):
        """根据工单获取创建人[乐言账号]信息"""
        if not bo.feisuo_creator_user_id:
            return None
        return exception_pool_type.User(
            id=bo.feisuo_creator_user_id, type="LEYAN", nick=bo.feisuo_creator  # type: ignore[typeddict-item]
        )

    @hybrid_method
    def ly_creator_by(self, creator_user_id: int):
        """根据工单创建人[乐言账号]进行筛选"""
        return self.ly_creator is not None and self.ly_creator["id"] == creator_user_id

    @ly_creator_by.expression
    def ly_creator_by_expression(self, creator_user_id: int):
        """根据工单创建人[乐言账号]进行筛选"""
        return self.bo_info["ly_creator"]["id"] == creator_user_id  # type: ignore[index] #

    @classmethod
    def build_ly_creator_user_group(cls, bo: "BusinessOrder"):
        """根据工单获取创建人用户组[乐言账号]信息"""
        if bo.feisuo_creator_group is None:
            return  # type: ignore[unreachable]

        return [
            exception_pool_type.UserGroup(
                uuid=user_group["uuid"],
                name=user_group["name"],
                type=user_group["type"],
            )
            for user_group in bo.feisuo_creator_group
        ]

    @hybrid_method
    def ly_creator_user_group_by(self, user_group_uuid_list: List[str]):
        """根据工单创建人用户组[乐言账号]进行筛选"""
        if groups := self.ly_creator_user_group:
            for group in groups:
                if group["uuid"] in user_group_uuid_list:
                    return True
        return False

    @ly_creator_user_group_by.expression
    def ly_creator_user_group_by_expression(self, user_group_uuid_list: List[str]):
        """根据工单创建人用户组[乐言账号]进行筛选"""
        return or_(  # type: ignore[arg-type]
            func.json_contains(
                self.bo_info["ly_creator_user_group"],
                func.json_object("uuid", user_group_uuid),
            )
            for user_group_uuid in user_group_uuid_list
        )

    # 当前步骤任务执行人/用户组[不区分平台/乐言账号]
    @hybrid_method
    def assignee_by(self, assignee_name: str):
        """根据工单当前步骤执行人进行筛选"""
        if self.platform_assignee is not None and self.platform_assignee["nick"] == assignee_name:
            return True
        if self.ly_assignee is not None and self.ly_assignee["nick"] == assignee_name:
            return True
        return False

    @assignee_by.expression
    def assignee_by_expression(self, assignee_name: str):
        """根据工单当前步骤执行人进行筛选"""
        return or_(
            self.current_job_info["platform_assignee"]["nick"] == assignee_name,  # type: ignore[index,arg-type]
            self.current_job_info["ly_assignee"]["nick"] == assignee_name,  # type: ignore[index,arg-type]
            self.assignee_name == assignee_name,  # type: ignore[arg-type]  # 兼容历史数据
        )

    @hybrid_method
    def assignee_user_group_by(self, user_group_uuid_list: List[str]):
        """根据工单工单当前步骤执行人用户组进行筛选"""
        assignee_groups = (self.platform_assignee_user_group or []) + (self.ly_assignee_user_group or [])
        for group in assignee_groups:
            if group["uuid"] in user_group_uuid_list:
                return True
        return False

    @assignee_user_group_by.expression
    def assignee_user_group_by_expression(self, user_group_uuid_list: List[str]):
        """根据工单工单当前步骤执行人用户组进行筛选"""
        return or_(  # type: ignore[arg-type]
            func.json_contains(
                self.current_job_info[find_field],  # type: ignore[literal-required]
                func.json_object("uuid", user_group_uuid),
            )
            for user_group_uuid in user_group_uuid_list
            for find_field in {"platform_assignee_user_group", "ly_assignee_user_group"}
        )

    # 平台账号当前步骤任务执行人/用户组
    @classmethod
    def build_platform_assignee(cls, job: "Job"):
        """根据工单步骤获取当前步骤处理人[平台账号]信息"""
        if not job.assignee_user_id:
            return
        if job.assignee_type != UserType.ASSISTANT:
            return
        return exception_pool_type.User(
            id=job.assignee_user_id,
            type=job.assignee_type.name,  # type: ignore[typeddict-item]
            nick=job.assignee,  # type: ignore[typeddict-item]
        )

    @hybrid_method
    def platform_assignee_by(self, assignee_user_id: int):
        """根据工单步骤获取当前步骤处理人[平台账号]进行筛选"""
        return self.platform_assignee is not None and self.platform_assignee["id"] == assignee_user_id

    @platform_assignee_by.expression
    def platform_assignee_by_expression(self, assignee_user_id: int):
        """根据工单步骤获取当前步骤处理人[平台账号]进行筛选"""
        return self.current_job_info["platform_assignee"]["id"] == assignee_user_id  # type: ignore[index] #

    @classmethod
    def build_platform_assignee_user_group(cls, job: "Job"):
        """根据工单步骤获取当前步骤处理人用户组[平台账号]信息"""
        if not job.assignee_user_id:
            return
        if job.assignee_type != UserType.ASSISTANT:
            return
        if job.assignee_group is None:
            return  # type: ignore[unreachable]
        return [
            exception_pool_type.UserGroup(
                uuid=user_group["uuid"],
                name=user_group["name"],
                type=user_group["type"],
            )
            for user_group in job.assignee_group
        ]

    @hybrid_method
    def platform_assignee_user_group_by(self, user_group_uuid_list: List[str]):
        """根据工单步骤获取当前步骤处理人用户组[平台账号]进行筛选"""
        groups = self.platform_assignee_user_group or []
        for group in groups:
            if group["uuid"] in user_group_uuid_list:
                return True
        return False

    @platform_assignee_user_group_by.expression
    def platform_assignee_user_group_by_expression(self, user_group_uuid_list: List[str]):
        """根据工单步骤获取当前步骤处理人用户组[平台账号]进行筛选"""
        return or_(  # type: ignore[arg-type]
            func.json_contains(
                self.current_job_info["platform_assignee_user_group"],
                func.json_object("uuid", user_group_uuid),
            )
            for user_group_uuid in user_group_uuid_list
        )

    # 乐言账号当前步骤任务执行人/用户组
    @classmethod
    def build_ly_assignee(cls, job: "Job"):
        """根据工单步骤获取当前步骤处理人[乐言账号]信息"""
        if not job.feisuo_assignee_user_id:
            return
        return exception_pool_type.User(
            id=job.feisuo_assignee_user_id, type="LEYAN", nick=job.feisuo_assignee  # type: ignore[typeddict-item]
        )

    @hybrid_method
    def ly_assignee_by(self, assignee_user_id: int):
        """根据工单步骤获取当前步骤处理人[乐言账号]进行筛选"""
        return self.ly_assignee is not None and self.ly_assignee["id"] == assignee_user_id

    @ly_assignee_by.expression
    def ly_assignee_by_expression(self, assignee_user_id: int):
        """根据工单步骤获取当前步骤处理人[乐言账号]进行筛选"""
        return self.current_job_info["ly_assignee"]["id"] == assignee_user_id  # type: ignore[index] #

    @classmethod
    def build_ly_assignee_user_group(cls, job: "Job"):
        """根据工单步骤获取当前步骤处理人用户组[乐言账号]信息"""
        if job.feisuo_assignee_group is None:
            return  # type: ignore[unreachable]
        return [
            exception_pool_type.UserGroup(
                uuid=user_group["uuid"],
                name=user_group["name"],
                type=user_group["type"],
            )
            for user_group in job.feisuo_assignee_group
        ]

    @hybrid_method
    def ly_assignee_user_group_by(self, user_group_uuid_list: List[str]):
        """根据工单步骤获取当前步骤处理人用户组[乐言账号]进行筛选"""
        groups = self.ly_assignee_user_group or []
        for group in groups:
            if group["uuid"] in user_group_uuid_list:
                return True
        return False

    @ly_assignee_user_group_by.expression
    def ly_assignee_user_group_by_expression(self, user_group_uuid_list: List[str]):
        """根据工单步骤获取当前步骤处理人用户组[乐言账号]进行筛选"""
        return or_(  # type: ignore[arg-type]
            func.json_contains(
                self.current_job_info["ly_assignee_user_group"],
                func.json_object("uuid", user_group_uuid),
            )
            for user_group_uuid in user_group_uuid_list
        )

    @property
    def business_order_brief(self) -> BusinessOrderBrief:
        """
        生成 EXCEPTIONAL_BUSINESS_ORDER Kafka 所需要的 business_order_brief 信息。
        :return:
        """
        if self.bo_info.get("creator"):
            creator = Account(**self.bo_info["creator"])
        else:
            creator = Account()
        current_job_brief = JobBrief(**self.current_job_info)
        exception_info = ExceptionInfo(**(self.exception_info or {}))
        source_type = self.bo_info.get("source_type") or ""
        return BusinessOrderBrief(
            id=str(self.business_order_id),
            name=self.name,
            source_type=str(source_type),
            creator=creator,
            current_job_brief=current_job_brief,
            exception_info=exception_info,
        )

    def send_exception_pool_msg_to_kafka(self, event_type: ExceptionPoolType = ExceptionPoolType.ADD) -> None:
        """
        发送异常池消息到 Kafka。
        :param event_type:      操作类型，ADD 为加入、更新异常，REMOVE 为移出异常。
        :return:
        """
        from loguru import logger

        from robot_processor.ext import exceptional_business_order_producer

        try:
            event_msg = ExceptionPoolEventMsg(
                type=event_type,
                event_time=int(time.time()),
                org_id=int(self.org_id),
                sid=self.sid,
                business_order_brief=self.business_order_brief,
            ).dict()
            logger.info("发送异常工单 {} Type: {} 至 Kafka……".format(self.business_order_id, event_type))
            exceptional_business_order_producer(event_msg)
        except Exception as e:
            logger.error("工单 ID: {} Type: {} 同步 Kafka 发生异常：{}".format(self.business_order_id, event_type, e))

    class Queries:
        @staticmethod
        def by_job(job: "Job"):
            return cast(
                ExceptionBusinessOrder,
                ExceptionBusinessOrder.query.filter(
                    ExceptionBusinessOrder.business_order_id == str(job.business_order_id),
                    ExceptionBusinessOrder.current_job_id == str(job.id),
                ).first(),
            )

        @staticmethod
        def in_processing_by_job(job: "Job"):
            if record := ExceptionBusinessOrder.Queries.by_job(job):
                return record.is_processing
            return False

    class Utils:
        @staticmethod
        @in_transaction()
        def remove(business_order_id):
            ExceptionBusinessOrder.query.filter(
                ExceptionBusinessOrder.business_order_id == str(business_order_id)
            ).delete()

        @staticmethod
        def if_exists(
            business_order_id: int,
            job_id: int,
            exc_info: Optional[List[str]],  # 如果传入需要匹配的错误信息，会过滤匹配错误信息的异常任务记录
        ):
            from robot_processor.business_order.binlog.job_processor import exception_ruler_keeper
            from robot_processor.business_order.models import Job

            cls = ExceptionBusinessOrder

            record = (
                cls.query.filter(cls.business_order_id == str(business_order_id))
                .filter(cls.current_job_id == str(job_id))
                .first()
            )
            if not record:
                return False

            # 需要匹配错误信息
            if exc_info:
                job = Job.query.get(job_id)
                step_type = job.raw_step_type  # type: ignore[union-attr]
                task_type = job.raw_task_info.get("task_type", None)  # type: ignore[union-attr]

                if exception_ruler_keeper._exception_ruler is None:
                    from loguru import logger

                    logger.info("异常规则尚且没有完成初始化，进行一次初始化操作。")
                    # 如果尚且没有完成初始化，则进行一次初始化操作。
                    exception_ruler_keeper.init()

                maybe_rule_ids = [
                    str(
                        exception_ruler_keeper.exception_ruler.process(
                            item, step_type, task_type
                        ).match_result.match_rule.id
                    )
                    for item in exc_info
                ]
                if str((record.exception_info or {}).get("id")) not in maybe_rule_ids:
                    # 未匹配
                    return False

            return True


def _init_exception_pool_operator_registry():
    sql_field_resolver_registry = ExceptionBusinessOrder.sql_field_resolver_registry
    sql_table_field_registry = ExceptionBusinessOrder.sql_table_field_registry

    sql_table_field_registry.register(ExceptionBusinessOrder.__table__)

    # 为 creator 注册 sql field resolver
    class CreatorEqual(FieldResolver):
        def __call__(self, field, value, context, **extra):
            return ExceptionBusinessOrder.creator_by(value["nick"])

    class CreatorEqualAny(FieldResolver):
        def __call__(self, field, value, context, **extra):
            return or_(*[ExceptionBusinessOrder.creator_by(each["nick"]) for each in value])

    sql_field_resolver_registry.register(
        name="creator",
        resolvers={
            OperatorEnum.EQ: CreatorEqual(),
            OperatorEnum.EQ_ANY: CreatorEqualAny(),
        },
    )

    class AssigneeEqual(FieldResolver):
        def __call__(self, field, value, context, **extra):
            return ExceptionBusinessOrder.assignee_by(value["nick"])

    class AssigneeEqualAny(FieldResolver):
        def __call__(self, field, value, context, **extra):
            return or_(*[ExceptionBusinessOrder.assignee_by(each["nick"]) for each in value])

    sql_field_resolver_registry.register(
        name="assignee",
        resolvers={
            OperatorEnum.EQ: AssigneeEqual(),
            OperatorEnum.EQ_ANY: AssigneeEqualAny(),
        },
    )

    # 提供 Form 的 sql field resolver
    class FormEqual(FieldResolver):
        def __call__(self, field, value, context, **extra):
            from robot_processor.business_order.models import BusinessOrder

            return BusinessOrder.form_id == value

    class FormEqualAny(FieldResolver):
        def __call__(self, field, value, context, **extra):
            from robot_processor.business_order.models import BusinessOrder

            return BusinessOrder.form_id.in_(value)

    sql_field_resolver_registry.register(
        name="form_id",
        resolvers={OperatorEnum.EQ: FormEqual(), OperatorEnum.EQ_ANY: FormEqualAny()},
    )

    # 店铺 Shop 支持的操作符
    class ShopEqual(FieldResolver):
        def __call__(self, field, value, context, **extra):
            return ExceptionBusinessOrder.sid == value["sid"]

    class ShopEqualAny(FieldResolver):
        def __call__(self, field, value, context, **extra):
            return ExceptionBusinessOrder.sid.in_([item["sid"] for item in value])

    sql_field_resolver_registry.register(
        name="shop",
        resolvers={OperatorEnum.EQ: ShopEqual(), OperatorEnum.EQ_ANY: ShopEqualAny()},
    )

    # will_retry | 是否自动重试
    class WillRetryIsTrue(FieldResolver):
        def __call__(self, field, value, context, **extra):
            from robot_processor.web_grpc_services.enums import AutoRetryStatus

            return ExceptionBusinessOrder.auto_retry_status == AutoRetryStatus.ENABLED_IN_CYCLE.value

    class WillRetryIsFalse(FieldResolver):
        def __call__(self, field, value, context, **extra):
            from robot_processor.web_grpc_services.enums import AutoRetryStatus

            return ExceptionBusinessOrder.auto_retry_status != AutoRetryStatus.ENABLED_IN_CYCLE.value

    sql_field_resolver_registry.register(
        name="will_retry",
        resolvers={
            OperatorEnum.IS_TRUE: WillRetryIsTrue(),
            OperatorEnum.IS_FALSE: WillRetryIsFalse(),
        },
    )


_init_exception_pool_operator_registry()
