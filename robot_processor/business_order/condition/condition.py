from enum import Enum
from typing import Any
from typing import Literal
from typing import TypedDict

from loguru import logger

from robot_processor.form.models import WidgetRef
from robot_processor.form.widget_condition import Operator
from robot_processor.form.widget_condition import ValueType
from robot_processor.form.widget_condition import check_if_satisfy_condition


class Relation(Enum):
    # 满足全部
    AND = 1
    # 满足任一
    OR = 2

    @classmethod
    def valid_relation(cls):
        return [k for k, v in Relation.__members__.items()]


class BranchType(Enum):
    # 默认分支
    DEFAULT = 1
    # 正常分支
    NORMAL = 2


class ConditionDataDict(TypedDict, total=False):
    # 组件类型
    ref_type: str
    # 数据类型
    value_type: Literal["STRING", "NUMBER", "ENUM", "DATETIME", "DATE", "TIME", "OBJECT"]
    # 运算符
    operator: str
    # 左值
    ref: dict | str
    # 右值
    value: Any


class ConditionDict(TypedDict, total=False):
    data: ConditionDataDict
    # 实际没有用到条件组嵌套条件组，这里的所有判断可以简化 type: Literal["single"]


class ConditionGroupDict(TypedDict, total=False):
    relation: Literal["AND", "OR"]
    condition_list: list[ConditionDict]


class BranchDict(TypedDict, total=False):
    id: str
    name: str
    type: Literal["DEFAULT", "NORMAL"]
    condition_group: ConditionGroupDict


class Executable:
    def exec(self) -> bool:
        raise NotImplementedError


class Condition(Executable):
    def __init__(self, widget_value, value_type: ValueType, operator: Operator, target):
        """

        @param widget_value: 组件的值, 一般是用户的输入
        @param value_type: 数据类型
        @param operator: 比较的方式
        @param target: 目标值, 表单设计的时候就固定了
        """
        self.widget_value = widget_value
        self.value_type = value_type
        self.operator = operator
        self.target = target

    def exec(self) -> bool:
        return check_if_satisfy_condition(
            value_type=self.value_type, operator=self.operator, src=self.widget_value, target=self.target
        )


class ConditionGroup(Executable):
    def __init__(self, relation: Relation, executable_list: list[Executable]):
        self.relation = relation
        self.executable_list = executable_list

    def exec(self) -> bool:
        result = self.relation == Relation.AND
        for executable in self.executable_list:
            exe_re = executable.exec()
            result = (result and exe_re) if self.relation == Relation.AND else (result or exe_re)
        return result

    @classmethod
    def build_condition_group(cls, args, condition_group_data: dict) -> "ConditionGroup":
        relation = Relation[condition_group_data["relation"]]
        exec_list: list[Executable] = []
        for condition in condition_group_data["condition_list"]:
            con_data = condition["data"]
            if condition["type"] == "single":
                operator = Operator[con_data["operator"].upper()]
                widget_value = args.get_widget_ref_value(WidgetRef.parse(con_data["ref"], force_parse_str=True))
                if "value_type" not in con_data:
                    logger.warning(f"condition value_type is not specified, use widget_type instead {con_data}")
                    value_type = ValueType[con_data["ref"]["widget_type"].upper()]
                else:
                    value_type = ValueType[con_data["value_type"].upper()]
                target = con_data.get("value")
                exec_list.append(Condition(widget_value, value_type, operator, target))
            else:
                exec_list.append(ConditionGroup.build_condition_group(args, con_data))
        return ConditionGroup(relation, exec_list)


class Branch:
    def __init__(
        self,
        name: str,
        enable: bool,
        next_step_uuid: str,
        branch_type: str,
        order: int,
        condition_group: ConditionGroup | None = None,
    ):
        self.name = name
        self.condition_group = condition_group
        self.enable = enable
        self.next_step_uuid = next_step_uuid
        self.branch_type = BranchType[branch_type]
        # 排序
        self.order = order

    @classmethod
    def build_branch(cls, args, branch_data: dict):
        """

        @param args: JobArguments
        @param branch_data: 分支的配置数据
        """
        br = Branch(
            branch_data["name"], branch_data["enable"], branch_data["next"], branch_data["type"], branch_data["order"]
        )
        if not br.is_default():
            br.condition_group = ConditionGroup.build_condition_group(args, branch_data["condition_group"])
        return br

    def accept(self) -> bool:
        result = self.is_default() or (self.enable and self.condition_group is not None and self.condition_group.exec())
        logger.debug(f"accept branch {self.info()}")
        return result

    def info(self):
        return {"name": self.name, "next_step_uuid": self.next_step_uuid}

    def is_default(self) -> bool:
        return self.branch_type == BranchType.DEFAULT
