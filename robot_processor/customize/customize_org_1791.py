import time
from functools import lru_cache

import arrow
from loguru import logger

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.business_order.schema import BusinessOrderSchema, CreateAction
from robot_processor.client import get_buyer_nick
from robot_processor.customize.create_bo_by_trade_event import MemoAutoBoCreator
from robot_processor.enums import Creator, FromType
from robot_processor.ext import cache
from robot_processor.form.models import Form
from robot_processor.shop.auth_manager import \
    get_kiosk_shop_by_erp_platform_sid, OrgErpInfo
from robot_processor.shop.models import Shop
from robot_processor.utils import ResultUtil
from rpa.erp.wdtulti import WdtUltiQM, WdtUltiOpenAPIClient, WdtultiOrderModel


def strftime(ts):
    time_struct = time.localtime(ts)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_struct)


def forms_by_sid(sid):
    forms = Form.Queries.by_sids([sid]).all()
    return forms


@lru_cache()
def shop_by_sid(sid):
    return Shop.query.filter_by(sid=sid).first()


@lru_cache()
def wdtulti_shops_by_shop(sid: str):
    return WdtUltiOpenAPIClient(sid).all_shops()


def account_id_from_order(shop: Shop, order: WdtultiOrderModel):
    details = wdtulti_shops_by_shop(shop.sid)
    for wdtulti_shop in details:
        if wdtulti_shop.shop_id == order.shop_id:
            return wdtulti_shop.account_id
    return None


def do_create_bo(shop: Shop, tid: str, trade_no: str):
    forms = forms_by_sid(shop.sid)
    forms = list(filter(lambda form: form.name == '拆单发货-通知发货明细', forms))
    if not forms:
        logger.error(f"在店铺{shop.sid}中找不到工单模版")
        return False
    form = forms[0]
    widgets_info = MemoAutoBoCreator.get_form_first_step_widgets_info(Form.query.get(form.id))
    buyer_nick = '无昵称'
    if shop.platform == 'TAOBAO' or shop.platform == 'TMALL':
        buyer_nick = get_buyer_nick(shop.sid, tid=tid)
    body = BusinessOrderSchema(
        user_nick='飞梭机器人',
        sid=shop.sid,
        form_id=form.id,
        create_action=CreateAction.SUBMIT.value,
        creator_type=Creator.RPA.value,
        data={
            widgets_info.get("买家昵称"): buyer_nick,
            widgets_info.get("订单/子订单"): [{"tid": tid}],
            widgets_info.get("旺店通系统单号"): trade_no
        },
        from_type=FromType.LEYAN,
        uid="飞梭机器人",
    )
    res = BusinessManager.create_customize_business_order(shop.org_id, body)
    if ResultUtil.not_success(res):
        return False
    return True


def org_1791(org_id: int) -> bool:
    shop = Shop.query.filter_by(org_id=org_id).first()
    assert shop
    org_erp_info = OrgErpInfo.query.filter_by(org_id=org_id).filter_by(
        erp_type="wdtulti").first()
    assert org_erp_info
    if org_id == 1791:
        shop = Shop.query.filter_by(sid='516972254').first()
        assert shop
    wdt_ulti_qm = WdtUltiQM(shop.sid)

    now = arrow.now()

    last_ts = cache.get(f"customize_org_{org_id}_ts")
    if not last_ts:
        logger.warning("没有找到上一次的执行记录，从1个小时前开始恢复")
        last_ts = int(now.shift(hours=-1).timestamp())

    now_ts = int(now.timestamp())
    start_ts = last_ts

    while start_ts <= now_ts:
        next_ts = start_ts + 3600 if start_ts + 3600 <= now_ts else now_ts
        start_time = strftime(start_ts)
        end_time = strftime(next_ts)
        page_size = 100
        page_no = 1
        while True:
            try:
                resp = wdt_ulti_qm.get_sent_orders_by_time(page_no, page_size, start_time=start_time, end_time=end_time)
                for order in resp.order:
                    if order.trade_mask >= 2048:
                        if cache.get(f"customize_org_{org_id}_created_trade_no_{order.trade_no}") is None:
                            order_shop: None | Shop = None
                            if org_id == 1791:
                                kiosk_shop = get_kiosk_shop_by_erp_platform_sid(
                                    org_erp_info.id, order.shop_id)
                                if kiosk_shop:
                                    order_shop = Shop.query.filter_by(
                                        sid=kiosk_shop.sid).first()
                            else:
                                account_id = account_id_from_order(shop, order)
                                if not account_id:
                                    logger.warning(f"shop_id: {order.shop_id} 找不到 "
                                                   f"account_id")
                                    continue
                                order_shop = shop_by_sid(account_id)
                            if order_shop:
                                for sub_order in order.detail_list:
                                    orders_resp = wdt_ulti_qm.get_orders(sub_order.src_tid)
                                    if len(orders_resp.order or []) < 2:
                                        logger.info(f"拆单后又合并 src_tid: {sub_order.src_tid}")
                                        continue
                                    success = do_create_bo(order_shop, sub_order.src_tid, order.trade_no)
                                    if not success:
                                        logger.error(f"创建工单失败 trade_no: {order.trade_no} src_tid: {sub_order.src_tid}")
                                cache.set(f"customize_org_{org_id}_created_trade_no_{order.trade_no}", 1,
                                          timeout=86400 * 30)
                            else:
                                logger.warning(f"shop_id: {order.shop_id} "
                                               f"找不到飞梭店铺")
                if page_no * page_size >= resp.total_count:
                    break
            except Exception as e:
                logger.opt(exception=e).error(
                    f"处理时发生异常， 入参: {page_no} {page_size} {start_time} {end_time} 异常: {str(e)}")
            finally:
                page_no = page_no + 1
        start_ts = next_ts + 1

    cache.set(f"customize_org_{org_id}_ts", now_ts, 86400 * 100)
    return True
