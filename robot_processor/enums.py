import typing
from datetime import datetime
from enum import Enum
from enum import IntEnum
from enum import StrEnum
from numbers import Number
from typing import Collection
from typing import List
from typing import Optional
from typing import Set
from typing import Tuple
from typing import Union

from pydantic import BaseModel
from sqlalchemy import and_
from sqlalchemy import or_
from sqlalchemy.sql.operators import between_op
from sqlalchemy.sql.operators import contains_op
from sqlalchemy.sql.operators import in_op


class HasEnumMixin(Enum):
    @classmethod
    def has(cls, items: Union[str, List, Set, Tuple]):
        ch = cls.__members__.keys()
        if isinstance(items, (list, set, tuple)):
            return set(items).issubset(ch)
        return items in ch


class PageSort(Enum):
    """list排序"""

    ASC = 1
    DESC = 2
    # 按照到期时间分类排序: 已到期->临近->没有配置
    TIMEOUT = 3
    CREATEDESC = 4
    CREATEACS = 5
    # 暂时冗余ASC 和 DESC,之后可以去掉 ASC 和 DESC
    UPDATEASC = 6
    UPDATEDESC = 7


class StepType(IntEnum):
    """步骤类型"""

    human = 1
    auto = 2
    exclusive_gateway = 3
    """
    排他网关定义了一组分支的唯一决策，
    所有流出的分支被顺序评估，
    第一个条件被评估为true的分支被执行，
    并不再继续评估下面的分支
    """
    iterate_gw_begin = 4
    """
    Iterate Gateway
    遍历网关的作用是按照指定规则处理数据结构中的每个元素
    重点是处理每个元素，不同的实现方式可能会有不同的顺序
    对于某个元素的'规则'请参考'排他网关'
    遍历网关有开始和结束两个节点，等待所有元素处理完后，汇聚到结束节点，才继续往下流转
    """
    iterate_gw_end = 5
    # 开始步骤
    begin = 6
    # 跳转
    jump = 7
    # 审批节点
    approve = 8
    event = 9


class Creator(IntEnum):
    # 买家
    USER = 1
    # 平台客服
    ASSISTANT = 2
    # N8N自动创建
    RPA = 3
    # 飞梭账号
    LEYAN = 4
    # 从小黑板进入的乐言员工
    LDAP = 5
    # 系统操作
    SYSTEM = 6


UserType = Creator  # alias


class JobStatus(IntEnum):
    # 等待执行(初始状态)
    INIT = 0
    # 等待人工
    PENDING = 1
    # job 执行中
    RUNNING = 2
    # 结束状态
    FAILED = 3
    # 成功状态
    SUCCEED = 4

    if typing.TYPE_CHECKING:
        label: str

    @classmethod
    def check(cls, value):
        try:
            return JobStatus(int(value)) if value else None
        except BaseException:
            return


JobStatus.INIT.label = "初始化"
JobStatus.PENDING.label = "等待中"
JobStatus.RUNNING.label = "处理中"
# alias for export business order status
JobStatus.FAILED.label = "处理中"
JobStatus.SUCCEED.label = "成功"


class BusinessOrderStatus(IntEnum):
    """
    工单实例状态

    与JobStatus是不同的状态体系
    """

    # 等待执行(初始状态)
    INIT = 0
    # job 执行中
    RUNNING = 2
    # @deprecated 结束状态
    FAILED = 3
    # 成功状态
    SUCCEED = 4
    # 关闭状态
    CLOSE = 5
    # 暂停中 客服手动在页面点击暂停
    PAUSED = 7
    # 待处理 当第一个步骤提交之后工单的状态
    PENDING = 6
    # 异常中
    IN_EXCEPTION = 8
    # 待提交
    TO_BO_SUBMITTED = 9
    # 待领取
    TO_BE_COLLECTED = 10

    if typing.TYPE_CHECKING:
        action: str

    @classmethod
    def check(cls, value):
        try:
            return BusinessOrderStatus(int(value)) if value is not None else None
        except BaseException:
            return

    @property
    def label(self):
        return {
            BusinessOrderStatus.INIT: "初始化",
            BusinessOrderStatus.RUNNING: "进行中",
            BusinessOrderStatus.FAILED: "失败",
            BusinessOrderStatus.SUCCEED: "已完成",
            BusinessOrderStatus.CLOSE: "已关闭",
            BusinessOrderStatus.PAUSED: "暂停中",
            BusinessOrderStatus.PENDING: "待受理",
            BusinessOrderStatus.IN_EXCEPTION: "异常中",
            BusinessOrderStatus.TO_BO_SUBMITTED: "待提交",
            BusinessOrderStatus.TO_BE_COLLECTED: "待领取",
        }[self]

    for_widget_string = label

    @classmethod
    def get_unprocessed(cls):
        return [
            cls.RUNNING,
            cls.PENDING,
            cls.PAUSED,
            cls.IN_EXCEPTION,
            cls.TO_BO_SUBMITTED,
        ]


BusinessOrderStatus.CLOSE.action = "close"
BusinessOrderStatus.RUNNING.action = "reopen"


class JobProcessMark(IntEnum):
    """
    job处理标记

    注意：
    只是标记对job做了什么处理（最新记录），与JobStatus不同
    后续可能与"history"搭配使用
    """

    # 未处理
    UNPROCESSED = 0
    # 通过
    ACCEPT = 1
    # 退回
    REJECT = 2
    # 撤回
    RECALL = 3
    # 关闭 bo.status = CLOSE
    CLOSE = 4
    # 重启 bo.status = RUNNING
    REOPEN = 5
    # 保存
    SAVE = 6
    # 更新历史数据
    UPGRADE = 7
    # 分派任务
    ASSIGN = 8
    # 领取任务
    PICK = 9
    # 转交任务
    DELIVER = 10
    # n8n重试任务
    RETRY = 11
    # 暂停
    PAUSE = 12
    # 启用  取消暂停
    UNPAUSE = 13
    # 删除
    DELETE = 14
    # 恢复
    RECOVER = 15
    # 跳过
    SKIP = 16
    # 完成
    FINISH = 17
    # 跳转
    JUMP = 18
    # 审批
    ENDORSE = 19
    # 驳回
    OVERRULE = 20


class JobType(HasEnumMixin, StrEnum):
    # NORMAL = "NORMAL"
    # 改SKU
    CHANGE_SKU = "CHANGE_SKU"
    # 获取物流信息
    QUERY_LOGISTICS = "QUERY_LOGISTICS"
    # 支付宝
    ALIPAY = "ALIPAY"
    # 修改备注
    MEMO = "MEMO"
    # 拦截物流(发叮叮)
    SEND_MESSAGE = "SEND_MESSAGE"
    # 修改收货地址
    CHANGE_ADDRESS = "CHANGE_ADDRESS"
    # [deprecated]补发配件
    # COMPENSATE_ITEM = "COMPENSATE_ITEM"
    # 售后上传 聚水潭API补发 + mola-client进行售后确认和修改地址
    AFTER_SALE_UPLOAD = "AFTER_SALE_UPLOAD"
    # [deprecated]新聚水潭售后上传 mola 创建补发
    # JST_AFTER_SALE_UPLOAD = "JST_AFTER_SALE_UPLOAD"
    # 聚水潭确认售后单（只做确认）
    AFTER_SALE_CONFIRM = "AFTER_SALE_CONFIRM"
    # 拆分版的聚水潭补发，只做补发
    NEW_JST_AFTER_SALE_UPLOAD = "NEW_JST_AFTER_SALE_UPLOAD"
    # 生成补发快递单号
    AFTER_SALE_LOGISTICS = "AFTER_SALE_LOGISTICS"
    # 确认支付状态
    CONFIRM_ALIPAY = "CONFIRM_ALIPAY"
    # N8N
    N8N = "N8N"
    # JOB_EXECUTE_CRON
    JOB_EXECUTE_CRON = "JOB_EXECUTE_CRON"
    # 发送千牛消息
    SEND_QIANNIU = "SEND_QIANNIU"
    # 修改抖音备注
    DOUDIAN_MEMO = "DOUDIAN_MEMO"
    # 智能外呼
    SMARTCALL = "SMARTCALL"
    SMARTCALL_BY_TID = "SMARTCALL_BY_TID"
    # 补全商品信息
    COMPLETE_ITEM = "COMPLETE_ITEM"
    # 订单信息
    TRADE = "TRADE"
    # 平台订单信息
    PLATFORM_TRADE = "PLATFORM_TRADE"
    # 发送QQ群消息
    SEND_QQ_GROUP = "SEND_QQ_GROUP"
    # 发送微信群消息
    SEND_WECHAT_GROUP = "SEND_WECHAT_GROUP"
    # 步骤结束
    JOB_FINISH = "JOB_FINISH"
    # 补全订单中商品信息
    COMPLETE_TRADE_ITEM = "COMPLETE_TRADE_ITEM"
    # 拦截物流状态确认
    CONFIRM_LOGISTICS_CANCEL = "CONFIRM_LOGISTICS_CANCEL"
    # pdd 订单号发消息
    PDD_SEND_MSG_TID = "PDD_SEND_MSG_TID"
    # 抖店铺 订单号发消息
    DOUDIAN_SEND_MSG_TID = "DOUDIAN_SEND_MSG_TID"
    # 新建售后单
    AFTER_SALE_TRADE = "AFTER_SALE_TRADE"
    # [deprecated]订单评论
    # AFTER_SALE_RATE = "AFTER_SALE_RATE"
    # 邮政查件
    EMS_TRACE = "EMS_TRACE"
    # pdd备注
    PDD_MEMO = "PDD_MEMO"
    # pdd备注 API版本
    PDD_MEMO_API = "PDD_MEMO_API"
    # YTO_DING
    YTO_DING = "YTO_DING"
    # 根据订单获取买家昵称
    NICK_BY_TRADE = "NICK_BY_TRADE"
    # 百胜订单信息回执
    BAISHENG_TRADE = "BAISHENG_TRADE"
    # RPA 客户端
    RPA_CLIENT = "RPA_CLIENT"
    # 聚水潭 复制订单
    JST_COPY_ORDER = "JST_COPY_ORDER"
    # 聚水潭订单信息回执
    JST_TRADE = "JST_TRADE"
    # 文本赋值
    ASSIGN_STRING = "ASSIGN_STRING"
    # 聚水潭指定发货仓
    JST_WMS_CO_ID_UPLOAD = "JST_WMS_CO_ID_UPLOAD"
    # 发送企业微信群消息
    SEND_WECOM_GROUP = "SEND_WECOM_GROUP"
    # 定制需求 568661
    REDMINE_568661 = "REDMINE_568661"
    # 定制需求 569481
    REDMINE_569481 = "REDMINE_569481"
    # 定制需求 598678
    REDMINE_598678 = "REDMINE_598678"
    # 旺店通补发
    WDT_AFTER_SALE_UPLOAD = "WDT_AFTER_SALE_UPLOAD"
    # 退货入仓
    CHECK_LID = "CHECK_LID"
    # 快麦订单信息回执
    KUAIMAI_TRADE = "KUAIMAI_TRADE"
    # 快麦订单信息回执输出到当前表单
    KUAIMAI_TRADE_INFO = "KUAIMAI_TRADE_INFO"
    # 旺店通旗舰版补发
    WDTULTI_AFTER_SALE_UPLOAD = "WDTULTI_AFTER_SALE_UPLOAD"
    # 中通 API 相关服务

    # 中通 - 发起拦截
    ZTO_INTERCEPT = "ZTO_INTERCEPT"
    # 中通 - 发起售后
    ZTO_INCIDENT = "ZTO_INCIDENT"
    ZTO_GET_ORDER_INFO = "ZTO_GET_ORDER_INFO"
    # 中通 - 查询售后信息
    ZTO_QUERY_INCIDENT = "ZTO_QUERY_INCIDENT"
    # 中通 - 查询拦截信息
    ZTO_QUERY_INTERCEPT = "ZTO_QUERY_INTERCEPT"
    # 中通 - 取消拦截
    ZTO_CANCEL_INTERCEPT = "ZTO_CANCEL_INTERCEPT"
    # 中通 - RPA 创建售后工单
    ZTO_RPA_CREATE_INCIDENT = "ZTO_RPA_CREATE_INCIDENT"
    # 中通 - RPA 查询售后工单
    ZTO_RPA_QUERY_INCIDENT = "ZTO_RPA_QUERY_INCIDENT"

    # 中通 - 发起撤单
    ZTO_CANCEL_LOGISTIC = "ZTO_CANCEL_LOGISTIC"
    # 中通 - 确认拦截状态
    ZTO_CONFIRM_INTERCEPT = "ZTO_CONFIRM_INTERCEPT"
    # 中通 - 查询物流轨迹
    ZTO_QUERY_LOGISTIC = "ZTO_QUERY_LOGISTIC"

    # EMS 撤单
    EMS_CANCEL_LOGISTIC = "EMS_CANCEL_LOGISTIC"
    # EMS 确认撤单状态
    EMS_CONFIRM_INTERCEPT = "EMS_CONFIRM_INTERCEPT"
    # EMS 修改地址
    EMS_CHANGE_ADDRESS = "EMS_CHANGE_ADDRESS"
    # EMS 查询物流轨迹
    EMS_QUERY_LOGISTIC = "EMS_QUERY_LOGISTIC"

    # 顺丰查询物流轨迹
    SF_QUERY_LOGISTIC = "SF_QUERY_LOGISTIC"

    # JT 撤单
    JT_CANCEL_LOGISTIC = "JT_CANCEL_LOGISTIC"
    # JT 查询物流轨迹
    JT_QUERY_LOGISTIC = "JT_QUERY_LOGISTIC"
    # JT 确认撤单状态
    JT_CONFIRM_INTERCEPT = "JT_CONFIRM_INTERCEPT"

    # 申通下发拦截
    STO_INTERCEPT = "STO_INTERCEPT"
    # 申通下发拦截确认状态
    STO_CONFIRM_INTERCEPT = "STO_CONFIRM_INTERCEPT"
    # 申通查看物流轨迹
    STO_QUERY_TRACE = "STO_QUERY_TRACE"

    # 快麦创建补发
    KUAIMAI_AFTER_SALE_UPLOAD = "KUAIMAI_AFTER_SALE_UPLOAD"
    # 快麦解决补发
    KUAIMAI_CONFIRM_AFTER_SALE = "KUAIMAI_CONFIRM_AFTER_SALE"
    # 快麦查询售后单
    KUAIMAI_QUERY_AFTER_SALE_LIST = "KUAIMAI_QUERY_AFTER_SALE_LIST"
    # 快麦修改卖家备注和旗帜
    KUAIMAI_MODIFY_SELLER_MEMO_AND_FLAG = "KUAIMAI_MODIFY_SELLER_MEMO_AND_FLAG"

    # 旺店通旗舰版订单信息回执
    WDT_ULTIMATE_TRADE = "WDT_ULTIMATE_TRADE"
    # 抖店订单解密
    DOUDIAN_DECRYPT = "DOUDIAN_DECRYPT"
    # 管易云创建换货单
    GYY_CREATE_TRADE_RETURN_ORDER = "GYY_CREATE_TRADE_RETURN_ORDER"
    # 旺店通企业版订单信息回执
    WDT_TRADE = "WDT_TRADE"
    # 旺店通企业版订单信息回执输出当前表单
    WDT_TRADE_INFO = "WDT_TRADE_INFO"
    # 租户1051的定制需求
    CUSTOMIZE_1051_HAS_TRADE_AFTER_ORDER_CREATE = "CUSTOMIZE_1051_HAS_TRADE_AFTER_ORDER_CREATE"
    # 淘宝订单回执
    TAOBAO_TRADE = "TAOBAO_TRADE"
    # 淘宝开启退款通道
    TAOBAO_REFUND = "TAOBAO_REFUND"
    # 淘宝退定金
    DEPOSIT_REFUND = "DEPOSIT_REFUND"
    # 管易云查询换货单
    GYY_GET_RETURN_ORDERS = "GYY_GET_RETURN_ORDERS"
    # 修改聚水潭卖家备注
    JST_REMARK_UPLOAD = "JST_REMARK_UPLOAD"
    # 修改聚水潭线下备注
    JST_SET_NODE = "JST_SET_NODE"
    # 聚水潭订单信息回执[输出表单]
    JST_TRADE_INFO = "JST_TRADE_INFO"
    # 飞书发消息
    SEND_FEISHU_GROUP_WITH_WEBHOOK = "SEND_FEISHU_GROUP_WITH_WEBHOOK"
    # 淘宝获取订单中的买家地址
    TAOBAO_RECEIVER_ADDRESS_BY_TID = "TAOBAO_RECEIVER_ADDRESS_BY_TID"
    # 定制 597874
    REDMINE_597874 = "REDMINE_597874"
    # 定制雷度快递单号转换
    # https://www.teambition.com/task/655c8cb838b5a3a6e429fd5d
    CONVERTER_JST_LOGISTICS_INFO_COPIED_TO_STRING = "CONVERTER_JST_LOGISTICS_INFO_COPIED_TO_STRING"
    # 订单信息回执[拼多多API]
    PDD_TRADE_INFO = "PDD_TRADE_INFO"
    # 基于万里牛售后单下单[RPA]
    WLN_CREATE_EXCHANGE_RPA = "WLN_CREATE_EXCHANGE_RPA"
    # 抖店备注[API]
    DOUDIAN_MEMO_API = "DOUDIAN_MEMO_API"
    # 聚水潭修改发货仓
    JST_MODIFY_WMS = "JST_MODIFY_WMS"
    # 合并若干张图片至一张图片
    MERGE_IMAGES = "MERGE_IMAGES"
    # 旺店通旗舰版订单信息回执输出当前表单
    WDTULTI_TRADE_INFO = "WDTULTI_TRADE_INFO"
    # 快麦查询商品供应商
    KUAIMAI_SUPPLIER = "KUAIMAI_SUPPLIER"
    # 旺店通旗舰版修改客服备注
    WDTULTI_CS_REMARK = "WDTULTI_CS_REMARK"
    # 订单信息回执[抖店API]
    DOUDIAN_TRADE_INFO = "DOUDIAN_TRADE_INFO"
    # 售后信息回执[抖店API]
    DOUDIAN_REFUND_INFO = "DOUDIAN_REFUND_INFO"
    # 聚水潭换货单
    JST_AFTER_SALE_EXCHANGE = "JST_AFTER_SALE_EXCHANGE"
    # 抖店订单解密[API]
    DOUDIAN_DECRYPT_API = "DOUDIAN_DECRYPT_API"
    # 抖店订单解密[API][地址组件]
    DOUDIAN_DECRYPT_ADDRESS_API = "DOUDIAN_DECRYPT_ADDRESS_API"
    # 退款信息回执
    TAOBAO_REFUND_INFO = "TAOBAO_REFUND_INFO"
    # 退款信息回执[拼多多]
    PDD_REFUND_INFO = "PDD_REFUND_INFO"
    # 聚水潭售后订单列表查询
    JST_AFTER_SALE_LIST = "JST_AFTER_SALE_LIST"
    # 聚水潭更新售后单信息
    JST_AFTER_SALE_UPDATE = "JST_AFTER_SALE_UPDATE"
    # 聚水潭维修单信息
    JST_REPAIR_INFO = "JST_REPAIR_INFO"
    # 聚水潭创建维修单
    JST_AFTER_SALE_REPAIR = "JST_AFTER_SALE_REPAIR"
    # 聚水潭拒收退货单
    JST_AFTER_SALE_RETURN_REFUSE = "JST_AFTER_SALE_RETURN_REFUSE"
    # 淘宝同意退款
    TAOBAO_REFUNDS_AGREE = "TAOBAO_REFUND_AGREE"
    # 拼多多客户端小额打款
    PDD_CLIENT_PAY = "PDD_CLIENT_PAY"
    # 是否是订单级的全部退款(淘宝)
    TAOBAO_IS_FULL_REFUND_BY_TID = "TAOBAO_IS_FULL_REFUND_BY_TID"
    # 聚水潭改标签
    JST_CHANGE_LABEL = "JST_CHANGE_LABEL"
    # 万里牛api创建售后单
    WLN_AFTERSALE_UPLOAD = "WLN_AFTERSALE_UPLOAD"
    # 万里牛 API 查询售后单详情
    WLN_AFTERSALE_RETURN_ORDER = "WLN_AFTERSALE_RETURN_ORDER"
    # 万里牛订单信息回执
    WLN_TRADE = "WLN_TRADE"
    # 万里牛修改订单备注
    WLN_MODIFY_TRADE_REMARK = "WLN_MODIFY_TRADE_REMARK"
    # 修改淘宝未发货订单的SKU
    TAOBAO_CHANGE_SKU = "TAOBAO_CHANGE_SKU"
    # 修改淘宝SKU的库存
    TAOBAO_SKU_QUANTITY_UPDATE = "TAOBAO_SKU_QUANTITY_UPDATE"
    # 旺店通旗舰版销售出库单查询
    WDTULTI_STOCKOUT = "WDTULTI_STOCKOUT"
    # 发送短信
    SMS_SEND = "SMS_SEND"
    # 获取发送短信结果
    SMS_SEND_RESULT = "SMS_SEND_RESULT"
    # 旺店通旗舰版换货
    WDTULTI_AFTER_SALE_EXCHANGE = "WDTULTI_AFTER_SALE_EXCHANGE"
    # Mola 上传发票
    MOLA_UPLOAD_INVOICE = "MOLA_UPLOAD_INVOICE"
    # 旺店通旗舰版查询发票
    WDTULTI_GET_INVOICE = "WDTULTI_GET_INVOICE"
    # 旺店通企业版根据平台退换单号获取退换单
    WDT_REFUND_QUERY_BY_SRC_REFUND_NO = "WDT_REFUND_QUERY_BY_SRC_REFUND_NO"
    # 旺店通企业版根据ERP退换单号获取退货入库单
    WDT_STOCKIN_ORDER_QUERY_REFUND_BY_REFUND_NO = "WDT_STOCKIN_ORDER_QUERY_REFUND_BY_REFUND_NO"
    # 淘宝基于OAID发送短信
    TAOBAO_SMS_SEND_BY_OAID = "TAOBAO_SMS_SEND_BY_OAID"
    # 聚水潭创建线下店铺订单
    JST_ORDER_UPLOAD = "JST_ORDER_UPLOAD"
    # 圆通发起拦截
    YTO_INTERCEPT_REPORT = "YTO_INTERCEPT_REPORT"
    # 圆通确认拦截结果
    YTO_INTERCEPT_CONFIRM = "YTO_INTERCEPT_CONFIRM"
    # 淘宝买家已支付未完结订单数量
    TAOBAO_GET_PAID_TRADES_NUMBER = "TAOBAO_GET_PAID_TRADES_NUMBER"
    # 淘宝订单多包裹回传
    TAOBAO_ADD_MULTI_PACKAGE = "TAOBAO_ADD_MULTI_PACKAGE"
    # 拼多多订单多包裹回传
    PDD_ADD_MULTI_PACKAGE = "PDD_ADD_MULTI_PACKAGE"
    # 吉客云定制补发
    JACKYUN_ORG_1935_AFTERSALE_UPLOAD = "JACKYUN_ORG_1935_AFTERSALE_UPLOAD"
    # 圆通查看物流轨迹
    YTO_QUERY_TRACE = "YTO_QUERY_TRACE"
    # 旺店通企业版根据ERP退换单号获取退货入库单输出当前表单
    WDT_STOCKIN_ORDER_QUERY_REFUND_INFO_BY_REFUND_NO = "WDT_STOCKIN_ORDER_QUERY_REFUND_INFO_BY_REFUND_NO"
    # 获取当前时间
    NOW = "NOW"
    # 日期时间转换
    DATETIME_CONVERT = "DATETIME_CONVERT"
    # 韵达查看物流轨迹
    YD_QUERY_TRACE = "YD_QUERY_TRACE"
    # 韵达发起拦截
    YD_INTERCEPT_REPORT = "YD_INTERCEPT_REPORT"
    # 韵达确认拦截结果
    YD_CONFIRM_INTERCEPT = "YD_CONFIRM_INTERCEPT"
    # 淘宝通过订单号查询退款信息
    TAOBAO_REFUND_INFO_BY_TID = "TAOBAO_REFUND_INFO_BY_TID"
    # 复制工单实例
    COPY_BUSINESS_ORDER = "COPY_BUSINESS_ORDER"
    # 数值计算-除法
    NUMBER_DIVIDE = "NUMBER_DIVIDE"
    # 将复合组件转化为文本格式
    CONVERTER_TABLE_TO_STRING = "CONVERTER_TABLE_TO_STRING"
    # 获取复合组件的行数
    GET_TABLE_LENGTH = "GET_TABLE_LENGTH"
    # 旺店通企业版根据退款物流单号获取飞梭店铺ID
    WDT_GET_SHOP_ID_BY_LOGISTICS_NO = "WDT_GET_SHOP_ID_BY_LOGISTICS_NO"
    # 获取物流轨迹(收费)
    ALIYUN_LOGISTICS_TRACE = "ALIYUN_LOGISTICS_TRACE"
    # 抖店仅退款同意退款
    DOUDIAN_REFUND_ONLY_AGREE = "DOUDIAN_REFUND_ONLY_AGREE"
    # 抖店退货退款同意退款
    DOUDIAN_REFUND_WITH_RETURNS_AGREE = "DOUDIAN_REFUND_WITH_RETURNS_AGREE"
    # 单行文本转换为订单
    STRING_TO_TID = "STRING_TO_TID"
    # 旺店通企业版根据退回物流单号获取退换单
    WDT_REFUND_QUERY_BY_LOGISTICS_NO = "WDT_REFUND_QUERY_BY_LOGISTICS_NO"
    # 管易云订单回执
    GYY_TRADE = "GYY_TRADE"
    # 管易云新增退货单
    GYY_TRADE_RETURN_ADD = "GYY_TRADE_RETURN_ADD"
    # 管易云查询退货单
    GYY_TRADE_RETURN_GET = "GYY_TRADE_RETURN_GET"
    # 管易云审核退换货单
    GYY_TRADE_RETURN_APPROVE = "GYY_TRADE_RETURN_APPROVE"
    # 吉客云补发
    JACKYUN_AFTERSALE_UPLOAD = "JACKYUN_AFTERSALE_UPLOAD"
    # 吉客云订单回执
    JACKYUN_TRADE = "JACKYUN_TRADE"
    # 拼多多仅退款同意退款
    PDD_REFUND_ONLY_AGREE = "PDD_REFUND_ONLY_AGREE"
    # 聚水潭售后单查询
    JST_REFUND = "JST_REFUND"
    # 旺店通旗舰版根据物流单号获取退换单
    WDTULTI_REFUND_QUERY_BY_LOGISTICS_NO = "WDTULTI_REFUND_QUERY_BY_LOGISTICS_NO"
    # 旺店通旗舰版根据退换单号获取退货入库单
    WDTULTI_STOCKIN_REFUND_QUERY_BY_REFUND_NO = "WDTULTI_STOCKIN_REFUND_QUERY_BY_REFUND_NO"
    # 聚水潭根据内部单号获取出库单
    JST_OUT_ORDER_BY_O_ID = "JST_OUT_ORDER_BY_O_ID"
    # 拼多多退货退款同意退款
    PDD_REFUND_WITH_RETURN_AGREE = "PDD_REFUND_WITH_RETURN_AGREE"
    # 发送邮件
    SEND_EMAIL = "SEND_EMAIL"
    # 京东物流下发拦截
    JDL_INTERCEPT = "JDL_INTERCEPT"
    # 京东物流下发拦截确认状态
    JDL_CONFIRM_INTERCEPT = "JDL_CONFIRM_INTERCEPT"
    # 京东物流查看物流轨迹
    JDL_QUERY_TRACE = "JDL_QUERY_TRACE"
    # 组件值复制
    COPY_WIDGET_VALUE = "COPY_WIDGET_VALUE"
    # 雅帝森定制(供应商通知)
    CUSTOMIZE_ORG_254 = "CUSTOMIZE_ORG_254"
    # 雅帝森定制(生成图片)
    CUSTOMIZE_ORG_254_1 = "CUSTOMIZE_ORG_254_1"
    # 拼多多订单解密
    PDD_DECRYPT = "PDD_DECRYPT"
    # 拼多多地址解密[地址组件]
    PDD_DECRYPT_ADDRESS = "PDD_DECRYPT_ADDRESS"
    # 通过 dict 的数据映射
    DICT_MATCH = "DICT_MATCH"
    # 获取列表第一个元素
    FIRST_ITEM = "FIRST_ITEM"
    # 京东获取订单备注
    JD_GET_TRADE_MEMO = "JD_GET_TRADE_MEMO"
    # 京东订单备注
    JD_MEMO = "JD_MEMO"
    # 顺丰下发拦截
    SF_INTERCEPT = "SF_INTERCEPT"
    # 判断一个元素在不在列表中
    ITEM_EXISTS = "EXISTS_ITEM"
    # 淘宝订单解密API
    TAOBAO_DECRYPT_API = "TAOBAO_DECRYPT_API"
    # 聚水潭供应商(聚水潭新授权使用)
    JST_SUPPLIER = "JST_SUPPLIER"
    # 旺店通旗舰版订单日志
    WDTULTI_TRADE_LOG = "WDTULTI_TRADE_LOG"
    # 旺店通企业版订单日志
    WDT_TRADE_LOG = "WDT_TRADE_LOG"
    # 数值比较-相等
    NUMBER_EQUAL = "NUMBER_EQUAL"
    # 数值计算-加常量
    NUMBER_ADD_CONST = "NUMBER_ADD_CONST"
    # 百胜确认订单
    BAISHENG_ORDER_QR = "BAISHENG_ORDER_QR"
    # 数值初始化为0
    NUMBER_INIT = "NUMBER_INIT"
    # 获取订单中的原单补发商品
    AFTER_SALE_ITEMS_BY_TID = "AFTER_SALE_ITEMS_BY_TID"
    # 创建工单
    CREATE_BUSINESS_ORDER = "CREATE_BUSINESS_ORDER"
    # 更新工单
    UPDATE_BUSINESS_ORDER = "UPDATE_BUSINESS_ORDER"
    # 旺店通旗舰版退货物流包裹查询
    WDTULTI_STOCKIN_REFUND_RETURN_LOGISTICS_PACKAGE_QUERY = "WDTULTI_STOCKIN_REFUND_RETURN_LOGISTICS_PACKAGE_QUERY"
    # 旺店通企业版退货物流包裹查询
    WDT_STOCKIN_REFUND_LOGISITCS_QUERY = "WDT_STOCKIN_REFUND_LOGISITCS_QUERY"
    # 旺店通企业版创建退货入库单
    WDT_STOCKIN_REFUND_PUSH = "WDT_STOCKIN_REFUND_PUSH"
    # 修改快手备注
    KUAISHOU_MEMO = "KUAISHOU_MEMO"
    # 快手同意退款
    KUAISHOU_REFUND_AGREE = "KUAISHOU_REFUND_AGREE"
    # 更新飞梭工单备注
    BUSINESS_ORDER_MEMO = "BUSINESS_ORDER_MEMO"
    # 百胜E3获取退单列表
    BAISHENG_RETURN_LIST_GET = "BAISHENG_RETURN_LIST_GET"
    # 容器构造函数
    COLLECTION_CONSTRUCTOR = "COLLECTION_CONSTRUCTOR"
    # 将一个组件包装为列表容器
    LIST_OF = "LIST_OF"
    # 鲁班到家下单
    LBDJ_SAVE_ORDER = "LBDJ_SAVE_ORDER"
    # 数值计算
    NUMBER_CALCULATOR = "NUMBER_CALCULATOR"
    # 查询工单数据
    QUERY_BUSINESS_ORDER = "QUERY_BUSINESS_ORDER"
    # 京东小额打款
    JD_INDEMNITY_CREATE = "JD_INDEMNITY_CREATE"
    # 快手同意退款
    KUAISHOU_REFUND_CONFIRM_RECEIPT = "KUAISHOU_REFUND_CONFIRM_RECEIPT"
    # 吉客云更新退换补发单物流信息
    JACKYUN_RETURN_CHANGE_UPDATE = "JACKYUN_RETURN_CHANGE_UPDATE"
    # 获取淘宝商品类目
    TAOBAO_GET_CATEGORY_BY_SPU_ID = "TAOBAO_GET_CATEGORY_BY_SPU_ID"
    # 订单备注查询
    QUERY_LATEST_SELLER_MEMO = "QUERY_LATEST_SELLER_MEMO"
    # 百胜订单备注
    BAISHENG_SELLER_MEMO = "BAISHENG_SELLER_MEMO"
    # 吉客云更新退换补发单自定义字段
    JACKYUN_RETURN_CHANGE_CUSTOM_FIELDS_UPDATE = "JACKYUN_RETURN_CHANGE_CUSTOM_FIELDS_UPDATE"
    # 淘宝未发货改地址
    TAOBAO_CHANGE_ADDRESS = "TAOBAO_CHANGE_ADDRESS"
    # 拼多多未发货改地址
    PDD_CHANGE_ADDRESS = "PDD_CHANGE_ADDRESS"
    # 获取淘宝买家备注
    TAOBAO_BUYER_MESSAGE = "TAOBAO_BUYER_MESSAGE"
    # 小红书订单备注
    XHS_MEMO = "XHS_MEMO"
    # 抖店仅退款拒绝退款
    DOUDIAN_REFUND_ONLY_REFUSE = "DOUDIAN_REFUND_ONLY_REFUSE"
    # 抖店退货退款拒绝退款
    DOUDIAN_REFUND_WITH_RETURNS_REFUSE = "DOUDIAN_REFUND_WITH_RETURNS_REFUSE"
    # 吉客云订单更新物流信息和客服备注
    JACKYUN_UPDATE_LOGISTICS_INFO = "JACKYUN_UPDATE_LOGISTICS_INFO"
    # 吉客云创建退货单
    JACKYUN_AFTERSALE_RETURN_UPLOAD = "JACKYUN_AFTERSALE_RETURN_UPLOAD"
    # 代码执行
    PYTHON_SCRIPT = "PYTHON_SCRIPT"
    # 京东服务下单
    JDL_SERVICE_CREATE_ORDER = "JDL_SERVICE_CREATE_ORDER"


class AuthType(StrEnum):
    # ERP
    JST = "JST"
    WDT = "WDT"
    WDTULTI = "WDTULTI"
    WANLINIU = "WANLINIU"
    KUAIMAI = "KUAIMAI"
    BAISHENG = "BAISHENG"
    GUANYIYUN = "GUANYIYUN"
    WDGJ = "WDGJ"
    JACKYUN = "JACKYUN"

    CHATBOT = "CHATBOT"
    INSPECT = "INSPECT"
    DOUYIN_ATX = "DouYin-ATX"
    FS_TB = "FS-TB"
    ALIPAY = "ALIPAY"
    FS_RPA = "FS-RPA"
    PDD_YDD = "PDD-YDD"
    PDD_ATX = "PDD-ATX"
    KS = "KS"
    # 快手-乐分析
    KS_LFX = "KS-LFX"
    # 飞鸽，也就是新柒加
    PIGEON = "PIGEON"
    DOUYIN_XYZ = "DOUYIN_XYZ"
    # 京东言准
    JD_YZ = "JD_YZ"
    JD_FS = "JD_FS"
    XHS_ERP = "XHS_ERP"

    # 物流
    # EMS
    EMS = "EMS"
    # 中通
    ZTO = "ZTO"
    # 京东
    JDL = "JDL"
    # 圆通
    YTO = "YTO"
    # 韵达
    YUNDAEX = "YUNDAEX"
    # 顺丰实际上只要绑定月结卡，就都可以使用了，并不需要绑定到各个店铺上。
    # 顺丰
    SF = "SF"
    # 极兔和申通实际上并不需要在飞梭方面授权，都是商家和网店方面操作的。
    # 极兔
    JT = "JT"
    # 申通
    STO = "STO"

    @classmethod
    def get_editable_auth_type_list(cls):
        """支持通过接口进行维护的授权信息列表"""
        return [
            AuthType.WDT,
            AuthType.WDTULTI,
            AuthType.WANLINIU,
            AuthType.KUAIMAI,
            AuthType.GUANYIYUN,
            AuthType.WDGJ,
            AuthType.JACKYUN,
            AuthType.BAISHENG,
            AuthType.FS_RPA,
            AuthType.KS_LFX,
            AuthType.JD_YZ,
            AuthType.JD_FS,
            AuthType.EMS,
            AuthType.ZTO,
            AuthType.JDL,
            AuthType.YTO,
            AuthType.YUNDAEX,
            AuthType.SF,
            AuthType.PIGEON,
            AuthType.DOUYIN_XYZ,
            AuthType.XHS_ERP,
        ]

    @classmethod
    def has_unique_limit(cls, auth_type):
        return auth_type in [AuthType.FS_RPA]

    @classmethod
    def need_check_order(cls, auth_type):
        return auth_type in (AuthType.KS_LFX, AuthType.JD_YZ)


class ErpType(Enum):
    # 聚水潭
    JST = 1

    # 旺店通
    WDT = 2

    # 旺店通旗舰版
    WDTULTI = 3

    # 快麦
    KUAIMAI = 4

    #  百胜
    BAISHENG = 5

    # 万里牛
    WANLINIU = 6

    # 多鸿
    DUOHONG = 7

    # 白手套
    BAISHOUTAO = 8

    # 金蝶管易云
    GUANYIYUN = 9

    # 吉客云
    JACKYUN = 10

    # 网店管家
    WDGJ = 11

    # 旺店通极速版
    WDT_LIGHT = 12

    @property
    def label(self):
        return {
            ErpType.JST: "聚水潭",
            ErpType.WDT: "旺店通",
            ErpType.WDTULTI: "旺店通旗舰版",
            ErpType.KUAIMAI: "快麦",
            ErpType.BAISHENG: "百胜",
            ErpType.WANLINIU: "万里牛",
            ErpType.DUOHONG: "多鸿",
            ErpType.BAISHOUTAO: "白手套",
            ErpType.GUANYIYUN: "管易云",
            ErpType.JACKYUN: "吉客云",
            ErpType.WDGJ: "网店管家",
            ErpType.WDT_LIGHT: "旺店通极速版",
        }[self]


class FromType(IntEnum):
    # 创建来源
    ASSISTANT = 1
    BUYER = 2
    LEYAN = 4
    USER_TASK = 5
    KAFKA_TRADE_EVENT = 6


class PaymentStatus(IntEnum):
    # 2，3，5 都在待支付列表中
    # 待审批
    WAIT_APPROVAL = 1
    # 待支付
    WAIT_PAY = 2
    # 支付中
    PAYING = 3
    # 支付完成
    PAY_FINISH = 4
    # 支付失败
    PAY_FAILED = 5
    # 已关闭
    CLOSED = 10
    # 已驳回（不在任何列表中）,即最初状态
    INIT = 11

    def can_recall(self):
        return self in (
            PaymentStatus.WAIT_APPROVAL,
            PaymentStatus.WAIT_PAY,
            PaymentStatus.PAY_FAILED,
        )

    def can_close(self):
        return self not in (PaymentStatus.PAYING, PaymentStatus.PAY_FINISH)

    def can_skip(self):
        return self.can_close()


class PaymentMethod(Enum):
    # 绑定淘宝昵称
    TB_BIND_NICK = 1

    # 真实姓名和支付宝账号
    ALIPAY_ACCOUNT = 2

    # 根据淘宝订单号打款
    TB_ORDER_NO = 3


class WidgetCategory(Enum):
    ONLINE_RETAIL = 1
    BASIC = 2
    ENHANCE = 3
    SYSTEM = 4

    if typing.TYPE_CHECKING:
        label: str


WidgetCategory.ONLINE_RETAIL.label = "电商组件"
WidgetCategory.BASIC.label = "基础组件"
WidgetCategory.ENHANCE.label = "增强组件"
WidgetCategory.SYSTEM.label = "系统组件"


class DataType(IntEnum):
    # 自动获取
    AUTO = 1
    # 选择组件
    SELECT = 2
    # 当前输入
    INPUT = 3
    # 当前输入-下拉选项
    INPUT_SELECT = 4
    # 富文本
    INPUT_TEXTAREA = 5
    # INPUT_SELECT_EXTRA
    INPUT_SELECT_EXTRA = 6
    # 样式：下拉多选
    INPUT_MULTI_SELECT_DROPDOWN = 7
    # 样式：下拉单选带系统字段（fixed concept）
    SELECT_WITH_FIXED = 8
    # 规则设置组件
    RULES = 9
    # 消息发送规则设置组件
    MSG_RULES = 10

    CRON = 12
    # value 格式的富文本
    TEXT_TEMPLATE = 13

    @classmethod
    def is_input(cls, value: int):
        return value in [
            cls.INPUT_SELECT.value,
            cls.INPUT.value,
            cls.INPUT_TEXTAREA.value,
            cls.INPUT_SELECT_EXTRA.value,
            cls.INPUT_MULTI_SELECT_DROPDOWN.value,
            cls.RULES.value,
            cls.MSG_RULES.value,
            cls.CRON.value,
        ]


class UpdateMemoType(Enum):
    # 追加
    APPEND = 0
    # 覆盖 - 新备注优先
    COVER_NEW_PRIOR = 1
    # 覆盖 - 原备注优先
    COVER_OLD_PRIOR = 2
    # 覆盖
    OVERWRITE = 3


class FormCategory(Enum):
    # 打款类型
    ALIPAY = 1
    BUYER_TABLE = 2


class FormMold(IntEnum):
    # 自定义
    CUSTOM = 1
    # 买家工单(此类工单在步骤的配置上有不同的区别)
    BUYER = 2
    # 智能工单
    EVENT = 3


class AssistantOnlineStatusType(Enum):
    ONLINE = 1
    OFFLINE = 0


class SelectType(IntEnum):
    # 全部帐号
    all = 1
    # 指定帐号
    part = 2


class AssigneeRule(IntEnum):
    """执行客服分派类型"""

    # 手动
    MANUAL = 1
    # 随机
    RANDOM = 2
    # 自定义
    CUSTOMIZE = 3
    # 在线分配
    ONLINE = 4
    # 自由领取
    FREE_PICK = 5

    @classmethod
    def is_auto_assign_rule(cls, value):
        return cls(value) in (cls.RANDOM, cls.ONLINE, cls.FREE_PICK)


class TimeoutEnum(Enum):
    UNSET = 0
    UN_TIMEOUT = 1
    TIMEOUT = 2


class TradeStatus(IntEnum):
    DEFAULT_TRADE_STATUS = 0
    PAY_PENDING = 1
    SELLER_CONSIGNED_PART = 2
    TRADE_BUYER_SIGNED = 3
    TRADE_CLOSED = 4
    TRADE_CLOSED_BY_TAOBAO = 5
    TRADE_FINISHED = 6
    TRADE_NO_CREATE_PAY = 7
    WAIT_BUYER_CONFIRM_GOODS = 8
    WAIT_BUYER_PAY = 9
    WAIT_PRE_AUTH_CONFIRM = 10
    WAIT_SELLER_SEND_GOODS = 11
    ALL_WAIT_PAY = 12
    ALL_CLOSED = 13
    PAID_FORBID_CONSIGN = 14
    UNRECOGNIZED = -1


class ProductCode(StrEnum):
    FS_001 = "FS_001"
    FS_YOUTH_001 = "FS_YOUTH_001"

    if typing.TYPE_CHECKING:
        codes: typing.Set[int]


ProductCode.FS_001.codes = {1, 2, 3, 4}
ProductCode.FS_YOUTH_001.codes = {2, 3, 4}


class ChannelType(Enum):
    TAOBAO = 0
    TMALL = 1
    DOUDIAN = 2
    JD = 3
    PDD = 4
    KUAISHOU = 5
    BAISHENG = 6
    MOGUJIE = 7
    SUNING = 8
    XIAOHONGSHU = 9
    WEIMOB = 10
    YOUZAN = 11
    VIPSHOP = 12
    KAOLA = 13


class JobAssignAction(StrEnum):
    PICK = "PICK"  # 任务池自由领取
    DELIVER = "DELIVER"  # 任务owner转交
    ASSIGN = "ASSIGN"  # admin指派  暂不支持


class PageType(Enum):
    REPORTS = 0
    TASK_CENTER = 1
    EXCEPTION_POOL = 2
    TRANSFER_INFO = 3


class NodeType(StrEnum):
    ALL_TASK = "ALL_TASK"
    ERROR_TASK = "ERROR_TASK"
    PRODUCT_CENTER = "PRODUCT_CENTER"
    DATA_STATISTIC_ALL = "DATA_STATISTIC_ALL"
    PAY_ALL = "PAY_ALL"
    PAY_CHECK = "PAY_CHECK"
    PAY_TO_PAY = "PAY_TO_PAY"
    PAY_PAID = "PAY_PAID"
    PAY_CLOSE = "PAY_CLOSE"


class NoticeCategory(StrEnum):
    SYSTEM = "SYSTEM"  # 系统通知
    BUSINESS_ORDER = "BUSINESS_ORDER"  # 工单相关
    EXCEPTION_TASK = "EXCEPTION_TASK"  # 异常任务


class NoticeChannel(StrEnum):
    WORKBENCH = "WORKBENCH"  # 网页工单
    TASK_CENTER = "TASK_CENTER"  # 任务中心
    MINI_APP = "MINI_APP"  # 侧边栏


class NoticeScope(StrEnum):
    ALL = "ALL"  # 全网发布
    PLATFORM = "PLATFORM"  # 指定平台
    ORG = "ORG"  # 指定租户


class NoticeStatus(StrEnum):
    RELEASED = "RELEASED"  # 已发布
    SAVED = "SAVED"  # 已保存
    IN_REVIEW = "IN_REVIEW"  # 审核中
    DELETED = "DELETED"  # 已删除


class DisplayStyle(StrEnum):
    PLATFORM_NOTICE = "PLATFORM_NOTICE"  # 平台公告
    VERSION_UPGRADE = "VERSION_UPGRADE"  # 版本更新


class NoticeAction(StrEnum):
    RELEASE = "RELEASE"  # 发布
    REJECT = "REJECT"  # 退回
    DELETE = "DELETE"  # 删除


class EnumMixin(Enum):
    @classmethod
    def has(cls, t):
        return isinstance(t, cls) or t in cls.__members__ or t in cls._value2member_map_

    @classmethod
    def get(cls, t):
        if not cls.has(t):
            raise ValueError(f"不支持的枚举类型{t} of {cls}")
        return t if isinstance(t, cls) else cls.__members__.get(t) or cls._value2member_map_.get(t)


class ColumnType(EnumMixin):
    NUMBER = Number
    STR = str
    DATE = datetime
    LIST = list

    if typing.TYPE_CHECKING:
        implement_types: typing.Tuple

    def verifier(self, t):
        return issubclass(t, self.implement_types)

    @classmethod
    def get_by_type(cls, type_or_value):
        if isinstance(type_or_value, str):
            return cls.__members__[type_or_value]
        for mem in cls.__members__.values():
            if mem.verifier(type_or_value):
                return mem
        raise ValueError("不支持的字段类型")


ColumnType.NUMBER.implement_types = (Number,)
ColumnType.STR.implement_types = (str, bytes)
ColumnType.DATE.implement_types = (datetime,)
ColumnType.LIST.implement_types = (list,)


class BasicOperator(BaseModel):
    name: str
    label: str
    types: List[ColumnType]
    operator: str

    def __hash__(self):
        return hash(self.name + self.operator)

    def __repr__(self):
        return self.operator

    def to_dict(self):
        return {
            "name": self.name,
            "label": self.label,
            "types": [t.name for t in self.types],
            "operator": self.operator,
        }

    def dict(self, *args, **kwargs):
        return self.to_dict()


class ColumnOperator(EnumMixin):
    EQUAL = BasicOperator(
        name="EQUAL",
        label="等于",
        types=[ColumnType.NUMBER, ColumnType.STR],
        operator="=",
    )
    UNEQUAL = BasicOperator(
        name="UNEQUAL",
        types=[ColumnType.NUMBER, ColumnType.STR],
        operator="!=",
        label="不等于",
    )
    LT = BasicOperator(name="LT", types=[ColumnType.NUMBER], operator="<", label="小于")
    LTE = BasicOperator(name="LTE", types=[ColumnType.NUMBER], operator="<=", label="小于等于")
    GT = BasicOperator(name="GT", types=[ColumnType.NUMBER], operator=">", label="大于")
    GTE = BasicOperator(name="GTE", types=[ColumnType.NUMBER], operator=">=", label="大于等于")
    IN = BasicOperator(name="IN", types=[ColumnType.LIST], operator="IN", label="在")
    NOT_IN = BasicOperator(name="NOT_IN", types=[ColumnType.LIST], operator="NOT_IN", label="不在")
    LIKE = BasicOperator(name="LIKE", types=[ColumnType.STR], operator="LIKE", label="像")
    BETWEEN = BasicOperator(name="BETWEEN", types=[ColumnType.NUMBER], operator="BETWEEN", label="属于")

    DATE_EQ = BasicOperator(name="DATE_EQ", types=[ColumnType.DATE], operator="BETWEEN", label="日期等于")
    DATE_AFTER = BasicOperator(name="DATE_AFTER", types=[ColumnType.DATE], operator=">", label="日期晚于")
    DATE_BEFORE = BasicOperator(name="DATE_BEFORE", types=[ColumnType.DATE], operator="<", label="日期早于")
    DATE_BETWEEN = BasicOperator(name="DATE_BETWEEN", types=[ColumnType.DATE], operator="BETWEEN", label="日期属于")

    if typing.TYPE_CHECKING:
        value_handler: typing.Any
        op: typing.Any

    @classmethod
    def has(cls, t):
        """
        name/operator 都可以拿到枚举类型
        """
        try:
            cls(t)
            return True
        except ValueError:
            return t in cls.__members__ or t in [key.operator for key in cls._value2member_map_.keys()]

    @classmethod
    def get(cls, t):
        if not cls.has(t):
            raise ValueError(f"不支持的枚举类型{t} of {cls}")
        try:
            return cls(t)
        except ValueError:
            if t in cls.__members__:
                # 通过name拿
                return cls.__members__[t]
            for value in cls._value2member_map_:
                # 通过operator拿
                if value.operator == t:
                    return value
        raise ValueError

    @classmethod
    def operators(cls, column_type: str | type | None = None) -> list[dict]:
        """列出指定 column type 支持的所有操作符, 如果没有指定 column_type 则返回所有操作符."""
        results = []
        for key, item in cls.__members__.items():
            if column_type and not ColumnType.get_by_type(column_type) in item.value.types:
                continue
            results.append(item.value.to_dict())
        return results

    def __repr__(self):
        return repr(self.value)

    def to_dict(self):
        return self.value.to_dict()


ColumnOperator.LIKE.value_handler = lambda v: f"%{v}%"
ColumnOperator.LIKE.op = contains_op

ColumnOperator.IN.value_handler = lambda v: v if isinstance(v, Collection) else [v]
ColumnOperator.IN.op = in_op

ColumnOperator.BETWEEN.value_handler = lambda v: list(v)[:2]
ColumnOperator.BETWEEN.op = lambda col, value: between_op(col, value[0], value[-1])


class JobActionCheckResult(EnumMixin):
    NO_VALID_ASSIGNEE = "无可执行客服"
    ASSIGNEE_DELETED = "执行客服已删除"
    ASSIGNEE_DISABLE = "执行客服已禁用"


class OperatorRelation(Enum):
    AND = "AND"
    OR = "OR"

    if typing.TYPE_CHECKING:
        implement: typing.Any


OperatorRelation.AND.implement = and_
OperatorRelation.OR.implement = or_


class UserStatus(HasEnumMixin, IntEnum):
    NORMAL = 1
    DISABLE = 0
    DELETED = -1


class TitlesMode(HasEnumMixin, StrEnum):
    CUSTOMER = "customer"
    HEADER = "header"


class ProviderFactoryType(HasEnumMixin):
    PRODUCT = "product"


class JobTaskRunStatus(Enum):
    # 等待执行(初始状态)
    INIT = 0
    # 执行中
    RUNNING = 1
    # 结束状态
    FAILED = 2
    # 成功状态
    SUCCEED = 3


class LogisticsCancelStatus(EnumMixin):
    SUCCEED = "截单成功"
    FAILED = "截单失败"
    RUNNING = "拦截结果确认中"


class VisibilityType(StrEnum):
    ORG = "0"
    PLATFORM = "1"
    ERP = "3"


class ShopStatus(HasEnumMixin, StrEnum):
    DISABLE = "DISABLE"
    ENABLE = "ENABLE"
    AUTHORIZED = "AUTHORIZED"


class ContainsType(HasEnumMixin, StrEnum):
    all = "all"
    any = "any"


class WidgetValueUniqueCheckType(HasEnumMixin, IntEnum):
    REMIND = 1
    CREATE_FORBIDDEN = 2


class AssignStrategy(HasEnumMixin, IntEnum):
    # 自动分派的策略
    AUTO = 1  # 平均
    CREATOR = 2  # 分派给创建人
    PREV_ASSIGNEE = 3  # 分派给上一个人工步骤客服


class QueryErpItemsType(HasEnumMixin, StrEnum):
    FUZZY = "fuzzy"
    EQ = "eq"


class BusinessOrderTimeoutType(IntEnum):
    NOT_SET = 0  # 没有设置时效条件
    NOT_TIMEOUT = 1  # 根据时效条件判定，未超时
    TIMEOUT = 2  # 根据时效条件判定，已超时


class QueryOrderRule(StrEnum):
    tid = "tid"  # 使用订单号
    o_id = "o_id"  # 使用erp内部订单号
    newest = "newest"  # 若有多笔订单，使用最新的订单


class WdtTradeStatus(IntEnum):
    label: str

    CANCELLED = 5
    WAIT_PAY = 10
    WAIT_FINAL_PAYMENT = 12
    WAIT_SELECT_WAREHOUSE = 13
    WAIT_UNPAID = 15
    DELAY_APPROVAL = 16
    PRE_ORDER_PRE_HANDLE = 19
    ORDER_PRE_HANDLE = 20
    PRE_HANDLE_APPOINT = 21
    PRE_HANDLE_GRAP_ORDER = 22
    PRE_ORDER = 25
    WAIT_GRAP_ORDER = 27
    WAIT_E_APPROVAL = 30
    WAIT_FINANCE_APPROVAL = 35
    WAIT_SEND_WAREHOUSE = 40
    SEND_WAREHOUSE = 45
    SUBMITTED_WAREHOUSE = 50
    WAIT_CONFIRMED = 53
    CONFIRMED = 55
    SHIPPED = 95
    PART_PAY = 105
    COMPLETED = 110
    ERROR_SHIP = 113

    @classmethod
    def has_value(cls, value):
        return value in cls._value2member_map_


WdtTradeStatus.CONFIRMED.label = "已确认（已审核）"
WdtTradeStatus.WAIT_CONFIRMED.label = "未确认"
WdtTradeStatus.SHIPPED.label = "已发货"
WdtTradeStatus.CANCELLED.label = "已取消"
WdtTradeStatus.COMPLETED.label = "已完成"
WdtTradeStatus.ERROR_SHIP.label = "异常发货"
WdtTradeStatus.WAIT_PAY.label = "待付款"
WdtTradeStatus.WAIT_FINAL_PAYMENT.label = "待尾款"
WdtTradeStatus.WAIT_SELECT_WAREHOUSE.label = "待选仓"
WdtTradeStatus.WAIT_UNPAID.label = "等未付"
WdtTradeStatus.DELAY_APPROVAL.label = "延时审核"
WdtTradeStatus.PRE_ORDER_PRE_HANDLE.label = "预订单前处理"
WdtTradeStatus.ORDER_PRE_HANDLE.label = "前处理(赠品，合并，拆分)"
WdtTradeStatus.PRE_HANDLE_APPOINT.label = "委外前处理"
WdtTradeStatus.PRE_HANDLE_GRAP_ORDER.label = "抢单前处理"
WdtTradeStatus.PRE_ORDER.label = "预订单"
WdtTradeStatus.WAIT_GRAP_ORDER.label = "待抢单"
WdtTradeStatus.WAIT_E_APPROVAL.label = "待客审"
WdtTradeStatus.WAIT_FINANCE_APPROVAL.label = "待财审"
WdtTradeStatus.WAIT_SEND_WAREHOUSE.label = "待递交仓库"
WdtTradeStatus.SEND_WAREHOUSE.label = "递交仓库中"
WdtTradeStatus.SUBMITTED_WAREHOUSE.label = "已递交仓库"
WdtTradeStatus.PART_PAY.label = "部分打款"


class JstTradeStatus(EnumMixin, StrEnum):
    """
    客户端订单信息回执直接返回中文，api返回的是英文
    """

    无 = "None"
    待付款 = "WaitPay"
    等待审核 = "WaitConfirm"
    已客审待财审 = "WaitFConfirm"
    异常 = "Question"
    发货中 = "Delivering"
    等待外仓发货 = "WaitOuterSent"
    已发货 = "Sent"
    外仓发货 = "OuterSent"
    被合并 = "Merged"
    取消 = "Cancelled"
    被替换 = "Replaced"
    被拆分 = "Split"
    删除 = "Delete"
    锁定 = "Lock"

    @staticmethod
    def get_readable_name(raw_name):
        reversed_d = {i.value: i.name for i in JstTradeStatus.__members__.values()}
        return reversed_d.get(raw_name)


class AutoRetryRecordStatus(Enum):
    NOT_ENABLE = "NOT_ENABLE"
    PENDING = "PENDING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"


class PlanWhenAssignException(IntEnum):
    """
    分配失败的处理规则。
    """

    RE_ASSIGN = 1
    ENTER_EXCEPTION_POOL = 2


class Action(StrEnum):
    """
    工单可执行的操作。
    """

    accept = "accept"
    save = "save"
    reject = "reject"
    recall = "recall"
    skip = "skip"
    pause = "pause"
    unpause = "unpause"
    remind = "remind"
    deliver = "deliver"
    retry = "retry"
    assign = "assign"
    complete = "complete"
    close = "close"
    reopen = "reopen"
    delete = "delete"
    pick = "pick"
    upgrade = "upgrade"
    recover = "recover"
    overrule = "overrule"
    approve = "approve"
    copy = "copy"
    update_order = "update_order"
    # 以下为预留操作。
    create_and_save = "create_and_save"
    create_and_submit = "create_and_submit"
    publish = "publish"
    process = "process"
    panic = "panic"
    finish = "finish"

    @property
    def label(self):
        return {
            Action.accept: "提交",
            Action.save: "保存",
            Action.reject: "退回",
            Action.recall: "撤回",
            Action.skip: "跳过",
            Action.pause: "暂停",
            Action.unpause: "启用",
            Action.remind: "催促",
            Action.deliver: "转交",
            Action.retry: "重试",
            Action.assign: "指派",
            Action.complete: "完成",
            Action.close: "关闭",
            Action.reopen: "重启",
            Action.delete: "删除",
            Action.pick: "领取",
            Action.upgrade: "订正",
            Action.recover: "恢复",
            Action.overrule: "驳回",
            Action.approve: "审批",
            Action.copy: "复制",
            Action.update_order: "更新工单数据",
            # 以下为预留操作。
            Action.create_and_save: "create_and_save",
            Action.create_and_submit: "create_and_submit",
            Action.publish: "publish",
            Action.process: "process",
            Action.panic: "panic",
            Action.finish: "finish",
        }[self]


class ApproveType(IntEnum):
    """
    审批类型。
    """

    # 或签，也就是任意一人签署即可。
    ANY_ONE_TO_SIGN = 1
    # 并行会签，也就是需要多人签署，但是是并行处理。
    PARALLEL_MULTI_TO_SIGN = 2
    # 依次会签，也就是需要多人签署，但是需要按照顺序进行处理。
    SEQUENTIAL_MULTI_TO_SIGN = 3


class PermissionFunctionCode(StrEnum):
    """
    角色管理中的权限。
    """

    # 删除
    BUSINESS_ORDER_DELETE = "BUSINESS_ORDER_DELETE"
    # 异常工单:重试
    BUSINESS_ORDER_RETRY = "BUSINESS_ORDER_RETRY"
    # 异常工单:指派
    BUSINESS_ORDER_ASSIGN = "BUSINESS_ORDER_ASSIGN"
    # 异常工单:关闭
    BUSINESS_ORDER_CLOSE = "BUSINESS_ORDER_CLOSE"
    AUTO_APP = "AUTO_APP"
    BATCH_PAY = "BATCH_PAY"
    BUSINESS_ORDER_FRONT_DESK = "BUSINESS_ORDER_FRONT_DESK"
    # 我的工单/买家工单
    BUSINESS_ORDER_HANDLER = "BUSINESS_ORDER_HANDLER"
    BUSINESS_ORDER_MANAGER = "BUSINESS_ORDER_MANAGER"
    BUYER_SERVER_BUFFET = "BUYER_SERVER_BUFFET"
    # 智能报表
    CUSTOMIZE_BI = "CUSTOMIZE_BI"
    DATA_REPORT = "DATA_REPORT"
    DETAIL_DATA_REPORT = "DETAIL_DATA_REPORT"
    DETAIL_DATA_REPORT_MULTI_EXPORT = "DETAIL_DATA_REPORT_MULTI_EXPORT"
    DETAIL_DATA_REPORT_MULTI_VIEW = "DETAIL_DATA_REPORT_MULTI_VIEW"
    DETAIL_DATA_REPORT_SINGLE_EXPORT = "DETAIL_DATA_REPORT_SINGLE_EXPORT"
    DETAIL_DATA_REPORT_SINGLE_VIEW = "DETAIL_DATA_REPORT_SINGLE_VIEW"
    EXCEPTIONAL_EDIT = "EXCEPTIONAL_EDIT"
    EXCEPTIONAL_VIEW = "EXCEPTIONAL_VIEW"
    ORG_EXCEPTIONAL_EDIT = "ORG_EXCEPTIONAL_EDIT"
    ORG_EXCEPTIONAL_VIEW = "ORG_EXCEPTIONAL_VIEW"
    # 【群店】删除工单
    ORG_TASK_BUSINESS_ORDER_DELETE = "ORG_TASK_BUSINESS_ORDER_DELETE"
    ORG_TASK_EDIT = "ORG_TASK_EDIT"
    # 【群店】导入
    ORG_TASK_IMPORT = "ORG_TASK_IMPORT"
    ORG_TASK_VIEW = "ORG_TASK_VIEW"
    PROVIDER_CENTER = "PROVIDER_CENTER"
    PROVIDER_EDIT = "PROVIDER_EDIT"
    PROVIDER_EXPORT = "PROVIDER_EXPORT"
    PROVIDER_VIEW = "PROVIDER_VIEW"
    SYSTEM_SETTING = "SYSTEM_SETTING"
    # 【单店】删除工单
    TASK_BUSINESS_ORDER_DELETE = "TASK_BUSINESS_ORDER_DELETE"
    TASK_CENTER = "TASK_CENTER"
    TASK_EDIT = "TASK_EDIT"
    # 【单店】导入
    TASK_IMPORT = "TASK_IMPORT"
    TASK_VIEW = "TASK_VIEW"
    TEMPLATE_CENTER = "TEMPLATE_CENTER"
    TEMPLATE_CREATE_AND_EDIT = "TEMPLATE_CREATE_AND_EDIT"
    TEMPLATE_VIEW = "TEMPLATE_VIEW"
    TASK_BUSINESS_ORDER_COPY = "TASK_BUSINESS_ORDER_COPY"
    ORG_TASK_BUSINESS_ORDER_COPY = "ORG_TASK_BUSINESS_ORDER_COPY"
    # 催促
    REMIND_WITHOUT_ADMIN = "REMIND_WITHOUT_ADMIN"


class SmartCallPlatform(Enum):
    TAOBAO = "TAOBAO"
    DY = "DY"
    PDD = "PDD"

    @staticmethod
    def adapt(platform) -> Optional["SmartCallPlatform"]:
        try:
            if platform == "TMALL":
                platform = "TAOBAO"
            result = SmartCallPlatform(platform)
        except Exception:
            return None
        return result


class StoInterceptStatus(Enum):
    SUCCESS = 1
    FAILURE = 2
    REVERSE_PRINT = 3
    REVERSE_SIGN = 4
    CANCELLED = -2


class ZTOInterceptStatus(StrEnum):
    INTERCEPTING = "INTERCEPTING"
    INTERCEPT_SUCCESS = "INTERCEPT_SUCCESS"
    INTERCEPT_FAIL = "INTERCEPT_FAIL"
    INTERCEPT_CANCEL = "INTERCEPT_CANCEL"
    PACKAGE_DELIVERYING = "PACKAGE_DELIVERYING"
    INTERCEPT_DISCONTINUE = "INTERCEPT_DISCONTINUE"


class JTInterceptStatus(StrEnum):
    SUCCESS = "success"
    FAIL = "fail"
    PROCESSING = "processing"


StoInterceptStatus.SUCCESS.description = "拦截成功"  # type: ignore[attr-defined]
StoInterceptStatus.FAILURE.description = "拦截失败"  # type: ignore[attr-defined]
StoInterceptStatus.REVERSE_PRINT.description = "逆向打印"  # type: ignore[attr-defined]
StoInterceptStatus.CANCELLED.description = "拦截取消"  # type: ignore[attr-defined]
StoInterceptStatus.REVERSE_SIGN.description = "逆向签收"  # type: ignore[attr-defined]


class ShopPlatform(StrEnum):
    JD = "JD", "京东"
    TAOBAO = "TAOBAO", "淘宝"
    TMALL = "TMALL", "天猫"
    DOUDIAN = "DOUDIAN", "抖店"
    PDD = "PDD", "拼多多"
    KAOLA = "KAOLA", "网易考拉"
    VIPSHOP = "VIPSHOP", "唯品会"
    YOUZAN = "YOUZAN", "有赞"
    WEIMOB = "WEIMOB", "微盟"
    MOGUJIE = "MOGUJIE", "蘑菇街"
    XIAOHONGSHU = "XIAOHONGSHU", "小红书"
    SUNING = "SUNING", "苏宁易购"
    ALIBABA = "ALIBABA", "1688"
    BEIDIAN = "BEIDIAN", "贝店"
    KUAISHOU = "KUAISHOU", "快手"

    _label_: str  # for mypy check

    def __new__(cls, value, label=None):
        self = str.__new__(cls, value)
        self._value_ = value
        self._label_ = label or value
        return self

    @property
    def label(self):
        return self._label_


class Icon(StrEnum):
    ICON_UNSPECIFIED = ""
    JST = "JST"  # 聚水潭
    WDT = "WangDianTong"  # 旺店通
    BaiShengE3 = "BaiShengE3"  # 百胜E3
    KuaiMai = "KuaiMai"  # 快卖
    WanLiNiu = "WanLiNiu"  # 万里牛
    GuanYiYun = "GuanYiYun"  # 管易云
    DuoHong = "DuoHong"  # 多鸿
    ALIPAY = "ALIPAY"  # 支付宝
    QN = "QN"  # 千牛
    PDD = "PDD"  # 拼多多
    DouYin = "DouYin"  # 抖音
    TaoBao = "TaoBao"  # 淘宝
    TMall = "TMall"  # 天猫
    Product = "Product"  # 商品
    Order = "Order"  # 订单
    DingTalk = "DingTalk"  # 钉钉
    QQ = "QQ"  # QQ
    WeChat = "WeChat"  # 微信
    TaskComplete = "TaskComplete"  # 任务完结
    TaskSchedule = "TaskSchedule"  # 任务调度
    VIP = "VIP"  # VIP定制
    SmartCall = "SmartCall"  # 智能外呼
    JD = "JD"  # 京东
    WeCom = "WeCom"  # 企业微信
    GoodsReturn = "GoodsReturn"  # 退货
    ZT = "ZT"  # 中通
    SetText = "SetText"  # 文本赋值
    STO = "STO"  # 申通


class MemoFlag(StrEnum):
    BLANK = "[无旗帜]"
    RED = "红色旗帜"
    YELLOW = "黄色旗帜"
    GREEN = "绿色旗帜"
    BLUE = "蓝色旗帜"
    PURPLE = "紫色旗帜"
    ORANGE = "橙色旗帜"
    SKY_BLUE = "天蓝旗帜"
    PINK = "粉色旗帜"
    LEAF_GREEN = "草绿旗帜"
    ROSE_RED = "玫红旗帜"
    GRAY = "灰色旗帜"

    @staticmethod
    def taobao_flag_mapper():
        return {
            0: MemoFlag.GRAY,
            1: MemoFlag.RED,
            2: MemoFlag.YELLOW,
            3: MemoFlag.GREEN,
            4: MemoFlag.BLUE,
            5: MemoFlag.PURPLE,
            6: MemoFlag.ORANGE,
            7: MemoFlag.SKY_BLUE,
            8: MemoFlag.PINK,
            9: MemoFlag.LEAF_GREEN,
            10: MemoFlag.ROSE_RED,
        }

    def taobao_flag_id(self):
        return dict((v, k) for k, v in self.taobao_flag_mapper().items())[self]

    @staticmethod
    def from_taobao_flag_id(flag_id: str | int):
        return MemoFlag.taobao_flag_mapper()[int(flag_id)]

    @staticmethod
    def pdd_flag_mapper():
        return {
            1: MemoFlag.RED,
            2: MemoFlag.YELLOW,
            3: MemoFlag.GREEN,
            4: MemoFlag.BLUE,
            5: MemoFlag.PURPLE,
        }

    def pdd_flag_id(self):
        return dict((v, k) for k, v in self.pdd_flag_mapper().items())[self]

    @staticmethod
    def from_pdd_flag_id(flag_id: str | int):
        return MemoFlag.pdd_flag_mapper()[int(flag_id)]

    @staticmethod
    def doudian_flag_mapper():
        return {
            0: MemoFlag.GRAY,
            1: MemoFlag.PURPLE,
            2: MemoFlag.BLUE,
            3: MemoFlag.GREEN,
            4: MemoFlag.ORANGE,
            5: MemoFlag.RED,
        }

    def doudian_flag_id(self):
        return dict((v, k) for k, v in self.doudian_flag_mapper().items())[self]

    @staticmethod
    def from_doudian_flag_id(flag_id: str | int):
        return MemoFlag.doudian_flag_mapper()[int(flag_id)]

    @staticmethod
    def xhs_flag_mapper():
        #  1灰旗 2红旗 3黄旗 4绿旗 5蓝旗 6紫旗
        return {
            1: MemoFlag.GRAY,
            2: MemoFlag.RED,
            3: MemoFlag.YELLOW,
            4: MemoFlag.GREEN,
            5: MemoFlag.BLUE,
            6: MemoFlag.PURPLE,
        }

    def xhs_flag_id(self):
        return dict((v, k) for k, v in self.xhs_flag_mapper().items())[self]

    @staticmethod
    def from_xhs_flag_id(flag_id: str | int):
        return MemoFlag.xhs_flag_mapper()[int(flag_id)]

    @staticmethod
    def jd_flag_mapper():
        return {
            0: MemoFlag.GRAY,
            1: MemoFlag.RED,
            2: MemoFlag.YELLOW,
            3: MemoFlag.GREEN,
            4: MemoFlag.BLUE,
            5: MemoFlag.PURPLE,
        }

    def jd_flag_id(self):
        return dict((v, k) for k, v in self.jd_flag_mapper().items())[self]

    @staticmethod
    def from_jd_flag_id(flag_id: str | int):
        return MemoFlag.doudian_flag_mapper()[int(flag_id)]

    @staticmethod
    def ks_flag_mapper():
        return {
            0: MemoFlag.BLANK,
            1: MemoFlag.RED,
            2: MemoFlag.YELLOW,
            3: MemoFlag.GREEN,
            4: MemoFlag.BLUE,
            5: MemoFlag.PURPLE,
            6: MemoFlag.GRAY,
        }

    def ks_flag_id(self):
        return dict((v, k) for k, v in self.ks_flag_mapper().items())[self]

    @staticmethod
    def from_ks_flag_id(flag_id: str | int):
        return MemoFlag.ks_flag_mapper()[int(flag_id)]


class UpdateStrategy(StrEnum):
    """工单更新策略"""

    SAFE = "SAFE"  # 仅工单工作流在可更新节点时允许更新
    FORCE = "FORCE"  # 强制更新
