from robot_types import core
from robot_types import helper
from robot_types import model
from robot_types.helper.predefined import BizType
from robot_types.helper.sql import SQLCriteriaResolver
from robot_types.helper.symbol import SymbolResolver

from .enum_options import form_id_resolver
from .enum_options import rpa_resolver
from .enum_options import shop_resolver
from .enum_options import user_resolver


class utils:
    @staticmethod
    def register_typename_shop_to_registry(registry: helper.operator_resolver.Registry):
        registry.register_typename(
            typename=helper.get_typename(model.resource.Shop),
            resolvers={
                helper.OperatorEnum.EQ: helper.operator_resolver.Operator(
                    label="等于", type_spec=model.resource.type_spec.Shop
                ),
                helper.OperatorEnum.EQ_ANY: helper.operator_resolver.Operator(
                    label="等于任一",
                    type_spec=core.TypeSpec("array", items=model.resource.type_spec.Shop),
                    extra={"default": True},
                ),
            },
        )


def _invoice_notification_rule_filter_context():
    symbol_resolver = SymbolResolver()
    BizType.INVOICE_NOTIFICATION_RULE_FILTER_CONTEXT.register_symbol_resolver(symbol_resolver)
    symbol_resolver.registry.register_typename(helper.get_typename(model.resource.Shop), shop_resolver)

    operator_registry = helper.operator_resolver.Registry()
    BizType.INVOICE_NOTIFICATION_RULE_FILTER_CONTEXT.register_operator_registry(operator_registry)
    utils.register_typename_shop_to_registry(operator_registry)


_invoice_notification_rule_filter_context()


def _bo_event_filter():
    operator_registry = helper.operator_resolver.Registry()
    BizType.BO_EVENT_FILTER.register_operator_registry(operator_registry)

    operator_value_unique = helper.operator_resolver.Operator("值唯一", None)
    if "Select" in operator_registry.typenames:
        operator_registry.typenames["Select"][helper.OperatorEnum.VALUE_UNIQUE] = operator_value_unique
    if "string" in operator_registry.types:
        operator_registry.types["string"][helper.OperatorEnum.VALUE_UNIQUE] = operator_value_unique
    if "number" in operator_registry.types:
        operator_registry.types["number"][helper.OperatorEnum.VALUE_UNIQUE] = operator_value_unique
    if "datetime" in operator_registry.types:
        operator_registry.types["datetime"][helper.OperatorEnum.VALUE_UNIQUE] = operator_value_unique
    if "date" in operator_registry.types:
        operator_registry.types["date"][helper.OperatorEnum.VALUE_UNIQUE] = operator_value_unique
    if "time" in operator_registry.types:
        operator_registry.types["time"][helper.OperatorEnum.VALUE_UNIQUE] = operator_value_unique
    if "boolean" in operator_registry.types:
        operator_registry.types["boolean"][helper.OperatorEnum.VALUE_UNIQUE] = operator_value_unique
    if "array" in operator_registry.types:
        operator_registry.types["array"][helper.OperatorEnum.VALUE_UNIQUE] = operator_value_unique
    if "collection" in operator_registry.types:
        operator_registry.types["collection"][helper.OperatorEnum.VALUE_UNIQUE] = operator_value_unique


_bo_event_filter()


def _bo_form_validator():
    operator_registry = helper.operator_resolver.Registry()
    BizType.BO_FORM_VALIDATOR.register_operator_registry(operator_registry)
    # 仅提供 "值重复" 的操作符，因为 "值唯一" 在这个场景是没有意义的，容易配置错误
    operator_value_recorded = helper.operator_resolver.Operator("重复", None)
    if "Select" in operator_registry.typenames:
        operator_registry.typenames["Select"][helper.OperatorEnum.VALUE_RECORDED] = operator_value_recorded
    if "string" in operator_registry.types:
        operator_registry.types["string"][helper.OperatorEnum.VALUE_RECORDED] = operator_value_recorded
    if "number" in operator_registry.types:
        operator_registry.types["number"][helper.OperatorEnum.VALUE_RECORDED] = operator_value_recorded
    if "datetime" in operator_registry.types:
        operator_registry.types["datetime"][helper.OperatorEnum.VALUE_RECORDED] = operator_value_recorded
    if "date" in operator_registry.types:
        operator_registry.types["date"][helper.OperatorEnum.VALUE_RECORDED] = operator_value_recorded
    if "time" in operator_registry.types:
        operator_registry.types["time"][helper.OperatorEnum.VALUE_RECORDED] = operator_value_recorded
    if "boolean" in operator_registry.types:
        operator_registry.types["boolean"][helper.OperatorEnum.VALUE_RECORDED] = operator_value_recorded
    if "array" in operator_registry.types:
        operator_registry.types["array"][helper.OperatorEnum.VALUE_RECORDED] = operator_value_recorded
    if "collection" in operator_registry.types:
        operator_registry.types["collection"][helper.OperatorEnum.VALUE_RECORDED] = operator_value_recorded


_bo_form_validator()


def _exception_filter_context():
    symbol_resolver = SymbolResolver()
    BizType.EXCEPTION_FILTER_CONTEXT.register_symbol_resolver(symbol_resolver)
    symbol_resolver.registry.register_typename(helper.get_typename(model.resource.Shop), shop_resolver)
    symbol_resolver.registry.register_typename(helper.get_typename(model.resource.User), user_resolver)
    symbol_resolver.registry.register_typename(helper.get_typename(model.resource.FormID), form_id_resolver)
    symbol_resolver.registry.register(["current_job_rpa_name"], rpa_resolver)

    # 注册 operator
    operator_registry = helper.operator_resolver.Registry()
    BizType.EXCEPTION_FILTER_CONTEXT.register_operator_registry(operator_registry)
    operator_registry.register_typename(
        typename=helper.get_typename(model.resource.User),
        resolvers={
            helper.OperatorEnum.EQ: helper.operator_resolver.Operator("等于", model.resource.type_spec.Shop),
            helper.OperatorEnum.EQ_ANY: helper.operator_resolver.Operator(
                "等于任一", core.TypeSpec("array", items=model.resource.type_spec.User), extra={"default": True}
            ),
        },
    )
    # 限定 business_order_id 支持的操作符
    operator_registry.restrict_by_name(
        name="business_order_id",
        type_spec=model.exception_pool.type_spec.FilterContext.properties["business_order_id"],
        allowed=[helper.OperatorEnum.EQ, helper.OperatorEnum.EQ_ANY],
    )
    operator_registry.register_name(
        name="form_id",
        resolvers={
            helper.OperatorEnum.EQ: helper.operator_resolver.Operator(
                label="等于", type_spec=model.resource.type_spec.FormID, extra={"default": True}
            ),
            helper.OperatorEnum.EQ_ANY: helper.operator_resolver.Operator(
                label="等于任一", type_spec=core.TypeSpec("array", items=model.resource.type_spec.FormID)
            ),
        },
    )
    utils.register_typename_shop_to_registry(operator_registry)
    # 限定 auto_retry_status 支持的操作符
    operator_registry.restrict_by_name(
        name="auto_retry_status",
        type_spec=model.exception_pool.type_spec.FilterContext.properties["auto_retry_status"],
        allowed=[helper.OperatorEnum.EQ, helper.OperatorEnum.EQ_ANY],
    )

    # 限定 current_job_rpa_name 支持的操作符
    operator_registry.restrict_by_name(
        name="current_job_rpa_name",
        type_spec=model.exception_pool.type_spec.FilterContext.properties["current_job_rpa_name"],
        allowed=[helper.OperatorEnum.EQ, helper.OperatorEnum.EQ_ANY],
    )
    operator_registry.restrict_by_name(
        name="will_retry",
        type_spec=model.exception_pool.type_spec.FilterContext.properties["will_retry"],
        allowed=[helper.OperatorEnum.IS_TRUE, helper.OperatorEnum.IS_FALSE],
    )


_exception_filter_context()


def _invoice_goods_issuing_rule_filter_context():
    from robot_processor.invoice.goods.models import GoodsIssuingRule
    from robot_processor.invoice.goods.models import IssuingItem

    sql_criteria_resolver = SQLCriteriaResolver()
    BizType.INVOICE_GOODS_ISSUING_RULE_FILTER_CONTEXT.register_sql_criteria_resolver(sql_criteria_resolver)
    sql_criteria_resolver.table_field_registry.register_field(name="name", table_field=GoodsIssuingRule.name)
    sql_criteria_resolver.table_field_registry.register_field(name="issuing_item_title", table_field=IssuingItem.title)
    sql_criteria_resolver.table_field_registry.register_field(
        name="issuing_item_tax_rate", table_field=IssuingItem.tax_rate
    )


_invoice_goods_issuing_rule_filter_context()
