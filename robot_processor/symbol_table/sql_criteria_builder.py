from robot_types.core import Condition
from robot_types.core import Filter
from robot_types.helper import ValueResolver
from robot_types.helper.sql import SQLCriteriaResolver
from sqlalchemy import and_ as sql_and
from sqlalchemy import or_ as sql_or


class SQLCriteriaBuilder:
    def __init__(self, sql_criteria_resolver: SQLCriteriaResolver):
        self.sql_criteria_resolver = sql_criteria_resolver

    def filter_to_sql_criteria(self, filter_: Filter, value_resolver: ValueResolver):
        where_clauses = []
        for condition in filter_.conditions:
            if isinstance(condition, Condition):
                where_clauses.append(self.condition_to_sql_criteria(condition, value_resolver))
            else:
                where_clauses.append(self.filter_to_sql_criteria(condition, value_resolver))
        if filter_.relation == "and":
            return sql_and(*where_clauses)
        else:
            return sql_or(*where_clauses)

    def condition_to_sql_criteria(self, condition: Condition, value_resolver: ValueResolver):
        if condition.b:
            value = condition.b.with_resolver(value_resolver).resolve().unwrap()
        else:
            value = None
        name = condition.a.var.path
        return self.sql_criteria_resolver.resolve(
            type_spec=condition.a.type_spec, op=condition.o, value=value, name=name
        )
