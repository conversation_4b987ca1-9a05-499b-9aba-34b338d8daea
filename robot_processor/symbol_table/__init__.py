from robot_processor.signals import booting
from robot_processor.symbol_table.models import Symbol
from robot_processor.symbol_table.models import TypeSpec
from robot_processor.symbol_table.models import Value

__all__ = ["TypeSpec", "Value", "Symbol"]


@booting.connect
def init_app(app):
    from . import models
    from .api import bp
    from .registry import init_biz_type

    app.register_blueprint(bp, url_prefix="/v1")
