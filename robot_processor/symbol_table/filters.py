from dataclasses import dataclass
from datetime import date
from datetime import datetime
from datetime import time
from enum import Enum
from enum import StrEnum
from typing import Any
from typing import Callable
from typing import cast

from leyan_proto.digismart.robot.symbol_table_pb2 import Filter as pb_Filter
from leyan_proto.digismart.robot.symbol_table_pb2 import Operand as pb_Operand
from pydantic import BaseModel
from pydantic import Field
from robot_types.helper.sql import SQLCriteriaResolver
from sqlalchemy import BinaryExpression
from sqlalchemy.orm import QueryableAttribute

from robot_processor.symbol_table import named_typespec
from robot_processor.symbol_table.models import EnumOption
from robot_processor.symbol_table.models import Namespace
from robot_processor.symbol_table.models import Symbol
from robot_processor.symbol_table.models import TypeSpec
from robot_processor.symbol_table.models import Value


class OperatorEnum(StrEnum):
    OPERATOR_UNSPECIFIED = "_", pb_Filter.OPERATOR_UNSPECIFIED
    EXISTS = "exists", pb_Filter.EXISTS
    NOT_EXISTS = "not_exists", pb_Filter.NOT_EXISTS
    EQ = "eq", pb_Filter.EQ
    NE = "ne", pb_Filter.NE
    CONTAINS = "contains", pb_Filter.CONTAINS
    NOT_CONTAINS = "not_contains", pb_Filter.NOT_CONTAINS
    EQ_ANY = "eq_any", pb_Filter.EQ_ANY
    NE_ANY = "ne_any", pb_Filter.NE_ANY
    CONTAINS_ANY = "contains_any", pb_Filter.CONTAINS_ANY
    CONTAINS_ALL = "contains_all", pb_Filter.CONTAINS_ALL
    DISJOINT = "disjoint", pb_Filter.DISJOINT
    GT = "gt", pb_Filter.GT
    GTE = "gte", pb_Filter.GTE
    LT = "lt", pb_Filter.LT
    LTE = "lte", pb_Filter.LTE
    BETWEEN = "between", pb_Filter.BETWEEN
    IS_TRUE = "is_true", pb_Filter.IS_TRUE
    IS_FALSE = "is_false", pb_Filter.IS_FALSE
    ALL = "all", pb_Filter.ALL
    ANY = "any", pb_Filter.ANY
    VALUE_UNIQUE = "value_unique", pb_Filter.VALUE_UNIQUE
    VALUE_RECORDED = "value_recorded", pb_Filter.VALUE_RECORDED
    IS_EMPTY = "is_empty", pb_Filter.IS_EMPTY
    IS_NOT_EMPTY = "is_not_empty", pb_Filter.IS_NOT_EMPTY

    def __new__(cls, value, pb_value):
        self = str.__new__(cls, value)
        self._value_ = value
        return self

    def __init__(self, _, pb_value):
        self.pb_value = pb_value

    @classmethod
    def from_pb(cls, pb_value):
        for member in cls:
            if member.pb_value == pb_value:
                return member
        raise ValueError(f"Unknown OperatorEnum value: {pb_value}")


# 因为数据类型 (TypeEnum) 是有限的，操作符 (OperatorEnum) 也是有限的
# 通过列举出所有 左值 (a) 操作符 (o) 右值 (b) 的组合，来提供所有的操作符，而非动态生成
@dataclass
class Operand:
    a: TypeSpec
    o: OperatorEnum
    b: TypeSpec | None


class OperandEnum(Enum):
    STRING_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.STRING), OperatorEnum.EXISTS, None),
        "不为空",
    )
    STRING_NOT_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.STRING), OperatorEnum.NOT_EXISTS, None),
        "为空",
    )
    STRING_IS_EMPTY = (
        Operand(TypeSpec(type=TypeSpec.STRING), OperatorEnum.IS_EMPTY, None),
        "为空字符串",
    )
    STRING_IS_NOT_EMPTY = (
        Operand(TypeSpec(type=TypeSpec.STRING), OperatorEnum.IS_NOT_EMPTY, None),
        "不为空字符串",
    )
    STRING_VALUE_UNIQUE = (
        Operand(TypeSpec(type=TypeSpec.STRING), OperatorEnum.VALUE_UNIQUE, None),
        "不重复",
    )
    STRING_VALUE_RECORDED = (
        Operand(TypeSpec(type=TypeSpec.STRING), OperatorEnum.VALUE_RECORDED, None),
        "重复",
    )
    STRING_EQ = (
        Operand(
            TypeSpec(type=TypeSpec.STRING),
            OperatorEnum.EQ,
            TypeSpec(type=TypeSpec.STRING),
        ),
        "等于",
    )
    STRING_EQ_ANY = (
        Operand(
            TypeSpec(type=TypeSpec.STRING),
            OperatorEnum.EQ_ANY,
            TypeSpec(type=TypeSpec.ARRAY, spec=[TypeSpec(type=TypeSpec.STRING)]),
        ),
        "等于任一",
    )
    STRING_NE_ANY = (
        Operand(
            TypeSpec(type=TypeSpec.STRING),
            OperatorEnum.NE_ANY,
            TypeSpec(type=TypeSpec.ARRAY, spec=[TypeSpec(type=TypeSpec.STRING)]),
        ),
        "不等于任一",
    )
    STRING_CONTAINS = (
        Operand(
            TypeSpec(type=TypeSpec.STRING),
            OperatorEnum.CONTAINS,
            TypeSpec(type=TypeSpec.STRING),
        ),
        "包含",
    )
    STRING_NOT_CONTAINS = (
        Operand(
            TypeSpec(type=TypeSpec.STRING),
            OperatorEnum.NOT_CONTAINS,
            TypeSpec(type=TypeSpec.STRING),
        ),
        "不包含",
    )
    STRING_DISJOINT = (
        Operand(
            TypeSpec(type=TypeSpec.STRING),
            OperatorEnum.DISJOINT,
            TypeSpec(type=TypeSpec.STRING),
        ),
        "不包含任一",
    )
    STRING_CONTAINS_ANY = (
        Operand(
            TypeSpec(type=TypeSpec.STRING),
            OperatorEnum.CONTAINS_ANY,
            TypeSpec(type=TypeSpec.ARRAY, spec=[TypeSpec(type=TypeSpec.STRING)]),
        ),
        "包含任一",
    )
    STRING_CONTAINS_ALL = (
        Operand(
            TypeSpec(type=TypeSpec.STRING),
            OperatorEnum.CONTAINS_ALL,
            TypeSpec(type=TypeSpec.ARRAY, spec=[TypeSpec(type=TypeSpec.STRING)]),
        ),
        "包含全部",
    )

    NUMBER_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.NUMBER), OperatorEnum.EXISTS, None),
        "不为空",
    )
    NUMBER_VALUE_UNIQUE = (
        Operand(TypeSpec(type=TypeSpec.NUMBER), OperatorEnum.VALUE_UNIQUE, None),
        "不重复",
    )
    NUMBER_VALUE_RECORDED = (
        Operand(TypeSpec(type=TypeSpec.NUMBER), OperatorEnum.VALUE_RECORDED, None),
        "重复",
    )
    NUMBER_NOT_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.NUMBER), OperatorEnum.NOT_EXISTS, None),
        "为空",
    )
    NUMBER_EQ = (
        Operand(
            TypeSpec(type=TypeSpec.NUMBER),
            OperatorEnum.EQ,
            TypeSpec(type=TypeSpec.NUMBER),
        ),
        "等于",
    )
    NUMBER_GT = (
        Operand(
            TypeSpec(type=TypeSpec.NUMBER),
            OperatorEnum.GT,
            TypeSpec(type=TypeSpec.NUMBER),
        ),
        "大于",
    )
    NUMBER_GTE = (
        Operand(
            TypeSpec(type=TypeSpec.NUMBER),
            OperatorEnum.GTE,
            TypeSpec(type=TypeSpec.NUMBER),
        ),
        "大于等于",
    )
    NUMBER_LT = (
        Operand(
            TypeSpec(type=TypeSpec.NUMBER),
            OperatorEnum.LT,
            TypeSpec(type=TypeSpec.NUMBER),
        ),
        "小于",
    )
    NUMBER_LTE = (
        Operand(
            TypeSpec(type=TypeSpec.NUMBER),
            OperatorEnum.LTE,
            TypeSpec(type=TypeSpec.NUMBER),
        ),
        "小于等于",
    )
    NUMBER_BETWEEN = (
        Operand(
            TypeSpec(type=TypeSpec.NUMBER),
            OperatorEnum.BETWEEN,
            TypeSpec(
                type=TypeSpec.COLLECTION,
                spec=[
                    TypeSpec(type=TypeSpec.NUMBER, name="min"),
                    TypeSpec(type=TypeSpec.NUMBER, name="max"),
                ],
            ),
        ),
        "在...之间",
    )

    BOOLEAN_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.BOOLEAN), OperatorEnum.EXISTS, None),
        "不为空",
    )
    BOOLEAN_NOT_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.BOOLEAN), OperatorEnum.NOT_EXISTS, None),
        "为空",
    )
    BOOLEAN_VALUE_UNIQUE = (
        Operand(TypeSpec(type=TypeSpec.BOOLEAN), OperatorEnum.VALUE_UNIQUE, None),
        "不重复",
    )
    BOOLEAN_VALUE_RECORDED = (
        Operand(TypeSpec(type=TypeSpec.BOOLEAN), OperatorEnum.VALUE_RECORDED, None),
        "重复",
    )
    BOOLEAN_IS_TRUE = (
        Operand(TypeSpec(type=TypeSpec.BOOLEAN), OperatorEnum.IS_TRUE, None),
        "为真",
    )
    BOOLEAN_IS_FALSE = (
        Operand(TypeSpec(type=TypeSpec.BOOLEAN), OperatorEnum.IS_FALSE, None),
        "为假",
    )

    DATETIME_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.DATETIME), OperatorEnum.EXISTS, None),
        "不为空",
    )
    DATETIME_NOT_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.DATETIME), OperatorEnum.NOT_EXISTS, None),
        "为空",
    )
    DATETIME_VALUE_UNIQUE = (
        Operand(TypeSpec(type=TypeSpec.DATETIME), OperatorEnum.VALUE_UNIQUE, None),
        "不重复",
    )
    DATETIME_VALUE_RECORDED = (
        Operand(TypeSpec(type=TypeSpec.DATETIME), OperatorEnum.VALUE_RECORDED, None),
        "重复",
    )
    DATETIME_LT = (
        Operand(
            TypeSpec(type=TypeSpec.DATETIME),
            OperatorEnum.LT,
            TypeSpec(type=TypeSpec.DATETIME),
        ),
        "日期时间早于",
    )
    DATETIME_GT = (
        Operand(
            TypeSpec(type=TypeSpec.DATETIME),
            OperatorEnum.GT,
            TypeSpec(type=TypeSpec.DATETIME),
        ),
        "日期时间晚于",
    )
    DATETIME_BETWEEN = (
        Operand(
            TypeSpec(type=TypeSpec.DATETIME),
            OperatorEnum.BETWEEN,
            TypeSpec(
                type=TypeSpec.COLLECTION,
                spec=[
                    TypeSpec(type=TypeSpec.DATETIME, name="min"),
                    TypeSpec(type=TypeSpec.DATETIME, name="max"),
                ],
            ),
        ),
        "日期时间在...之间",
    )
    DATETIME_DATE_EQ = (
        Operand(
            TypeSpec(type=TypeSpec.DATETIME),
            OperatorEnum.EQ,
            TypeSpec(type=TypeSpec.DATE),
        ),
        "日期等于",
    )
    DATETIME_DATE_LT = (
        Operand(
            TypeSpec(type=TypeSpec.DATETIME),
            OperatorEnum.LT,
            TypeSpec(type=TypeSpec.DATE),
        ),
        "日期早于",
    )
    DATETIME_DATE_GT = (
        Operand(
            TypeSpec(type=TypeSpec.DATETIME),
            OperatorEnum.GT,
            TypeSpec(type=TypeSpec.DATE),
        ),
        "日期晚于",
    )
    DATETIME_DATE_BETWEEN = (
        Operand(
            TypeSpec(type=TypeSpec.DATETIME),
            OperatorEnum.BETWEEN,
            TypeSpec(
                type=TypeSpec.COLLECTION,
                spec=[
                    TypeSpec(type=TypeSpec.DATE, name="min"),
                    TypeSpec(type=TypeSpec.DATE, name="max"),
                ],
            ),
        ),
        "日期在...之间",
    )
    DATETIME_TIME_EQ = (
        Operand(
            TypeSpec(type=TypeSpec.DATETIME),
            OperatorEnum.EQ,
            TypeSpec(type=TypeSpec.TIME),
        ),
        "时间等于",
    )
    DATETIME_TIME_LT = (
        Operand(
            TypeSpec(type=TypeSpec.DATETIME),
            OperatorEnum.LT,
            TypeSpec(type=TypeSpec.TIME),
        ),
        "时间早于",
    )
    DATETIME_TIME_GT = (
        Operand(
            TypeSpec(type=TypeSpec.DATETIME),
            OperatorEnum.GT,
            TypeSpec(type=TypeSpec.TIME),
        ),
        "时间晚于",
    )
    DATETIME_TIME_BETWEEN = (
        Operand(
            TypeSpec(type=TypeSpec.DATETIME),
            OperatorEnum.BETWEEN,
            TypeSpec(
                type=TypeSpec.COLLECTION,
                spec=[
                    TypeSpec(type=TypeSpec.TIME, name="min"),
                    TypeSpec(type=TypeSpec.TIME, name="max"),
                ],
            ),
        ),
        "时间在...之间",
    )

    DATE_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.DATE), OperatorEnum.EXISTS, None),
        "不为空",
    )
    DATE_NOT_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.DATE), OperatorEnum.NOT_EXISTS, None),
        "为空",
    )
    DATE_VALUE_UNIQUE = (
        Operand(TypeSpec(type=TypeSpec.DATE), OperatorEnum.VALUE_UNIQUE, None),
        "不重复",
    )
    DATE_VALUE_RECORDED = (
        Operand(TypeSpec(type=TypeSpec.DATE), OperatorEnum.VALUE_RECORDED, None),
        "重复",
    )
    DATE_EQ = (
        Operand(TypeSpec(type=TypeSpec.DATE), OperatorEnum.EQ, TypeSpec(type=TypeSpec.DATE)),
        "等于",
    )
    DATE_LT = (
        Operand(TypeSpec(type=TypeSpec.DATE), OperatorEnum.LT, TypeSpec(type=TypeSpec.DATE)),
        "早于",
    )
    DATE_GT = (
        Operand(TypeSpec(type=TypeSpec.DATE), OperatorEnum.GT, TypeSpec(type=TypeSpec.DATE)),
        "晚于",
    )
    DATE_BETWEEN = (
        Operand(
            TypeSpec(type=TypeSpec.DATE),
            OperatorEnum.BETWEEN,
            TypeSpec(
                type=TypeSpec.COLLECTION,
                spec=[
                    TypeSpec(type=TypeSpec.DATE, name="min"),
                    TypeSpec(type=TypeSpec.DATE, name="max"),
                ],
            ),
        ),
        "在...之间",
    )

    TIME_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.TIME), OperatorEnum.EXISTS, None),
        "不为空",
    )
    TIME_NOT_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.TIME), OperatorEnum.NOT_EXISTS, None),
        "为空",
    )
    TIME_VALUE_UNIQUE = (
        Operand(TypeSpec(type=TypeSpec.TIME), OperatorEnum.VALUE_UNIQUE, None),
        "不重复",
    )
    TIME_VALUE_RECORDED = (
        Operand(TypeSpec(type=TypeSpec.TIME), OperatorEnum.VALUE_RECORDED, None),
        "重复",
    )
    TIME_EQ = (
        Operand(TypeSpec(type=TypeSpec.TIME), OperatorEnum.EQ, TypeSpec(type=TypeSpec.TIME)),
        "等于",
    )
    TIME_LT = (
        Operand(TypeSpec(type=TypeSpec.TIME), OperatorEnum.LT, TypeSpec(type=TypeSpec.TIME)),
        "早于",
    )
    TIME_GT = (
        Operand(TypeSpec(type=TypeSpec.TIME), OperatorEnum.GT, TypeSpec(type=TypeSpec.TIME)),
        "晚于",
    )
    TIME_BETWEEN = (
        Operand(
            TypeSpec(type=TypeSpec.TIME),
            OperatorEnum.BETWEEN,
            TypeSpec(
                type=TypeSpec.COLLECTION,
                spec=[
                    TypeSpec(type=TypeSpec.TIME, name="min"),
                    TypeSpec(type=TypeSpec.TIME, name="max"),
                ],
            ),
        ),
        "在...之间",
    )

    COLLECTION_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.COLLECTION), OperatorEnum.EXISTS, None),
        "不为空",
    )
    COLLECTION_NOT_EXISTS = (
        Operand(TypeSpec(type=TypeSpec.COLLECTION), OperatorEnum.NOT_EXISTS, None),
        "为空",
    )
    COLLECTION_VALUE_UNIQUE = (
        Operand(TypeSpec(type=TypeSpec.COLLECTION), OperatorEnum.VALUE_UNIQUE, None),
        "不重复",
    )
    COLLECTION_VALUE_RECORDED = (
        Operand(TypeSpec(type=TypeSpec.COLLECTION), OperatorEnum.VALUE_RECORDED, None),
        "重复",
    )
    ARRAY_EXISTS = (
        Operand(
            TypeSpec(type=TypeSpec.ARRAY, spec=[TypeSpec(type=TypeSpec.ANY)]),
            OperatorEnum.EXISTS,
            None,
        ),
        "不为空",
    )
    ARRAY_NOT_EXISTS = (
        Operand(
            TypeSpec(type=TypeSpec.ARRAY, spec=[TypeSpec(type=TypeSpec.ANY)]),
            OperatorEnum.NOT_EXISTS,
            None,
        ),
        "为空",
    )
    ARRAY_VALUE_UNIQUE = (
        Operand(
            TypeSpec(type=TypeSpec.ARRAY, spec=[TypeSpec(type=TypeSpec.ANY)]),
            OperatorEnum.VALUE_UNIQUE,
            None,
        ),
        "不重复",
    )
    ARRAY_VALUE_RECORDED = (
        Operand(
            TypeSpec(type=TypeSpec.ARRAY, spec=[TypeSpec(type=TypeSpec.ANY)]),
            OperatorEnum.VALUE_RECORDED,
            None,
        ),
        "重复",
    )
    ARRAY_ALL = (
        Operand(
            TypeSpec(type=TypeSpec.ARRAY, spec=[TypeSpec(type=TypeSpec.ANY)]),
            OperatorEnum.ALL,
            None,
        ),
        "全部满足",
    )
    ARRAY_ANY = (
        Operand(
            TypeSpec(type=TypeSpec.ARRAY, spec=[TypeSpec(type=TypeSpec.ANY)]),
            OperatorEnum.ANY,
            None,
        ),
        "任意满足",
    )

    LEYAN_USER_EQ = (
        Operand(
            named_typespec.resource.LeyanUser.to_type_spec(),
            OperatorEnum.EQ,
            named_typespec.resource.LeyanUser.to_type_spec(),
        ),
        "等于",
    )

    def __new__(cls, value, label):
        self = object.__new__(cls)
        self._value_ = value
        self.label = label
        return self

    def __init__(self, operand, label):
        self.operand: Operand = operand
        self.label: str = label

    @classmethod
    def get_operands(cls, typespec: TypeSpec):
        """找到 typespec 支持的 operand"""
        return cast(
            list[OperandEnum],
            list(filter(lambda member: cls._typespec_match(member.operand.a, typespec), cls)),
        )

    @classmethod
    def get_by(cls, typespec: TypeSpec, operator: OperatorEnum, target: TypeSpec | None):
        filter_by_a = cls.get_operands(typespec)
        if not filter_by_a:
            raise ValueError(f"No operand found for {typespec}")
        filter_by_operator = []
        for each in filter_by_a:
            if each.operand.o == operator:
                filter_by_operator.append(each)
        if not filter_by_operator:
            raise ValueError(f"No operand found for {typespec} {operator}")
        filter_by_b = []
        for each in filter_by_operator:
            if cls._typespec_and_properties_match(each.operand.b, target):
                filter_by_b.append(each)
        if not filter_by_b:
            raise ValueError(f"No operand found for {typespec} {operator} {target}")
        if len(filter_by_b) > 1:
            raise ValueError(f"Multiple operands found for {typespec} {operator} {target}. match: {filter_by_b}")
        return filter_by_b[0]

    @staticmethod
    def _typespec_match(
        operand_typespec: TypeSpec | None,
        target_typespec: TypeSpec | None,
    ):
        if operand_typespec is None:
            return target_typespec is None
        elif target_typespec is None:
            return False
        if operand_typespec.typename is not None:
            return operand_typespec.typename == target_typespec.typename
        # 操作符仅提供 type 的声明，不提供 properties / items
        return operand_typespec.type == target_typespec.type

    @classmethod
    def _typespec_and_properties_match(
        cls,
        operand_typespec: TypeSpec | None,
        target_typespec: TypeSpec | None,
    ):
        if operand_typespec is None:
            return True
        if not cls._typespec_match(operand_typespec, target_typespec):
            return False
        if operand_typespec.type == TypeSpec.COLLECTION:
            for prop in operand_typespec.properties:
                if prop not in target_typespec.properties:  # type: ignore[union-attr]
                    return False
                if not cls._typespec_and_properties_match(
                    operand_typespec.properties[prop],
                    target_typespec.properties[prop],  # type: ignore[union-attr]
                ):
                    return False
        elif operand_typespec.type == TypeSpec.ARRAY:
            return cls._typespec_match(operand_typespec.items, target_typespec.items)  # type: ignore[union-attr]
        return True

    def to_pb(self):
        return pb_Operand(
            label=self.label,
            target=self.operand.a.to_pb(),
            operator=self.operand.o.pb_value,
            expect=self.operand.b.to_pb() if self.operand.b else None,
        )

    def evaluate(self, a: Any, b: Any) -> bool:
        """计算 a 和 b 的布尔结果"""

        def ensure_datetime(raw):
            if isinstance(raw, datetime):
                return raw
            if isinstance(raw, str):
                return datetime.strptime(raw, "%Y-%m-%d %H:%M:%S")
            return raw

        def ensure_date(raw):
            if isinstance(raw, date):
                return raw
            elif isinstance(raw, datetime):
                return raw.date()
            elif isinstance(raw, str):
                return datetime.strptime(raw, "%Y-%m-%d").date()
            return raw

        def ensure_time(raw):
            if isinstance(raw, time):
                return raw
            elif isinstance(raw, datetime):
                return raw.time()
            elif isinstance(raw, str):
                return datetime.strptime(raw, "%H:%M:%S").time()
            return raw

        allow_null = self.operand.o in [
            OperatorEnum.EXISTS,
            OperatorEnum.NOT_EXISTS,
            OperatorEnum.IS_TRUE,
            OperatorEnum.IS_FALSE,
            OperatorEnum.VALUE_UNIQUE,
            OperatorEnum.VALUE_RECORDED,
        ]
        if not allow_null:
            if a is None:
                return False
            if Value.ConstValue(self.operand.a, a).check().is_err():
                raise ValueError(f"{self} expect {self.operand.a}, got {a}")
            if self.operand.b is not None:
                if b is None:
                    return False
                elif Value.ConstValue(self.operand.b, b).check().is_err():
                    raise ValueError(f"{self} expect {self.operand.b}, got {b}")

        match self:
            case (
                OperandEnum.STRING_EXISTS
                | OperandEnum.NUMBER_EXISTS
                | OperandEnum.BOOLEAN_EXISTS
                | OperandEnum.DATETIME_EXISTS
                | OperandEnum.DATE_EXISTS
                | OperandEnum.TIME_EXISTS
            ):
                return a is not None

            case (
                OperandEnum.STRING_NOT_EXISTS
                | OperandEnum.NUMBER_NOT_EXISTS
                | OperandEnum.BOOLEAN_NOT_EXISTS
                | OperandEnum.DATETIME_NOT_EXISTS
                | OperandEnum.DATE_NOT_EXISTS
                | OperandEnum.TIME_NOT_EXISTS
            ):
                return a is None

            case OperandEnum.STRING_IS_EMPTY:
                return a == ""

            case OperandEnum.STRING_IS_NOT_EMPTY:
                return a != ""

            case (
                OperandEnum.STRING_VALUE_UNIQUE
                | OperandEnum.NUMBER_VALUE_UNIQUE
                | OperandEnum.BOOLEAN_VALUE_UNIQUE
                | OperandEnum.DATETIME_VALUE_UNIQUE
                | OperandEnum.DATE_VALUE_UNIQUE
                | OperandEnum.TIME_VALUE_UNIQUE
                | OperandEnum.COLLECTION_VALUE_UNIQUE
                | OperandEnum.ARRAY_VALUE_UNIQUE
            ):
                return not a

            case (
                OperandEnum.STRING_VALUE_RECORDED
                | OperandEnum.NUMBER_VALUE_RECORDED
                | OperandEnum.BOOLEAN_VALUE_RECORDED
                | OperandEnum.DATETIME_VALUE_RECORDED
                | OperandEnum.DATE_VALUE_RECORDED
                | OperandEnum.TIME_VALUE_RECORDED
                | OperandEnum.COLLECTION_VALUE_RECORDED
                | OperandEnum.ARRAY_VALUE_RECORDED
            ):
                return isinstance(a, set) and len(a) > 0

            case (OperandEnum.STRING_EQ | OperandEnum.NUMBER_EQ | OperandEnum.DATE_EQ | OperandEnum.TIME_EQ):
                return a == b

            case OperandEnum.DATETIME_DATE_EQ:
                return ensure_datetime(a).date() == b

            case OperandEnum.DATETIME_TIME_EQ:
                return ensure_datetime(a).time() == b

            case OperandEnum.STRING_EQ_ANY:
                return cast(str, a) in cast(list[str], b)

            case OperandEnum.STRING_NE_ANY:
                return cast(str, a) not in cast(list[str], b)

            case OperandEnum.STRING_CONTAINS:
                return cast(str, a) in cast(str, b)

            case OperandEnum.STRING_NOT_CONTAINS:
                return cast(str, a) not in cast(str, b)

            case OperandEnum.STRING_CONTAINS_ANY:
                return any([each in cast(str, a) for each in cast(list[str], b)])

            case OperandEnum.STRING_DISJOINT:
                return all([each not in cast(str, a) for each in cast(list[str], b)])

            case OperandEnum.STRING_CONTAINS_ALL:
                return all([each in cast(str, a) for each in cast(list[str], b)])

            case OperandEnum.NUMBER_GT:
                return a > b

            case OperandEnum.NUMBER_GTE:
                return a >= b

            case OperandEnum.NUMBER_LT:
                return a < b

            case OperandEnum.NUMBER_LTE:
                return a <= b

            case OperandEnum.DATETIME_LT:
                return ensure_datetime(a) < ensure_datetime(b)

            case OperandEnum.DATETIME_GT:
                return ensure_datetime(a) > ensure_datetime(b)

            case (
                OperandEnum.DATETIME_BETWEEN
                | OperandEnum.DATE_BETWEEN
                | OperandEnum.TIME_BETWEEN
                | OperandEnum.NUMBER_BETWEEN
            ):
                cast_value = {
                    OperandEnum.DATETIME_BETWEEN: ensure_datetime,
                    OperandEnum.DATE_BETWEEN: ensure_date,
                    OperandEnum.TIME_BETWEEN: ensure_time,
                    OperandEnum.NUMBER_BETWEEN: lambda x: x,
                }[self]
                if b["min"] is None and b["max"] is not None:
                    return cast_value(a) <= cast_value(b["max"])
                elif b["min"] is not None and b["max"] is None:
                    return cast_value(a) >= cast_value(b["min"])
                elif b["min"] is not None and b["max"] is not None:
                    return cast_value(b["min"]) <= cast_value(a) <= cast_value(b["max"])
                else:
                    raise ValueError("Invalid operand")

            case OperandEnum.DATETIME_DATE_LT:
                return ensure_datetime(a).date() < ensure_date(b)

            case OperandEnum.DATETIME_DATE_GT:
                return ensure_datetime(a).date() > ensure_date(b)

            case OperandEnum.DATETIME_DATE_BETWEEN:
                if b["min"] is None and b["max"] is not None:
                    return ensure_datetime(a).date() <= ensure_date(b["max"])
                elif b["min"] is not None and b["max"] is None:
                    return ensure_datetime(a).date() >= ensure_date(b["min"])
                elif b["min"] is not None and b["max"] is not None:
                    return ensure_date(b["min"]) <= ensure_datetime(a).date() <= ensure_date(b["max"])
                else:
                    raise ValueError("Invalid operand")

            case OperandEnum.DATETIME_TIME_LT:
                return ensure_datetime(a).time() < ensure_time(b)

            case OperandEnum.DATETIME_TIME_GT:
                return ensure_datetime(a).time() > ensure_time(b)

            case OperandEnum.DATE_LT:
                return ensure_date(a) < ensure_date(b)

            case OperandEnum.DATE_GT:
                return ensure_date(a) > ensure_date(b)

            case OperandEnum.TIME_LT:
                return ensure_time(a) < ensure_time(b)

            case OperandEnum.TIME_GT:
                return ensure_time(a) > ensure_time(b)

            case OperandEnum.BOOLEAN_IS_TRUE:
                return a is True

            case OperandEnum.BOOLEAN_IS_FALSE:
                return a is False

            case OperandEnum.ARRAY_EXISTS:
                return a is not None

            case OperandEnum.ARRAY_NOT_EXISTS:
                return a is None

            case OperandEnum.ARRAY_ALL:
                return all(a)

            case OperandEnum.ARRAY_ANY:
                return any(a)

            case _:
                raise NotImplementedError(
                    "{a_type} {op} {b_type} not implemented".format(
                        a_type=self.operand.a.type,
                        op=self.operand.o,
                        b_type=self.operand.b.type if self.operand.b else None,
                    )
                )

    def to_sql_criteria(self, a: QueryableAttribute | None, b: Any) -> BinaryExpression[bool]:
        """生成 SQL 查询条件"""
        from sqlalchemy import and_
        from sqlalchemy import false
        from sqlalchemy import or_
        from sqlalchemy.sql import func
        from sqlalchemy.sql.operators import eq as sql_eq
        from sqlalchemy.sql.operators import ge as sql_ge
        from sqlalchemy.sql.operators import gt as sql_gt
        from sqlalchemy.sql.operators import le as sql_le
        from sqlalchemy.sql.operators import lt as sql_lt
        from sqlalchemy.sql.operators import ne as sql_ne

        allow_null = self.operand.o in [OperatorEnum.EXISTS, OperatorEnum.NOT_EXISTS]
        if (
            not isinstance(b, QueryableAttribute)
            and self.operand.b is not None
            and Value.ConstValue(self.operand.b, b).check(allow_null).is_err()
        ):
            raise ValueError(f"{self} expect {self.operand.b}, got {b}")

        if a is None:
            return false()  # type: ignore[return-value]

        match self:
            case (
                OperandEnum.STRING_EXISTS
                | OperandEnum.NUMBER_EXISTS
                | OperandEnum.BOOLEAN_EXISTS
                | OperandEnum.DATETIME_EXISTS
                | OperandEnum.DATE_EXISTS
                | OperandEnum.TIME_EXISTS
            ):
                return a.isnot(None)

            case (
                OperandEnum.STRING_NOT_EXISTS
                | OperandEnum.NUMBER_NOT_EXISTS
                | OperandEnum.BOOLEAN_NOT_EXISTS
                | OperandEnum.DATETIME_NOT_EXISTS
                | OperandEnum.DATE_NOT_EXISTS
                | OperandEnum.TIME_NOT_EXISTS
            ):
                return a.is_(None)

            case (OperandEnum.STRING_EQ | OperandEnum.NUMBER_EQ | OperandEnum.DATE_EQ | OperandEnum.TIME_EQ):
                return sql_eq(a, b)

            case OperandEnum.DATETIME_DATE_EQ:
                return sql_eq(func.date(a), cast(datetime | QueryableAttribute, b))

            case OperandEnum.DATETIME_TIME_EQ:
                return sql_eq(func.time(a), cast(time | QueryableAttribute, b))

            case OperandEnum.STRING_EQ_ANY:
                return or_(sql_eq(a, match_string) for match_string in b)  # type: ignore[arg-type, return-value]

            case OperandEnum.STRING_NE_ANY:
                return and_(sql_ne(a, exclude_string) for exclude_string in b)  # type: ignore[arg-type, return-value]

            case OperandEnum.STRING_CONTAINS:
                return a.like(f"%{b}%")

            case OperandEnum.STRING_NOT_CONTAINS:
                return a.notlike(f"%{b}%")

            case OperandEnum.STRING_CONTAINS_ANY:
                return or_(a.like(f"%{each}%") for each in b)  # type: ignore[arg-type, return-value]

            case OperandEnum.STRING_DISJOINT:
                return and_(a.notlike(f"%{each}%") for each in b)  # type: ignore[arg-type, return-value]

            case OperandEnum.STRING_CONTAINS_ALL:
                return and_(a.like(f"%{each}%") for each in b)  # type: ignore[arg-type, return-value]

            case (OperandEnum.NUMBER_GT | OperandEnum.DATETIME_GT | OperandEnum.DATE_GT | OperandEnum.TIME_GT):
                return sql_gt(a, b)

            case OperandEnum.DATETIME_DATE_GT:
                return sql_gt(func.date(a), b)

            case OperandEnum.DATETIME_TIME_GT:
                return sql_gt(func.time(a), b)

            case OperandEnum.NUMBER_GTE:
                return sql_ge(a, b)

            case (OperandEnum.NUMBER_LT | OperandEnum.DATETIME_LT | OperandEnum.DATE_LT | OperandEnum.TIME_LT):
                return sql_lt(a, b)

            case OperandEnum.DATETIME_DATE_LT:
                return sql_lt(func.date(a), b)

            case OperandEnum.DATETIME_TIME_LT:
                return sql_lt(func.time(a), b)

            case OperandEnum.NUMBER_LTE:
                return sql_le(a, b)

            case (
                OperandEnum.NUMBER_BETWEEN
                | OperandEnum.DATETIME_BETWEEN
                | OperandEnum.DATE_BETWEEN
                | OperandEnum.TIME_BETWEEN
            ):
                if b["min"] is None and b["max"] is not None:
                    return sql_le(a, b["max"])
                elif b["min"] is not None and b["max"] is None:
                    return sql_ge(a, b["min"])
                elif b["min"] is not None and b["max"] is not None:
                    return and_(sql_ge(a, b["min"]), sql_le(a, b["max"]))  # type: ignore[return-value]
                else:
                    raise ValueError(f"Invalid operand value b: {b}")

            case OperandEnum.DATETIME_DATE_BETWEEN:
                if b["min"] is None and b["max"] is not None:
                    return sql_le(func.date(a), b["max"])
                elif b["min"] is not None and b["max"] is None:
                    return sql_ge(func.date(a), b["min"])
                elif b["min"] is not None and b["max"] is not None:
                    return and_(  # type: ignore[return-value]
                        sql_ge(func.date(a), b["min"]), sql_le(func.date(a), b["max"])
                    )
                else:
                    raise ValueError(f"Invalid operand value b: {b}")

            case OperandEnum.DATETIME_TIME_BETWEEN:
                if b["min"] is None and b["max"] is not None:
                    return sql_le(func.time(a), b["max"])
                elif b["min"] is not None and b["max"] is None:
                    return sql_ge(func.time(a), b["min"])
                elif b["min"] is not None and b["max"] is not None:
                    return and_(  # type: ignore[return-value]
                        sql_ge(func.time(a), b["min"]), sql_le(func.time(a), b["max"])
                    )
                else:
                    raise ValueError(f"Invalid operand value b: {b}")

            case _:
                raise NotImplementedError(
                    "{a_type} {op} {b_type} not implemented".format(
                        a_type=self.operand.a.type,
                        op=self.operand.o,
                        b_type=self.operand.b.type if self.operand.b else None,
                    )
                )


def FilterContextField(title: str, get_enum_option: Callable[[dict], list[EnumOption]] | None = None):
    return Field(None, title=title, get_enum_option=get_enum_option)


class FilterContextMixin(BaseModel):
    __name__: str

    @classmethod
    def to_namespace(cls, context: dict | None):
        from robot_processor.symbol_table._model_converter import field_to_typespec

        namespace = Namespace(name=cls.__name__, symbols=[])
        for field_name in cls.__fields__:
            field = cls.__fields__[field_name]
            typespec = field_to_typespec(field)
            symbol = Symbol(type_spec=typespec, name=field_name, label=field.field_info.title)
            if get_enum_option := field.field_info.extra["get_enum_option"]:
                symbol.options.enum = get_enum_option(context)
            namespace.symbols.append(symbol)

        return namespace


def query_filter_to_sql_criteria(query_filter: pb_Filter, resolver: SQLCriteriaResolver):
    from sqlalchemy import and_
    from sqlalchemy import or_

    from robot_processor.symbol_table.assembler import type_spec_from_pb
    from robot_processor.utils import message_to_dict

    where_clauses = []
    relation = or_ if query_filter.relation == "or" else and_
    for condition in query_filter.conditions:
        field_name = condition.a.var.path
        field_typespec = type_spec_from_pb(condition.a.type_spec)
        o = OperatorEnum.from_pb(condition.o[0]).value
        if condition.HasField("b"):
            b = message_to_dict(condition.b.const)["value"]
        else:
            b = None

        where_clauses.append(resolver.resolve(field_typespec, o, b, name=field_name))
    return relation(*where_clauses)
