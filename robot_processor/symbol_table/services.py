from dataclasses import dataclass
from functools import wraps
from typing import TYPE_CHECKING
from typing import Callable

import robot_types.core
import robot_types.helper
from google.protobuf.internal.containers import RepeatedScalarFieldContainer
from google.protobuf.json_format import ParseDict
from leyan_proto.digismart.robot.symbol_table_pb2 import Filter as pb_Filter
from leyan_proto.digismart.robot.symbol_table_pb2 import Operand as pb_Operand
from leyan_proto.digismart.robot_web import symbol_table_pb2_grpc
from leyan_proto.digismart.robot_web.symbol_table_pb2 import ListTypeSpecOperatorsResponse
from pydantic import BaseModel
from result import Err
from result import Ok
from sqlalchemy import BooleanClauseList
from sqlalchemy.orm import QueryableAttribute

from robot_processor.symbol_table.filters import OperandEnum
from robot_processor.symbol_table.filters import OperatorEnum
from robot_processor.symbol_table.models import TypeSpec
from robot_processor.symbol_table.models import Value
from robot_processor.utils import unquote_jmespath


class SymbolTableServicer(symbol_table_pb2_grpc.SymbolTableServicer):
    def ListTypeSpecOperators(self, request, context):
        type_spec = TypeSpec.from_pb(request.value.type_spec)
        response = ListTypeSpecOperatorsResponse(
            succeed=True,
            data=ListTypeSpecOperatorsResponse.Data(operators=FilterServicer.list_typespec_operands(type_spec)),
        )
        return response


class Condition(BaseModel):
    """一个筛选(Filter)是由若干个条件表达式(Condition)和条件表达式的关系(Relation: and/or)构成"""

    a: Value
    o: list[str]
    b: Value | None

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        kwargs["exclude_none"] = True
        return super().dict(**kwargs)


class FilterServicer:
    """提供了一系列的 Value 相关的筛选方法
    支持:
        对提供的 TypeSpec 给出支持的操作符列表 (list_typespec_operands)
        计算 Filter 的结果 (evaluate)

    """

    @classmethod
    def list_typespec_operands(cls, typespec: TypeSpec) -> list[pb_Operand]:
        """获取所有操作符"""
        enum_operands = OperandEnum.get_operands(typespec)

        if typespec.type == TypeSpec.ARRAY and typespec.items.type != TypeSpec.ARRAY:
            # 是否是末级 array 标识，影响支持的操作符
            # 在渲染支持的操作符时，非末级的 array 仅支持 ALL 和 ANY 操作符，引导用户选到最后一层
            end_flag = typespec.items.type != TypeSpec.ARRAY

            # 如果是数组，递归获取数组元素的操作符
            cascade = cls.list_typespec_operands(typespec.items)
            operands = []
            for enum_operand in enum_operands:
                operand = enum_operand.to_pb()
                # 仅有 ARRAY_ALL 和 ARRAY_ANY 需要支持 cascade
                if enum_operand in [OperandEnum.ARRAY_ALL, OperandEnum.ARRAY_ANY]:
                    operand.cascade.extend(cascade)
                    operands.append(operand)
                elif end_flag:
                    operands.append(operand)
        else:
            operands = [enum_operand.to_pb() for enum_operand in enum_operands]

        return operands

    @classmethod
    def evaluate_with_detail(
        cls,
        filter_: pb_Filter,
        context: dict | None = None,
        value_unique_res: dict | None = None,
        reverse: bool = False,  # 反转结果，满足 condition 的为失败
    ):
        value_unique_res = value_unique_res or dict()

        @dataclass
        class NormalizedOperator:
            operand: OperandEnum
            cascade_operators: list[OperatorEnum]

        def normalize_operator(a: Value, o: OperatorEnum | list[OperatorEnum], b: Value | None) -> NormalizedOperator:
            if isinstance(o, OperatorEnum):
                operator, cascade = o, []
            elif isinstance(o, (list, RepeatedScalarFieldContainer)):
                operator, cascade = o[0], o[1:]
            else:
                raise TypeError(f"Unsupported type: {type(o)}")

            operand = OperandEnum.get_by(a.type_spec, operator, b.type_spec if b else None)
            return NormalizedOperator(operand=operand, cascade_operators=cascade)

        def evaluate_condition(a: Value, o: OperatorEnum | list[OperatorEnum], b: Value | None):
            normalized = normalize_operator(a, o, b)
            operand, cascade_operators = (
                normalized.operand,
                normalized.cascade_operators,
            )
            match operand:
                case OperandEnum.ARRAY_ALL | OperandEnum.ARRAY_ANY:
                    raw_a = [
                        evaluate_condition(a_item, cascade_operators, b) for a_item in a.get_array_item_value_list()
                    ]
                    raw_b = None
                case _:
                    match operand.operand.o:
                        case OperatorEnum.VALUE_UNIQUE | OperatorEnum.VALUE_RECORDED:
                            raw_a = value_unique_res.get(unquote_jmespath(a.var.path), set())  # type: ignore
                            raw_b = None
                        case _:
                            raw_a, raw_b = (
                                a.get_raw().expect("evaluate_condition(a.get_raw)"),
                                (b.get_raw().expect("evaluate_condition(b.get_raw)") if b else None),
                            )
            return operand.evaluate(raw_a, raw_b)

        if not filter_.conditions:
            return Ok(None)

        condition_results = [
            evaluate_condition(
                Value.from_pb(condition.a).context(context),
                list(map(OperatorEnum.from_pb, condition.o)),
                Value.from_pb(condition.b) if condition.HasField("b") else None,
            )
            for condition in filter_.conditions
        ]
        if reverse:
            condition_results = [not result for result in condition_results]
        if (filter_.relation.lower() == "or" and any(condition_results)) or all(condition_results):
            return Ok(None)
        errors: list[dict] = [
            dict(loc=loc, details=None) for loc, if_satisfied in enumerate(condition_results) if not if_satisfied
        ]
        for error in errors:
            condition = filter_.conditions[error["loc"]]
            if condition.o[0] in [
                OperatorEnum.VALUE_UNIQUE.pb_value,
                OperatorEnum.VALUE_RECORDED.pb_value,
            ]:
                error["details"] = list(value_unique_res.get(unquote_jmespath(condition.a.var.path), []))
        return Err(errors)

    @classmethod
    def evaluate(
        cls,
        filter_: pb_Filter,
        context: dict | None = None,
        value_unique_res: dict | None = None,
    ) -> bool:
        return cls.evaluate_with_detail(filter_, context, value_unique_res).is_ok()

    @classmethod
    def to_sql_criteria(cls, filter_: pb_Filter, get_column: Callable[[str], QueryableAttribute]) -> BooleanClauseList:
        from sqlalchemy import and_
        from sqlalchemy import or_
        from sqlalchemy import true

        def normalize_operator(a: Value, o: OperatorEnum | list[OperatorEnum], b: Value | None) -> OperandEnum:
            if isinstance(o, OperatorEnum):
                operator = o
            elif isinstance(o, (list, RepeatedScalarFieldContainer)):
                operator = o[0]
            else:
                raise TypeError(f"Unsupported type: {type(o)}")

            return OperandEnum.get_by(a.type_spec, operator, b.type_spec if b else None)

        def evaluate_condition(a: Value, o: OperatorEnum | list[OperatorEnum], b: Value | None):
            operand = normalize_operator(a, o, b)
            a_column = get_column(unquote_jmespath(a.var.path))  # type: ignore[union-attr]
            if b is None:
                b_column = None
            elif b.qualifier == Value.VAR:
                b_column = get_column(unquote_jmespath(b.var.path))  # type: ignore[union-attr]
            elif b.qualifier == Value.CONST:
                b_column = b.get_raw().expect("evaluate_condition(b_column)")
            else:
                raise NotImplementedError(f"Unsupported qualifier: {b.qualifier}")
            return operand.to_sql_criteria(a_column, b_column)

        if not filter_.conditions:
            return true()  # type: ignore[return-value]
        if filter_.relation.lower() == "or":
            relation = or_
        else:
            relation = and_  # type: ignore[assignment]
        return relation(  # type: ignore[return-value]
            *[
                evaluate_condition(
                    Value.from_pb(condition.a),
                    OperatorEnum.from_pb(condition.o[0]),
                    Value.from_pb(condition.b) if condition.HasField("b") else None,
                )
                for condition in filter_.conditions
            ]
        )

    @classmethod
    def _normalize_condition(cls, condition):
        condition["o"] = [item_o["operator"] if isinstance(item_o, dict) else item_o for item_o in condition["o"]]
        return condition

    @classmethod
    def _normalize_term(cls, condition):
        return Condition.validate(condition).dict()

    @classmethod
    def to_pb_from_dict(cls, raw: dict):
        filter_dict = raw.copy()
        filter_dict["conditions"] = list(map(cls._normalize_condition, filter_dict.get("conditions", [])))

        return ParseDict(filter_dict, pb_Filter(), ignore_unknown_fields=True)

    if TYPE_CHECKING:
        from robot_processor.form.schemas import StepValidationRule

    @classmethod
    def to_pb_from_step_validation(cls, validation_rule: "StepValidationRule"):
        obj = dict(
            relation=validation_rule.relation,
            conditions=validation_rule.dict()["filters"],
        )
        obj["conditions"] = list(map(cls._normalize_condition, obj["conditions"]))
        obj["conditions"] = list(map(cls._normalize_term, obj["conditions"]))
        return ParseDict(obj, pb_Filter(), ignore_unknown_fields=True)

    @staticmethod
    def filter_specified_condition(filter_: pb_Filter, field: str):
        conditions: list[pb_Filter.Condition] = []
        for filter_condition in filter_.conditions:
            if filter_condition.a.WhichOneof("value") != "var":
                continue
            if filter_condition.a.var.path != field:
                continue
            conditions.append(filter_condition)
        return conditions

    @staticmethod
    def evaluate_predicate(predicate: robot_types.core.Predicate, context: dict) -> bool:
        value = robot_types.core.Value(type_spec=robot_types.core.TypeSpec("boolean"), predicate=predicate)
        result = value.with_resolver(robot_types.helper.ValueResolver(context)).resolve()
        return result.is_ok() and result.unwrap()
