from functools import singledispatch
from typing import TYPE_CHECKING

from robot_types.core import Condition
from robot_types.core import Const
from robot_types.core import Filter
from robot_types.core import Scope
from robot_types.core import Value
from robot_types.core import Var
from robot_types.helper.operator_resolver import Operator
from robot_types.helper.operator_resolver import OperatorRegistry
from robot_types.helper.operator_resolver import OperatorResolver

if TYPE_CHECKING:
    from robot_processor.business_order.business_order_manager import FormValidator


@singledispatch
def to_log(val, **kwargs) -> str:
    from json import dumps

    def truncate(value):
        max_str_length = kwargs.get("max_str_length", 100)
        if isinstance(value, str) and len(value) > max_str_length:
            return value[:max_str_length] + "..."
        elif isinstance(value, dict):
            return {k: truncate(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [truncate(v) for v in value]
        else:
            return value

    # fallback
    return dumps(truncate(val), ensure_ascii=False, default=repr)


@to_log.register
def const_to_log(val: Const, **kwargs):
    from json import dumps

    return dumps(val.value, ensure_ascii=False, default=repr)


@to_log.register
def var_to_log(val: Var, **kwargs):
    if "form_validator" in kwargs and kwargs.get("in_each_scope"):
        symbol_table = kwargs["form_validator"].symbol_table
        scope = kwargs["form_validator"].current_scope
        symbol = symbol_table.lookup(val.path, scope=scope)
        if symbol:
            return f"> {symbol.label}"
    return f"${val.path}"


@to_log.register
def condition_to_log(
    val: Condition,
    *,
    operator_registry: OperatorRegistry | None = None,
    with_left=True,
    **kwargs,
):
    op_str = operator_to_log(
        val.o,
        operators=OperatorResolver(operator_registry or OperatorRegistry()).resolve(
            val.a.type_spec, val.a.var.path if val.a.qualifier == "var" else None
        ),
    )
    if val.o == ["each"] and "form_validator" in kwargs:
        try:
            kwargs = kwargs.copy()
            form_validator: "FormValidator" = kwargs["form_validator"].copy()
            a_symbol = form_validator.symbol_table.lookup(val.a.var.path, scope=form_validator.current_scope)
            each_op_scope = Scope(id=val.a.var.path, parent=form_validator.current_scope.id)
            form_validator.symbol_table.add_scope(each_op_scope)
            form_validator.symbol_table.add_symbols(each_op_scope, a_symbol.children[0].children)
            form_validator.current_scope = each_op_scope
            kwargs["form_validator"] = form_validator
            kwargs["in_each_scope"] = True
        except Exception:
            pass
    right = "" if val.b is None else to_log(val.b, **kwargs)
    if with_left:
        return "{left} {o} {right}".format(left=to_log(val.a, **kwargs), o=op_str, right=right)
    else:
        return "{o} {right}".format(o=op_str, right=right)


@to_log.register
def filter_to_log(val: Filter, *, indent=0, **kwargs):
    # 如果只有一个条件，直接返回该条件的格式化结果
    if len(val.conditions) == 1:
        return to_log(val.conditions[0], **kwargs)

    indent_str = "  " * indent
    next_indent_str = "  " * (indent + 1)
    formatted_conditions = []
    for i, condition in enumerate(val.conditions):
        prefix = "" if i == 0 else f"{val.relation} "
        if isinstance(condition, Filter):
            condition_str = filter_to_log(condition, indent=indent + 1, **kwargs)
        else:
            condition_str = to_log(condition, **kwargs)
        formatted_conditions.append(f"{next_indent_str}{prefix}{condition_str}")
    return f"(\n{chr(10).join(formatted_conditions)}\n{indent_str})"


@to_log.register
def value_to_log(val: Value, **kwargs):
    match val.qualifier:
        case "var":
            return to_log(val.var, **kwargs)
        case "const":
            return to_log(val.const, **kwargs)
        case "predicate":
            return to_log(val.predicate, **kwargs)
        case "fn":
            return "fn"
    return repr(val)


# 因为 singledispatch 不支持 generic type ，就不做 to_log.register 了
def operator_to_log(val: list, operators: dict[str, Operator]):
    op_str_list: list[str] = []
    for op in val:
        op_def = operators.get(op)
        if op_def is None:
            op_str_list.append(op)
        else:
            op_str_list.append(op_def.label)
            operators = op_def.cascade or {}
    return "/".join(op_str_list)
