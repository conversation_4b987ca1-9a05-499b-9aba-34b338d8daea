import typing

from leyan_logging import context
from leyan_logging.context import LoggingContextSetter  # noqa, 保留此 import，以免需要修改其他 module 的 import
from leyan_logging.context import Sid  # noqa, 保留此 import，以免需要修改其他 module 的 import
from leyan_logging.context import SimpleContextVar
from leyan_logging.context import SqlSource  # noqa, 保留此 import，以免需要修改其他 module 的 import
from leyan_logging.context import TracingBaggageContextVar
from leyan_logging.context import User
from leyan_logging.context import bind_logger_info  # noqa, 保留此 import，以免需要修改其他 module 的 import

if typing.TYPE_CHECKING:
    import robot_processor.business_order.models


class JobContextKey(LoggingContextSetter):
    def set(self, job: "robot_processor.business_order.models.Job"):
        from robot_processor.enums import StepType

        match job.raw_step_type:
            case StepType.auto:
                job_task_type = job.task_type
            case _ as step_type:
                job_task_type = step_type.name
        context.update(
            job_name=job.job_name,
            job_task_type=job_task_type,
        )
        BusinessOrderId.set(job.business_order_id)
        JobId.set(job.id)


class DramatiqMessageKey(LoggingContextSetter):
    def set(self, message):
        context.update(message_id=message.message_id, actor_name=message.actor_name)


BusinessOrderId = TracingBaggageContextVar("business_order_id")
JobId = TracingBaggageContextVar("job_id")
LoginUserNick = User
OrgId = SimpleContextVar("org_id")
Job = JobContextKey()
JobTaskType = SimpleContextVar("job_task_type")
DramatiqMessage = DramatiqMessageKey()
KafkaTopic = SimpleContextVar("topic")
FormId = SimpleContextVar("form_id")
StepId = SimpleContextVar("step_id")

ErpType = SimpleContextVar("erp_type")
ErpAccount = SimpleContextVar("erp_account")
Event = SimpleContextVar("event")
RefundId = SimpleContextVar("refund_id")
Tid = SimpleContextVar("tid")

TCronJobName = SimpleContextVar("tcron_job_name")

InvoiceWorkflow = SimpleContextVar("invoice_workflow_id")
InvoiceRequest = SimpleContextVar("invoice_request_id")
