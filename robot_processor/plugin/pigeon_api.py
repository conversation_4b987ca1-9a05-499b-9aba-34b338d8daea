import jwt
from flask import Blueprint, current_app
from loguru import logger
from pydantic import BaseModel, Field
from result import Err, Ok

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.client import kiosk_client
from robot_processor.enums import ChannelType, Creator, ShopStatus
from robot_processor.shop.models import Shop
from robot_processor.users.models import PlatformUser, LeyanUser, PlatformUserMapping
from robot_processor.utils import GenericResponse, unwrap_optional, omit_none_fields_before_validate
from robot_processor.validator import validate
from rpa.doudian import doudian_openapi_pigeon_client, schemas as doudian_schemas

api = Blueprint("pigeon-plugin-api", __name__)


@api.route("/goto")
def pigeon_shop_grant():
    """测试环境保留一个版本，下个版本切换到 shop_grant_api.doudian_shop_grant"""
    from robot_processor.shop.shop_grant_api import doudian_shop_grant

    return doudian_shop_grant("pigeon")


class PigeonSessionInfoQuery(BaseModel):
    sid: str = Field(description="店铺ID")
    token: str = Field(description="飞鸽 token")


@omit_none_fields_before_validate("platform", "isExpired")
class PigeonSessionInfo(BaseModel):
    nick: str = Field(description="平台账号昵称")
    sid: str = Field(description="店铺 ID")
    platform: str = Field(ChannelType.DOUDIAN.name, description="店铺平台")
    isExpired: bool = Field(False, description="店铺授权是否过期")
    assistant_info: AccountDetailV2 | None = Field(
        None, description="乐言账号，如果为 None 说明没有绑定"
    )
    token: str = Field(description="飞梭 token")
    shop_title: str | None = Field(description="店铺名称")


PigeonSessionInfoResponse = GenericResponse[PigeonSessionInfo]


@api.get("/session_info")
@validate
def get_pigeon_session_info(query: PigeonSessionInfoQuery) -> PigeonSessionInfoResponse:
    """获取抖音侧边栏客服 session 的信息"""
    shop: Shop | None = (
        Shop.query.filter(Shop.sid == query.sid)
        .filter(Shop.platform == ChannelType.DOUDIAN.name)
        .first()
    )
    if not shop:
        return PigeonSessionInfoResponse.Failed("店铺不存在")
    contract = shop.contract
    if (grant_record := shop.get_pigeon_grant_record()) is None:
        return PigeonSessionInfoResponse.Failed("店铺飞鸽授权信息不存在")
    access_token = grant_record.access_token

    pigeon_request = doudian_schemas.PigeonTokenExchangeParam(token=query.token)
    pigeon_res = doudian_openapi_pigeon_client.pigeon_token_exchange(pigeon_request, access_token)
    if pigeon_res.is_err():
        logger.opt(exception=pigeon_res.err()).warning("pigeon token exchange failed.")
        return PigeonSessionInfoResponse.Failed("飞鸽 token 校验失败")
    pigeon_response = unwrap_optional(pigeon_res.ok())

    platform_user_name = pigeon_response.login_param.customer_service_name
    session_info = PigeonSessionInfo(
        nick=platform_user_name,
        sid=shop.sid,
        platform=shop.platform,
        isExpired=bool(contract and contract.is_expired),
        assistant_info=None,
        token="",
        shop_title=shop.title,
    )
    token_res = kiosk_client.get_jwt_for_user(platform_user_name, session_info.sid, session_info.platform)
    match token_res:
        case Err(error_message):
            logger.warning("get jwt for user failed: {}", error_message)
        case Ok(jwt_payload):
            token = jwt.encode(jwt_payload.dict(), current_app.config["JWT_KEY"], algorithm="HS256")
            session_info.token = token
            session_info.assistant_info = AccountDetailV2(user_id=jwt_payload.user_id, user_nick=jwt_payload.nick_name,
                                                          user_type=Creator.LEYAN, phone=jwt_payload.phone_number)

    return PigeonSessionInfoResponse.Success(session_info)


class BindPigeonUserBody(BaseModel):
    sid: str = Field(description="店铺 ID")
    user_nick: str = Field(description="当前登录账号的昵称")
    mobile: str = Field(description="手机号")
    verify_code: str = Field(description="验证码")


BindPigeonUserResponse = GenericResponse[str]


@api.post("/bind_user")
@validate
def bind_pigeon_user(body: BindPigeonUserBody) -> BindPigeonUserResponse:
    """子账号不存在绑定的乐言账号时，通过手机号码和乐言账号进行绑定"""
    shop: Shop | None = (
        Shop.query.filter(Shop.sid == body.sid)
        .filter(Shop.platform == ChannelType.DOUDIAN.name)
        .filter(Shop.status == ShopStatus.ENABLE)
        .first()
    )
    if not shop:
        return BindPigeonUserResponse.Failed("店铺不存在")
    shop = unwrap_optional(shop)
    if not shop.channel_id:
        return BindPigeonUserResponse.Failed("未找到店铺的合同信息，请联系销售进行开店")

    platform_user = PlatformUser.find_or_create_doudian_user(shop, body.user_nick)
    if not (leyan_user := LeyanUser.find_by_phone(body.mobile)):
        return BindPigeonUserResponse.Failed("手机号码未绑定")

    if (
        res := kiosk_client.verify_code(body.mobile, body.verify_code)
    ) and res.is_err():
        return BindPigeonUserResponse.Failed(res.err())
    if (
        res := PlatformUserMapping.bind(platform_user, leyan_user, shop)  # type: ignore[assignment]
    ) and res.is_err():
        return BindPigeonUserResponse.Failed(res.err())

    return BindPigeonUserResponse.Success("success")
