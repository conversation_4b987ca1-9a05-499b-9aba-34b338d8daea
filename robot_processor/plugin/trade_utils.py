"""
订单接口工具类
https://leyan.yuque.com/digismart/lqmmke/icdezbxn63h94zng#dKe6B
"""

import concurrent.futures
import dataclasses
from typing import List
from typing import cast

import arrow
from google.protobuf.json_format import MessageToDict
from loguru import logger
from result import Err
from result import Ok

from robot_processor.client import trade_client
from robot_processor.enums import ErpType
from robot_processor.error.client_request import JstRequestError
from robot_processor.logging import to_log
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.models import Shop
from robot_processor.utils import message_to_dict
from robot_processor.utils import unwrap_optional
from rpa.erp.guanyiyunapi.schemas import GyyOrder
from rpa.erp.guanyiyunapi.sdk import GyyQmSDK
from rpa.erp.jackyun.schemas import JackyunOrder
from rpa.erp.jackyun.sdk import JackyunQmSDK
from rpa.erp.jackyun.sdk import JackyunSDK
from rpa.erp.jackyun.utils import filter_orders
from rpa.erp.jst import JstBaseOrder, OpenOrdersOutSimpleQuery, \
    OutOrdersQueryResp
from rpa.erp.jst import JstNewSDK
from rpa.erp.jst import JstQmSDK
from rpa.erp.jst import schemas as jst_schemas
from rpa.erp.kuaimai import KuaimaiOrder
from rpa.erp.kuaimai import KuaimaiQmSDK
from rpa.erp.wdt import StockoutOrderQueryTradeResp
from rpa.erp.wdt import Trade as WdtTrade
from rpa.erp.wdt import WdtClient
from rpa.erp.wdtulti import Pager
from rpa.erp.wdtulti import TradeQueryParams
from rpa.erp.wdtulti import WdtultiOrderModel
from rpa.erp.wdtulti import WdtUltiQM
from rpa.erp.wln import Order as WlnOrder
from rpa.erp.wln import WlnClient
from rpa.erp.wln.utils import add_goods_code


class ErrorCode:
    wdt01 = "wdt01"  # 旺店通openapi有限流
    can_not_find = "can_not_find"  # 订单不存在
    not_support = "not_support"  # 不支持的erp平台


class ErpTradeFindException(BaseException):
    def __init__(self, code):
        self.code = code


@dataclasses.dataclass()
class BatchInfo:
    """
    出库批次
    """

    batch_no: str = ""
    sku_id: str = ""


@dataclasses.dataclass()
class ErpSubTradeInfo:
    """
    子订单信息，一个子订单一个商品，拼多多平台无子订单概念
    """

    oid: str = ""  # 子订单号
    title: str = ""
    outer_title: str = ""
    sku_description: str = ""  # 商品描述
    pic_path: str = ""
    sku_id: str = ""
    outer_sku_id: str = ""
    spu_id: str = ""
    outer_spu_id: str = ""
    quantity: int = 0
    payment: float = 0.0
    price: float = 0.0
    suite_sku_id: str = ""  # 所属组合商品的sku_id
    batch_list: List[BatchInfo] = dataclasses.field(default_factory=list)


@dataclasses.dataclass()
class ErpTradeInfo:
    """
    订单信息，一个订单可能包含多个子订单
    """

    orders: List[ErpSubTradeInfo] = dataclasses.field(default_factory=list)
    trade_id: str = ""
    buyer_nick: str = ""
    status: str = ""
    payment: float = 0.0  # 订单支付金额
    paid_at: str = ""


class ErpTradeManager:
    """
    将各种erp平台的订单信息转换为统一的格式

    各个erp平台在获取订单时会遇到限制：
    1.jst归档订单需要等待一段时间后获取，客户端不接受这个延时
    2.旺店通openapi有限流
    3.小型erp有限流

    如果遇到拆分的情况:
    1.jst 获取最老的原始单
    2，wdt系列，获取所有订单中src_tid为当前订单的商品，trade_id直接返回当前订单的tid

    组合商品的处理： https://leyan.yuque.com/digismart/lqmmke/vqrmnxiaaecg6g8q

    提供的 API:
        查询订单列表：query_orders_xxx
        筛选订单：filter_order_xxx
        数据结构转换：convert_order_xxx
    """

    @staticmethod
    def detect_erp(org_id: int, tid: str):
        erp_info_list = ErpInfo.get_distinct_erp_by_org_id(org_id)
        for erp_info in erp_info_list:
            erp_order = ErpTradeManager.from_erp_info(erp_info, tid)
            if erp_order is not None:
                return erp_info, erp_order
        return None

    @staticmethod
    def get_erp_info_list(org_id: int, erp_type: ErpType | None):
        erp_info_list = ErpInfo.get_distinct_erp_by_org_id(org_id)
        if erp_type is not None:
            return [erp_info for erp_info in erp_info_list if erp_info.erp_type == erp_type]
        return erp_info_list

    @staticmethod
    def from_erp_info(erp_info: ErpInfo, tid: str):
        match erp_info.erp_type:
            case ErpType.JST:
                jst_client = JstQmSDK(erp_info=erp_info)
                return ErpTradeManager.filter_order_jst(ErpTradeManager.query_orders_jst(jst_client, tid))
            case ErpType.WDT:
                wdt_client = WdtClient(erp_info=erp_info)
                return ErpTradeManager.filter_order_wdt(ErpTradeManager.query_orders_wdt(wdt_client, tid))
            case ErpType.WDTULTI:
                wdtulti_client = WdtUltiQM(erp_info=erp_info)
                return ErpTradeManager.filter_order_wdtulti(ErpTradeManager.query_orders_wdtulti(wdtulti_client, tid))
            case ErpType.KUAIMAI:
                kuaimai_client = KuaimaiQmSDK(erp_info=erp_info)
                return ErpTradeManager.filter_order_kuaimai(ErpTradeManager.query_orders_kuaimai(kuaimai_client, tid))
            case ErpType.WANLINIU:
                wanliniu_client = WlnClient.from_erp_info(erp_info)
                return ErpTradeManager.filter_order_wanliniu(
                    ErpTradeManager.query_orders_wanliniu(wanliniu_client, tid)
                )
            case ErpType.GUANYIYUN:
                gyy_client = GyyQmSDK(erp_info=erp_info)
                return ErpTradeManager.filter_order_gyy(ErpTradeManager.query_orders_gyy(gyy_client, tid))
            case ErpType.JACKYUN:
                jackyun_client = JackyunQmSDK()
                return ErpTradeManager.filter_order_jackyun(ErpTradeManager.query_orders_jackyun(jackyun_client, tid))

    @staticmethod
    def detect_shop(org_id: int, erp_info: ErpInfo, erp_order) -> Shop | None:
        from robot_processor.shop.auth_manager import get_shop_by_erp_platform_sid

        match erp_info.erp_type, erp_order:
            case ErpType.JST, JstBaseOrder():
                return get_shop_by_erp_platform_sid(org_id, ErpType.JST.name, erp_order.shop_id).unwrap_or(None)
            case ErpType.WDT, WdtTrade():
                return get_shop_by_erp_platform_sid(org_id, ErpType.WDT.name, int(erp_order.shop_id)).unwrap_or(None)
            case ErpType.WDTULTI, WdtultiOrderModel():
                return get_shop_by_erp_platform_sid(org_id, ErpType.WDTULTI.name, int(erp_order.shop_id)).unwrap_or(
                    None
                )
        return None

    @staticmethod
    def detect_shop_by_tid(org_id: int, erp_info: ErpInfo, tid: str) -> dict[int, list[str]]:
        ret: dict[int, list[str]] = {}
        from robot_processor.shop.auth_manager import get_shop_by_erp_platform_sid

        match erp_info.erp_type:
            case ErpType.JST:
                jst_client = JstQmSDK(erp_info=erp_info)
                jst_orders = []
                out_orders_resp: OutOrdersQueryResp | None = None
                try:
                    jst_orders = (
                        jst_client.query_orders({"so_ids": tid}).orders
                        or jst_client.query_orders({"so_ids": tid, "archive": "true"}).orders
                    )
                except JstRequestError as e:
                    if "企业标识为空" in e.message:
                        jst_new_sdk = JstNewSDK(erp_info=erp_info)
                        out_orders_resp = ErpTradeManager.query_out_orders_jst(
                            jst_client, jst_new_sdk,
                            OpenOrdersOutSimpleQuery(so_ids=[tid]))
                    else:
                        logger.opt(exception=e).error(f"查询订单 {tid} 发生异常")
                        return ret
                if jst_orders:
                    for jst_order in jst_orders:
                        shop = get_shop_by_erp_platform_sid(org_id, ErpType.JST.name, jst_order.shop_id).unwrap_or(None)
                        if shop:
                            if ret.get(shop.id):
                                ret[shop.id].extend(jst_order.so_id.split(","))
                            else:
                                ret[shop.id] = jst_order.so_id.split(",")
                elif out_orders_resp and out_orders_resp.datas:
                    for jst_out_order in out_orders_resp.datas:
                        shop = get_shop_by_erp_platform_sid(
                            org_id, ErpType.JST.name, jst_out_order.shop_id).unwrap_or(None)
                        if shop:
                            if ret.get(shop.id):
                                ret[shop.id].extend(jst_out_order.so_id.split(","))
                            else:
                                ret[shop.id] = jst_out_order.so_id.split(",")
            case ErpType.WDT:
                wdt_client = WdtClient(erp_info=erp_info)
                wdt_order_resp = wdt_client.trade_query(tid)
                if wdt_order_resp.response.trades:
                    for wdt_order in wdt_order_resp.response.trades:
                        shop = get_shop_by_erp_platform_sid(org_id, ErpType.WDT.name, int(wdt_order.shop_id)).unwrap_or(
                            None
                        )
                        if shop:
                            if ret.get(shop.id):
                                ret[shop.id].extend(wdt_order.src_tids.split(","))
                            else:
                                ret[shop.id] = wdt_order.src_tids.split(",")
            case ErpType.WDTULTI:
                wdtulti_client = WdtUltiQM(erp_info=erp_info)
                wdtulti_order_resp = wdtulti_client.get_orders(src_tid=tid)
                if wdtulti_order_resp.order:
                    for wdtulti_order in wdtulti_order_resp.order:
                        shop = get_shop_by_erp_platform_sid(
                            org_id, ErpType.WDTULTI.name, int(wdtulti_order.shop_id)
                        ).unwrap_or(None)
                        if shop:
                            if ret.get(shop.id):
                                ret[shop.id].extend(wdtulti_order.src_tids.split(","))
                            else:
                                ret[shop.id] = wdtulti_order.src_tids.split(",")
            case ErpType.JACKYUN:
                jackyun_client = JackyunQmSDK(erp_info=erp_info)
                jackyun_order_resp = jackyun_client.order_list(tid)
                if jackyun_order_resp.response.jackyunData.trades:
                    for jackyun_order in jackyun_order_resp.response.jackyunData.trades:
                        shop = get_shop_by_erp_platform_sid(
                            org_id, ErpType.JACKYUN.name, jackyun_order.shopId
                        ).unwrap_or(None)
                        if shop:
                            if ret.get(shop.id):
                                ret[shop.id].extend(jackyun_order.online_trade_no.split(","))
                            else:
                                ret[shop.id] = jackyun_order.online_trade_no.split(",")
        for key, value in ret.items():
            ret[key] = list(set(value))
        return ret

    @staticmethod
    def detect_shop_by_logistics_no(org_id: int, erp_info: ErpInfo, logistics_no: str) -> dict[int, list[str]]:
        ret: dict[int, list[str]] = {}
        from robot_processor.shop.auth_manager import get_shop_by_erp_platform_sid

        match erp_info.erp_type:
            case ErpType.WDT:
                wdt_client = WdtClient(erp_info=erp_info)
                wdt_trade_resp = wdt_client.trade_query(logistics_no=logistics_no)
                if wdt_trade_resp.response.trades:
                    for wdt_trade in wdt_trade_resp.response.trades:
                        shop = get_shop_by_erp_platform_sid(org_id, ErpType.WDT.name, int(wdt_trade.shop_id)).unwrap_or(
                            None
                        )
                        if shop:
                            if ret.get(shop.id):
                                ret[shop.id].extend(wdt_trade.src_tids.split(","))
                            else:
                                ret[shop.id] = wdt_trade.src_tids.split(",")
            case ErpType.WDTULTI:
                wdtulti_client = WdtUltiQM(erp_info=erp_info)
                wdtulti_trade_resp = wdtulti_client.trade_query(
                    TradeQueryParams(logistics_no=logistics_no), pager=Pager(page_size=100)
                )
                if wdtulti_trade_resp.order:
                    for wdtulti_trade in wdtulti_trade_resp.order:
                        shop = get_shop_by_erp_platform_sid(
                            org_id, ErpType.WDTULTI.name, int(wdtulti_trade.shop_id)
                        ).unwrap_or(None)
                        if shop:
                            if ret.get(shop.id):
                                ret[shop.id].extend(wdtulti_trade.src_tids.split(","))
                            else:
                                ret[shop.id] = wdtulti_trade.src_tids.split(",")
        for key, value in ret.items():
            ret[key] = list(set(value))
        return ret

    @staticmethod
    def detect_shop_by_return_logistics_no(org_id: int, erp_info: ErpInfo, logistics_no: str) -> dict[int, list[str]]:
        ret: dict[int, list[str]] = {}
        from robot_processor.shop.auth_manager import get_shop_by_erp_platform_sid

        match erp_info.erp_type:
            case ErpType.WDT:
                wdt_client = WdtClient(erp_info=erp_info)
                wdt_refund_resp = wdt_client.refund_query_by_logistics_no(logistics_no)
                if wdt_refund_resp.response.refunds:
                    for wdt_refund in wdt_refund_resp.response.refunds:
                        shop = get_shop_by_erp_platform_sid(
                            org_id, ErpType.WDT.name, int(wdt_refund.shop_id)
                        ).unwrap_or(None)
                        if shop:
                            if ret.get(shop.id):
                                ret[shop.id].extend(wdt_refund.tid.split(","))
                            else:
                                ret[shop.id] = wdt_refund.tid.split(",")
            case ErpType.WDTULTI:
                wdtulti_client = WdtUltiQM(erp_info=erp_info)
                wdtulti_refund_resp = wdtulti_client.refund_query_by_logistics_no(logistics_no)
                if wdtulti_refund_resp.data.order:
                    for wdtulti_refund in wdtulti_refund_resp.data.order:
                        shop = get_shop_by_erp_platform_sid(
                            org_id, ErpType.WDTULTI.name, wdtulti_refund.shop_id
                        ).unwrap_or(None)
                        if shop:
                            if ret.get(shop.id):
                                ret[shop.id].extend(wdtulti_refund.src_tids.split(","))
                            else:
                                ret[shop.id] = wdtulti_refund.src_tids.split(",")
        for key, value in ret.items():
            ret[key] = list(set(value))
        return ret

    @staticmethod
    def detect_shop_by_refund_id(org_id: int, erp_info: ErpInfo, refund_id: str) -> dict[int, list[str]]:
        ret: dict[int, list[str]] = {}
        from robot_processor.shop.auth_manager import get_shop_by_erp_platform_sid

        match erp_info.erp_type:
            case ErpType.WDT:
                wdt_client = WdtClient(erp_info=erp_info)
                wdt_refund_resp = wdt_client.refund_query_by_src_refund_no(refund_id)
                if wdt_refund_resp.response.refunds:
                    for wdt_refund in wdt_refund_resp.response.refunds:
                        shop = get_shop_by_erp_platform_sid(
                            org_id, ErpType.WDT.name, int(wdt_refund.shop_id)
                        ).unwrap_or(None)
                        if shop:
                            if ret.get(shop.id):
                                ret[shop.id].extend(wdt_refund.tid.split(","))
                            else:
                                ret[shop.id] = wdt_refund.tid.split(",")
            case ErpType.WDTULTI:
                wdtulti_client = WdtUltiQM(erp_info=erp_info)
                now = arrow.now()
                start_time = now.shift(days=-30).format("YYYY-MM-DD HH:mm:ss")
                end_time = now.format("YYYY-MM-DD HH:mm:ss")
                wdtulti_refund_resp = wdtulti_client.rawrefund_query_by_refund_no(refund_id, start_time, end_time)
                if wdtulti_refund_resp.data.order:
                    for wdtulti_refund in wdtulti_refund_resp.data.order:
                        shop = get_shop_by_erp_platform_sid(
                            org_id, ErpType.WDTULTI.name, wdtulti_refund.shop_id
                        ).unwrap_or(None)
                        if shop:
                            if ret.get(shop.id):
                                ret[shop.id].extend(wdtulti_refund.tid.split(","))
                            else:
                                ret[shop.id] = wdtulti_refund.tid.split(",")
        for key, value in ret.items():
            ret[key] = list(set(value))
        return ret

    @staticmethod
    def process(shop: Shop, tid: str, version: str):
        from rpa.erp.jst import JstBaseOrder

        code = ""
        reason = ""
        trade: ErpTradeInfo | JstBaseOrder = ErpTradeInfo()
        if erps := shop.erps.all():
            erp: ErpInfo = erps[0]
            try:
                if erp.erp_type == ErpType.JST:
                    # 兼容老版本的客户端，解析的格式不一样
                    if version == "1":
                        trade = ErpTradeManager.from_jst_old(tid=tid, shop=shop)
                    if version == "2":
                        trade = ErpTradeManager.from_jst(tid=tid, shop=shop)
                elif erp.erp_type == ErpType.WDT:
                    trade = ErpTradeManager.from_wdt(tid=tid, shop=shop)
                elif erp.erp_type == ErpType.WDTULTI:
                    trade = ErpTradeManager.from_wdtulti(tid=tid, shop=shop)
                elif erp.erp_type == ErpType.KUAIMAI:
                    trade = ErpTradeManager.from_kuaimai(tid=tid, shop=shop)
                elif erp.erp_type == ErpType.WANLINIU:
                    trade = ErpTradeManager.from_wanliniu(tid=tid, shop=shop)
                elif erp.erp_type == ErpType.GUANYIYUN:
                    trade = ErpTradeManager.from_guanyiyun(tid=tid, shop=shop)
                elif erp.erp_type == ErpType.JACKYUN:
                    trade = ErpTradeManager.from_jackyun(tid=tid, shop=shop)
                # 其他erp平台的订单获取稳定性不如走平台，暂时不支持
                else:
                    pass
            except ErpTradeFindException as e:
                code = e.code
                reason = e.code
            except BaseException as e:
                trade = ErpTradeInfo()
                code = "unknown"
                reason = str(e)
        else:
            reason = "没有绑定erp"
        if isinstance(trade, JstBaseOrder):
            trades = [trade.dict()]
        else:
            trades = [dataclasses.asdict(trade)]
        return trades, code, reason

    @staticmethod
    def query_orders_wanliniu(client: WlnClient, tid):
        return client.query_trades([tid.split(",")[0]]).data

    @staticmethod
    def filter_order_wanliniu(wln_orders: list[WlnOrder]) -> WlnOrder | None:
        if not wln_orders:
            return None
        return sorted(wln_orders, key=lambda x: x.trade_no)[0]

    @staticmethod
    def convert_order_wanliniu(wln_order: WlnOrder, client: WlnClient, tid: str) -> ErpTradeInfo:
        add_goods_code(client, wln_order)
        order = ErpTradeInfo()
        erp_orders = []
        for item in wln_order.orders or []:
            if item.tp_tid != tid:
                continue
            erp_sub_trade_info = ErpSubTradeInfo(
                oid=item.sub_order_no or "",
                title=item.item_name or "",
                outer_sku_id=item.sku_code or "",
                outer_spu_id=item.goods_code or "",
                pic_path=item.item_image_url or "",
                price=float(item.price or 0.0),
                payment=float(item.payment or 0.0),
            )
            try:
                if item.size:
                    erp_sub_trade_info.quantity = int(item.size)
            except Exception:  # noqa
                logger.exception(f"万里牛订单信息 {item}")
            erp_orders.append(erp_sub_trade_info)
        order.orders = erp_orders
        order.payment = float(wln_order.sum_sale or 0.0)
        order.trade_id = tid
        order.buyer_nick = wln_order.buyer or ""
        return order

    @staticmethod
    def from_wanliniu(tid, shop):
        wln_client = WlnClient.init_by_sid(shop.sid)
        wanliniu_orders = ErpTradeManager.query_orders_wanliniu(wln_client, tid)
        if not wanliniu_orders:
            raise ErpTradeFindException(code=ErrorCode.can_not_find)
        wanliniu_order = unwrap_optional(ErpTradeManager.filter_order_wanliniu(wanliniu_orders))
        order = ErpTradeManager.convert_order_wanliniu(wanliniu_order, wln_client, tid)
        return order

    @staticmethod
    def query_orders_jst_old(client: JstQmSDK, tid: str):
        return client.query_orders(query_params={"so_ids": tid}).orders

    @staticmethod
    def filter_order_jst(orders: list[JstBaseOrder]) -> JstBaseOrder | None:
        if not orders:
            return None
        # 按照 内部单号（o_id）进行排序，取出 o_id 最小的一笔订单（也就是最旧的一笔订单）。这里将这笔订单称之为 原始单 （origin_order）。
        return sorted(orders, key=lambda x: x.o_id)[0]

    @staticmethod
    def from_jst_old(tid, shop):
        jst_client = JstQmSDK(shop.sid)
        jst_orders = ErpTradeManager.query_orders_jst_old(jst_client, tid)
        if not jst_orders:
            raise ErpTradeFindException(code=ErrorCode.can_not_find)
        jst_order = unwrap_optional(ErpTradeManager.filter_order_jst(jst_orders))
        # 修正非淘系平台的子订单号 https://redmine.leyantech.com/issues/604058
        if not shop.is_taobao():
            for item in jst_order.items:
                if item.outer_oi_id and item.outer_oi_id.startswith(tid):
                    item.outer_oi_id = tid
        return jst_order

    @staticmethod
    def query_orders_jst(client: JstQmSDK, tid: str):
        from robot_processor.job.jst_trade import ExtraInfoAdder

        orders_set = set()
        orders_set.update(client.query_orders({"so_ids": tid}).orders)
        orders_set.update(client.query_orders({"so_ids": tid, "archive": "true"}).orders)
        orders = list(orders_set)
        ExtraInfoAdder.trans_order_status_name(orders)
        return orders

    @staticmethod
    def convert_order_jst(jst_order: JstBaseOrder, tid: str, shop: Shop | None = None):
        # 修正非淘系平台的子订单号 https://redmine.leyantech.com/issues/604058
        if shop and not shop.is_taobao():
            for item in jst_order.items:
                if item.outer_oi_id and item.outer_oi_id.startswith(tid):
                    item.outer_oi_id = tid
        # 组合需要输出的信息。
        order = ErpTradeInfo()
        order.orders = [
            ErpSubTradeInfo(
                oid=item.outer_oi_id or "",
                title=item.name or "",
                sku_description=item.properties_value or "",
                pic_path=item.pic or "",
                sku_id=item.shop_sku_id or "",
                outer_sku_id=item.sku_id or "",
                quantity=item.qty or 0,
                payment=(item.item_pay_amount if item.item_pay_amount is not None else 0.0),
                price=item.price if item.price is not None else 0.0,
                suite_sku_id=item.src_combine_sku_id or "",
                spu_id=item.shop_i_id or "",
                outer_spu_id=item.i_id or "",
            )
            for item in jst_order.items
            if item.item_status != "Replaced"
        ]
        if jst_order.pays:
            valid_pays = [pay for pay in jst_order.pays if pay.is_order_pay and pay.status == "Confirmed"]
            if valid_pays:
                order.payment = valid_pays[0].amount or 0
                order.paid_at = valid_pays[0].pay_date or ""
        order.trade_id = jst_order.so_id or ""
        order.buyer_nick = str(jst_order.buyer_id)
        order.status = jst_order.status or ""
        return order

    @staticmethod
    def from_jst(tid: str, shop: Shop) -> ErpTradeInfo:
        """
        该方法仅用于 ERP 订单查询接口，并返回原单信息，不做拆分、合并逻辑。
        :param tid:
        :param shop:
        :return:
        """
        jst_client = JstQmSDK(shop.sid)
        jst_orders = ErpTradeManager.query_orders_jst(jst_client, tid)
        if not jst_orders:
            raise ErpTradeFindException(code=ErrorCode.can_not_find)
        jst_order = unwrap_optional(ErpTradeManager.filter_order_jst(jst_orders))
        order = ErpTradeManager.convert_order_jst(jst_order, tid=tid, shop=shop)
        return order

    @staticmethod
    def query_out_orders_jst(qm_client: JstQmSDK, open_client: JstNewSDK, params: jst_schemas.OpenOrdersOutSimpleQuery):
        qm_params = params.to_qm_params()
        executor = concurrent.futures.ThreadPoolExecutor(max_workers=2)
        qm_future = executor.submit(
            qm_client.query_out_orders, qm_params, str(params.page_index), str(params.page_size)
        )
        open_future = executor.submit(open_client.query_out_orders, params)
        concurrent.futures.wait([qm_future, open_future])
        qm_result: Ok[jst_schemas.OutOrdersQueryResp] | Err[Exception]
        open_result: Ok[jst_schemas.OpenOrdersOutSimpleQuery.Response] | Err[Exception]
        try:
            qm_result = Ok(qm_future.result())
        except Exception as e:
            qm_result = Err(e)
        try:
            open_result = Ok(open_future.result())
        except Exception as e:
            open_result = Err(e)

        match qm_result, open_result:
            case Err(qm_error), Err():
                raise qm_error
            case Ok(jst_schemas.OutOrdersQueryResp(data_count=int() as data_count) as qm_response), _ if data_count > 0:
                logger.info(f"co_id: {qm_client.co_id} use qm for query: {to_log(qm_params)}")
                data = cast(jst_schemas.OutOrdersQueryResp, qm_response)
                return data
            case _, Ok(
                jst_schemas.OpenOrdersOutSimpleQuery.Response(
                    data=jst_schemas.OutOrdersQueryResp(data_count=int() as data_count)
                ) as open_response
            ) if (data_count > 0):
                logger.info(f"co_id: {open_client.co_id} use open for query: {to_log(params.dict())}")
                data = cast(jst_schemas.OpenOrdersOutSimpleQuery.Response, open_response).data
                return data
            case Ok(qm_response), _:
                logger.info(f"co_id: {qm_client.co_id} fallback use qm for query: {to_log(qm_params)}")
                data = cast(jst_schemas.OutOrdersQueryResp, qm_response)
                return data
            case _:
                raise NotImplementedError

    @staticmethod
    def query_orders_wdt(client: WdtClient, tid: str):
        return client.trade_query(wdt_tid=tid).response.trades

    @staticmethod
    def filter_order_wdt(wdt_orders: list[WdtTrade]) -> WdtTrade | None:
        if not wdt_orders:
            return None
        return sorted(wdt_orders, key=lambda x: x.trade_no or "")[0]

    @staticmethod
    def convert_order_wdt(wdt_orders: list[WdtTrade], tid: str, shop: Shop):
        oldest_trade = unwrap_optional(ErpTradeManager.filter_order_wdt(wdt_orders))
        order = ErpTradeInfo()
        erp_orders: List[ErpSubTradeInfo] = []
        combine_skus = []
        # 旺店通系列的erp订单直接返回了组合商品的所有子商品
        for o in wdt_orders:
            if not o.goods_list:
                continue
            stockout_resp: StockoutOrderQueryTradeResp = WdtClient(shop.sid).stockout_order_query_trade(o.trade_no)
            for good in o.goods_list:
                # 订单可能是被合并的，只取src_tid=tid的商品
                if good.src_tid != tid:
                    continue
                # 旺店通的子订单号，在拼多多平台中，会是tid:xx 前端会匹配失败，这边直接返回src_tid
                if shop.is_pdd():
                    oid = str(good.src_tid)
                else:
                    oid = str(good.src_oid)
                batch_list = []
                for stockout in stockout_resp.response.stockout_list:
                    for detail in stockout.details_list:
                        for position in detail.position_list:
                            if good.spec_no == detail.spec_no and position.stockout_order_detail_id == str(
                                detail.rec_id
                            ):
                                batch_list.append(BatchInfo(batch_no=position.batch_no, sku_id=good.spec_no))
                erp_sub_trade_info = ErpSubTradeInfo(
                    sku_id=good.platform_spec_id or "",
                    outer_sku_id=good.spec_no or "",
                    spu_id=good.platform_goods_id or "",
                    outer_spu_id=good.goods_no or "",
                    oid=oid,
                    title=good.api_spec_name or "",
                    outer_title=good.spec_name or "",
                    sku_description=good.prop2 or "",
                    pic_path="",  # 取不到图片
                    quantity=int(float(good.num)) if good.num is not None else 0,
                    payment=float(good.paid) if good.paid is not None else 0.0,
                    price=float(good.price) if good.price is not None else 0.0,
                    suite_sku_id=good.suite_no or "",
                    batch_list=batch_list,
                )
                if erp_sub_trade_info.suite_sku_id:
                    if erp_sub_trade_info.suite_sku_id in combine_skus:
                        # 组合商品的其他部分
                        for sub_erp_order in erp_orders:
                            if sub_erp_order.outer_sku_id == erp_sub_trade_info.suite_sku_id:
                                sub_erp_order.batch_list.extend(erp_sub_trade_info.batch_list)
                        continue
                    else:
                        # 组合商品的第一部分
                        combine_skus.append(erp_sub_trade_info.suite_sku_id)
                        erp_sub_trade_info.outer_sku_id = erp_sub_trade_info.suite_sku_id
                        erp_sub_trade_info.outer_spu_id = erp_sub_trade_info.suite_sku_id
                        erp_orders.append(erp_sub_trade_info)
                else:
                    # 单品
                    erp_orders.append(erp_sub_trade_info)
        # 合并sku_id相同的order
        order_map = dict()
        for erp_sub_trade_info in erp_orders:
            if erp_sub_trade_info.sku_id not in order_map:
                order_map[erp_sub_trade_info.sku_id] = erp_sub_trade_info
            else:
                order_map[erp_sub_trade_info.sku_id].quantity = (
                    order_map[erp_sub_trade_info.sku_id].quantity + erp_sub_trade_info.quantity
                )
                order_map[erp_sub_trade_info.sku_id].payment = (
                    order_map[erp_sub_trade_info.sku_id].payment + erp_sub_trade_info.payment
                )
        order.orders = list(order_map.values())
        order.paid_at = oldest_trade.pay_time or ""
        order.payment = float(oldest_trade.paid) if oldest_trade.paid is not None else 0
        order.trade_id = tid
        order.buyer_nick = oldest_trade.buyer_nick or ""
        order.status = str(oldest_trade.trade_status)
        return order

    @staticmethod
    def from_wdt(tid: str, shop: Shop) -> ErpTradeInfo:
        # 旺店通系列的组合商品会拆分成响应的普通商品返回,这里提供组合商品的sku给前端，前端自己计算展示逻辑
        wdt_client = WdtClient(shop.sid)
        wdt_orders = ErpTradeManager.query_orders_wdt(wdt_client, tid)
        order = ErpTradeManager.convert_order_wdt(wdt_orders, tid=tid, shop=shop)
        return order

    @staticmethod
    def query_orders_wdtulti(client: WdtUltiQM, tid: str):
        return client.get_orders(src_tid=tid).order

    @staticmethod
    def filter_order_wdtulti(wdtulti_orders: list[WdtultiOrderModel]) -> WdtultiOrderModel | None:
        if not wdtulti_orders:
            return None
        return wdtulti_orders[0]

    @staticmethod
    def convert_order_wdtulti(wdtulti_orders: list[WdtultiOrderModel], tid: str, shop: Shop) -> ErpTradeInfo:
        oldest_trade = unwrap_optional(ErpTradeManager.filter_order_wdtulti(wdtulti_orders))
        order = ErpTradeInfo()
        erp_orders: List[ErpSubTradeInfo] = []
        combine_skus = []
        # 旺店通系列的erp订单直接返回了组合商品的所有子商品
        for t in wdtulti_orders:
            for detail in t.detail_list or []:
                # 旺店通的补发单的src_tid会带有-1的后缀，以下逻辑等于过滤补发单
                if not (detail.src_tid or "") == tid:
                    continue
                if shop.is_pdd():
                    oid = str(detail.src_tid)
                else:
                    oid = str(detail.src_oid)
                erp_sub_trade_info = ErpSubTradeInfo(
                    sku_id=detail.api_spec_id or "",
                    outer_sku_id=detail.spec_no or "",
                    spu_id=detail.api_goods_id or "",
                    outer_spu_id=detail.goods_no or "",
                    oid=oid,  # 旺店通的子订单号，在拼多多平台中，会是tid:xx 前端会匹配失败，这边直接返回src_tid
                    title=detail.api_spec_name or "",
                    outer_title=detail.spec_name or "",
                    sku_description=detail.prop2 or "",
                    pic_path=detail.img_url or "",
                    quantity=int(float(detail.num or "0")),
                    payment=float(detail.paid or "0"),
                    price=float(detail.price or "0"),
                    suite_sku_id=detail.suite_no or "",
                )
                if erp_sub_trade_info.suite_sku_id:
                    if erp_sub_trade_info.suite_sku_id in combine_skus:
                        continue
                    else:
                        combine_skus.append(erp_sub_trade_info.suite_sku_id)
                        erp_sub_trade_info.outer_sku_id = erp_sub_trade_info.suite_sku_id
                        erp_sub_trade_info.outer_spu_id = erp_sub_trade_info.suite_sku_id
                        erp_orders.append(erp_sub_trade_info)
                else:
                    erp_orders.append(erp_sub_trade_info)

        order.orders = erp_orders
        order.paid_at = oldest_trade.pay_time
        order.payment = float(oldest_trade.paid) if oldest_trade.paid is not None else None
        order.trade_id = tid
        order.buyer_nick = ""  # 拿不到
        order.status = oldest_trade.trade_status
        return order

    @staticmethod
    def from_wdtulti(tid, shop):
        wdtulti_client = WdtUltiQM(shop.sid)
        wdtulti_orders = ErpTradeManager.query_orders_wdtulti(wdtulti_client, tid)
        if not wdtulti_orders:
            raise ErpTradeFindException(code=ErrorCode.can_not_find)
        order = ErpTradeManager.convert_order_wdtulti(wdtulti_orders, tid=tid, shop=shop)
        return order

    @staticmethod
    def query_orders_gyy(client: GyyQmSDK, tid: str):
        return client.query_trade_by_tid(tid)

    @staticmethod
    def filter_order_gyy(gyy_orders: list[GyyOrder]) -> GyyOrder | None:
        if not gyy_orders:
            return None
        # 管易云拆单只是发货单，不是订单, 但是赠品是另外的订单。。
        # 原单应该是最小的
        return sorted(gyy_orders, key=lambda x: x.code)[0]

    @staticmethod
    def convert_order_gyy(gyy_orders: list[GyyOrder], tid: str) -> ErpTradeInfo:
        oldest_gyy_order = unwrap_optional(ErpTradeManager.filter_order_gyy(gyy_orders))
        order = ErpTradeInfo()
        sub_orders = []
        for gyy_order in gyy_orders:
            if gyy_order.order_type_name != "销售订单":
                continue
            for detail in gyy_order.details:
                # 管易云SKU和SPU一致
                sub_orders.append(
                    ErpSubTradeInfo(
                        sku_id="",
                        outer_sku_id=detail.item_code,
                        spu_id="",
                        outer_spu_id=detail.item_code,
                        oid=detail.oid,
                        title=detail.item_name,
                        outer_title=detail.item_name,
                        sku_description=detail.platform_sku_name,
                        pic_path="",
                        quantity=int(float(detail.qty)) if detail.qty is not None else None,
                        payment=float(detail.amount) if detail.amount is not None else None,
                        price=float(detail.price) if detail.price is not None else None,
                        # FIXME(<EMAIL>): 组合商品
                        suite_sku_id="",
                    )
                )
        order.orders = sub_orders
        order.paid_at = oldest_gyy_order.paytime
        order.payment = float(oldest_gyy_order.payment_amount) if oldest_gyy_order.payment_amount is not None else None
        order.trade_id = tid
        order.buyer_nick = ""
        return order

    @staticmethod
    def from_guanyiyun(shop, tid):
        gyy_client = GyyQmSDK(shop.sid)
        gyy_orders = ErpTradeManager.query_orders_gyy(gyy_client, tid)
        order = ErpTradeManager.convert_order_gyy(gyy_orders, tid=tid)
        return order

    @staticmethod
    def query_orders_jackyun(client: JackyunQmSDK, tid: str):
        return filter_orders(client.order_list(tid).response.jackyunData.trades)

    @staticmethod
    def filter_order_jackyun(jackyun_orders: list[JackyunOrder]) -> JackyunOrder | None:
        if not jackyun_orders:
            return None
        return jackyun_orders[0]

    @staticmethod
    def convert_order_jackyun(jackyun_order: JackyunOrder, client: JackyunSDK, tid: str):
        order = ErpTradeInfo()
        sub_orders = []
        sell_total = 0.0
        for goods_detail in jackyun_order.goodsDetail:
            goods_resp = client.goods_list_by_goods_no(goods_detail.goodsNo)
            goods = goods_resp.result.data.goods.pop(0)
            outer_sku_id = ""
            if goods.goodsNo == goods_detail.goodsNo:
                outer_sku_id = goods.skuName
            divide_sell_total = float(goods_detail.divideSellTotal if goods_detail.divideSellTotal else "0")
            sell_total = sell_total + divide_sell_total
            sub_order = ErpSubTradeInfo(
                sku_id=goods_detail.platSkuId,
                outer_sku_id=outer_sku_id,
                spu_id=goods_detail.platGoodsId,
                outer_spu_id=goods_detail.goodsNo,
                oid=goods_detail.sourceSubtradeNo,
                title=goods_detail.goodsName,
                outer_title=goods_detail.goodsName,
                sku_description="",
                pic_path="",
                quantity=int(float(goods_detail.sellCount)) if goods_detail.sellCount is not None else None,
                payment=divide_sell_total,
                price=float(goods_detail.sellPrice) if goods_detail.sellPrice is not None else None,
                # FIXME(<EMAIL>): 组合商品
                suite_sku_id="",
            )
            sub_orders.append(sub_order)
        order.orders = sub_orders
        order.paid_at = jackyun_order.payTime
        order.payment = sell_total
        order.trade_id = tid
        order.buyer_nick = ""
        return order

    @staticmethod
    def from_jackyun(shop, tid):
        qm = JackyunQmSDK(shop.sid)
        sdk = JackyunSDK(shop.sid)
        jackyun_orders = ErpTradeManager.query_orders_jackyun(qm, tid)
        if not jackyun_orders:
            raise ErpTradeFindException(code=ErrorCode.can_not_find)
        jackyun_order = unwrap_optional(ErpTradeManager.filter_order_jackyun(jackyun_orders))
        order = ErpTradeManager.convert_order_jackyun(jackyun_order, sdk, tid)
        return order

    @staticmethod
    def query_orders_kuaimai(client: KuaimaiQmSDK, tid: str):
        trades = client.get_orders(tid=tid).trades
        return trades

    @staticmethod
    def filter_order_kuaimai(kuaimai_orders: list[KuaimaiOrder]) -> KuaimaiOrder | None:
        kuaimai_orders = [trade for trade in kuaimai_orders if trade.is_ordinary()]
        if not kuaimai_orders:
            return None
        return sorted(kuaimai_orders, key=lambda x: x.sid)[0]

    @staticmethod
    def convert_order_kuaimai(kuaimai_order: KuaimaiOrder, tid: str):
        order = ErpTradeInfo()
        erp_orders = []
        for detail in kuaimai_order.orders:
            erp_orders.append(
                ErpSubTradeInfo(
                    # 这个字段在查询的时候，对应到商品库 KUAIMAI_ITEM_SKU.SYS_SKU_ID 字段
                    sku_id=str(detail.skuSysId),
                    # 这个字段在查询的时候，对应到商品库 KUAIMAI_ITEM_SKU.SKU_OUTER_ID 字段
                    outer_sku_id=detail.sysOuterId,
                    # 这个字段看起来是平台的 SPU
                    spu_id=detail.numIid,
                    # 目前在查询的订单里，没有拿到 KUAIMAI_ITEM_SKU.SYS_ITEM_ID 的相关字段。
                    # 假定该字段为 SYS_ITEM_ID 尝试下匹配。
                    outer_spu_id=detail.sysItemOuterId,
                    oid=str(detail.oid),
                    title=detail.sysTitle,
                    outer_title=detail.title,
                    sku_description=detail.sysSkuPropertiesName,
                    pic_path=detail.picPath,
                    quantity=int(float(detail.num)) if detail.num is not None else None,
                    payment=float(detail.payment) if detail.payment is not None else None,
                    price=float(detail.price) if detail.price is not None else None,
                    suite_sku_id="",
                )
            )
        order.orders = erp_orders
        order.paid_at = kuaimai_order.payTime
        order.payment = float(kuaimai_order.payAmount) if kuaimai_order.payAmount is not None else None
        order.trade_id = tid
        order.buyer_nick = ""
        return order

    @staticmethod
    def from_kuaimai(shop, tid):
        # fixme 需要补充快麦平台的组合商品逻辑
        kuaimai_client = KuaimaiQmSDK(shop.sid)
        kuaimai_orders = ErpTradeManager.query_orders_kuaimai(kuaimai_client, tid)
        if not kuaimai_orders:
            raise ErpTradeFindException(code=ErrorCode.can_not_find)
        kuaimai_order = ErpTradeManager.filter_order_kuaimai(kuaimai_orders)
        if not kuaimai_order:
            raise ErpTradeFindException(code=ErrorCode.can_not_find)
        return ErpTradeManager.convert_order_kuaimai(kuaimai_order, tid)


class PlatformTradeManager:
    @staticmethod
    def process(shop, tids: List[str], version):
        trades = []
        is_match = False
        if shop.is_taobao():
            from robot_processor.plugin.trade_api import get_taobao_trade

            trades = get_taobao_trade(shop.sid, tids)
            is_match = all(
                [t.get("seller_nick") == shop.nick or t.get("seller_nick") in shop.history_nick_list for t in trades]
            )
        elif shop.is_pdd():
            trade = trade_client.get_trade_by_tid_and_channel(tids)
            if trade:
                trade_dict = MessageToDict(
                    trade,
                    preserving_proto_field_name=True,
                    including_default_value_fields=True,
                )
                trade_info_list = trade_dict.get("trade_info_list", [])
                trades = [trade_info.get("pdd_trade_info", {}) for trade_info in trade_info_list]
                is_match = all([t.get("store_id") == shop.sid for t in trades])
        elif shop.is_doudian():
            if trade := trade_client.get_trade_by_tid_and_channel(tids, shop.platform, shop.sid):
                for trade_info in trade.trade_info_list:
                    dy_trade_info = message_to_dict(trade_info.dy_trade_info)
                    for sku_order in dy_trade_info.get("sku_order_list") or []:
                        sku_order["code"] = sku_order.get("out_sku_id", "")
                    trades.append(dy_trade_info)
                is_match = all([t.get("shop_id") == shop.sid for t in trades])
        elif shop.is_alibaba():
            trade = trade_client.get_trade_by_tid_and_channel(tids, shop.platform, org_id=shop.org_id)
            if trade:
                trade_dict = MessageToDict(
                    trade,
                    preserving_proto_field_name=True,
                    including_default_value_fields=True,
                )
                trade_info_list = trade_dict.get("trade_info_list", [])
                trades = [trade_info.get("alibaba_trade_info", {}) for trade_info in trade_info_list]
                is_match = all([t.get("base_info").get("store_id") == shop.sid for t in trades])
        elif shop.is_ks():
            trade = trade_client.get_trade_by_tid_and_channel(tids, shop.platform, shop.sid, shop.get_access_token())
            if trade:
                for trade_info in trade.trade_info_list:
                    ks_trade_info = message_to_dict(trade_info.ks_trade_info)
                    trades.append(ks_trade_info)
                is_match = all([t.get("store_id") == shop.sid for t in trades])
        elif shop.is_jd():
            trade_resp = trade_client.get_trade_by_tid_and_channel(
                tids=tids,
                channel_type=shop.platform,
                sid=shop.sid,
            )
            if trade_resp:
                for trade_info in trade_resp.trade_info_list:
                    jd_trade_info = message_to_dict(trade_info.jd_trade_info)
                    trades.append(jd_trade_info)
                is_match = all([t.get("shop_id") == shop.sid for t in trades])
        return trades, is_match
