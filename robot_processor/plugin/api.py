import concurrent.futures
import json
from base64 import b64encode
from collections import defaultdict
from hashlib import md5
from typing import List
from typing import Optional

from flask import current_app
from flask import jsonify
from flask import request
from loguru import logger
from pydantic import BaseModel
from pydantic import Field

from robot_processor.client import aliyun_addrp_client
from robot_processor.client import buyer_client
from robot_processor.client import chat_history_client
from robot_processor.client import erp_item_client
from robot_processor.client import file_oss_client
from robot_processor.client import image_oss_client
from robot_processor.client import item_client
from robot_processor.client import trade_client
from robot_processor.client import video_oss_client
from robot_processor.client.conf import app_config
from robot_processor.client.oss import InvalidImageError
from robot_processor.client.oss import UploadError
from robot_processor.client_mixins import Session
from robot_processor.currents import g
from robot_processor.decorators import org_required
from robot_processor.decorators import shop_required
from robot_processor.enums import ErpType
from robot_processor.enums import ShopPlatform
from robot_processor.error.base import BizError
from robot_processor.ext import cache
from robot_processor.ext import db
from robot_processor.plugin.erp_schemas import ErpSkuInventoriesQueryParams
from robot_processor.plugin.erp_schemas import ErpSkuInventoriesResponseData
from robot_processor.plugin.erp_schemas import ErpSkuQueryMethod
from robot_processor.plugin.erp_schemas import ErpSkuQueryParams
from robot_processor.plugin.erp_schemas import ErpSkuResponseData
from robot_processor.plugin.erp_schemas import QuerySkuSourceType
from robot_processor.plugin.erp_schemas import SkuInventory
from robot_processor.plugin.erp_schemas import SkuInventoryResponseData
from robot_processor.plugin.erp_utils import jackyun_get_order_skus
from robot_processor.plugin.erp_utils import jst_compute_available_qty
from robot_processor.plugin.erp_utils import jst_get_order_skus
from robot_processor.plugin.erp_utils import jst_get_warehouses
from robot_processor.plugin.erp_utils import jst_match_skus_inventories
from robot_processor.plugin.erp_utils import jst_query_skus
from robot_processor.plugin.erp_utils import wdgj_get_order_skus
from robot_processor.plugin.erp_utils import wdt_get_order_skus
from robot_processor.plugin.erp_utils import wdt_match_skus_inventories
from robot_processor.plugin.erp_utils import wdt_query_skus
from robot_processor.plugin.erp_utils import wdt_ulti_get_order_skus
from robot_processor.plugin.erp_utils import wdt_ulti_match_skus_inventories
from robot_processor.plugin.erp_utils import wdt_ulti_query_skus
from robot_processor.plugin.platform_utils import alibaba_get_order_skus
from robot_processor.plugin.platform_utils import dy_get_order_skus
from robot_processor.plugin.platform_utils import jd_get_order_skus
from robot_processor.plugin.platform_utils import pdd_get_order_skus
from robot_processor.plugin.platform_utils import taobao_get_order_skus
from robot_processor.plugin.schema import AuthEnableCheckRequest
from robot_processor.plugin.schema import ErpItemSkuListSchema
from robot_processor.plugin.schema import ErpSkuListSchema
from robot_processor.plugin.schema import HighLevelSearchItemSchema
from robot_processor.plugin.schema import HistorySchema
from robot_processor.plugin.schema import ImageCompressAckRequest
from robot_processor.plugin.schema import ImageCompressAckResponse
from robot_processor.plugin.schema import Spu
from robot_processor.plugin.tasks import async_compress_image
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.models import Shop
from robot_processor.utils import GenericResponse
from robot_processor.utils import is_buyer_nick_encrypt
from robot_processor.utils import wrap_status_code_in_response_body
from robot_processor.validator import validate
from rpa.erp.jackyun.sdk import JackyunQmSDK
from rpa.erp.jackyun.sdk import JackyunSDK
from rpa.erp.jst import JstNewSDK
from rpa.erp.jst import WmsPartnerQueryResp
from rpa.erp.wdgj.sdk import WdgjQmSDK
from rpa.erp.wdt.wdt import WdtClient
from rpa.erp.wdt.wdt import WdtOpenAPIClient
from rpa.erp.wdtulti.wdtulti import WdtUltiOpenAPIClient
from rpa.erp.wdtulti.wdtulti import WdtUltiQM

from ..error.base import ClientRequestError
from ..error.client_request import WdtRequestError
from . import plugin_api as api


@api.before_request
@org_required
def before_request():
    sid = request.args.get("sid")
    if sid:
        if sid not in g.org_sids:
            return jsonify(reason="非法的店铺id"), 400
        shop = Shop.query.filter_by(sid=sid).first()
        if not shop:
            return jsonify(reason="不存在的店铺"), 400
        g.shop = shop
    elif g.auth.store_id:
        shop = Shop.query.filter_by(sid=g.auth.store_id).first()
        if shop:
            g.shop = shop


api.after_request(wrap_status_code_in_response_body)

# 创建一个线程池，用于并发请求地址解析服务。
parse_address_executor = concurrent.futures.ThreadPoolExecutor(thread_name_prefix="parse_address")
parse_address_session = Session()


@api.route("/erp/is-bind")
def check_bind_erp():
    sid: str = get_sid()
    shop = Shop.query.filter_by(sid=sid).first()
    if not shop:
        return jsonify(reason=f"不存在的店铺 {sid=}"), 400
    erp_info = ErpInfo.get_all_erp_by_sid(sid)
    spu_list = item_client.get_provider_item(shop.sid, shop.nick, shop.platform)
    return jsonify(sid=sid, is_bind=bool(erp_info) and bool(spu_list))


@api.route("/erp")
def get_erp_info():
    sid: str = get_sid()
    shop = Shop.query.filter_by(sid=sid).first()
    erp_info: ErpInfo | None = (
        ErpInfo.query.filter_by(shop_id=shop.id).order_by(ErpInfo.id.desc()).first()  # type: ignore[union-attr]
    )
    return jsonify(type=erp_info.erp_type.name if erp_info else "UNKNOWN")  # type: ignore[union-attr]


@api.route("/spu/<spu_id>/sku")
def get_sku(spu_id):
    """
    获取商品可选SKU

    入参：
    - query: `source` (str 类型)

    返回值格式如下：
    {
        "spu_id": "str",
        "sku_infos": [
            {
                "props": "str",
                "price": 0.0,
                "outer_sku_id": "str"
            }
        ],
        "combine_sku_infos": [
            {
                "props": "str",
                "price": 0.0,
                "outer_sku_id": "str"
            }
        ]
    }
    """
    sid: str = get_sid()
    if not sid:
        return jsonify(reason="缺失店铺id"), 400
    source = request.args.get("source")
    if "erp" == source:
        shop = g.shop
        erp_type = compute_erp_type(source, shop)
        access_token = get_access_token(erp_type)
        sku = item_client.get_provider_sku(
            shop.sid, shop.nick, shop.platform, token=access_token, spu_id=[spu_id], page_size=500
        )

        return jsonify(item_client.convert_to_old_spu_sku(sku))
    sku = find_sku(g.shop, spu_id) or {}
    return jsonify(sku)


@api.route("/uploads/<file_type>", methods=["POST"])
def upload_image(file_type):
    """上传图片/文件"""
    try:
        if file_type == "image":
            res = request.files["image"]
            url = image_oss_client.upload_image(g.org_id, res, need_zip=False)
            async_compress_image.send(url)
        elif file_type == "file":
            res = request.files["file"]
            url = file_oss_client.upload_file(g.org_id, res)
        elif file_type == "video":
            res = request.files["video"]
            url = video_oss_client.upload_video(g.org_id, res)
        else:
            return jsonify(reason="不支持的文件类型"), 400

    except InvalidImageError as e:
        return jsonify(succeed=False, error=e.message), 400
    except UploadError as e:
        return jsonify(succeed=False, error=e.message), 406

    # 兼容买家自助服务台的格式。
    return jsonify(succeed=True, url=url, success=True, data={"url": url})


@api.route("/uploads/image-compress-ack")
@org_required
@validate
def upload_image_ack(query: ImageCompressAckRequest) -> ImageCompressAckResponse:
    """上传图片压缩确认"""
    url = query.url
    res = cache.get(url)
    return ImageCompressAckResponse(ack=bool(res))


@api.route("/spus")
@validate
def get_spu_list(query: Spu):
    """获取spu列表"""
    sid = get_sid()
    if not sid:
        return jsonify(reason="缺失店铺id"), 400

    query_param = query.query_param
    page_no = query.page_no
    page_size = query.page_size
    source = query.source
    if "erp" == source:
        shop = g.shop
        erp_type = compute_erp_type(source, shop)
        access_token = get_access_token(erp_type)
        spu_list = item_client.get_provider_item(
            g.shop.sid,
            g.shop.nick,
            g.shop.platform,
            keywords=query_param,
            page_no=page_no,
            query_type=query.query_type,
            page_size=page_size,
            query_entity=query.query_entity,
            token=access_token,
        )
    else:
        if g.shop.is_taobao():
            record = g.shop.get_recent_record()
            if not record or not record.access_token:
                return jsonify(spu_list=[], reason="缺失授权记录"), 400
            token: str = record.access_token
        elif g.shop.is_ks():
            access_token = g.shop.get_access_token()
            if not access_token:
                return jsonify(item_infos=[], reason="缺失快手授权记录"), 400
            token = access_token
        else:
            token = ""
        spu_list = item_client.get_spu_list(
            token,
            query_param,
            query.query_entity,
            page_no,
            page_size,
            query_type=query.query_type,
            sid=g.shop.sid,
            nick=g.shop.nick,
            channel_type=g.shop.platform,
        )
    return jsonify(spu_list)


@api.route("/batch/spus")
def batch_get_spu():
    """
    批量获取spu详情
    """

    spu_id_list = request.args.getlist("spu_id", str)
    if not spu_id_list:
        return jsonify(data=None, msg="商品id为空"), 400
    if len(spu_id_list) > app_config.BATCH_SPUS_SPU_ID_LIST_LENGTH:
        return jsonify(data=None, msg=f"查询太多的商品，仅支持{app_config.BATCH_SPUS_SPU_ID_LIST_LENGTH}条"), 400

    source = request.args.get("source")
    if "erp" == source:
        shop = g.shop
        erp_type = compute_erp_type(source, shop)
        access_token = get_access_token(erp_type)
        result = item_client.get_provider_item(
            shop.sid,
            shop.nick,
            shop.platform,
            token=access_token,
            spu_id=spu_id_list,
            query_entity=HighLevelSearchItemSchema(spu=",".join(spu_id_list)),
        )
        spu_list = result.get("item_detail_infos", [])
    else:
        spu_list = [spu for spu_id in spu_id_list if (spu := find_spu(g.shop, spu_id))]

    return jsonify(data=spu_list, msg="")


def find_sku(shop: Shop, spu_id: str) -> dict | None:
    sku = None
    if shop.is_taobao() or shop.is_pdd() or shop.is_doudian() or shop.is_alibaba() or shop.is_jd():
        record = shop.get_recent_record()
        if record:
            token = record.access_token
        else:
            token = ""
        sku = item_client.get_sku_by_spu(g.shop.sid, spu_id, token, shop.platform)
    elif shop.is_ks():
        if access_token := shop.get_access_token():
            sku = item_client.get_sku_by_spu(g.shop.sid, spu_id, access_token, shop.platform)
    if isinstance(sku, dict) and len(sku) > 0:
        sku.update({"combine_sku_infos": []})
    return sku


def find_spu(shop, spu_id):
    spu = None
    try:
        if shop.is_taobao() or shop.is_pdd() or shop.is_doudian() or shop.is_alibaba() or shop.is_jd():
            spu = item_client.get_spu(spu_id, sid=shop.sid, channel_type=shop.platform)
        elif shop.is_ks():
            if access_token := shop.get_access_token():
                spu = item_client.get_spu(spu_id, sid=shop.sid, channel_type=shop.platform, access_token=access_token)
    except BaseException as e:
        logger.error(f"get spu by platform error: {str(e)}")
        return None

    if not spu:
        return None

    if not spu.get("item_title") and not spu.get("pic_url"):
        return None

    return spu


@api.route("/batch/skus")
def batch_get_sku():
    """
    获取商品可选SKU

    入参：
     - query：    `page_no` （int, 查询页数）
                  `spu_id`  （array[str], 查询的 SPU_ID）
                  `spu_id[]`（array[str], 查询的 SPU_ID，该字段名称是为了兼容前端千牛侧边栏的数据。）

    返回值格式如下：
    {
        "data": [
            {
                "spu_id": "str",
                "sku_infos": [
                    {
                        "spu_id": "str",
                        "sku_id": "str",
                        "props": "str",
                        "pic_url": "str",
                        "price": 0.0,
                        "outer_sku_id": "str",
                        "item_title": "str",
                        "quantity": 0
                    }
                ],
                "combine_sku_infos": [
                    {
                        "spu_id": "str",
                        "sku_id": "str",            // 没有设置值
                        "props": "str",
                        "pic_url": "str",
                        "price": 0.0,
                        "outer_sku_id": "str",
                        "item_title": "str",
                        "quantity": 0
                    }
                ]
            }
        ],
        "msg": ""
    }
    """
    try:
        page_no = int(request.args.get("page_no", 1))
    except ValueError:
        return jsonify(data=None, msg="error"), 400

    spu_id_list = request.args.getlist("spu_id", str)
    if len(spu_id_list) > app_config.BATCH_SKUS_SPU_ID_LIST_LENGTH:
        return jsonify(data=None, msg=f"查询太多的商品，仅支持{app_config.BATCH_SKUS_SPU_ID_LIST_LENGTH}条"), 400

    mini_app_spu_id_list = request.args.getlist("spu_id[]", str)
    if len(spu_id_list) > app_config.BATCH_SKUS_SPU_ID_LIST_LENGTH:
        return jsonify(data=None, msg=f"查询太多的商品，仅支持{app_config.BATCH_SKUS_SPU_ID_LIST_LENGTH}条"), 400

    spu_ids = spu_id_list + mini_app_spu_id_list

    source = request.args.get("source")
    if "erp" == source:
        shop = g.shop
        erp_type = compute_erp_type(source, shop)
        access_token = get_access_token(erp_type)
        result = item_client.get_provider_sku(
            shop.sid, shop.nick, shop.platform, token=access_token, spu_id=spu_ids, page_no=page_no, page_size=2000
        )

        spu_sku_map = defaultdict(list)
        combine_spu_sku_map = defaultdict(list)
        # 由于超时是会抛出异常的，然后因为被捕获了，于是超时的返回值是 {}。
        # 直接获取的话，会得到 None，因此需要设置默认值。
        for item in result.get("skuInfo", []):
            spu_sku_map[item["spu_id"]].append(item)
        for item in result.get("combineItems", []):
            combine_spu_sku_map[item.get("spu_id")].append(item)

        sku_list = [
            {"spu_id": key, "sku_infos": value, "combine_sku_infos": combine_spu_sku_map.get(key, [])}
            for key, value in spu_sku_map.items()
        ]
    else:
        sku_list = [sku for spu_id in spu_id_list if (sku := find_sku(g.shop, spu_id))]

    return jsonify(data=sku_list, msg="")


@api.route("/spus/<spu_id>")
def get_spu(spu_id):
    """获取spu详情"""
    sid = get_sid()
    if not sid:
        return jsonify(reason="缺失店铺id"), 400

    source = request.args.get("source")
    if "erp" == source:
        shop = g.shop
        erp_type = compute_erp_type(source, shop)
        access_token = get_access_token(erp_type)
        spu = item_client.get_provider_item(
            shop.sid,
            shop.nick,
            shop.platform,
            token=access_token,
            spu_id=[spu_id],
            page_size=1,
            query_entity=HighLevelSearchItemSchema(spu=spu_id),
        )
        return jsonify(item_client.convert_to_single_spu(spu))
    spu = find_spu(g.shop, spu_id)
    if spu is None:
        return jsonify(spu=[], reason="结果为空"), 404
    return jsonify(spu)


@api.post("/history-images")
@shop_required
@validate
def get_history_images(body: HistorySchema):
    logger.info(body)
    is_contact_encrypted = body.contact is None or is_buyer_nick_encrypt(body.contact)
    contact_code = body.ccode
    buyer_open_uid = body.buyer_open_uid

    if not is_contact_encrypted:
        buyer_nick = body.contact
    elif buyer_open_uid is not None:
        if not buyer_open_uid:
            buyer_nick = ""
        elif buyer_res := buyer_client.get_buyer_nick_by_open_uid(
            buyer_open_uid=buyer_open_uid, app_id=app_config.TAOBAO_APP_KEY
        ):
            buyer_nick = buyer_res.buyer_nick
        else:
            buyer_nick = trade_client.try_get_plaintext_nick_from_trade(
                buyer_open_uid=buyer_open_uid,
            )

    else:
        buyer_nick = ""

    if not buyer_nick and not contact_code:
        return jsonify({"success": False, "reason": "无法获取买家信息"}), 400

    body.buyer_open_uid = buyer_open_uid
    body.contact = buyer_nick
    body.ccode = contact_code or None

    resp = {"result": {"results": chat_history_client.get_history_images(g.shop.nick, body.contact, count=body.count)}}

    return jsonify(resp), 200


def compute_erp_type(source: str, shop: "Shop") -> Optional[ErpType]:
    if source != "erp":
        return None
    erp_info: ErpInfo = shop.erps.filter(ErpInfo.shop_id == shop.id).first()  # type: ignore[assignment]
    return erp_info.erp_type if erp_info is not None else None


def get_access_token(erp_type: Optional[ErpType]):
    if erp_type not in [ErpType.KUAIMAI, ErpType.WANLINIU, ErpType.DUOHONG]:
        return None
    apollo_key = f"{erp_type.name}_SECRET_KEY"
    jwt_key = current_app.config.get(apollo_key, "jwtS")
    return g.shop.get_mola_kuaimai_token(jwt_key)


@api.post("/items/<source>")
@shop_required
@validate
def get_erp_items(source, body: ErpSkuListSchema):
    match source:
        case "erp":
            erp_type = compute_erp_type(source, g.shop)
            access_token = get_access_token(erp_type)
        case _:
            if g.shop.is_ks():
                access_token = g.shop.get_access_token()
                if not access_token:
                    return jsonify(item_infos=[], reason="缺失快手授权记录"), 400
            else:
                access_token = None
    resp = erp_item_client.get_erp_sku_list(
        source=source,
        keywords=body.keywords,
        sku_ids=body.sku_ids,
        query_entity=body.query_entity,
        items=body.items,
        outer_sku_ids=body.outer_sku_ids,
        sid=g.shop.sid,
        seller_nick=g.shop.nick,
        channel_type=g.shop.platform,
        access_token=access_token,
        page_no=body.page_no,
        page_size=body.page_size,
        query_type=body.query_type,
        erp_item_filter=body.filter,
    )
    return jsonify(resp), 200


class Warehouse(BaseModel):
    name: str = Field(description="仓库名称")
    warehouse_no: str = Field(description="（分）仓库编号")


class WarehouseResponseData(BaseModel):
    warehouses: List[Warehouse]


WarehouseResponse = GenericResponse[Optional[WarehouseResponseData]]


@api.get("/items_warehouse")
@shop_required
@validate
def get_item_warehouse() -> WarehouseResponse:
    try:
        erp_type = compute_erp_type("erp", g.shop)
        if erp_type == ErpType.JST:
            jst = JstNewSDK(g.shop.sid)
            wms_partner_query_response = jst.wms_partner_query()
            if wms_partner_query_response.data is None:
                return WarehouseResponse.Failed(msg=f"查询失败：{wms_partner_query_response.msg}")
            warehouses = [
                Warehouse(name=warehouse.name, warehouse_no=warehouse.wms_co_id)
                for warehouse in wms_partner_query_response.data.datas
            ]
        elif erp_type == ErpType.WDT:
            wdt = WdtClient(g.shop.sid)
            warehouses = [
                Warehouse(name=warehouse.name, warehouse_no=warehouse.warehouse_no)
                for warehouse in wdt.warehouse_query().response.warehouses
            ]
        elif erp_type == ErpType.WDTULTI:
            wdt_ulti_qm = WdtUltiQM(g.shop.sid)
            warehouses = [
                Warehouse(name=warehouse.name, warehouse_no=warehouse.warehouse_no)
                for warehouse in wdt_ulti_qm.all_warehouse_query()
            ]
        else:
            return WarehouseResponse.Failed("不支持的erp类型")
    except ClientRequestError as e:
        return WarehouseResponse.Failed(msg=e.message)
    except BizError as e:
        return WarehouseResponse.Failed(msg=e.biz_display)

    return WarehouseResponse.Success(WarehouseResponseData(warehouses=warehouses))


SkuInventoryResponse = GenericResponse[Optional[SkuInventoryResponseData]]


@api.get("/items_sku_inventory")
@shop_required
@validate
def get_item_inventories() -> SkuInventoryResponse:
    sku_ids = list(set(request.args.getlist("sku_ids[]", str)))
    warehouse_no_query_arg = request.args.get("warehouse_no")
    if not sku_ids:
        return SkuInventoryResponse.Failed("缺失sku_ids").dict()
    erp_type = compute_erp_type("erp", g.shop)
    if erp_type == ErpType.JST:
        jst = JstNewSDK(g.shop.sid)
        # 聚水潭一次 api 只能查询一个仓库的库存信息
        # 如果不指定仓库编号，默认展示20条
        if not warehouse_no_query_arg:
            warehouse_list, err = jst_get_warehouses(jst, size=20)
            if err:
                return SkuInventoryResponse.Failed(msg=err)
        else:
            try:
                warehouse_no = int(warehouse_no_query_arg)
            except ValueError:
                warehouse_no = 0
            all_warehouse_list, err = jst_get_warehouses(jst)
            if err:
                return SkuInventoryResponse.Failed(msg=err)
            warehouse_list = [
                WmsPartnerQueryResp.Data.WmsPartner(
                    wms_co_id=int(warehouse_no),
                    name={item.wms_co_id: item.name for item in all_warehouse_list}.get(warehouse_no, ""),
                )
            ]

        inventories = []
        for warehouse in warehouse_list:
            inventory_query_response = jst.inventory_query(sku_ids=sku_ids, wms_co_id=warehouse.wms_co_id)
            if inventory_query_response.data is None:
                return SkuInventoryResponse.Failed(msg=f"查询失败：{inventory_query_response.msg}")
            for item in inventory_query_response.data.inventorys:
                inventories.append(
                    SkuInventory(
                        sku_id=item.sku_id,
                        warehouse_name=warehouse.name,
                        qty=item.qty,
                        available_qty=jst_compute_available_qty(item),
                    )
                )
    elif erp_type == ErpType.WDT:
        wdt = WdtClient(g.shop.sid)
        try:
            inventories = [
                SkuInventory(
                    sku_id=stock.spec_no,
                    warehouse_name=stock.warehouse_name,
                    qty=stock.stock_num,
                    available_qty=stock.avaliable_num,
                )
                for sku_id in sku_ids
                for stock in wdt.stock_query(spec_no=sku_id, warehouse_no=warehouse_no_query_arg).response.stocks
            ]
        except WdtRequestError as e:
            return SkuInventoryResponse.Failed(msg=f"查询失败：{e.message}")
    elif erp_type == ErpType.WDTULTI:
        wdt_ulti_client = WdtUltiOpenAPIClient(g.shop.sid)
        try:
            inventories = [
                SkuInventory(
                    sku_id=stock.spec_no,
                    warehouse_name=stock.warehouse_name,
                    qty=stock.stock_num,
                    available_qty=stock.available_send_stock,
                )
                for stock in wdt_ulti_client.query_sku_stocks(
                    sku_ids=sku_ids, warehouse_no=warehouse_no_query_arg
                ).data.detail_list
            ]
        except WdtRequestError as e:
            return SkuInventoryResponse.Failed(msg=f"查询失败：{e.message}")
    else:
        return SkuInventoryResponse.Failed("不支持的erp类型")

    return SkuInventoryResponse.Success(SkuInventoryResponseData(inventories=inventories))


ErpSkuInventoriesResponse = GenericResponse[Optional[ErpSkuInventoriesResponseData]]


@api.post("/erp_sku_inventories")
@shop_required
@validate
def get_sku_inventories(body: ErpSkuInventoriesQueryParams):
    """
    查询 SKU 的库存信息。
    :param body:
    :return:
    """
    shop = g.shop
    erp_info: ErpInfo | None = ErpInfo.query.filter(ErpInfo.shop_id == shop.id).order_by(ErpInfo.id.desc()).first()
    if erp_info is None:
        return ErpSkuInventoriesResponse.Failed(msg="店铺没有绑定 ERP")
    if erp_info.erp_type != ErpType.JST and body.query_method in [
        ErpSkuQueryMethod.SPU,
        ErpSkuQueryMethod.NAME,
    ]:
        return ErpSkuInventoriesResponse.Failed(msg="非聚水潭 ERP 无法使用款式编码进行查询")

    match erp_info.erp_type:
        case ErpType.JST:
            jst_new_sdk = JstNewSDK(shop.sid)
            if warehouse := body.warehouse:
                try:
                    warehouse_code = int(warehouse.warehouse_code)
                except ValueError:
                    return ErpSkuInventoriesResponse.Failed(msg="仓库编号格式不正确")
                warehouses = [
                    WmsPartnerQueryResp.Data.WmsPartner(
                        wms_co_id=warehouse_code,
                        name=warehouse.warehouse_name,
                    )
                ]
            else:
                warehouses, err = jst_get_warehouses(jst_new_sdk, size=20)
                # 查询仓库报错，则直接报错。
                if err:
                    return ErpSkuInventoriesResponse.Failed(msg=err)
            # 根据不同的查询条件请求不同的接口。
            if body.query_method == ErpSkuQueryMethod.SKU:
                sku_id_to_sku_info_mapping, err = jst_query_skus(jst_new_sdk, sku_ids=[body.keyword])
            elif body.query_method == ErpSkuQueryMethod.SPU:
                sku_id_to_sku_info_mapping, err = jst_query_skus(jst_new_sdk, spu_ids=[body.keyword])
            else:
                return ErpSkuInventoriesResponse.Failed(msg="暂不支持的查询方式")
            # 查询商品报错，则返回部分商品信息以及报错原因。
            if err:
                return ErpSkuInventoriesResponse.Success(
                    ErpSkuInventoriesResponseData(
                        sku_infos=list(sku_id_to_sku_info_mapping.values()),
                        is_completed=False,
                        reason="商品查询失败：{}".format(err),
                    )
                )
            if len(sku_id_to_sku_info_mapping) == 0:
                return ErpSkuInventoriesResponse.Failed(msg="未能找到商品信息")
            skus, err = jst_match_skus_inventories(jst_new_sdk, sku_id_to_sku_info_mapping, warehouses)
            # 补充商品库存信息报错，则返回部分商品信息以及报错原因。
            if err:
                return ErpSkuInventoriesResponse.Success(
                    ErpSkuInventoriesResponseData(
                        sku_infos=list(skus.values()), is_completed=False, reason="库存查询失败：{}".format(err)
                    )
                )
            return ErpSkuInventoriesResponse.Success(
                ErpSkuInventoriesResponseData(
                    sku_infos=list(skus.values()),
                )
            )
        case ErpType.WDT:
            wdt_sdk = WdtOpenAPIClient(shop.sid)
            wdt_client = WdtClient(shop.sid)
            # 根据不同的查询条件请求不同的接口。
            if body.query_method == ErpSkuQueryMethod.SKU:
                sku_id_to_sku_info_mapping, err = wdt_query_skus(wdt_sdk, sku_id=body.keyword)
            elif body.query_method == ErpSkuQueryMethod.SPU:
                sku_id_to_sku_info_mapping, err = wdt_query_skus(wdt_sdk, spu_id=body.keyword)
            else:
                return ErpSkuInventoriesResponse.Failed(msg="暂不支持的查询方式")
            # 查询商品报错，则返回部分商品信息以及报错原因。
            if err:
                return ErpSkuInventoriesResponse.Success(
                    ErpSkuInventoriesResponseData(
                        sku_infos=list(sku_id_to_sku_info_mapping.values()),
                        is_completed=False,
                        reason="商品查询失败：{}".format(err),
                    )
                )
            if len(sku_id_to_sku_info_mapping) == 0:
                return ErpSkuInventoriesResponse.Failed(msg="未能找到商品信息")
            if body.query_method == ErpSkuQueryMethod.SKU:
                if sku_info := sku_id_to_sku_info_mapping.get(body.keyword):
                    sku_id_to_sku_info_mapping = {body.keyword: sku_info}
                else:
                    return ErpSkuInventoriesResponse.Failed(msg="未匹配到对应 SKU 的信息")
            if warehouse := body.warehouse:
                skus, err = wdt_match_skus_inventories(wdt_client, sku_id_to_sku_info_mapping, warehouse.warehouse_code)
            else:
                skus, err = wdt_match_skus_inventories(wdt_client, sku_id_to_sku_info_mapping)
            # 补充商品库存信息报错，则返回部分商品信息以及报错原因。
            if err:
                return ErpSkuInventoriesResponse.Success(
                    ErpSkuInventoriesResponseData(
                        sku_infos=list(skus.values()), is_completed=False, reason="库存查询失败：{}".format(err)
                    )
                )
            return ErpSkuInventoriesResponse.Success(
                ErpSkuInventoriesResponseData(
                    sku_infos=list(skus.values()),
                )
            )
        case ErpType.WDTULTI:
            wdt_ulti_client = WdtUltiOpenAPIClient(shop.sid)
            # 根据不同的查询条件请求不同的接口。
            if body.query_method == ErpSkuQueryMethod.SKU:
                sku_id_to_sku_info_mapping, err = wdt_ulti_query_skus(wdt_ulti_client, sku_id=body.keyword)
            elif body.query_method == ErpSkuQueryMethod.SPU:
                sku_id_to_sku_info_mapping, err = wdt_ulti_query_skus(wdt_ulti_client, spu_id=body.keyword)
            else:
                return ErpSkuInventoriesResponse.Failed(msg="暂不支持的查询方式")
                # 查询商品报错，则返回部分商品信息以及报错原因。
            if err:
                return ErpSkuInventoriesResponse.Success(
                    ErpSkuInventoriesResponseData(
                        sku_infos=list(sku_id_to_sku_info_mapping.values()),
                        is_completed=False,
                        reason="商品查询失败：{}".format(err),
                    )
                )
            if len(sku_id_to_sku_info_mapping) == 0:
                return ErpSkuInventoriesResponse.Failed(msg="未能找到商品信息")
            if body.query_method == ErpSkuQueryMethod.SKU:
                if sku_info := sku_id_to_sku_info_mapping.get(body.keyword):
                    sku_id_to_sku_info_mapping = {body.keyword: sku_info}
                else:
                    return ErpSkuInventoriesResponse.Failed(msg="未匹配到对应 SKU 的信息")
            if warehouse := body.warehouse:
                skus, err = wdt_ulti_match_skus_inventories(
                    wdt_ulti_client, sku_id_to_sku_info_mapping, warehouse.warehouse_code
                )
            else:
                skus, err = wdt_ulti_match_skus_inventories(wdt_ulti_client, sku_id_to_sku_info_mapping)
            # 补充商品库存信息报错，则返回部分商品信息以及报错原因。
            if err:
                return ErpSkuInventoriesResponse.Success(
                    ErpSkuInventoriesResponseData(
                        sku_infos=list(skus.values()), is_completed=False, reason="库存查询失败：{}".format(err)
                    )
                )
            return ErpSkuInventoriesResponse.Success(
                ErpSkuInventoriesResponseData(
                    sku_infos=list(skus.values()),
                )
            )
        case _:
            return ErpSkuInventoriesResponse.Failed(msg="尚未支持的 ERP")


ErpSkusResponse = GenericResponse[Optional[ErpSkuResponseData]]


@api.post("/order_skus")
@shop_required
@validate
def get_skus_by_trades(body: ErpSkuQueryParams):
    shop: Shop | None = (
        db.ro_session.query(Shop)
        .filter(
            Shop.sid == body.sid,
        )
        .first()
    )
    if shop is None:
        return ErpSkusResponse.Failed(msg="未查询到对应的店铺")
    erp_info: ErpInfo | None = (
        db.ro_session.query(ErpInfo).filter(ErpInfo.shop_id == shop.id).order_by(ErpInfo.id.desc()).first()
    )
    if erp_info is None:
        return ErpSkusResponse.Failed(msg="店铺没有绑定 ERP")

    trades = []

    if body.query_source == QuerySkuSourceType.PLATFORM:
        match shop.platform:
            case ShopPlatform.TMALL | ShopPlatform.TAOBAO:
                for trade in body.get_final_trades():
                    has_missed, output_skus = taobao_get_order_skus(trade.tid, shop.sid, trade.oid)
                    if output_skus is None:
                        continue
                    trades.append(
                        ErpSkuResponseData.Order(
                            tid=trade.tid, oid=trade.oid, products=output_skus, has_missed=has_missed
                        )
                    )
            case ShopPlatform.PDD:
                for trade in body.get_final_trades():
                    has_missed, output_skus = pdd_get_order_skus(trade.tid, trade.oid)
                    if output_skus is None:
                        continue
                    trades.append(
                        ErpSkuResponseData.Order(
                            tid=trade.tid, oid=trade.oid, products=output_skus, has_missed=has_missed
                        )
                    )
            case ShopPlatform.DOUDIAN:
                for trade in body.get_final_trades():
                    has_missed, output_skus = dy_get_order_skus(trade.tid, trade.oid)
                    if output_skus is None:
                        continue
                    trades.append(
                        ErpSkuResponseData.Order(
                            tid=trade.tid, oid=trade.oid, products=output_skus, has_missed=has_missed
                        )
                    )
            case ShopPlatform.ALIBABA:
                for trade in body.get_final_trades():
                    has_missed, output_skus = alibaba_get_order_skus(trade.tid, trade.oid, shop.org_id)
                    if output_skus is None:
                        continue
                    trades.append(
                        ErpSkuResponseData.Order(
                            tid=trade.tid, oid=trade.oid, products=output_skus, has_missed=has_missed
                        )
                    )
            case ShopPlatform.JD:
                for trade in body.get_final_trades():
                    has_missed, output_skus = jd_get_order_skus(
                        trade.tid,
                        shop.sid,
                    )
                    if output_skus is None:
                        continue
                    trades.append(
                        ErpSkuResponseData.Order(
                            tid=trade.tid, oid=trade.oid, products=output_skus, has_missed=has_missed
                        )
                    )
            case _:
                return ErpSkusResponse.Failed(msg="尚未支持的平台")
    else:
        match erp_info.erp_type:
            case ErpType.JST:
                jst_new_sdk = JstNewSDK(shop.sid)
                for trade in body.get_final_trades():
                    has_missed, output_skus = jst_get_order_skus(shop, jst_new_sdk, trade.tid, trade.oid)
                    if output_skus is None:
                        continue
                    trades.append(
                        ErpSkuResponseData.Order(
                            tid=trade.tid, oid=trade.oid, products=output_skus, has_missed=has_missed
                        )
                    )
            case ErpType.WDT:
                wdt_client: WdtClient = WdtClient(shop.sid)
                wdt_sdk: WdtOpenAPIClient = WdtOpenAPIClient(shop.sid)
                for trade in body.get_final_trades():
                    has_missed, output_skus = wdt_get_order_skus(wdt_client, wdt_sdk, trade.tid, trade.oid)
                    if output_skus is None:
                        continue
                    trades.append(
                        ErpSkuResponseData.Order(
                            tid=trade.tid, oid=trade.oid, products=output_skus, has_missed=has_missed
                        )
                    )
            case ErpType.WDTULTI:
                wdt_ulti_qm_sdk = WdtUltiQM(shop.sid)
                wdt_ulti_sdk = WdtUltiOpenAPIClient(shop.sid)
                for trade in body.get_final_trades():
                    has_missed, output_skus = wdt_ulti_get_order_skus(
                        wdt_ulti_qm_sdk, wdt_ulti_sdk, trade.tid, trade.oid
                    )
                    if output_skus is None:
                        continue
                    trades.append(
                        ErpSkuResponseData.Order(
                            tid=trade.tid, oid=trade.oid, products=output_skus, has_missed=has_missed
                        )
                    )
            case ErpType.JACKYUN:
                jackyun_qm_sdk = JackyunQmSDK(shop.sid)
                jackyun_sdk = JackyunSDK(shop.sid)
                for trade in body.get_final_trades():
                    has_missed, output_skus = jackyun_get_order_skus(jackyun_qm_sdk, jackyun_sdk, trade.tid, trade.oid)
                    if output_skus is None:
                        continue
                    trades.append(
                        ErpSkuResponseData.Order(
                            tid=trade.tid, oid=trade.oid, products=output_skus, has_missed=has_missed
                        )
                    )
            case ErpType.WDGJ:
                wdgj_sdk = WdgjQmSDK(shop.sid)
                for trade in body.get_final_trades():
                    has_missed, output_skus = wdgj_get_order_skus(wdgj_sdk, trade.tid, trade.oid)
                    trades.append(
                        ErpSkuResponseData.Order(
                            tid=trade.tid, oid=trade.oid, products=output_skus, has_missed=has_missed
                        )
                    )
            case _:
                return ErpSkusResponse.Failed(msg="尚未支持的 ERP")

    if len(trades) == 0:
        return ErpSkusResponse.Failed(msg="未找到符合条件的订单")
    return ErpSkusResponse.Success(ErpSkuResponseData(trades=trades))


@api.post("/items_sku/<source>")
@shop_required
@validate
def get_items_by_channel(source, body: ErpItemSkuListSchema):
    shop = Shop.Queries.optimal_shop_by_sid(
        sid=body.channel_no or g.auth.store_id,
        platform=body.channel_type or g.auth.channel_type.upper(),
        org_id=g.shop.org_id,
    )
    if not shop:
        return jsonify(reason="缺失渠道信息参数"), 400
    match source:
        case "erp":
            erp_type = compute_erp_type(source, shop)
            access_token = get_access_token(erp_type)
        case _:
            if shop.is_ks():
                access_token = shop.get_access_token()
                if not access_token:
                    return jsonify(item_infos=[], reason="缺失快手授权记录"), 400
            else:
                access_token = None
    resp = erp_item_client.get_erp_sku_list(
        source=source,
        keywords=body.keywords,
        sku_ids=body.sku_ids,
        query_entity=body.query_entity,
        items=body.items,
        outer_sku_ids=body.outer_sku_ids,
        sid=shop.sid,
        seller_nick=shop.nick,
        channel_type=shop.platform,
        access_token=access_token,
        page_no=body.page_no,
        page_size=body.page_size,
        query_type=body.query_type,
        erp_item_filter=body.filter,
    )
    return jsonify(resp), 200


def generate_raw_body_and_digest(body: dict, secret: str) -> tuple[str, str]:
    # 生成请求体和数字签名。
    raw_body = json.dumps(body, separators=(",", ":"), ensure_ascii=False)
    content = raw_body + secret
    result = b64encode(md5(content.encode("utf-8")).digest()).decode()
    return raw_body, result


@api.get("/buyer_address/<media>")
@shop_required
def get_resolved_address_by_ai(media):
    """
    将前端输入的地址字符串解析成地址对象（省、市、区、区号、详细地址、收件人、手机号）
    """
    keywords: str = request.args.get("keywords", "")
    if not keywords:
        return jsonify(reason="缺失地址信息"), 400
    logger.info("input address: {}".format(keywords))
    keywords = keywords.replace("\r", " ")
    keywords = keywords.replace("\n", " ")
    keywords = keywords.replace("，", ", ")
    resp = aliyun_addrp_client.extract_express(keywords)
    return jsonify(resp), 200


@api.get("/auth/enable-check")
@shop_required
@validate
def auth_enable_check(query: AuthEnableCheckRequest):
    # 没全部删掉是因为前端的侧边栏代码还没删干净
    return jsonify(success=False, data={}, msg="")


def get_sid() -> str:
    sid = request.args.get("sid")
    if not sid:
        return g.auth.store_id or ""
    else:
        logger.info(f"get sid in query params,sid: {sid}")
        return sid
