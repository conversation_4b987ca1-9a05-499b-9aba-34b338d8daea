import json
from datetime import datetime
from datetime import timedelta
from typing import TypeV<PERSON>
from typing import cast

import arrow
from flask import Blueprint
from flask import make_response
from flask import request
from loguru import logger
from pydantic import ValidationError
from result import Err
from result import Ok
from robot_types.helper import TypeCheckError

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.bi.dashboard import ReportPanel
from robot_processor.bi.dataset.etl.pipeline import panel_dataset_from_database
from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.client import limiter
from robot_processor.client import taobao_client
from robot_processor.enums import Action
from robot_processor.enums import FromType
from robot_processor.error.errors import ScenarioError
from robot_processor.ext import db
from robot_processor.form.models import Form
from robot_processor.logging import to_log
from robot_processor.logging import vars as log_vars
from robot_processor.openapi.models import OpenApiCredentials
from robot_processor.openapi.schema import ErrorDetail
from robot_processor.openapi.schema import Method
from robot_processor.openapi.schema import Response
from robot_processor.openapi.schema import ResponseCode
from robot_processor.openapi.utils import compatible_sign
from robot_processor.openapi.utils import ensure_response
from robot_processor.openapi.utils import handle_validation_error
from robot_processor.openapi.utils import sign
from robot_processor.shop.models import Shop
from robot_processor.utils import dict_diff
from robot_processor.utils import filter_none
from robot_processor.utils import unwrap_optional
from rpa.mola import MolaClient

router = Blueprint("openapi-biz", __name__)
router.register_error_handler(ValidationError, handle_validation_error)


@router.post("/open")
@ensure_response
def dispatcher():
    from robot_processor.openapi.schema.dispatcher import CommonParam

    common_param = CommonParam(**request.args)
    log_vars.context.update(openapi_method=common_param.method)

    if not (credential := OpenApiCredentials.query_by_app_key(common_param.app_key)):
        return Response.Failed(
            ResponseCode.INVALID_ARGUMENT_APP_KEY,
            [ErrorDetail(loc=["query_string", "app_key"], msg="认证失败，请求使用的 app_key 不存在")],
        )

    sign_expect = sign(
        credential.app_key,
        credential.app_secret,
        common_param.method,
        common_param.timestamp,
        request.get_json(silent=True) or {},
    )
    compatible_sign_expect = compatible_sign(
        credential.app_key,
        credential.app_secret,
        common_param.method,
        common_param.timestamp,
        request.get_json(silent=True) or {},
    )
    if sign_expect != common_param.sign and compatible_sign_expect != common_param.sign:
        return Response.Failed(
            ResponseCode.INVALID_ARGUMENT_SIGNATURE,
            [ErrorDetail(loc=["query_string", "sign"], msg="无效签名")],
        )

    if not limiter.acquire_permission(*credential.get_limit(common_param.method)):
        return Response.Failed(ResponseCode.ACCESS_CONTROL_API_CALL_LIMITED)

    executor = Executor(credential)
    response = common_param.method.dispatch(executor)
    return response


T = TypeVar("T")


class Executor:
    def __init__(self, credential: OpenApiCredentials):
        self.credential = credential

    def get_request_body(self, request_model: type[T]) -> T:
        return request_model(**request.get_json())

    @Method.fs_tmall_rate.register
    def fs_tmall_rate(self):
        from robot_processor.openapi.schema.fs_tmall_rate import Request

        param = self.get_request_body(Request)
        shop = Shop.query.filter_by(nick=param.seller_nick).first()
        if not shop or int(shop.org_id) != self.credential.org_id:  # type: ignore[arg-type]
            return Response.Failed(
                ResponseCode.INVALID_ARGUMENT,
                [ErrorDetail(loc=["body", "seller_nick"], msg="店铺主账号错误")],
            )
        grant = shop.get_recent_record()
        if not grant:
            return Response.Failed(
                ResponseCode.INVALID_ARGUMENT,
                [ErrorDetail(loc=["body", "seller_nick"], msg="店铺主账号没有授权")],
            )
        dt = datetime.strptime(param.dt, "%Y%m%d")
        start_date = dt.strftime("%Y-%m-%d %H:%M:%S")
        dt = dt + timedelta(hours=23, minutes=59, seconds=59)
        end_date = dt.strftime("%Y-%m-%d %H:%M:%S")
        rates = []
        access_token = grant.access_token
        match taobao_client.taobao_traderates_get_by_datetime_range(access_token, 1, start_date, end_date):
            case Ok(resp):
                for rate in resp.get("trade_rates") or []:
                    match taobao_client.tmall_traderate_feeds_get(access_token, rate["oid"]):  # type: ignore[arg-type]
                        case Ok(detail_resp):
                            detail_resp["tid"] = rate["tid"]  # type: ignore[typeddict-unknown-key]
                            detail_resp["oid"] = rate["oid"]  # type: ignore[typeddict-unknown-key]
                            rates.append(detail_resp)
                        case Err(exception):
                            logger.warning(str(exception))
                            continue
            case Err(exception):
                return Response.Failed(
                    ResponseCode.FETCH_RATE_FAILED,
                    [ErrorDetail(loc=["body"], msg=str(exception))],
                )

        response = make_response(Response.Success(rates).json().encode("ascii"))
        response.headers.set("Content-Type", "application/json; charset=ascii")

        return response

    @Method.fs_order_create.register
    def fs_order_create(self):
        from robot_processor.openapi.schema.fs_order_create import CreateResult
        from robot_processor.openapi.schema.fs_order_create import Request

        param = self.get_request_body(Request)
        if not (form := Form.query.get(param.form_id)):
            return Response.Failed(
                ResponseCode.INVALID_ARGUMENT,
                [ErrorDetail(loc=["body", "form_id"], msg="表单不存在")],
            )
        if not form.versions.first():
            return Response.Failed(
                ResponseCode.INVALID_ARGUMENT,
                [ErrorDetail(loc=["body", "form_id"], msg="工单模板未发布")],
            )
        for entry in param.entries:
            if entry.key == "system_business_order_shop_nick" or entry.label == "所属店铺":
                shop_entry = entry
                break
        else:
            return Response.Failed(
                ResponseCode.INVALID_ARGUMENT,
                [ErrorDetail(loc=["body", "entries"], msg="缺少 所属店铺")],
            )
        # TODO: 系统组件修改为 compute 方式后，可以删除这段逻辑
        shop = (
            Shop.query.filter(Shop.org_id == str(self.credential.org_id)).filter(Shop.nick == shop_entry.value).first()
        )
        if not shop:
            return Response.Failed(
                ResponseCode.INVALID_ARGUMENT,
                [ErrorDetail(loc=["body", "entries"], msg="所属店铺不存在")],
            )

        manager = BusinessManager(
            form,
            shop,
            AccountDetailV2.openapi_processor(self.credential.app_key),
            FromType.KAFKA_TRADE_EVENT,
        )
        param.supply_entry_key(manager.ui_schema_for_create)
        data = dict(
            (entry.key, entry.value)
            for entry in param.entries
            if entry.key and not entry.key.startswith("system_business_order_")
        )
        type_check_result = manager.type_check(data, "forbid")
        if type_check_result.is_err():
            errors = type_check_result.err().errors
            details = []
            for error in errors:
                details.append(ErrorDetail(loc=["body", "entries"] + error.loc, msg=str(error)))
            return Response.Failed(ResponseCode.INVALID_ARGUMENT, details)
        res = manager.pipeline_create_order(data)
        match res:
            case Err(exception):
                return Response.Failed(ResponseCode.BUSINESS_ORDER_CREATE_FAILED, [{"msg": str(exception)}])
            case Ok(bo):
                return Response.Success(CreateResult(business_order_id=bo.id))

    @Method.fs_panel_data.register
    def fs_panel_data(self):
        from robot_processor.openapi.schema.fs_panel_data import Request

        param = self.get_request_body(Request)
        if not (panel := ReportPanel.query.get(param.panel_id)):
            return Response.Failed(
                ResponseCode.INVALID_ARGUMENT,
                [ErrorDetail(loc=["body", "panel_id"], msg="panel不存在")],
            )
        if panel.dashboard.org_id != self.credential.org_id:
            return Response.Failed(
                ResponseCode.INVALID_ARGUMENT,
                [ErrorDetail(loc=["body", "panel_id"], msg="非法的panel id")],
            )

        begin = arrow.get(param.begin_updated_at)
        end = arrow.get(param.end_updated_at)
        df = panel_dataset_from_database(panel).unwrap()
        if "最近更新时间" not in df.columns:
            return Response.Failed(
                ResponseCode.INVALID_ARGUMENT,
                [ErrorDetail(loc=["body"], msg="panel中不包含最近更新时间")],
            )
        records = json.loads(df.to_json(orient="records", force_ascii=False))  # type: ignore[arg-type]
        records = list(filter(lambda record: begin <= arrow.get(record["最近更新时间"]) <= end, records))
        return Response.Success(records)

    @Method.fs_voc_ask_list.register
    def voc_ask_list(self):
        from robot_processor.openapi.schema.voc_ask_list import Request

        param = self.get_request_body(Request)
        shop = Shop.query.filter_by(nick=param.seller_nick).first()
        if not shop or int(shop.org_id) != self.credential.org_id:  # type: ignore[arg-type]  #
            return Response.Failed(
                ResponseCode.INVALID_ARGUMENT,
                [ErrorDetail(loc=["body", "seller_nick"], msg="店铺主账号错误")],
            )
        res = MolaClient(shop.sid).get_voc_ask_list(param.spu_id, 1, 100)
        match res:
            case Ok(resp):
                for ask in resp["dataSource"]:
                    answer_res = MolaClient(shop.sid).get_voc_answer_list(ask["id"], 1, 100)
                    match answer_res:
                        case Ok(answer_resp):
                            ask["answers"] = answer_resp["dataSource"]
                        case Err(_):
                            ask["answers"] = []
                            continue
                return Response.Success(resp["dataSource"])
            case Err(error_message):
                return Response.Failed(
                    ResponseCode.FETCH_VOC_ASK_LIST_FAILED,
                    [ErrorDetail(loc=["body"], msg=error_message)],
                )

    @Method.fs_form_info.register
    def fs_form_info(self):
        from robot_processor.business_order.models import get_form_composer
        from robot_processor.form.models import Form
        from robot_processor.form.models import FormShop
        from robot_processor.form.models import FormVersion
        from robot_processor.openapi.schema.fs_form_info import FormInfo
        from robot_processor.openapi.schema.fs_form_info import JsonSchema
        from robot_processor.openapi.schema.fs_form_info import JsonSchemaObject
        from robot_processor.openapi.schema.fs_form_info import Query

        query = self.get_request_body(Query)
        form = (
            db.session.query(Form)
            .join(FormShop, FormShop.form_id == Form.id)
            .filter(FormShop.org_id == self.credential.org_id)
            .filter(Form.id == query.form_id)
            .first()
        )
        if form is None:
            return Response.Failed(ResponseCode.FORM_INVALID)
        form = cast(Form, form)
        if query.form_version_id:
            form_version = db.session.query(FormVersion).filter_by(form_id=form.id, id=query.form_version_id).first()
        elif query.form_version_no:
            form_version = (
                db.session.query(FormVersion).filter_by(form_id=form.id, version_no=query.form_version_no).first()
            )
        else:
            form_version = form.versions.first()
        if form_version is None:
            return Response.Failed(ResponseCode.FORM_VERSION_INVALID)
        form_version = cast(FormVersion, form_version)
        form_info = FormInfo(
            form_id=form.id,
            form_version_id=form_version.id,
            form_version_no=form_version.version_no,
            form_name=form.name,
            schemas=dict(),
        )
        form_composer = get_form_composer(form_version.id)
        for step in form_composer.steps:
            properties = {symbol.name: JsonSchema.from_symbol(symbol) for symbol in step.symbols if not symbol.redirect}
            form_info.schemas[step.name] = JsonSchemaObject(properties=properties)
        return Response[FormInfo].Success(form_info)

    @Method.fs_order_list.register
    def fs_order_list(self):
        from sqlalchemy import select

        from robot_processor.business_order.models import BusinessOrder
        from robot_processor.constants import FORMAT_DATE_TIME
        from robot_processor.constants import TIME_ZONE
        from robot_processor.form.models import FormShop
        from robot_processor.form.models import FormVersion
        from robot_processor.openapi.schema.fs_order_list import BusinessOrderView
        from robot_processor.openapi.schema.fs_order_list import OrderListResult
        from robot_processor.openapi.schema.fs_order_list import Query
        from robot_processor.utils import unwrap_optional

        query = self.get_request_body(Query)
        if (query.form_id is None) and (query.sid is None):
            return Response.Failed(
                ResponseCode.INVALID_ARGUMENT,
                [ErrorDetail(loc=["body"], msg="form_id 和 sid 至少需要一个")],
            )

        stmt = select(BusinessOrder).select_from(BusinessOrder)
        # 可选的索引
        # - KEY `idx_form_id_status` (`form_id`, `status`)
        # - KEY `idx_sid_status` (`sid`, `status`)
        # - KEY `ix_business_order_sid_updated_at` (`sid`, `updated_at`)
        if query.form_id:
            stmt = stmt.where(BusinessOrder.form_id == query.form_id)
        if query.sid:
            stmt = stmt.where(BusinessOrder.sid == query.sid)
        if query.status:
            stmt = stmt.where(BusinessOrder.status == query.status)
        if query.ignore_deleted:
            stmt = stmt.where(BusinessOrder.deleted.isnot(True))
        if query.update_begin:
            stmt = stmt.where(BusinessOrder.updated_at >= query.update_begin.timestamp())
        if query.update_end:
            stmt = stmt.where(BusinessOrder.updated_at <= query.update_end.timestamp())
        stmt = stmt.order_by(BusinessOrder.updated_at.asc())
        stmt = stmt.limit(query.size + 1).offset((query.page - 1) * query.size)
        order_list = db.session.execute(stmt).scalars().all()
        has_next = len(order_list) > query.size
        order_list = order_list[: query.size]
        form_version_map = FormVersion.query_version_map_by_id(
            id_list=list({order.form_version_id for order in order_list if order.form_version_id})
        )
        shop_map = FormShop.query_shop_map(form_id_list=list({order.form_id for order in order_list if order.form_id}))
        result = OrderListResult(page=query.page, size=query.size, has_next=has_next, data=[])
        for order in order_list:
            order_form_version_no = form_version_map[unwrap_optional(order.form_version_id)]
            order_shop = shop_map[(unwrap_optional(order.form_id), unwrap_optional(order.sid))]
            order_view = BusinessOrderView(
                id=order.id,
                form_id=order.form_id,
                form_version_id=order.form_version_id,
                form_version_no=order_form_version_no,
                shop_sid=order.sid,
                shop_platform=order_shop.platform,
                shop_nick=order_shop.nick,
                shop_title=order_shop.title,
                status=order.status.name,
                deleted=bool(order.deleted),
                created_at=arrow.get(order.created_at, tzinfo=TIME_ZONE).format(FORMAT_DATE_TIME),
                updated_at=arrow.get(order.updated_at, tzinfo=TIME_ZONE).format(FORMAT_DATE_TIME),
                data=order.data.copy(),
            )
            result.data.append(order_view)
        return Response[OrderListResult].Success(result)

    @Method.fs_order_update.register
    def fs_order_update(self):
        from robot_processor.business_order.fsm import BusinessOrderStatusController
        from robot_processor.business_order.job_action import JobAction
        from robot_processor.business_order.models import BusinessOrder
        from robot_processor.openapi.schema.fs_order_update import Request

        query = self.get_request_body(Request)
        # 检查工单是否有效
        business_order = db.session.get(BusinessOrder, query.business_order_id)
        if business_order is None:
            return Response.Failed(
                ResponseCode.BUSINESS_ORDER_NOT_FOUND,
                [ErrorDetail(loc=["body", "business_order_id"], msg=f"工单 {query.business_order_id} 不可用")],
            )
        if unwrap_optional(business_order.shop).org_id != str(self.credential.org_id):
            return Response.Failed(
                ResponseCode.BUSINESS_ORDER_NOT_FOUND,
                [ErrorDetail(loc=["body", "business_order_id"], msg=f"工单 {query.business_order_id} 不可用")],
            )

        business_controller = BusinessOrderStatusController(
            business_order=business_order, operator=self.credential.as_account(), is_admin=True
        )
        job_action = JobAction(
            job_id=unwrap_optional(business_order.current_job_id), business_order_status_controller=business_controller
        )

        try:
            # 工单数据更新类
            if query.action in [Action.save, Action.accept, Action.update_order]:
                if query.entries is None:
                    return Response.Failed(
                        ResponseCode.INVALID_ARGUMENT,
                        [ErrorDetail(loc=["body", "entries"], msg="entries 不能为空")],
                    )
                entries = unwrap_optional(query.entries)
                system_business_order_data = {
                    entry.key for entry in entries if entry.key.startswith("system_business_order_")
                }
                if system_business_order_data:
                    return Response.Failed(
                        ResponseCode.INVALID_ARGUMENT, [ErrorDetail(loc=["body", "entries"], msg="系统字段不允许更新")]
                    )
                data = {entry.key: entry.value for entry in query.entries}
                logger.info(
                    f"更新工单数据 {to_log(dict_diff(business_order.data, filter_none({**business_order.data, **data})))}"
                )
                if query.action == Action.update_order:
                    if query.strategy is None:
                        return Response.Failed(
                            ResponseCode.INVALID_ARGUMENT,
                            [ErrorDetail(loc=["body", "strategy"], msg="更新策略不能为空")],
                        )
                    job_action.update_order(
                        data,
                        self.credential.as_account(),
                        operate_reason=query.reason,
                        update_strategy=query.strategy,
                    )
                else:
                    business_manager = BusinessManager.from_business_order(business_order)
                    step = unwrap_optional(business_order.current_job).step
                    business_manager.type_check_by_step(step, data, "forbid")
                if query.action == Action.accept:
                    job_action.accept(data, None, self.credential.as_account(), operate_reason=query.reason)
                else:
                    job_action.save(data, self.credential.as_account(), operate_reason=query.reason)

            # 工单状态变更
            elif query.action == Action.close:
                job_action.close(self.credential.as_account(), query.reason)
            elif query.action == Action.reopen:
                job_action.reopen(self.credential.as_account(), query.reason)
            elif query.action == Action.pause:
                job_action.pause(self.credential.as_account(), query.reason)
            elif query.action == Action.unpause:
                job_action.unpause(self.credential.as_account(), query.reason)
            elif query.action == Action.complete:
                job_action.complete(self.credential.as_account(), query.reason)
            else:
                return Response.Failed(
                    ResponseCode.INVALID_ARGUMENT,
                    [ErrorDetail(loc=["body", "action"], msg="不支持的操作")],
                )

        except TypeCheckError as e:
            error_details = []
            for error in e.errors:
                error_details.append(ErrorDetail(loc=["body", "entries"] + error.loc, msg=str(error)))
            return Response.Failed(ResponseCode.INVALID_ARGUMENT, error_details)
        except ScenarioError as e:
            return Response.Failed(
                ResponseCode.BUSINESS_ORDER_CANNOT_OPERATE, [ErrorDetail(loc=["body"], msg=e.biz_display)]
            )
        except Exception as e:
            return Response.Failed(ResponseCode.UNKNOWN, [ErrorDetail(loc=["body"], msg=str(e))])

        return Response.Success(None)

    @Method.fs_order_info.register
    def fs_order_info(self):
        from sqlalchemy import select

        from robot_processor.business_order.models import BusinessOrder
        from robot_processor.constants import FORMAT_DATE_TIME
        from robot_processor.constants import TIME_ZONE
        from robot_processor.openapi.schema.fs_order_info import BusinessOrderView
        from robot_processor.openapi.schema.fs_order_info import Request
        from robot_processor.openapi.schema.fs_order_info import Response as OrderInfoResponse

        query = self.get_request_body(Request)
        stmt = select(BusinessOrder).select_from(BusinessOrder).where(BusinessOrder.id == query.business_order_id)
        if query.ignore_deleted is True:
            stmt = stmt.where(BusinessOrder.deleted.isnot(True))
        business_order: BusinessOrder | None = db.session.execute(stmt).scalars().first()
        if business_order is None or (unwrap_optional(business_order.shop).org_id != str(self.credential.org_id)):
            return Response.Failed(
                ResponseCode.BUSINESS_ORDER_NOT_FOUND, [ErrorDetail(loc=["business_order_id"], msg="工单不存在")]
            )
        shop = unwrap_optional(business_order.shop)
        data = BusinessOrderView(
            id=business_order.id,
            form_id=business_order.form_id,
            form_version_id=business_order.form_version.id,
            form_version_no=business_order.form_version.version_no,
            shop_sid=business_order.sid,
            shop_platform=shop.platform,
            shop_nick=shop.nick,
            shop_title=shop.title,
            status=business_order.status.name,
            deleted=bool(business_order.deleted),
            created_at=arrow.get(business_order.created_at, tzinfo=TIME_ZONE).format(FORMAT_DATE_TIME),
            updated_at=arrow.get(business_order.updated_at, tzinfo=TIME_ZONE).format(FORMAT_DATE_TIME),
            data=business_order.data.copy(),
        )
        response = OrderInfoResponse(data=data)
        return Response[OrderInfoResponse].Success(response)
