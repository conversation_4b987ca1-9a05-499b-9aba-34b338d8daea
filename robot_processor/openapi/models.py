"""
appKey 为租户级别的权限，只能访问自己租户的数据
"""

import datetime
import enum

import sqlalchemy as sa
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from typing_extensions import Self

from robot_processor.db import DbBaseModel
from robot_processor.utils import get_nonce


class OpenApiCredentials(DbBaseModel):
    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    org_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)
    app_key: Mapped[str] = mapped_column(sa.String(32), default=get_nonce)
    app_secret: Mapped[str] = mapped_column(sa.String(32), default=get_nonce)
    limit: Mapped[dict] = mapped_column(sa.JSON, nullable=False, default=lambda: {"default": "60/1minute"})

    class Status(enum.Enum):
        active = enum.auto()
        disable = enum.auto()

    status: Mapped[Status] = mapped_column(sa.Enum(Status), default=Status.active)
    created_at: Mapped[datetime.datetime] = mapped_column(sa.DateTime, server_default=sa.text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime, server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
    )  # noqa: E501

    @classmethod
    def query_by_app_key(cls, app_key) -> Self:
        return cls.query.filter(cls.app_key == app_key).first()  # type: ignore[return-value]

    def get_limit(self, method: str):
        return (
            "openapi:{}:{}".format(self.app_key, method),
            self.limit.get(method, "60/1minute"),
        )

    def as_account(self):
        from robot_processor.assistant.schema import AccountDetailV2
        from robot_processor.enums import Creator

        return AccountDetailV2(user_type=Creator.RPA, user_id=self.id, user_nick="开放平台接口")
