from flask.cli import AppGroup

cli = AppGroup("job-binlog")


@cli.command()
def start_consumer():
    """开始消费 topic=robotdb-job-binlog 的 kafka 消息"""
    from robot_processor.business_order.binlog.job_processor import exception_ruler_keeper
    from robot_processor.ext import job_binlog_consumer
    from robot_processor.utils import health_check

    health_check(True)
    # 在开始消费之前，先对 ruler 做一次初始化
    exception_ruler_keeper.update_exception_ruler()
    job_binlog_consumer.start_consume()
